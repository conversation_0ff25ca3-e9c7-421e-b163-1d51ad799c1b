#!/usr/bin/env bash
# Build script for Render.com deployment

set -o errexit  # exit on error

echo "🚀 Starting build process for Qurania LMS..."

# Set Django settings module for Render
export DJANGO_SETTINGS_MODULE=qurania_lms.settings_render

# Install Python dependencies with specific strategy
echo "📦 Installing Python dependencies..."
pip install --upgrade pip setuptools wheel
pip install --prefer-binary --no-build-isolation -r requirements.txt

# Create static directory if it doesn't exist
echo "📁 Preparing static files..."
mkdir -p staticfiles

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput --clear

# Run database migrations
echo "🗄️ Running database migrations..."
python manage.py migrate

# Create superuser if it doesn't exist
echo "👤 Creating superuser..."
python manage.py create_superuser_if_none

# Fix admin verification status
echo "🔧 Fixing admin verification status..."
python manage.py fix_admin_verification

# Setup production environment (create default templates, etc.)
echo "⚙️ Setting up production environment..."
python manage.py setup_production

# Create default email templates
echo "📧 Creating default email templates..."
python manage.py create_default_email_templates

# Test Django configuration
echo "🧪 Testing Django configuration..."
python manage.py check --deploy

# Show Django version and settings
echo "📋 Django configuration:"
python -c "
import django
from django.conf import settings
print(f'Django version: {django.get_version()}')
print(f'Settings module: {settings.SETTINGS_MODULE}')
print(f'Debug mode: {settings.DEBUG}')
print(f'Allowed hosts: {settings.ALLOWED_HOSTS}')
"

echo "✅ Build completed successfully!"
echo "🎉 Qurania LMS is ready for production!"
