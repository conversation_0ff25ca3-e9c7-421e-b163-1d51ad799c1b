{% extends 'lessons/base_lesson.html' %}
{% load static %}

{% block page_title %}حصصي{% endblock %}
{% block header_icon %}fas fa-chalkboard-teacher{% endblock %}
{% block page_description %}إدارة وعرض حصصك التعليمية{% endblock %}

{% block header_actions %}
<div class="flex flex-col sm:flex-row gap-2 sm:gap-4">
    <button onclick="showCalendarView()" 
            class="btn-primary flex items-center justify-center">
        <i class="fas fa-calendar-alt ml-2"></i>
        عرض التقويم
    </button>
    <a href="{% url 'lessons:teacher_availability' %}"
       class="btn-secondary flex items-center justify-center">
        <i class="fas fa-clock ml-2"></i>
        إدارة الأوقات المتاحة
    </a>
    <div class="relative">
        <button onclick="toggleDropdown('teacher-quick-actions')" 
                class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors flex items-center">
            <i class="fas fa-tools ml-2"></i>
            أدوات
        </button>
        <div id="teacher-quick-actions" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
            <a href="{% url 'lessons:teacher_reports' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <i class="fas fa-file-alt ml-2"></i>
                تقاريري
            </a>
            <a href="{% url 'lessons:teacher_students' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <i class="fas fa-users ml-2"></i>
                طلابي
            </a>
            <a href="{% url 'lessons:teacher_performance' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <i class="fas fa-chart-line ml-2"></i>
                إحصائياتي
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block lesson_stats %}
{% include 'components/lesson_stats.html' with stats=teacher_stats user_type='teacher' %}
{% endblock %}

{% block lesson_filters %}
{% include 'components/lesson_filters.html' with user_type='teacher' available_students=my_students %}
{% endblock %}

{% block lesson_content %}
<div class="space-y-6">
    
    <!-- الحصة القادمة -->
    {% if next_lesson %}
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-blue-900">
                <i class="fas fa-star text-yellow-500 ml-2"></i>
                حصتك القادمة
            </h3>
            <span class="text-sm text-blue-700" data-time-until="{{ next_lesson.scheduled_date|date:'c' }}">
                <!-- سيتم تحديثها بـ JavaScript -->
            </span>
        </div>
        
        <div class="bg-white rounded-lg p-4 border border-blue-100">
            {% include 'components/lesson_card.html' with lesson=next_lesson user_type='teacher' %}
        </div>
        
        {% if next_lesson.scheduled_date|time_until_minutes <= 15 %}
        <div class="mt-4 flex items-center justify-center">
            <a href="{% url 'lessons:lesson_room' next_lesson.id %}"
               class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center">
                <i class="fas fa-video ml-2"></i>
                دخول الحصة الآن
            </a>
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- الحصص الجارية -->
    {% if live_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-green-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-green-900">
                <i class="fas fa-video text-green-600 ml-2"></i>
                الحصص الجارية
            </h3>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 animate-pulse">
                <span class="w-2 h-2 bg-green-600 rounded-full ml-2"></span>
                {{ live_lessons|length }} جارية
            </span>
        </div>
        <div class="space-y-3">
            {% for lesson in live_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- حصص اليوم -->
    {% if today_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-calendar-day text-blue-600 ml-2"></i>
                حصص اليوم ({{ today_lessons|length }})
            </h3>
            <span class="text-sm text-gray-500">{{ today_date|date:"l، Y/m/d" }}</span>
        </div>
        <div class="space-y-3">
            {% for lesson in today_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- الحصص المكتملة التي تحتاج تقارير -->
    {% if pending_reports %}
    <div class="bg-yellow-50 rounded-lg border border-yellow-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-yellow-900">
                <i class="fas fa-exclamation-triangle text-yellow-600 ml-2"></i>
                حصص تحتاج تقارير ({{ pending_reports|length }})
            </h3>
            <button onclick="submitAllReports()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm">
                <i class="fas fa-file-alt ml-1"></i>
                كتابة جميع التقارير
            </button>
        </div>
        <div class="space-y-3">
            {% for lesson in pending_reports %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- جميع الحصص -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-history text-gray-600 ml-2"></i>
                جميع حصصي
            </h3>
            <div class="flex items-center space-x-2 space-x-reverse">
                <select id="period-filter" class="form-control text-sm" onchange="filterByPeriod(this.value)">
                    <option value="all">جميع الفترات</option>
                    <option value="today">اليوم</option>
                    <option value="week">هذا الأسبوع</option>
                    <option value="month">هذا الشهر</option>
                    <option value="last_month">الشهر الماضي</option>
                </select>
                <button onclick="toggleCalendarView()" class="p-2 text-gray-600 hover:text-gray-800">
                    <i class="fas fa-calendar"></i>
                </button>
            </div>
        </div>
        
        <div id="lessons-container" class="space-y-3">
            {% for lesson in lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% empty %}
                <div class="text-center py-12">
                    <i class="fas fa-chalkboard-teacher text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد حصص</h3>
                    <p class="text-gray-500 mb-4">لم يتم جدولة أي حصص لك بعد</p>
                    <a href="{% url 'lessons:teacher_availability' %}" class="btn-primary">
                        <i class="fas fa-clock ml-2"></i>
                        تحديث أوقاتك المتاحة
                    </a>
                </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if lessons.has_other_pages %}
        <div class="mt-6 flex justify-center">
            <nav class="flex items-center space-x-2 space-x-reverse">
                {% if lessons.has_previous %}
                    <a href="?page={{ lessons.previous_page_number }}" 
                       class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                {% endif %}
                
                {% for num in lessons.paginator.page_range %}
                    {% if lessons.number == num %}
                        <span class="px-3 py-2 text-sm bg-blue-600 text-white rounded">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}" 
                           class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}
                
                {% if lessons.has_next %}
                    <a href="?page={{ lessons.next_page_number }}" 
                       class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<!-- Calendar View (مخفي افتراضياً) -->
<div id="calendar-view" class="hidden">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-calendar-alt text-blue-600 ml-2"></i>
                التقويم الشهري
            </h3>
            <button onclick="hideCalendarView()" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="calendar-container">
            <!-- سيتم إنشاؤه بـ JavaScript -->
        </div>
    </div>
</div>
{% endblock %}

{% block lesson_js %}
<script>
document.body.dataset.userType = 'teacher';

function filterByPeriod(period) {
    const params = new URLSearchParams(window.location.search);
    params.set('period', period);
    window.location.href = `${window.location.pathname}?${params.toString()}`;
}

function showCalendarView() {
    document.getElementById('calendar-view').classList.remove('hidden');
    // إنشاء التقويم هنا
    createCalendar();
}

function hideCalendarView() {
    document.getElementById('calendar-view').classList.add('hidden');
}

function toggleCalendarView() {
    const calendarView = document.getElementById('calendar-view');
    if (calendarView.classList.contains('hidden')) {
        showCalendarView();
    } else {
        hideCalendarView();
    }
}

function createCalendar() {
    // تنفيذ بسيط للتقويم - يمكن تحسينه لاحقاً
    const container = document.getElementById('calendar-container');
    container.innerHTML = `
        <div class="text-center py-8">
            <i class="fas fa-calendar-alt text-gray-400 text-4xl mb-4"></i>
            <p class="text-gray-600">سيتم إضافة التقويم التفاعلي قريباً</p>
        </div>
    `;
}

function submitAllReports() {
    // فتح modal لكتابة التقارير المتعددة
    alert('سيتم إضافة هذه الميزة قريباً');
}

// تحديث العد التنازلي للحصة القادمة
function updateNextLessonCountdown() {
    const countdownElement = document.querySelector('[data-time-until]');
    if (countdownElement) {
        const targetTime = new Date(countdownElement.dataset.timeUntil);
        const now = new Date();
        const diff = targetTime - now;
        
        if (diff > 0) {
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            
            if (hours > 0) {
                countdownElement.textContent = `خلال ${hours} ساعة و ${minutes} دقيقة`;
            } else if (minutes > 0) {
                countdownElement.textContent = `خلال ${minutes} دقيقة`;
            } else {
                countdownElement.textContent = 'حان الوقت!';
                countdownElement.classList.add('text-red-600', 'font-bold', 'animate-pulse');
            }
        } else {
            countdownElement.textContent = 'انتهى الوقت';
        }
    }
}

// تحديث العد التنازلي كل دقيقة
setInterval(updateNextLessonCountdown, 60000);
updateNextLessonCountdown(); // تشغيل فوري

// تحديث تلقائي للحصص الجارية
setInterval(() => {
    if (document.querySelector('.animate-pulse')) {
        window.lessonsManager?.refreshLessonsData();
    }
}, 30000);
</script>
{% endblock %}
