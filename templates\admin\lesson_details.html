{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الحصة - {{ SITE_NAME }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-islamic-primary mb-2">
                    <i class="fas fa-info-circle text-islamic-gold ml-3"></i>
                    تفاصيل الحصة
                </h1>
                <p class="text-gray-600">{{ lesson.title }}</p>
            </div>
            <div class="flex gap-3">
                <a href="{% url 'admin_lessons' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-arrow-right ml-1"></i>
                    العودة للحصص
                </a>
                {% if lesson.status == 'in_progress' %}
                    <a href="{{ lesson.get_jitsi_url }}" target="_blank"
                       class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-video ml-1"></i>
                        مراقبة الحصة
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Lesson Info -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Basic Info -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-islamic-primary mb-4">
                    <i class="fas fa-info text-islamic-gold ml-2"></i>
                    معلومات الحصة
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">عنوان الحصة</label>
                        <p class="text-gray-900 font-medium">{{ lesson.title }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            {% if lesson.status == 'scheduled' %}bg-blue-100 text-blue-800
                            {% elif lesson.status == 'in_progress' %}bg-green-100 text-green-800
                            {% elif lesson.status == 'completed' %}bg-gray-100 text-gray-800
                            {% elif lesson.status == 'cancelled' %}bg-red-100 text-red-800
                            {% elif lesson.status == 'missed' %}bg-yellow-100 text-yellow-800
                            {% endif %}">
                            {{ lesson.get_status_display }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الموعد المجدول</label>
                        <p class="text-gray-900">{{ lesson.scheduled_date|date:"Y-m-d H:i" }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">المدة</label>
                        <p class="text-gray-900">{{ lesson.duration_minutes }} دقيقة</p>
                    </div>
                    
                    {% if lesson.started_at %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">وقت البداية الفعلي</label>
                        <p class="text-gray-900">{{ lesson.started_at|date:"Y-m-d H:i" }}</p>
                    </div>
                    {% endif %}

                    {% if lesson.ended_at %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">وقت النهاية الفعلي</label>
                        <p class="text-gray-900">{{ lesson.ended_at|date:"Y-m-d H:i" }}</p>
                    </div>
                    {% endif %}
                </div>
                
                {% if lesson.description %}
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">وصف الحصة</label>
                    <p class="text-gray-900 bg-gray-50 p-3 rounded-lg">{{ lesson.description }}</p>
                </div>
                {% endif %}
                
                {% if lesson.teacher_notes %}
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">ملاحظات المعلم</label>
                    <p class="text-gray-900 bg-blue-50 p-3 rounded-lg">{{ lesson.teacher_notes }}</p>
                </div>
                {% endif %}
                
                {% if lesson.admin_notes %}
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">ملاحظات الإدارة</label>
                    <p class="text-gray-900 bg-yellow-50 p-3 rounded-lg">{{ lesson.admin_notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Participants -->
        <div>
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-islamic-primary mb-4">
                    <i class="fas fa-users text-islamic-gold ml-2"></i>
                    المشاركون
                </h2>
                
                <!-- Student -->
                <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-user-graduate text-blue-600 text-xl ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-900">الطالب</p>
                            <p class="text-blue-600">{{ lesson.enrollment.student.get_full_name }}</p>
                            <p class="text-sm text-gray-600">{{ lesson.enrollment.student.email }}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Teacher -->
                <div class="mb-4 p-3 bg-green-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-chalkboard-teacher text-green-600 text-xl ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-900">المعلم</p>
                            <p class="text-green-600">{{ lesson.enrollment.teacher.get_full_name }}</p>
                            <p class="text-sm text-gray-600">{{ lesson.enrollment.teacher.email }}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Course -->
                <div class="p-3 bg-purple-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-book text-purple-600 text-xl ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-900">الدورة</p>
                            <p class="text-purple-600">{{ lesson.enrollment.course.title }}</p>
                            <p class="text-sm text-gray-600">{{ lesson.enrollment.course.get_course_type_display }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Records -->
    {% if attendance_records %}
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-bold text-islamic-primary mb-4">
            <i class="fas fa-clock text-islamic-gold ml-2"></i>
            سجل الحضور
        </h2>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">وقت الدخول</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">وقت الخروج</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مدة الحضور</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for attendance in attendance_records %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <i class="fas fa-user text-gray-400 ml-2"></i>
                                <span class="text-sm font-medium text-gray-900">{{ attendance.user.get_full_name }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ attendance.joined_at|date:"H:i:s" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if attendance.left_at %}
                                {{ attendance.left_at|date:"H:i:s" }}
                            {% else %}
                                <span class="text-green-600">لا يزال متصل</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ attendance.duration_minutes }} دقيقة
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- Lesson Content -->
    {% if content_records %}
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-bold text-islamic-primary mb-4">
            <i class="fas fa-book-open text-islamic-gold ml-2"></i>
            محتوى الحصة
        </h2>
        
        <div class="space-y-4">
            {% for content in content_records %}
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                        {{ content.get_content_type_display }}
                    </span>
                    {% if content.quality_score %}
                    <span class="text-sm text-gray-600">
                        التقييم: {{ content.quality_score }}/10
                    </span>
                    {% endif %}
                </div>
                
                <h3 class="font-medium text-gray-900 mb-2">{{ content.surah_name }}</h3>
                <p class="text-sm text-gray-600 mb-2">
                    من الآية {{ content.from_verse }} إلى الآية {{ content.to_verse }}
                </p>
                
                {% if content.notes %}
                <p class="text-sm text-gray-700 bg-gray-50 p-2 rounded">{{ content.notes }}</p>
                {% endif %}
                
                <p class="text-xs text-gray-500 mt-2">
                    سجل بواسطة: {{ content.recorded_by.get_full_name }} - {{ content.recorded_at|date:"Y-m-d H:i" }}
                </p>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Rating -->
    {% if rating %}
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-bold text-islamic-primary mb-4">
            <i class="fas fa-star text-islamic-gold ml-2"></i>
            تقييم الحصة
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div class="text-center">
                <p class="text-sm text-gray-600">تقييم المعلم</p>
                <p class="text-2xl font-bold text-yellow-600">{{ rating.teacher_rating }}/5</p>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-600">جودة الحصة</p>
                <p class="text-2xl font-bold text-blue-600">{{ rating.lesson_quality }}/5</p>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-600">الجودة التقنية</p>
                <p class="text-2xl font-bold text-green-600">{{ rating.technical_quality }}/5</p>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-600">الرضا العام</p>
                <p class="text-2xl font-bold text-purple-600">{{ rating.overall_satisfaction }}/5</p>
            </div>
        </div>
        
        {% if rating.comment %}
        <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600 mb-1">تعليق الطالب:</p>
            <p class="text-gray-900">{{ rating.comment }}</p>
        </div>
        {% endif %}
        
        <p class="text-xs text-gray-500 mt-3">
            تم التقييم في: {{ rating.created_at|date:"Y-m-d H:i" }}
        </p>
    </div>
    {% endif %}
</div>
{% endblock %}
