"""
إعدادات تطبيق المستخدمين مع تشغيل المجدول التلقائي
"""

from django.apps import AppConfig
import threading
import time
import logging

logger = logging.getLogger(__name__)


class UsersConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'users'
    verbose_name = 'إدارة المستخدمين'

    def ready(self):
        """تفعيل الإشارات وتشغيل المجدول التلقائي عند بدء Django"""
        # تعطيل الإشارات أثناء البناء والمايجريشن
        import os
        if os.environ.get('DJANGO_SETTINGS_MODULE') == 'qurania_lms.settings_render':
            # في بيئة الإنتاج، لا نحمل الإشارات أثناء البناء
            return

        import users.signals

        # تشغيل المجدول التلقائي فقط مع runserver
        import sys
        import os

        logger.info(f"🔍 فحص شروط تشغيل المجدول...")
        logger.info(f"sys.argv: {sys.argv}")
        logger.info(f"RUN_MAIN: {os.environ.get('RUN_MAIN')}")

        # تعطيل المجدول التلقائي مؤقتاً حتى يتم حل مشاكل الاستيراد
        logger.info("⚠️ المجدول التلقائي معطل مؤقتاً - يمكن تشغيله يدوياً من لوحة التحكم")
        # if ('runserver' in sys.argv or
        #     os.environ.get('RUN_MAIN') == 'true'):  # للتأكد من عدم التشغيل المزدوج
        #     logger.info("✅ شروط التشغيل متوفرة - بدء المجدول...")
        #     self.start_background_scheduler()
        # else:
        #     logger.info("❌ شروط التشغيل غير متوفرة - لن يتم تشغيل المجدول")

    def start_background_scheduler(self):
        """بدء المجدول في الخلفية"""
        def run_scheduler():
            try:
                # انتظار قليل للتأكد من تحميل Django بالكامل
                time.sleep(5)

                from .services import BackgroundLessonScheduler
                scheduler = BackgroundLessonScheduler()
                scheduler.start()

            except Exception as e:
                logger.error(f"خطأ في تشغيل المجدول التلقائي: {e}")

        # تشغيل المجدول في thread منفصل
        scheduler_thread = threading.Thread(
            target=run_scheduler,
            daemon=True,  # ينتهي مع انتهاء البرنامج الرئيسي
            name="LessonReminderScheduler"
        )
        scheduler_thread.start()
        logger.info("🚀 تم بدء المجدول التلقائي للتذكيرات")
