# Generated by Django 4.2.7 on 2025-06-19 01:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('subscriptions', '0003_remove_monitoring_constraints'),
        ('lessons', '0009_alter_livelesson_jitsi_room_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='Lesson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lesson_type', models.CharField(choices=[('subscription', 'حصة من الباقة'), ('trial', 'حصة تجريبية'), ('makeup', 'حصة تعويضية')], max_length=20, verbose_name='نوع الحصة')),
                ('lesson_number', models.PositiveIntegerField(blank=True, null=True, verbose_name='رقم الحصة في الباقة')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الحصة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الحصة')),
                ('scheduled_date', models.DateTimeField(verbose_name='موعد الحصة')),
                ('duration_minutes', models.PositiveIntegerField(choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة'), (90, '90 دقيقة')], default=45, verbose_name='مدة الحصة (بالدقائق)')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت البداية الفعلي')),
                ('ended_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت النهاية الفعلي')),
                ('status', models.CharField(choices=[('scheduled', 'مجدولة'), ('live', 'جارية'), ('completed', 'مكتملة'), ('rated', 'تم تقييمها'), ('cancelled_by_student', 'ألغاها الطالب'), ('cancelled_by_teacher', 'ألغاها المعلم'), ('cancelled_by_admin', 'ألغاها المدير'), ('rescheduled', 'تم إعادة جدولتها'), ('no_show_student', 'غياب الطالب'), ('no_show_teacher', 'غياب المعلم')], default='scheduled', max_length=25, verbose_name='حالة الحصة')),
                ('cancelled_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الإلغاء')),
                ('cancellation_reason', models.CharField(blank=True, choices=[('emergency', 'ظرف طارئ'), ('illness', 'مرض'), ('technical_issue', 'مشكلة تقنية'), ('schedule_conflict', 'تضارب في المواعيد'), ('personal_reason', 'سبب شخصي'), ('other', 'أخرى')], max_length=20, verbose_name='سبب الإلغاء')),
                ('cancellation_note', models.TextField(blank=True, verbose_name='ملاحظة الإلغاء')),
                ('rescheduled_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت إعادة الجدولة')),
                ('reschedule_reason', models.TextField(blank=True, verbose_name='سبب إعادة الجدولة')),
                ('jitsi_room_id', models.CharField(blank=True, max_length=100, unique=True, verbose_name='معرف غرفة Jitsi')),
                ('jitsi_room_password', models.CharField(blank=True, max_length=20, verbose_name='كلمة مرور الغرفة')),
                ('teacher_report', models.TextField(blank=True, verbose_name='تقرير المعلم')),
                ('teacher_report_submitted', models.BooleanField(default=False, verbose_name='تم إرسال تقرير المعلم')),
                ('student_evaluation_submitted', models.BooleanField(default=False, verbose_name='تم إرسال تقييم الطالب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('cancelled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cancelled_lessons', to=settings.AUTH_USER_MODEL, verbose_name='ألغيت بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_lessons', to=settings.AUTH_USER_MODEL, verbose_name='منشئ الحصة')),
                ('original_lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='makeup_lessons', to='lessons.lesson', verbose_name='الحصة الأصلية')),
                ('rescheduled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rescheduled_lessons', to=settings.AUTH_USER_MODEL, verbose_name='أعيد جدولتها بواسطة')),
                ('student', models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='lessons_as_student', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
                ('subscription', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='unified_lessons', to='subscriptions.studentsubscription', verbose_name='الاشتراك')),
                ('teacher', models.ForeignKey(limit_choices_to={'user_type': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='lessons_as_teacher', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'حصة',
                'verbose_name_plural': 'الحصص',
                'ordering': ['scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='StudentEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_rating', models.PositiveIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='التقييم العام')),
                ('teaching_quality', models.PositiveIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='جودة التدريس')),
                ('lesson_content', models.PositiveIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='محتوى الحصة')),
                ('interaction_quality', models.PositiveIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='جودة التفاعل')),
                ('punctuality', models.PositiveIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='الالتزام بالوقت')),
                ('would_recommend', models.BooleanField(blank=True, null=True, verbose_name='هل تنصح بالمعلم؟')),
                ('interested_in_subscription', models.BooleanField(blank=True, null=True, verbose_name='مهتم بالاشتراك؟')),
                ('comment', models.TextField(blank=True, default='', verbose_name='تعليق')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقييم')),
                ('lesson', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='evaluation', to='lessons.lesson', verbose_name='الحصة')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submitted_evaluations', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_evaluations', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'تقييم الطالب',
                'verbose_name_plural': 'تقييمات الطلاب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MakeupRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField(verbose_name='سبب الطلب')),
                ('request_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('completed', 'مكتمل')], default='pending', max_length=15, verbose_name='حالة الطلب')),
                ('reviewed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المراجعة')),
                ('admin_notes', models.TextField(blank=True, verbose_name='ملاحظات الإدارة')),
                ('makeup_lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='makeup_request', to='lessons.lesson', verbose_name='الحصة التعويضية')),
                ('original_lesson', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='makeup_requests', to='lessons.lesson', verbose_name='الحصة الأصلية')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='makeup_requests', to=settings.AUTH_USER_MODEL, verbose_name='طالب الحصة التعويضية')),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_makeup_requests', to=settings.AUTH_USER_MODEL, verbose_name='راجعه')),
            ],
            options={
                'verbose_name': 'طلب حصة تعويضية',
                'verbose_name_plural': 'طلبات الحصص التعويضية',
                'ordering': ['-request_date'],
            },
        ),
        migrations.AlterField(
            model_name='lessonattendance',
            name='lesson',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='lessons.lesson', verbose_name='الحصة'),
        ),
        migrations.AlterField(
            model_name='lessoncontent',
            name='lesson',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_records', to='lessons.lesson', verbose_name='الحصة'),
        ),
        migrations.AddIndex(
            model_name='lesson',
            index=models.Index(fields=['lesson_type', 'status'], name='lessons_les_lesson__1d853c_idx'),
        ),
        migrations.AddIndex(
            model_name='lesson',
            index=models.Index(fields=['teacher', 'scheduled_date'], name='lessons_les_teacher_6db48f_idx'),
        ),
        migrations.AddIndex(
            model_name='lesson',
            index=models.Index(fields=['student', 'scheduled_date'], name='lessons_les_student_83f19b_idx'),
        ),
        migrations.AddIndex(
            model_name='lesson',
            index=models.Index(fields=['subscription', 'lesson_number'], name='lessons_les_subscri_18c3bf_idx'),
        ),
    ]
