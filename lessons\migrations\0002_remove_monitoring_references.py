# Generated by Django 4.2.7 on 2025-06-08 01:42

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0001_initial'),
    ]

    operations = [
        # حذف أي جداول متبقية من نظام المراقبة
        migrations.RunSQL(
            "DROP TABLE IF EXISTS lessons_universallessonmonitoring;",
            reverse_sql="-- لا يمكن التراجع عن حذف الجدول"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS lessons_lessonmonitoring;",
            reverse_sql="-- لا يمكن التراجع عن حذف الجدول"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS lessons_monitoringsession;",
            reverse_sql="-- لا يمكن التراجع عن حذف الجدول"
        ),
    ]
