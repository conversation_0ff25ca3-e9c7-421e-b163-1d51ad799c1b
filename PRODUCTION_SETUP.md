# 🚀 إعداد الإنتاج - قرآنيا LMS

## 📋 نظرة عامة
هذا الدليل يوضح كيفية إعداد النظام تلقائياً في بيئة الإنتاج على Render.com

## 🔄 العملية التلقائية

### 1️⃣ **أثناء البناء (Build Phase)**
يتم تشغيل `build.sh` تلقائياً والذي يقوم بـ:
- ✅ تثبيت المتطلبات
- ✅ تشغيل migrations
- ✅ جمع الملفات الثابتة
- ✅ إنشاء مدير عام افتراضي
- ✅ **إعداد نظام البريد الإلكتروني**
- ✅ **إنشاء قوالب البريد الجاهزة**

### 2️⃣ **أثناء التشغيل (Runtime)**
يتم تشغيل `start.sh` لبدء التطبيق

## 📧 قوالب البريد الإلكتروني الجاهزة

### 🆕 **القوالب التي يتم إنشاؤها تلقائياً:**

#### 1. **تذكير بالحصة - 24 ساعة**
- **الحدث:** `lesson_reminder_24h`
- **الموضوع:** تذكير: لديك حصة غداً
- **المحتوى:** تفاصيل الحصة والمعلم والوقت

#### 2. **تذكير بالحصة - 30 دقيقة**
- **الحدث:** `lesson_reminder_30min`
- **الموضوع:** تذكير عاجل: حصتك تبدأ خلال 30 دقيقة
- **المحتوى:** تذكير عاجل مع رابط مباشر

#### 3. **تذكير انتهاء الاشتراك - 7 أيام**
- **الحدث:** `subscription_expiry_7days`
- **الموضوع:** تذكير: اشتراكك ينتهي خلال أسبوع
- **المحتوى:** تفاصيل الاشتراك ورابط التجديد

## 🔧 الأوامر الإدارية الجديدة

### 📝 **إنشاء قوالب البريد**
```bash
python manage.py create_default_email_templates
```

### ⚙️ **إعداد SMTP**
```bash
python manage.py setup_default_email_settings
```

### 🚀 **إعداد شامل للإنتاج**
```bash
python manage.py setup_production
```

## 📋 خطوات ما بعد النشر

### 1️⃣ **تفعيل نظام البريد الإلكتروني**
1. الدخول إلى لوحة الإدارة
2. الذهاب إلى: **الإعدادات التقنية > إعدادات البريد الإلكتروني**
3. تعبئة بيانات SMTP:
   - **الخادم:** smtp.gmail.com (للـ Gmail)
   - **المنفذ:** 587
   - **اسم المستخدم:** بريدك الإلكتروني
   - **كلمة المرور:** كلمة مرور التطبيق
   - **TLS:** مفعل
4. **تفعيل النظام**

### 2️⃣ **مراجعة القوالب**
1. الذهاب إلى: **الإعدادات التقنية > قوالب البريد الإلكتروني**
2. مراجعة القوالب الجاهزة
3. تخصيص المحتوى حسب الحاجة
4. إضافة قوالب جديدة إذا لزم الأمر

### 3️⃣ **اختبار النظام**
1. الذهاب إلى: **الإعدادات التقنية > اختبار البريد الإلكتروني**
2. إرسال بريد تجريبي
3. التأكد من وصول البريد

## 🎯 المتغيرات المتاحة في القوالب

### 📧 **متغيرات الأكاديمية:**
- `{{ ACADEMY_NAME }}` - اسم الأكاديمية
- `{{ ACADEMY_SLOGAN }}` - شعار الأكاديمية
- `{{ ACADEMY_ADDRESS }}` - عنوان الأكاديمية
- `{{ ACADEMY_PHONE }}` - هاتف الأكاديمية
- `{{ ACADEMY_EMAIL }}` - بريد الأكاديمية

### 👤 **متغيرات المستخدم:**
- `{{ student_name }}` - اسم الطالب
- `{{ teacher_name }}` - اسم المعلم

### 📚 **متغيرات الحصة:**
- `{{ lesson_title }}` - عنوان الحصة
- `{{ lesson_date }}` - تاريخ الحصة
- `{{ lesson_time }}` - وقت الحصة
- `{{ lesson_duration }}` - مدة الحصة

### 📋 **متغيرات الاشتراك:**
- `{{ subscription_plan }}` - اسم الباقة
- `{{ expiry_date }}` - تاريخ الانتهاء
- `{{ remaining_lessons }}` - الحصص المتبقية

### 🔗 **الروابط:**
- `{{ dashboard_url }}` - رابط لوحة التحكم
- `{{ subscription_url }}` - رابط الاشتراكات

## 🔍 استكشاف الأخطاء

### ❌ **القوالب لا تظهر:**
```bash
# تشغيل الأمر يدوياً
python manage.py create_default_email_templates
```

### ❌ **إعدادات SMTP مفقودة:**
```bash
# إنشاء إعدادات افتراضية
python manage.py setup_default_email_settings
```

### ❌ **إعداد شامل:**
```bash
# إعادة إعداد كامل
python manage.py setup_production
```

## 📞 الدعم
في حالة وجود مشاكل، تحقق من:
1. سجلات Render
2. إعدادات قاعدة البيانات
3. متغيرات البيئة
4. إعدادات SMTP

---

## 🎉 النتيجة
بعد النشر، ستجد:
- ✅ قوالب البريد جاهزة ومتاحة
- ✅ إعدادات SMTP افتراضية
- ✅ نظام بريد إلكتروني كامل
- ✅ أوامر إدارية شاملة

**🚀 النظام جاهز للاستخدام فوراً!**
