from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone


class User(AbstractUser):
    """نموذج المستخدم المخصص"""

    # تخصيص حقل البريد الإلكتروني ليكون فريد
    email = models.EmailField(
        _('email address'),
        unique=True,
        error_messages={
            'unique': _("مستخدم بهذا البريد الإلكتروني موجود بالفعل."),
        },
    )

    USER_TYPES = (
        ('admin', _('مدير')),
        ('teacher', _('معلم')),
        ('student', _('طالب')),
    )

    STUDENT_LEVELS = (
        ('beginner', _('مبتدئ')),
        ('intermediate', _('متوسط')),
        ('advanced', _('متقدم')),
    )

    user_type = models.CharField(
        max_length=10,
        choices=USER_TYPES,
        default='student',
        verbose_name=_('نوع المستخدم')
    )

    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم الهاتف')
    )

    date_of_birth = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الميلاد')
    )

    # خاص بالطلاب
    student_level = models.CharField(
        max_length=15,
        choices=STUDENT_LEVELS,
        blank=True,
        null=True,
        verbose_name=_('مستوى الطالب')
    )

    # خاص بالمعلمين
    hourly_rate_30 = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('أجر الحصة 30 دقيقة')
    )

    hourly_rate_45 = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('أجر الحصة 45 دقيقة')
    )

    hourly_rate_60 = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('أجر الحصة 60 دقيقة')
    )

    # نسب عمولة المعلم حسب مدة الحصة
    commission_rate_30 = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=70.00,
        verbose_name=_('نسبة العمولة - 30 دقيقة %')
    )

    commission_rate_45 = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=70.00,
        verbose_name=_('نسبة العمولة - 45 دقيقة %')
    )

    commission_rate_60 = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=70.00,
        verbose_name=_('نسبة العمولة - 60 دقيقة %')
    )

    is_active_teacher = models.BooleanField(
        default=True,
        verbose_name=_('معلم نشط')
    )

    # حقول التحقق من التسجيل
    VERIFICATION_STATUS_CHOICES = (
        ('pending', _('في انتظار المراجعة')),
        ('approved', _('تم الموافقة')),
        ('rejected', _('تم الرفض')),
        ('under_review', _('قيد المراجعة')),
    )

    verification_status = models.CharField(
        max_length=15,
        choices=VERIFICATION_STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة التحقق')
    )

    verification_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات التحقق')
    )

    verified_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'admin'},
        related_name='verified_users',
        verbose_name=_('تم التحقق بواسطة')
    )

    verified_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ التحقق')
    )

    rejection_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('سبب الرفض')
    )

    # حقل لتتبع ما إذا كان الحساب نشطًا من قبل
    was_active = models.BooleanField(
        default=False,
        verbose_name=_('كان نشطًا سابقًا')
    )

    # حقول نظام الحظر
    is_banned = models.BooleanField(
        default=False,
        verbose_name=_('محظور')
    )

    ban_type = models.CharField(
        max_length=20,
        choices=(
            ('temporary', _('مؤقت')),
            ('permanent', _('دائم')),
        ),
        blank=True,
        null=True,
        verbose_name=_('نوع الحظر')
    )

    ban_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('سبب الحظر')
    )

    banned_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الحظر')
    )

    banned_until = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ انتهاء الحظر')
    )

    banned_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'admin'},
        related_name='banned_users',
        verbose_name=_('تم الحظر بواسطة')
    )

    # معلومات إضافية
    profile_picture = models.ImageField(
        upload_to='profile_pics/',
        blank=True,
        null=True,
        verbose_name=_('صورة الملف الشخصي')
    )

    bio = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('نبذة شخصية')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')

    def __str__(self):
        return f"{self.get_full_name()} ({self.get_user_type_display()})"

    def get_full_name(self):
        """إرجاع الاسم الكامل"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username

    def is_admin(self):
        """التحقق من كون المستخدم مدير"""
        return self.user_type == 'admin'

    def is_teacher(self):
        """التحقق من كون المستخدم معلم"""
        return self.user_type == 'teacher'

    def is_student(self):
        """التحقق من كون المستخدم طالب"""
        return self.user_type == 'student'

    def get_lesson_rate(self, duration_minutes):
        """الحصول على سعر الحصة حسب المدة"""
        if duration_minutes == 30:
            return self.hourly_rate_30 or 50.00
        elif duration_minutes == 45:
            return self.hourly_rate_45 or 70.00
        elif duration_minutes == 60:
            return self.hourly_rate_60 or 90.00
        else:
            return 70.00  # سعر افتراضي

    def get_commission_rate(self, duration_minutes):
        """الحصول على نسبة العمولة حسب مدة الحصة"""
        if duration_minutes == 30:
            return self.commission_rate_30
        elif duration_minutes == 45:
            return self.commission_rate_45
        elif duration_minutes == 60:
            return self.commission_rate_60
        else:
            return 70.00  # نسبة افتراضية

    def calculate_teacher_earning(self, lesson_price, duration_minutes):
        """حساب ربح المعلم من الحصة"""
        from decimal import Decimal
        commission_rate = self.get_commission_rate(duration_minutes)
        # تحويل commission_rate إلى Decimal لضمان التوافق
        commission_rate = Decimal(str(commission_rate))
        return (Decimal(str(lesson_price)) * commission_rate) / Decimal('100')

    def is_online(self):
        """التحقق من كون المستخدم متصل"""
        try:
            return self.user_status.is_online
        except:
            return False

    def get_last_seen(self):
        """الحصول على آخر ظهور للمستخدم"""
        try:
            return self.user_status.last_seen
        except:
            return None

    def is_verified(self):
        """التحقق من كون المستخدم تم التحقق منه"""
        return self.verification_status == 'approved'

    def is_pending_verification(self):
        """التحقق من كون المستخدم في انتظار التحقق"""
        return self.verification_status == 'pending'

    def is_rejected(self):
        """التحقق من كون المستخدم مرفوض"""
        return self.verification_status == 'rejected'

    def can_access_dashboard(self):
        """التحقق من إمكانية الوصول للوحة التحكم"""
        # المديرين يمكنهم الوصول دائماً
        if self.user_type == 'admin':
            return True

        # التحقق من الحظر أولاً - المستخدمين المحظورين لا يمكنهم الوصول للوحة التحكم
        if self.is_currently_banned():
            return False

        # المستخدمين الآخرين يحتاجون للتحقق والتفعيل
        return self.is_verified() and self.is_active

    def approve_verification(self, admin_user, notes=None):
        """الموافقة على التحقق"""
        from django.utils import timezone

        self.verification_status = 'approved'
        self.verified_by = admin_user
        self.verified_at = timezone.now()
        self.verification_notes = notes or ''
        self.is_active = True  # تفعيل الحساب
        self.was_active = True  # تعيين الحقل الجديد عند تفعيل الحساب
        self.save()

        # إرسال إشعار للمستخدم
        self._send_verification_notification('approved')

        # إرسال رسالة ترحيبية عند الموافقة على الحساب
        try:
            from users.views import _send_welcome_message
            _send_welcome_message(self, is_approved=True)
        except ImportError:
            pass

        # إرسال إشعار موافقة عبر النظام الجديد
        self._send_admin_action_notification('approved', admin_user, notes)

    def reject_verification(self, admin_user, reason, notes=None):
        """رفض التحقق"""
        from django.utils import timezone

        self.verification_status = 'rejected'
        self.verified_by = admin_user
        self.verified_at = timezone.now()
        self.rejection_reason = reason
        self.verification_notes = notes or ''
        self.is_active = False  # إلغاء تفعيل الحساب
        self.save()

        # إرسال إشعار للمستخدم
        self._send_verification_notification('rejected')

        # إرسال إشعار رفض عبر النظام الجديد
        self._send_admin_action_notification('rejected', admin_user, reason)

    def _send_verification_notification(self):
        """إرسال إشعار حالة التحقق"""
        try:
            # استخدام النظام الجديد للإشعارات
            pass  # تم استبداله بالنظام الجديد في _send_admin_action_notification
        except Exception:
            pass  # في حالة عدم وجود نظام الإشعارات

    def get_verification_status_display_ar(self):
        """عرض حالة التحقق بالعربية"""
        status_map = {
            'pending': 'في انتظار المراجعة',
            'approved': 'تم الموافقة',
            'rejected': 'تم الرفض',
            'under_review': 'قيد المراجعة',
        }
        return status_map.get(self.verification_status, self.verification_status)

    def is_currently_banned(self):
        """التحقق من كون المستخدم محظور حالياً"""
        if not self.is_banned:
            return False

        # إذا كان الحظر دائم
        if self.ban_type == 'permanent':
            return True

        # إذا كان الحظر مؤقت، تحقق من انتهاء المدة
        if self.ban_type == 'temporary' and self.banned_until:
            from django.utils import timezone
            return timezone.now() < self.banned_until

        return False

    def get_ban_status_display(self):
        """عرض حالة الحظر"""
        if not self.is_banned:
            return "غير محظور"

        if self.ban_type == 'permanent':
            return "محظور نهائياً"

        if self.ban_type == 'temporary' and self.banned_until:
            from django.utils import timezone
            if timezone.now() >= self.banned_until:
                return "انتهى الحظر"
            else:
                return f"محظور حتى {self.banned_until.strftime('%Y-%m-%d %H:%M')}"

        return "محظور"

    def ban_user(self, admin_user, ban_type, reason, banned_until=None):
        """حظر المستخدم"""
        from django.utils import timezone

        # حفظ حالة is_active الحالية في was_active
        if self.is_active and not self.was_active:
            self.was_active = True

        self.is_banned = True
        self.ban_type = ban_type
        self.ban_reason = reason
        self.banned_at = timezone.now()
        self.banned_by = admin_user
        self.banned_until = banned_until if ban_type == 'temporary' else None
        # تعطيل الحساب عند الحظر
        self.is_active = False
        self.save()

        # إرسال إشعار حظر
        self._send_admin_action_notification('suspended', admin_user, reason, banned_until)

    def unban_user(self, admin_user=None):
        """إلغاء حظر المستخدم"""
        self.is_banned = False
        self.ban_type = None
        self.ban_reason = None
        self.banned_at = None
        self.banned_until = None
        self.banned_by = None

        # إذا كان المستخدم نشطًا سابقًا، نقوم بتفعيل الحساب تلقائيًا
        if self.was_active:
            self.is_active = True

        self.save()

        # إرسال إشعار إلغاء الحظر
        if admin_user:
            self._send_admin_action_notification('unsuspended', admin_user)

    def can_register_again(self):
        """التحقق من إمكانية التسجيل مرة أخرى"""
        # إذا كان محظور حالياً، لا يمكن التسجيل
        if self.is_currently_banned():
            return False

        # إذا كان مرفوض، يمكن التسجيل مرة أخرى
        if self.verification_status == 'rejected':
            return True

        return False

    def update_last_seen(self):
        """تحديث آخر ظهور للمستخدم"""
        from django.utils import timezone
        status, created = UserStatus.objects.get_or_create(
            user=self,
            defaults={'last_seen': timezone.now(), 'is_online': True}
        )
        if not created:
            status.last_seen = timezone.now()
            status.is_online = True
            status.save()
        return status

    def _send_admin_action_notification(self, action_type, admin_user, reason=None, banned_until=None):
        """إرسال إشعار عند إجراء إداري"""
        try:
            from users.signals import send_manual_notification

            # تحديد نوع الإشعار حسب الإجراء
            notification_mapping = {
                'approved': 'approved',
                'rejected': 'suspended',  # نستخدم suspended للرفض
                'suspended': 'suspended',
                'unsuspended': 'unsuspended',
                'deleted': 'deleted',
                'under_review': 'under_review'
            }

            notification_type = notification_mapping.get(action_type)
            if not notification_type:
                return False

            # تحضير المعاملات
            kwargs = {}
            if reason:
                kwargs['reason'] = reason
            if banned_until:
                kwargs['suspended_until'] = banned_until

            # إرسال الإشعار
            result = send_manual_notification(self, notification_type, **kwargs)

            import logging
            logger = logging.getLogger(__name__)
            if result:
                logger.info(f"تم إرسال إشعار {action_type} للمستخدم {self.email} بواسطة {admin_user.email}")
            else:
                logger.error(f"فشل إرسال إشعار {action_type} للمستخدم {self.email}")

            return result

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"خطأ في إرسال إشعار إداري: {str(e)}")
            return False


class UserProfile(models.Model):
    """ملف تعريف إضافي للمستخدم"""

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name=_('المستخدم')
    )

    emergency_contact = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('جهة الاتصال في حالات الطوارئ')
    )

    emergency_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('هاتف الطوارئ')
    )

    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('العنوان')
    )

    preferred_language = models.CharField(
        max_length=5,
        choices=[('ar', 'العربية'), ('en', 'English')],
        default='ar',
        verbose_name=_('اللغة المفضلة')
    )

    timezone = models.CharField(
        max_length=50,
        default='Asia/Riyadh',
        verbose_name=_('المنطقة الزمنية')
    )

    class Meta:
        verbose_name = _('ملف تعريف المستخدم')
        verbose_name_plural = _('ملفات تعريف المستخدمين')

    def __str__(self):
        return f"ملف تعريف {self.user.get_full_name()}"


class PaymentGatewaySettings(models.Model):
    """إعدادات بوابات الدفع"""

    # PayPal Settings
    paypal_client_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('PayPal Client ID')
    )
    paypal_client_secret = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('PayPal Client Secret')
    )
    paypal_sandbox_mode = models.BooleanField(
        default=True,
        verbose_name=_('وضع التجربة PayPal')
    )
    paypal_webhook_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('PayPal Webhook ID')
    )
    paypal_enabled = models.BooleanField(
        default=False,
        verbose_name=_('تفعيل PayPal')
    )

    # Stripe Settings
    stripe_publishable_key = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Stripe Publishable Key')
    )
    stripe_secret_key = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Stripe Secret Key')
    )
    stripe_webhook_secret = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Stripe Webhook Secret')
    )
    stripe_enabled = models.BooleanField(
        default=False,
        verbose_name=_('تفعيل Stripe')
    )

    # Bank Transfer Settings
    bank_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('اسم البنك')
    )
    bank_account_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('رقم الحساب')
    )
    bank_account_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('اسم صاحب الحساب')
    )
    bank_iban = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('رقم IBAN')
    )
    bank_swift_code = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رمز SWIFT')
    )
    bank_branch = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('الفرع')
    )
    bank_transfer_enabled = models.BooleanField(
        default=True,
        verbose_name=_('تفعيل التحويل البنكي')
    )

    # General Settings
    default_currency = models.CharField(
        max_length=3,
        default='SAR',
        verbose_name=_('العملة الافتراضية')
    )
    auto_approve_payments = models.BooleanField(
        default=False,
        verbose_name=_('الموافقة التلقائية على المدفوعات')
    )
    payment_timeout_minutes = models.PositiveIntegerField(
        default=30,
        verbose_name=_('مهلة انتظار الدفع (بالدقائق)')
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('تم التحديث بواسطة')
    )

    class Meta:
        verbose_name = _("إعدادات بوابات الدفع")
        verbose_name_plural = _("إعدادات بوابات الدفع")

    def __str__(self):
        return "إعدادات بوابات الدفع"

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات الدفع أو إنشاؤها إذا لم تكن موجودة - قراءة مباشرة من قاعدة البيانات"""
        try:
            # قراءة مباشرة من قاعدة البيانات بدون تخزين مؤقت
            settings = cls.objects.get(pk=1)
            # إعادة تحميل البيانات من قاعدة البيانات للتأكد من الحداثة
            settings.refresh_from_db()
            return settings
        except cls.DoesNotExist:
            # إنشاء إعدادات جديدة إذا لم تكن موجودة
            settings = cls.objects.create(
                pk=1,
                paypal_enabled=False,
                stripe_enabled=False,
                bank_transfer_enabled=True
            )
            return settings

    def get_enabled_payment_methods(self):
        """الحصول على وسائل الدفع المفعلة"""
        methods = []

        if self.paypal_enabled and self.paypal_client_id and self.paypal_client_secret:
            methods.append({
                'id': 'paypal',
                'name': 'PayPal',
                'icon': 'fab fa-paypal',
                'color': 'blue',
                'enabled': True
            })

        if self.stripe_enabled and self.stripe_publishable_key and self.stripe_secret_key:
            methods.append({
                'id': 'stripe',
                'name': 'Stripe',
                'icon': 'fab fa-stripe',
                'color': 'purple',
                'enabled': True
            })

        if self.bank_transfer_enabled and self.bank_account_number:
            methods.append({
                'id': 'bank_transfer',
                'name': 'التحويل البنكي',
                'icon': 'fas fa-university',
                'color': 'green',
                'enabled': True
            })

        return methods

    def is_payment_method_enabled(self, method):
        """التحقق من تفعيل وسيلة دفع محددة"""
        if method == 'paypal':
            return self.paypal_enabled and self.paypal_client_id and self.paypal_client_secret
        elif method == 'stripe':
            return self.stripe_enabled and self.stripe_publishable_key and self.stripe_secret_key
        elif method == 'bank_transfer':
            return self.bank_transfer_enabled and self.bank_account_number
        return False


class AcademySettings(models.Model):
    """إعدادات الأكاديمية"""

    academy_name = models.CharField(
        max_length=100,
        default="أكاديمية القرآنية",
        verbose_name=_('اسم الأكاديمية')
    )

    academy_email = models.EmailField(
        max_length=100,
        default="<EMAIL>",
        verbose_name=_('البريد الإلكتروني للأكاديمية')
    )

    academy_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم هاتف الأكاديمية')
    )

    academy_whatsapp = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم واتساب الأكاديمية')
    )

    academy_support_email = models.EmailField(
        max_length=100,
        blank=True,
        null=True,
        default="<EMAIL>",
        verbose_name=_('البريد الإلكتروني للدعم الفني')
    )

    academy_logo = models.ImageField(
        upload_to='academy/',
        blank=True,
        null=True,
        verbose_name=_('شعار الأكاديمية')
    )

    academy_address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('عنوان الأكاديمية')
    )

    academy_description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف الأكاديمية')
    )

    academy_slogan = models.CharField(
        max_length=200,
        default="نظام قرآنيا التعليمي",
        blank=True,
        null=True,
        verbose_name=_('سلوجان الأكاديمية')
    )

    academy_working_hours = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ساعات العمل')
    )

    # معلومات الموقع الإلكتروني
    academy_website = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('موقع الأكاديمية الإلكتروني')
    )

    # المعلومات التجارية والضريبية
    commercial_register = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('السجل التجاري')
    )

    tax_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('الرقم الضريبي')
    )

    # معلومات البنك
    bank_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('اسم البنك')
    )

    bank_account = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('رقم الحساب البنكي')
    )

    iban = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('رقم الآيبان (IBAN)')
    )

    swift_code = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رمز السويفت (SWIFT)')
    )

    # معلومات إضافية للفواتير
    invoice_footer_text = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('نص تذييل الفاتورة')
    )

    invoice_terms = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('شروط وأحكام الفاتورة')
    )

    # إعدادات البريد الإلكتروني

    # إعدادات SMTP العامة
    smtp_host = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('خادم SMTP')
    )

    smtp_port = models.IntegerField(
        blank=True,
        null=True,
        default=587,
        verbose_name=_('منفذ SMTP')
    )

    smtp_use_tls = models.BooleanField(
        default=True,
        verbose_name=_('استخدام TLS')
    )

    smtp_use_ssl = models.BooleanField(
        default=False,
        verbose_name=_('استخدام SSL')
    )

    smtp_username = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('اسم مستخدم SMTP')
    )

    smtp_password = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('كلمة مرور SMTP (مشفرة)')
    )

    # إعدادات إضافية للخدمات المختلفة
    email_webhook_url = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('رابط Webhook للإحصائيات')
    )

    email_webhook_secret = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('مفتاح Webhook السري')
    )

    # إعدادات متقدمة للمرفقات
    max_attachments = models.IntegerField(
        default=5,
        verbose_name=_('الحد الأقصى للمرفقات')
    )

    max_attachment_size = models.IntegerField(
        default=25,
        verbose_name=_('الحد الأقصى لحجم المرفق (MB)')
    )

    # للتوافق مع النظام القديم
    @property
    def smtp_max_attachments(self):
        return self.max_attachments

    @property
    def smtp_max_attachment_size(self):
        return self.max_attachment_size

    # إعدادات التتبع والموافقة
    email_tracking_enabled = models.BooleanField(
        default=True,
        verbose_name=_('تفعيل تتبع الرسائل')
    )

    manual_approval_required = models.BooleanField(
        default=True,
        verbose_name=_('تتطلب موافقة يدوية')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('إعدادات الأكاديمية')
        verbose_name_plural = _('إعدادات الأكاديمية')

    def __str__(self):
        return self.academy_name

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات الأكاديمية، أو إنشاء إعدادات افتراضية إذا لم تكن موجودة"""
        settings, _ = cls.objects.get_or_create(pk=1)
        return settings


class UserStatus(models.Model):
    """حالة المستخدم (متصل/غير متصل)"""

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='user_status',
        verbose_name=_('المستخدم')
    )

    is_online = models.BooleanField(
        default=False,
        verbose_name=_('متصل')
    )

    last_seen = models.DateTimeField(
        auto_now=True,
        verbose_name=_('آخر ظهور')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('حالة المستخدم')
        verbose_name_plural = _('حالات المستخدمين')

    def __str__(self):
        status = "متصل" if self.is_online else "غير متصل"
        return f"{self.user.get_full_name()} - {status}"

    def get_status_display(self):
        """عرض حالة المستخدم"""
        if self.is_online:
            return "متصل"
        else:
            from django.utils import timezone
            from datetime import timedelta

            now = timezone.now()
            time_diff = now - self.last_seen

            if time_diff < timedelta(minutes=5):
                return "متصل مؤخراً"
            elif time_diff < timedelta(hours=1):
                minutes = int(time_diff.total_seconds() / 60)
                return f"آخر ظهور منذ {minutes} دقيقة"
            elif time_diff < timedelta(days=1):
                hours = int(time_diff.total_seconds() / 3600)
                return f"آخر ظهور منذ {hours} ساعة"
            else:
                days = time_diff.days
                return f"آخر ظهور منذ {days} يوم"

    def get_status_color(self):
        """لون حالة المستخدم"""
        if self.is_online:
            return "green"
        else:
            from django.utils import timezone
            from datetime import timedelta

            now = timezone.now()
            time_diff = now - self.last_seen

            if time_diff < timedelta(minutes=5):
                return "yellow"
            else:
                return "gray"


# ==================== تم استبدال نظام البريد الإلكتروني بنظام WhatsApp ====================
# انظر نماذج WhatsApp في نهاية الملف


# تم حذف EmailAttachment - لم تعد هناك حاجة لمرفقات البريد الإلكتروني


# تم حذف EmailTracking - لم تعد هناك حاجة لتتبع البريد الإلكتروني


# تم حذف EmailApprovalRequest - لم تعد هناك حاجة لطلبات الموافقة على البريد الإلكتروني


# تم حذف EmailTemplate - لم يعد مستخدماً




# ===== نهاية الملف =====
