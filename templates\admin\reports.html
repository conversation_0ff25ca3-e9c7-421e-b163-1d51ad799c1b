{% extends 'base.html' %}
{% load static %}

{% block title %}مركز التقارير المتقدم - نظام قرآنيا التعليمي{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/reports-safe.css' %}">
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 reports-page">
    <!-- Enhanced Header with Overview -->
    <div class="dashboard-overview">
        <div class="px-6 py-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <a href="{% url 'admin_dashboard' %}" class="text-white hover:text-yellow-200 transition-colors ml-4">
                        <i class="fas fa-arrow-right text-2xl"></i>
                    </a>
                    <div>
                        <h1 class="overview-title flex items-center">
                            <i class="fas fa-chart-bar text-yellow-300 ml-3"></i>
                            مركز التقارير الشامل
                        </h1>
                        <p class="overview-subtitle">تحليل متقدم وتقارير شاملة لجميع جوانب المنصة التعليمية</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button id="createReportBtn" class="bg-yellow-400 text-gray-900 px-6 py-3 rounded-lg hover:bg-yellow-300 transition-all duration-300 font-semibold shadow-lg">
                        <i class="fas fa-plus ml-2"></i>
                        إنشاء تقرير جديد
                    </button>
                    <button id="templatesBtn" class="bg-white bg-opacity-20 text-white px-6 py-3 rounded-lg hover:bg-opacity-30 transition-all duration-300 font-semibold backdrop-filter backdrop-blur-sm">
                        <i class="fas fa-file-alt ml-2"></i>
                        القوالب الجاهزة
                    </button>
                    <button id="exportAllBtn" class="bg-white bg-opacity-20 text-white px-6 py-3 rounded-lg hover:bg-opacity-30 transition-all duration-300 font-semibold backdrop-filter backdrop-blur-sm">
                        <i class="fas fa-download ml-2"></i>
                        تصدير شامل
                    </button>
                </div>
            </div>

            <!-- Quick Overview Stats -->
            <div class="overview-stats">
                <div class="overview-stat">
                    <div class="overview-stat-value">{{ total_reports|default:0 }}</div>
                    <div class="overview-stat-label">إجمالي التقارير</div>
                </div>
                <div class="overview-stat">
                    <div class="overview-stat-value">{{ active_reports|default:0 }}</div>
                    <div class="overview-stat-label">تقارير نشطة</div>
                </div>
                <div class="overview-stat">
                    <div class="overview-stat-value">{{ scheduled_reports|default:0 }}</div>
                    <div class="overview-stat-label">تقارير مجدولة</div>
                </div>
                <div class="overview-stat">
                    <div class="overview-stat-value">{{ total_downloads|default:0 }}</div>
                    <div class="overview-stat-label">إجمالي التحميلات</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Grid -->
    <div class="px-6 mb-8">
        <div class="stats-grid">
            <!-- Teachers Statistics -->
            <div class="stat-card-enhanced">
                <div class="flex items-center justify-between mb-4">
                    <div class="stat-icon teachers">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up ml-1"></i>
                        +12%
                    </div>
                </div>
                <div class="stat-value">{{ teachers_count|default:0 }}</div>
                <div class="stat-label">إجمالي المعلمين</div>
                <div class="mt-3 text-sm text-gray-600">
                    <span class="font-medium text-green-600">{{ active_teachers_count|default:0 }}</span> نشط
                    <span class="mx-2">•</span>
                    <span class="font-medium text-yellow-600">{{ teachers_avg_rating|default:0 }}/5</span> تقييم
                </div>
            </div>

            <!-- Students Statistics -->
            <div class="stat-card-enhanced">
                <div class="flex items-center justify-between mb-4">
                    <div class="stat-icon students">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up ml-1"></i>
                        +8%
                    </div>
                </div>
                <div class="stat-value">{{ students_count|default:0 }}</div>
                <div class="stat-label">إجمالي الطلاب</div>
                <div class="mt-3 text-sm text-gray-600">
                    <span class="font-medium text-green-600">{{ active_students_count|default:0 }}</span> نشط
                    <span class="mx-2">•</span>
                    <span class="font-medium text-blue-600">{{ students_completion_rate|default:0 }}%</span> إكمال
                </div>
            </div>

            <!-- Courses Statistics -->
            <div class="stat-card-enhanced">
                <div class="flex items-center justify-between mb-4">
                    <div class="stat-icon courses">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up ml-1"></i>
                        +5%
                    </div>
                </div>
                <div class="stat-value">{{ courses_count|default:0 }}</div>
                <div class="stat-label">إجمالي الدورات</div>
                <div class="mt-3 text-sm text-gray-600">
                    <span class="font-medium text-green-600">{{ active_courses_count|default:0 }}</span> نشطة
                    <span class="mx-2">•</span>
                    <span class="font-medium text-yellow-600">{{ courses_avg_rating|default:0 }}/5</span> تقييم
                </div>
            </div>

            <!-- Financial Statistics -->
            <div class="stat-card-enhanced">
                <div class="flex items-center justify-between mb-4">
                    <div class="stat-icon financial">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up ml-1"></i>
                        +15%
                    </div>
                </div>
                <div class="stat-value">${{ total_revenue|default:0|floatformat:0 }}</div>
                <div class="stat-label">إجمالي الإيرادات (دولار)</div>
                <div class="mt-3 text-sm text-gray-600">
                    <span class="font-medium text-purple-600">{{ total_payments|default:0 }}</span> معاملة
                    <span class="mx-2">•</span>
                    <span class="font-medium text-blue-600">${{ avg_course_price|default:0|floatformat:0 }}</span> متوسط السعر
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="px-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-1">
            <nav class="flex space-x-1 space-x-reverse" aria-label="Tabs">
                <button class="report-tab active flex-1 py-4 px-6 text-center text-sm font-medium rounded-lg transition-all duration-200" data-tab="overview">
                    <i class="fas fa-tachometer-alt text-lg mb-1 block"></i>
                    نظرة عامة
                </button>
                <button class="report-tab flex-1 py-4 px-6 text-center text-sm font-medium rounded-lg transition-all duration-200" data-tab="teachers">
                    <i class="fas fa-chalkboard-teacher text-lg mb-1 block"></i>
                    تقارير المعلمين
                </button>
                <button class="report-tab flex-1 py-4 px-6 text-center text-sm font-medium rounded-lg transition-all duration-200" data-tab="students">
                    <i class="fas fa-user-graduate text-lg mb-1 block"></i>
                    تقارير الطلاب
                </button>
                <button class="report-tab flex-1 py-4 px-6 text-center text-sm font-medium rounded-lg transition-all duration-200" data-tab="courses">
                    <i class="fas fa-book text-lg mb-1 block"></i>
                    تقارير الدورات
                </button>
                <button class="report-tab flex-1 py-4 px-6 text-center text-sm font-medium rounded-lg transition-all duration-200" data-tab="financial">
                    <i class="fas fa-money-bill-wave text-lg mb-1 block"></i>
                    التقارير المالية
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="px-6">
        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content active">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Users Growth Chart -->
                <div class="chart-container">
                    <div class="chart-title">نمو المستخدمين</div>
                    <div class="chart-wrapper">
                        <canvas id="usersGrowthChart"></canvas>
                    </div>
                </div>

                <!-- Revenue Chart -->
                <div class="chart-container">
                    <div class="chart-title">الإيرادات الشهرية</div>
                    <div class="chart-wrapper">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>

                <!-- Course Completion Chart -->
                <div class="chart-container">
                    <div class="chart-title">معدل إكمال الدورات</div>
                    <div class="chart-wrapper">
                        <canvas id="completionChart"></canvas>
                    </div>
                </div>

                <!-- Activity Distribution Chart -->
                <div class="chart-container">
                    <div class="chart-title">توزيع النشاطات</div>
                    <div class="chart-wrapper">
                        <canvas id="activityChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Reports Table -->
            <div class="reports-table">
                <div class="table-header">
                    <div class="table-title">التقارير الحديثة</div>
                    <div class="table-subtitle">آخر التقارير المولدة في النظام</div>
                </div>
                <div class="table-content">
                    {% for report in recent_reports %}
                    <div class="table-row">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-lg flex items-center justify-center ml-3
                                        {% if report.category == 'teachers' %}bg-blue-100 text-blue-600
                                        {% elif report.category == 'students' %}bg-green-100 text-green-600
                                        {% elif report.category == 'courses' %}bg-yellow-100 text-yellow-600
                                        {% else %}bg-purple-100 text-purple-600{% endif %}">
                                        <i class="fas
                                            {% if report.category == 'teachers' %}fa-chalkboard-teacher
                                            {% elif report.category == 'students' %}fa-user-graduate
                                            {% elif report.category == 'courses' %}fa-book
                                            {% else %}fa-money-bill-wave{% endif %}"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ report.title }}</h4>
                                        <p class="text-sm text-gray-600">{{ report.get_category_display }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                                <span>{{ report.created_at|date:"Y-m-d H:i" }}</span>
                                <span>{{ report.generated_reports.count }} تحميل</span>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <button class="text-green-600 hover:text-green-800 p-1" title="تحميل PDF">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800 p-1" title="تحميل Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="table-row text-center py-8">
                        <div class="text-gray-500">
                            <i class="fas fa-chart-bar text-4xl mb-4 block"></i>
                            <p>لا توجد تقارير حديثة</p>
                            <p class="text-sm mt-2">ابدأ بإنشاء تقرير جديد</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Teachers Reports Tab -->
        <div id="teachers-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Quick Stats -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-chart-pie text-islamic-primary ml-2"></i>
                            إحصائيات سريعة
                        </h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">إجمالي المعلمين</span>
                                <span class="text-lg font-bold text-islamic-primary">{{ teachers_count|default:0 }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">معلمون نشطون</span>
                                <span class="text-lg font-bold text-green-600">{{ active_teachers_count|default:0 }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">متوسط التقييم</span>
                                <span class="text-lg font-bold text-yellow-600">{{ teachers_avg_rating|default:0 }}/5</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">إجمالي الحصص</span>
                                <span class="text-lg font-bold text-blue-600">{{ teachers_total_lessons|default:0 }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-bolt text-islamic-gold ml-2"></i>
                            إجراءات سريعة
                        </h3>
                        <div class="space-y-3">
                            <button class="generate-report-btn w-full bg-islamic-primary text-white py-2 px-4 rounded-lg hover:bg-islamic-light transition-colors" data-category="teachers" data-type="performance">
                                <i class="fas fa-chart-line ml-2"></i>
                                تقرير أداء المعلمين
                            </button>
                            <button class="generate-report-btn w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors" data-category="teachers" data-type="attendance">
                                <i class="fas fa-calendar-check ml-2"></i>
                                تقرير الحضور والغياب
                            </button>
                            <button class="generate-report-btn w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors" data-category="teachers" data-type="ratings">
                                <i class="fas fa-star ml-2"></i>
                                تقرير التقييمات
                            </button>
                            <button class="generate-report-btn w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors" data-category="teachers" data-type="earnings">
                                <i class="fas fa-money-bill-wave ml-2"></i>
                                تقرير الأرباح
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Reports List -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-list text-islamic-primary ml-2"></i>
                                التقارير المحفوظة
                            </h3>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                                    <option>جميع التقارير</option>
                                    <option>آخر 7 أيام</option>
                                    <option>آخر 30 يوم</option>
                                </select>
                                <button class="bg-islamic-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-islamic-light transition-colors">
                                    <i class="fas fa-filter ml-1"></i>
                                    فلترة
                                </button>
                            </div>
                        </div>

                        <div class="p-6">
                            <div id="teachers-reports-list" class="space-y-4">
                                {% for report in teachers_reports %}
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <h4 class="font-medium text-gray-900">{{ report.title }}</h4>
                                            <p class="text-sm text-gray-600 mt-1">{{ report.description|truncatechars:100 }}</p>
                                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                                <span class="ml-4">
                                                    <i class="fas fa-calendar ml-1"></i>
                                                    {{ report.created_at|date:"Y-m-d H:i" }}
                                                </span>
                                                <span class="ml-4">
                                                    <i class="fas fa-user ml-1"></i>
                                                    {{ report.created_by.get_full_name }}
                                                </span>
                                                <span>
                                                    <i class="fas fa-download ml-1"></i>
                                                    {{ report.generated_reports.count }} تحميل
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="download-report-btn text-green-600 hover:text-green-800 p-2" data-report-id="{{ report.id }}" data-format="pdf" title="تحميل PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </button>
                                            <button class="download-report-btn text-blue-600 hover:text-blue-800 p-2" data-report-id="{{ report.id }}" data-format="excel" title="تحميل Excel">
                                                <i class="fas fa-file-excel"></i>
                                            </button>
                                            <button class="view-report-btn text-islamic-primary hover:text-islamic-light p-2" data-report-id="{{ report.id }}" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="delete-report-btn text-red-600 hover:text-red-800 p-2" data-report-id="{{ report.id }}" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="text-center py-8">
                                    <i class="fas fa-file-alt text-gray-400 text-4xl mb-4"></i>
                                    <p class="text-gray-500">لا توجد تقارير محفوظة بعد</p>
                                    <p class="text-sm text-gray-400 mt-2">ابدأ بإنشاء تقرير جديد باستخدام الإجراءات السريعة</p>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Students Reports Tab -->
        <div id="students-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Quick Stats -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-chart-pie text-islamic-primary ml-2"></i>
                            إحصائيات سريعة
                        </h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">إجمالي الطلاب</span>
                                <span class="text-lg font-bold text-islamic-primary">{{ students_count|default:0 }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">طلاب نشطون</span>
                                <span class="text-lg font-bold text-green-600">{{ active_students_count|default:0 }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">معدل الإكمال</span>
                                <span class="text-lg font-bold text-blue-600">{{ students_completion_rate|default:0 }}%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">إجمالي التسجيلات</span>
                                <span class="text-lg font-bold text-purple-600">{{ students_total_enrollments|default:0 }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-bolt text-islamic-gold ml-2"></i>
                            إجراءات سريعة
                        </h3>
                        <div class="space-y-3">
                            <button class="generate-report-btn w-full bg-islamic-primary text-white py-2 px-4 rounded-lg hover:bg-islamic-light transition-colors" data-category="students" data-type="progress">
                                <i class="fas fa-chart-line ml-2"></i>
                                تقرير تقدم الطلاب
                            </button>
                            <button class="generate-report-btn w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors" data-category="students" data-type="attendance">
                                <i class="fas fa-calendar-check ml-2"></i>
                                تقرير الحضور
                            </button>
                            <button class="generate-report-btn w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors" data-category="students" data-type="performance">
                                <i class="fas fa-trophy ml-2"></i>
                                تقرير الأداء
                            </button>
                            <button class="generate-report-btn w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors" data-category="students" data-type="enrollments">
                                <i class="fas fa-user-graduate ml-2"></i>
                                تقرير التسجيلات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Reports List -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-list text-islamic-primary ml-2"></i>
                                تقارير الطلاب المحفوظة
                            </h3>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                                    <option>جميع التقارير</option>
                                    <option>آخر 7 أيام</option>
                                    <option>آخر 30 يوم</option>
                                </select>
                                <button class="bg-islamic-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-islamic-light transition-colors">
                                    <i class="fas fa-filter ml-1"></i>
                                    فلترة
                                </button>
                            </div>
                        </div>

                        <div class="p-6">
                            <div id="students-reports-list" class="space-y-4">
                                {% for report in students_reports %}
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <h4 class="font-medium text-gray-900">{{ report.title }}</h4>
                                            <p class="text-sm text-gray-600 mt-1">{{ report.description|truncatechars:100 }}</p>
                                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                                <span class="ml-4">
                                                    <i class="fas fa-calendar ml-1"></i>
                                                    {{ report.created_at|date:"Y-m-d H:i" }}
                                                </span>
                                                <span class="ml-4">
                                                    <i class="fas fa-user ml-1"></i>
                                                    {{ report.created_by.get_full_name }}
                                                </span>
                                                <span>
                                                    <i class="fas fa-download ml-1"></i>
                                                    {{ report.generated_reports.count }} تحميل
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="download-report-btn text-green-600 hover:text-green-800 p-2" data-report-id="{{ report.id }}" data-format="pdf" title="تحميل PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </button>
                                            <button class="download-report-btn text-blue-600 hover:text-blue-800 p-2" data-report-id="{{ report.id }}" data-format="excel" title="تحميل Excel">
                                                <i class="fas fa-file-excel"></i>
                                            </button>
                                            <button class="view-report-btn text-islamic-primary hover:text-islamic-light p-2" data-report-id="{{ report.id }}" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="delete-report-btn text-red-600 hover:text-red-800 p-2" data-report-id="{{ report.id }}" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="text-center py-8">
                                    <i class="fas fa-user-graduate text-gray-400 text-4xl mb-4"></i>
                                    <p class="text-gray-500">لا توجد تقارير طلاب محفوظة بعد</p>
                                    <p class="text-sm text-gray-400 mt-2">ابدأ بإنشاء تقرير جديد باستخدام الإجراءات السريعة</p>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Courses Reports Tab -->
        <div id="courses-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Quick Stats -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-chart-pie text-islamic-primary ml-2"></i>
                            إحصائيات سريعة
                        </h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">إجمالي الدورات</span>
                                <span class="text-lg font-bold text-islamic-primary">{{ courses_count|default:0 }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">دورات نشطة</span>
                                <span class="text-lg font-bold text-green-600">{{ active_courses_count|default:0 }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">متوسط التقييم</span>
                                <span class="text-lg font-bold text-yellow-600">{{ courses_avg_rating|default:0 }}/5</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">إجمالي التسجيلات</span>
                                <span class="text-lg font-bold text-blue-600">{{ courses_total_enrollments|default:0 }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-bolt text-islamic-gold ml-2"></i>
                            إجراءات سريعة
                        </h3>
                        <div class="space-y-3">
                            <button class="generate-report-btn w-full bg-islamic-primary text-white py-2 px-4 rounded-lg hover:bg-islamic-light transition-colors" data-category="courses" data-type="popularity">
                                <i class="fas fa-chart-line ml-2"></i>
                                تقرير شعبية الدورات
                            </button>
                            <button class="generate-report-btn w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors" data-category="courses" data-type="completion">
                                <i class="fas fa-check-circle ml-2"></i>
                                تقرير معدل الإكمال
                            </button>
                            <button class="generate-report-btn w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors" data-category="courses" data-type="ratings">
                                <i class="fas fa-star ml-2"></i>
                                تقرير التقييمات
                            </button>
                            <button class="generate-report-btn w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors" data-category="courses" data-type="revenue">
                                <i class="fas fa-money-bill-wave ml-2"></i>
                                تقرير الإيرادات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Reports List -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-list text-islamic-primary ml-2"></i>
                                تقارير الدورات المحفوظة
                            </h3>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                                    <option>جميع التقارير</option>
                                    <option>آخر 7 أيام</option>
                                    <option>آخر 30 يوم</option>
                                </select>
                                <button class="bg-islamic-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-islamic-light transition-colors">
                                    <i class="fas fa-filter ml-1"></i>
                                    فلترة
                                </button>
                            </div>
                        </div>

                        <div class="p-6">
                            <div id="courses-reports-list" class="space-y-4">
                                {% for report in courses_reports %}
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <h4 class="font-medium text-gray-900">{{ report.title }}</h4>
                                            <p class="text-sm text-gray-600 mt-1">{{ report.description|truncatechars:100 }}</p>
                                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                                <span class="ml-4">
                                                    <i class="fas fa-calendar ml-1"></i>
                                                    {{ report.created_at|date:"Y-m-d H:i" }}
                                                </span>
                                                <span class="ml-4">
                                                    <i class="fas fa-user ml-1"></i>
                                                    {{ report.created_by.get_full_name }}
                                                </span>
                                                <span>
                                                    <i class="fas fa-download ml-1"></i>
                                                    {{ report.generated_reports.count }} تحميل
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="download-report-btn text-green-600 hover:text-green-800 p-2" data-report-id="{{ report.id }}" data-format="pdf" title="تحميل PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </button>
                                            <button class="download-report-btn text-blue-600 hover:text-blue-800 p-2" data-report-id="{{ report.id }}" data-format="excel" title="تحميل Excel">
                                                <i class="fas fa-file-excel"></i>
                                            </button>
                                            <button class="view-report-btn text-islamic-primary hover:text-islamic-light p-2" data-report-id="{{ report.id }}" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="delete-report-btn text-red-600 hover:text-red-800 p-2" data-report-id="{{ report.id }}" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="text-center py-8">
                                    <i class="fas fa-book text-gray-400 text-4xl mb-4"></i>
                                    <p class="text-gray-500">لا توجد تقارير دورات محفوظة بعد</p>
                                    <p class="text-sm text-gray-400 mt-2">ابدأ بإنشاء تقرير جديد باستخدام الإجراءات السريعة</p>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Reports Tab -->
        <div id="financial-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Quick Stats -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-chart-pie text-islamic-primary ml-2"></i>
                            إحصائيات سريعة
                        </h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">إجمالي الإيرادات</span>
                                <span class="text-lg font-bold text-islamic-primary">{{ total_revenue|default:0 }} ر.س</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">إيرادات هذا الشهر</span>
                                <span class="text-lg font-bold text-green-600">{{ monthly_revenue|default:0 }} ر.س</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">متوسط سعر الدورة</span>
                                <span class="text-lg font-bold text-blue-600">{{ avg_course_price|default:0 }} ر.س</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">إجمالي المدفوعات</span>
                                <span class="text-lg font-bold text-purple-600">{{ total_payments|default:0 }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-bolt text-islamic-gold ml-2"></i>
                            إجراءات سريعة
                        </h3>
                        <div class="space-y-3">
                            <button class="generate-report-btn w-full bg-islamic-primary text-white py-2 px-4 rounded-lg hover:bg-islamic-light transition-colors" data-category="financial" data-type="revenue">
                                <i class="fas fa-chart-line ml-2"></i>
                                تقرير الإيرادات
                            </button>
                            <button class="generate-report-btn w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors" data-category="financial" data-type="payments">
                                <i class="fas fa-credit-card ml-2"></i>
                                تقرير المدفوعات
                            </button>
                            <button class="generate-report-btn w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors" data-category="financial" data-type="refunds">
                                <i class="fas fa-undo ml-2"></i>
                                تقرير المرتجعات
                            </button>
                            <button class="generate-report-btn w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors" data-category="financial" data-type="commissions">
                                <i class="fas fa-percentage ml-2"></i>
                                تقرير العمولات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Reports List -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-list text-islamic-primary ml-2"></i>
                                التقارير المالية المحفوظة
                            </h3>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                                    <option>جميع التقارير</option>
                                    <option>آخر 7 أيام</option>
                                    <option>آخر 30 يوم</option>
                                </select>
                                <button class="bg-islamic-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-islamic-light transition-colors">
                                    <i class="fas fa-filter ml-1"></i>
                                    فلترة
                                </button>
                            </div>
                        </div>

                        <div class="p-6">
                            <div id="financial-reports-list" class="space-y-4">
                                {% for report in financial_reports %}
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <h4 class="font-medium text-gray-900">{{ report.title }}</h4>
                                            <p class="text-sm text-gray-600 mt-1">{{ report.description|truncatechars:100 }}</p>
                                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                                <span class="ml-4">
                                                    <i class="fas fa-calendar ml-1"></i>
                                                    {{ report.created_at|date:"Y-m-d H:i" }}
                                                </span>
                                                <span class="ml-4">
                                                    <i class="fas fa-user ml-1"></i>
                                                    {{ report.created_by.get_full_name }}
                                                </span>
                                                <span>
                                                    <i class="fas fa-download ml-1"></i>
                                                    {{ report.generated_reports.count }} تحميل
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="download-report-btn text-green-600 hover:text-green-800 p-2" data-report-id="{{ report.id }}" data-format="pdf" title="تحميل PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </button>
                                            <button class="download-report-btn text-blue-600 hover:text-blue-800 p-2" data-report-id="{{ report.id }}" data-format="excel" title="تحميل Excel">
                                                <i class="fas fa-file-excel"></i>
                                            </button>
                                            <button class="view-report-btn text-islamic-primary hover:text-islamic-light p-2" data-report-id="{{ report.id }}" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="delete-report-btn text-red-600 hover:text-red-800 p-2" data-report-id="{{ report.id }}" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="text-center py-8">
                                    <i class="fas fa-money-bill-wave text-gray-400 text-4xl mb-4"></i>
                                    <p class="text-gray-500">لا توجد تقارير مالية محفوظة بعد</p>
                                    <p class="text-sm text-gray-400 mt-2">ابدأ بإنشاء تقرير جديد باستخدام الإجراءات السريعة</p>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Report Modal -->
    <div id="createReportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-plus text-islamic-primary ml-2"></i>
                        إنشاء تقرير مخصص جديد
                    </h3>
                </div>

                <form id="createReportForm" class="p-6">
                    {% csrf_token %}
                    <div class="space-y-6">
                        <!-- Report Title -->
                        <div>
                            <label for="reportTitle" class="block text-sm font-medium text-gray-700 mb-2">
                                عنوان التقرير
                            </label>
                            <input type="text" id="reportTitle" name="title" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="مثال: تقرير أداء المعلمين الشهري">
                        </div>

                        <!-- Report Description -->
                        <div>
                            <label for="reportDescription" class="block text-sm font-medium text-gray-700 mb-2">
                                وصف التقرير
                            </label>
                            <textarea id="reportDescription" name="description" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                      placeholder="وصف مختصر لمحتوى التقرير وأهدافه..."></textarea>
                        </div>

                        <!-- Report Category -->
                        <div>
                            <label for="reportCategory" class="block text-sm font-medium text-gray-700 mb-2">
                                فئة التقرير
                            </label>
                            <select id="reportCategory" name="category" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="">اختر فئة التقرير...</option>
                                <option value="teachers">تقارير المعلمين</option>
                                <option value="students">تقارير الطلاب</option>
                                <option value="courses">تقارير الدورات</option>
                                <option value="financial">التقارير المالية</option>
                            </select>
                        </div>

                        <!-- Date Range -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="dateFrom" class="block text-sm font-medium text-gray-700 mb-2">
                                    من تاريخ
                                </label>
                                <input type="date" id="dateFrom" name="date_from"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                            </div>
                            <div>
                                <label for="dateTo" class="block text-sm font-medium text-gray-700 mb-2">
                                    إلى تاريخ
                                </label>
                                <input type="date" id="dateTo" name="date_to"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                            </div>
                        </div>

                        <!-- Filters -->
                        <div id="reportFilters" class="space-y-4">
                            <h4 class="text-md font-medium text-gray-900">فلاتر إضافية</h4>
                            <!-- Dynamic filters will be added here based on category -->
                        </div>

                        <!-- Schedule Options -->
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="scheduleReport" name="is_scheduled"
                                       class="rounded border-gray-300 text-islamic-primary focus:ring-islamic-primary">
                                <span class="mr-2 text-sm text-gray-700">جدولة التقرير (تلقائي)</span>
                            </label>

                            <div id="scheduleOptions" class="mt-4 hidden">
                                <select name="schedule_frequency"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                    <option value="daily">يومي</option>
                                    <option value="weekly">أسبوعي</option>
                                    <option value="monthly">شهري</option>
                                    <option value="quarterly">ربع سنوي</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 space-x-reverse mt-6 pt-6 border-t border-gray-200">
                        <button type="button" id="cancelCreateReport"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-6 py-2 text-sm font-medium text-white bg-islamic-primary rounded-lg hover:bg-islamic-light transition-colors">
                            <i class="fas fa-save ml-1"></i>
                            إنشاء التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl p-8 text-center">
                <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-islamic-primary mx-auto mb-4"></div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">جاري إنشاء التقرير...</h3>
                <p class="text-gray-600">يرجى الانتظار، قد تستغرق هذه العملية بضع دقائق</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.report-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            console.log('Clicked tab:', targetTab); // للتشخيص

            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => {
                content.classList.add('hidden');
                content.classList.remove('active');
            });

            // Add active class to clicked tab and show corresponding content
            this.classList.add('active');
            const targetContent = document.getElementById(targetTab + '-tab');
            if (targetContent) {
                targetContent.classList.remove('hidden');
                targetContent.classList.add('active');
                console.log('Showing tab:', targetTab); // للتشخيص
            } else {
                console.error('Tab content not found:', targetTab + '-tab'); // للتشخيص
            }
        });
    });

    // Modal functionality
    const createReportBtn = document.getElementById('createReportBtn');
    const createReportModal = document.getElementById('createReportModal');
    const cancelCreateReport = document.getElementById('cancelCreateReport');
    const createReportForm = document.getElementById('createReportForm');
    const loadingModal = document.getElementById('loadingModal');

    createReportBtn.addEventListener('click', function() {
        createReportModal.classList.remove('hidden');
    });

    cancelCreateReport.addEventListener('click', function() {
        createReportModal.classList.add('hidden');
        createReportForm.reset();
    });

    // Close modal when clicking outside
    createReportModal.addEventListener('click', function(e) {
        if (e.target === this) {
            this.classList.add('hidden');
            createReportForm.reset();
        }
    });

    // Schedule checkbox functionality
    const scheduleCheckbox = document.getElementById('scheduleReport');
    const scheduleOptions = document.getElementById('scheduleOptions');

    scheduleCheckbox.addEventListener('change', function() {
        if (this.checked) {
            scheduleOptions.classList.remove('hidden');
        } else {
            scheduleOptions.classList.add('hidden');
        }
    });

    // Category change - update filters
    const categorySelect = document.getElementById('reportCategory');
    const filtersContainer = document.getElementById('reportFilters');

    categorySelect.addEventListener('change', function() {
        updateFilters(this.value);
    });

    function updateFilters(category) {
        let filtersHTML = '<h4 class="text-md font-medium text-gray-900">فلاتر إضافية</h4>';

        switch(category) {
            case 'teachers':
                filtersHTML += `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="filters[is_active]" value="1" class="rounded border-gray-300 text-islamic-primary">
                                <span class="mr-2 text-sm text-gray-700">معلمون نشطون فقط</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="filters[is_verified]" value="1" class="rounded border-gray-300 text-islamic-primary">
                                <span class="mr-2 text-sm text-gray-700">معلمون محققون فقط</span>
                            </label>
                        </div>
                    </div>
                `;
                break;
            case 'students':
                filtersHTML += `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="filters[is_active]" value="1" class="rounded border-gray-300 text-islamic-primary">
                                <span class="mr-2 text-sm text-gray-700">طلاب نشطون فقط</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="filters[has_enrollments]" value="1" class="rounded border-gray-300 text-islamic-primary">
                                <span class="mr-2 text-sm text-gray-700">طلاب مسجلون فقط</span>
                            </label>
                        </div>
                    </div>
                `;
                break;
            case 'courses':
                filtersHTML += `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="filters[is_active]" value="1" class="rounded border-gray-300 text-islamic-primary">
                                <span class="mr-2 text-sm text-gray-700">دورات نشطة فقط</span>
                            </label>
                        </div>
                        <div>
                            <label for="courseType" class="block text-sm font-medium text-gray-700 mb-1">نوع الدورة</label>
                            <select name="filters[course_type]" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                <option value="">جميع الأنواع</option>
                                <option value="quran_memorization">حفظ القرآن</option>
                                <option value="quran_recitation">تلاوة القرآن</option>
                                <option value="islamic_studies">دراسات إسلامية</option>
                            </select>
                        </div>
                    </div>
                `;
                break;
            case 'financial':
                filtersHTML += `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="paymentStatus" class="block text-sm font-medium text-gray-700 mb-1">حالة الدفع</label>
                            <select name="filters[payment_status]" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                <option value="">جميع الحالات</option>
                                <option value="completed">مكتمل</option>
                                <option value="pending">معلق</option>
                                <option value="failed">فاشل</option>
                            </select>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="filters[include_refunds]" value="1" class="rounded border-gray-300 text-islamic-primary">
                                <span class="mr-2 text-sm text-gray-700">تضمين المرتجعات</span>
                            </label>
                        </div>
                    </div>
                `;
                break;
        }

        filtersContainer.innerHTML = filtersHTML;
    }

    // Form submission
    createReportForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Show loading modal
        createReportModal.classList.add('hidden');
        loadingModal.classList.remove('hidden');

        // Submit form via AJAX
        fetch('{% url "admin_reports_create" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            loadingModal.classList.add('hidden');

            if (data.success) {
                // Show success message and reload page
                alert('تم إنشاء التقرير بنجاح!');
                location.reload();
            } else {
                alert('حدث خطأ: ' + (data.error || 'خطأ غير معروف'));
            }
        })
        .catch(error => {
            loadingModal.classList.add('hidden');
            alert('حدث خطأ في الاتصال: ' + error.message);
        });
    });

    // Generate report buttons
    document.querySelectorAll('.generate-report-btn').forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            const type = this.getAttribute('data-type');

            // Pre-fill the form with quick report data
            document.getElementById('reportTitle').value = getQuickReportTitle(category, type);
            document.getElementById('reportDescription').value = getQuickReportDescription(category, type);
            document.getElementById('reportCategory').value = category;

            // Update filters
            updateFilters(category);

            // Show modal
            createReportModal.classList.remove('hidden');
        });
    });

    function getQuickReportTitle(category, type) {
        const titles = {
            'teachers': {
                'performance': 'تقرير أداء المعلمين',
                'attendance': 'تقرير حضور وغياب المعلمين',
                'ratings': 'تقرير تقييمات المعلمين',
                'earnings': 'تقرير أرباح المعلمين'
            },
            'students': {
                'progress': 'تقرير تقدم الطلاب',
                'attendance': 'تقرير حضور الطلاب',
                'performance': 'تقرير أداء الطلاب',
                'enrollments': 'تقرير تسجيلات الطلاب'
            },
            'courses': {
                'popularity': 'تقرير شعبية الدورات',
                'completion': 'تقرير معدل إكمال الدورات',
                'ratings': 'تقرير تقييمات الدورات',
                'revenue': 'تقرير إيرادات الدورات'
            },
            'financial': {
                'revenue': 'تقرير الإيرادات',
                'payments': 'تقرير المدفوعات',
                'refunds': 'تقرير المرتجعات',
                'commissions': 'تقرير العمولات'
            }
        };

        return titles[category]?.[type] || 'تقرير مخصص';
    }

    function getQuickReportDescription(category, type) {
        const descriptions = {
            'teachers': {
                'performance': 'تقرير شامل عن أداء المعلمين وإحصائياتهم',
                'attendance': 'تقرير حضور وغياب المعلمين في الحصص المجدولة',
                'ratings': 'تقرير تقييمات المعلمين من قبل الطلاب',
                'earnings': 'تقرير أرباح ومكاسب المعلمين'
            },
            'students': {
                'progress': 'تقرير تقدم الطلاب في الدورات والحصص',
                'attendance': 'تقرير حضور الطلاب في الحصص',
                'performance': 'تقرير أداء الطلاب وإنجازاتهم',
                'enrollments': 'تقرير تسجيلات الطلاب في الدورات'
            },
            'courses': {
                'popularity': 'تقرير شعبية الدورات وعدد المسجلين',
                'completion': 'تقرير معدل إكمال الطلاب للدورات',
                'ratings': 'تقرير تقييمات الدورات من قبل الطلاب',
                'revenue': 'تقرير إيرادات الدورات والمبيعات'
            },
            'financial': {
                'revenue': 'تقرير إجمالي الإيرادات والمبيعات',
                'payments': 'تقرير المدفوعات والمعاملات المالية',
                'refunds': 'تقرير المرتجعات والاسترداد',
                'commissions': 'تقرير عمولات المعلمين والشركاء'
            }
        };

        return descriptions[category]?.[type] || 'تقرير مخصص حسب المعايير المحددة';
    }

    // Download report buttons
    document.querySelectorAll('.download-report-btn').forEach(button => {
        button.addEventListener('click', function() {
            const reportId = this.getAttribute('data-report-id');
            const format = this.getAttribute('data-format');

            // Show loading
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.disabled = true;

            // Download report
            fetch(`{% url "admin_reports_download" 0 %}`.replace('0', reportId) + `?format=${format}`)
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('فشل في تحميل التقرير');
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `report_${reportId}.${format}`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);

                // Reset button
                this.innerHTML = format === 'pdf' ? '<i class="fas fa-file-pdf"></i>' : '<i class="fas fa-file-excel"></i>';
                this.disabled = false;
            })
            .catch(error => {
                alert('حدث خطأ في التحميل: ' + error.message);
                // Reset button
                this.innerHTML = format === 'pdf' ? '<i class="fas fa-file-pdf"></i>' : '<i class="fas fa-file-excel"></i>';
                this.disabled = false;
            });
        });
    });

    // Delete report buttons
    document.querySelectorAll('.delete-report-btn').forEach(button => {
        button.addEventListener('click', function() {
            const reportId = this.getAttribute('data-report-id');

            if (confirm('هل أنت متأكد من حذف هذا التقرير؟ لا يمكن التراجع عن هذا الإجراء.')) {
                fetch(`{% url "admin_reports_delete" 0 %}`.replace('0', reportId), {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the report element
                        this.closest('.border').remove();
                        alert('تم حذف التقرير بنجاح');
                    } else {
                        alert('حدث خطأ في الحذف: ' + (data.error || 'خطأ غير معروف'));
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال: ' + error.message);
                });
            }
        });
    });

    // Set default date range (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
    document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];

    // Initialize Charts
    initializeCharts();

    // Initialize first tab as active
    const firstTab = document.querySelector('.report-tab.active');
    if (firstTab) {
        const targetTab = firstTab.getAttribute('data-tab');
        const targetContent = document.getElementById(targetTab + '-tab');
        if (targetContent) {
            targetContent.classList.remove('hidden');
            targetContent.classList.add('active');
        }
    }

    // إصلاح أزرار التحميل
    setupDownloadButtons();

    // إصلاح أزرار العرض
    setupViewButtons();

    // إصلاح أزرار الحذف
    setupDeleteButtons();
});

// وظائف الأزرار
function setupDownloadButtons() {
    document.querySelectorAll('.download-report-btn').forEach(button => {
        button.addEventListener('click', function() {
            const reportId = this.getAttribute('data-report-id');
            const format = this.getAttribute('data-format');

            if (!reportId || !format) {
                alert('معرف التقرير أو الصيغة غير صحيح');
                return;
            }

            // Show loading
            const originalContent = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.disabled = true;

            // Download report
            const downloadUrl = `{% url 'admin_reports_download' 0 %}`.replace('0', reportId) + `?format=${format}`;

            fetch(downloadUrl, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('فشل في تحميل التقرير: ' + response.status);
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `report_${reportId}.${format}`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Show success message
                showNotification('تم تحميل التقرير بنجاح', 'success');
            })
            .catch(error => {
                console.error('Download error:', error);
                showNotification('حدث خطأ في التحميل: ' + error.message, 'error');
            })
            .finally(() => {
                // Reset button
                this.innerHTML = originalContent;
                this.disabled = false;
            });
        });
    });
}

function setupViewButtons() {
    document.querySelectorAll('.view-report-btn').forEach(button => {
        button.addEventListener('click', function() {
            const reportId = this.getAttribute('data-report-id');

            if (!reportId) {
                alert('معرف التقرير غير صحيح');
                return;
            }

            // Navigate to report detail page
            const viewUrl = `{% url 'admin_reports_view' 0 %}`.replace('0', reportId);
            window.location.href = viewUrl;
        });
    });
}

function setupDeleteButtons() {
    document.querySelectorAll('.delete-report-btn').forEach(button => {
        button.addEventListener('click', function() {
            const reportId = this.getAttribute('data-report-id');

            if (!reportId) {
                alert('معرف التقرير غير صحيح');
                return;
            }

            if (!confirm('هل أنت متأكد من حذف هذا التقرير؟ لا يمكن التراجع عن هذا الإجراء.')) {
                return;
            }

            // Show loading
            const originalContent = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.disabled = true;

            // Delete report
            const deleteUrl = `{% url 'admin_reports_delete' 0 %}`.replace('0', reportId);

            fetch(deleteUrl, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error('فشل في حذف التقرير: ' + response.status);
            })
            .then(data => {
                if (data.success) {
                    // Remove the report element from DOM
                    this.closest('.border').remove();
                    showNotification('تم حذف التقرير بنجاح', 'success');
                } else {
                    throw new Error(data.error || 'فشل في حذف التقرير');
                }
            })
            .catch(error => {
                console.error('Delete error:', error);
                showNotification('حدث خطأ في الحذف: ' + error.message, 'error');
                // Reset button
                this.innerHTML = originalContent;
                this.disabled = false;
            });
        });
    });
}

// وظيفة إظهار الإشعارات
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                'fa-info-circle'
            } ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Charts Configuration and Initialization
function initializeCharts() {
    // Chart.js default configuration
    Chart.defaults.font.family = 'Cairo, sans-serif';
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#64748b';

    // Users Growth Chart
    const usersCtx = document.getElementById('usersGrowthChart');
    if (usersCtx) {
        new Chart(usersCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'المعلمون',
                    data: [12, 19, 25, 32, 45, 52],
                    borderColor: '#2D5016',
                    backgroundColor: 'rgba(45, 80, 22, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'الطلاب',
                    data: [45, 67, 89, 123, 156, 189],
                    borderColor: '#50C878',
                    backgroundColor: 'rgba(80, 200, 120, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات (ر.س)',
                    data: [15000, 23000, 18000, 35000, 42000, 38000],
                    backgroundColor: [
                        'rgba(45, 80, 22, 0.8)',
                        'rgba(80, 200, 120, 0.8)',
                        'rgba(212, 175, 55, 0.8)',
                        'rgba(74, 124, 89, 0.8)',
                        'rgba(156, 175, 136, 0.8)',
                        'rgba(26, 48, 9, 0.8)'
                    ],
                    borderColor: [
                        '#2D5016',
                        '#50C878',
                        '#D4AF37',
                        '#4A7C59',
                        '#9CAF88',
                        '#1A3009'
                    ],
                    borderWidth: 2,
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Course Completion Chart
    const completionCtx = document.getElementById('completionChart');
    if (completionCtx) {
        new Chart(completionCtx, {
            type: 'doughnut',
            data: {
                labels: ['مكتمل', 'قيد التقدم', 'لم يبدأ'],
                datasets: [{
                    data: [65, 25, 10],
                    backgroundColor: [
                        '#50C878',
                        '#D4AF37',
                        '#9CAF88'
                    ],
                    borderWidth: 0,
                    cutout: '60%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                }
            }
        });
    }

    // Activity Distribution Chart
    const activityCtx = document.getElementById('activityChart');
    if (activityCtx) {
        new Chart(activityCtx, {
            type: 'radar',
            data: {
                labels: ['حفظ القرآن', 'تلاوة', 'دراسات إسلامية', 'تجويد', 'تفسير'],
                datasets: [{
                    label: 'نشاط الطلاب',
                    data: [85, 70, 60, 75, 55],
                    borderColor: '#2D5016',
                    backgroundColor: 'rgba(45, 80, 22, 0.2)',
                    pointBackgroundColor: '#2D5016',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }, {
                    label: 'نشاط المعلمين',
                    data: [90, 80, 70, 85, 65],
                    borderColor: '#50C878',
                    backgroundColor: 'rgba(80, 200, 120, 0.2)',
                    pointBackgroundColor: '#50C878',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        angleLines: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });
    }
}
</script>

<!-- Add CSRF token for AJAX requests -->
{% csrf_token %}

<style>
.report-tab.active {
    background-color: #1e40af;
    color: white;
}

.report-tab:not(.active) {
    background-color: #f8fafc;
    color: #64748b;
}

.report-tab:not(.active):hover {
    background-color: #e2e8f0;
    color: #475569;
}

.tab-content.hidden {
    display: none;
}

.tab-content.active {
    display: block;
}
</style>
{% endblock %}
