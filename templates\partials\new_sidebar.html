<!-- 🚀 القائمة الجانبية الجديدة الموحدة والمتجاوبة -->
<!-- تعمل على جميع الأجهزة: Mobile, Tablet, Desktop -->

<!-- زر التحكم (للموبايل والتابلت فقط) -->
<button class="new-sidebar-toggle" id="newSidebarToggle" aria-label="فتح القائمة الجانبية">
    <span class="hamburger-line"></span>
    <span class="hamburger-line"></span>
    <span class="hamburger-line"></span>
</button>

<!-- خلفية شفافة (للموبايل والتابلت فقط) -->
<div class="new-sidebar-overlay" id="newSidebarOverlay" aria-hidden="true"></div>

<!-- القائمة الجانبية الموحدة -->
<aside class="new-sidebar" id="newSidebar" role="navigation" aria-label="القائمة الرئيسية">
    
    <!-- زر الإغلاق (للموبايل والتابلت فقط) -->
    <button class="new-sidebar-close" id="newSidebarClose" aria-label="إغلاق القائمة الجانبية">
        <i class="fas fa-times"></i>
    </button>

    <!-- رأس القائمة: الشعار ومعلومات المستخدم -->
    <header class="new-sidebar-header">
        <!-- شعار الأكاديمية -->
        <div class="academy-logo-section">
            <div class="academy-logo-container">
                {% if ACADEMY_SETTINGS.academy_logo %}
                    <img src="{{ ACADEMY_SETTINGS.academy_logo.url }}" 
                         alt="{{ ACADEMY_SETTINGS.academy_name }}" 
                         class="academy-logo-img">
                {% else %}
                    <div class="academy-logo-placeholder">
                        <i class="fas fa-quran"></i>
                    </div>
                {% endif %}
                <div class="academy-info">
                    <h1 class="academy-name">{{ ACADEMY_SETTINGS.academy_name }}</h1>
                    <p class="academy-slogan">{{ ACADEMY_SLOGAN }}</p>
                </div>
            </div>
        </div>

        <!-- معلومات المستخدم -->
        <div class="user-info-section">
            <div class="user-info-container">
                {% if user.profile_picture %}
                    <img src="{{ user.profile_picture.url }}" 
                         alt="صورة المستخدم" 
                         class="user-avatar">
                {% else %}
                    <div class="user-avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                {% endif %}
                <div class="user-details">
                    <p class="user-name">{{ user.get_full_name }}</p>
                    <div class="user-role">
                        {% if user.is_admin %}
                            <span class="role-badge role-admin">
                                <i class="fas fa-crown"></i>
                                مدير النظام
                            </span>
                        {% elif user.is_teacher %}
                            <span class="role-badge role-teacher">
                                <i class="fas fa-chalkboard-teacher"></i>
                                معلم
                            </span>
                        {% elif user.is_student %}
                            <span class="role-badge role-student">
                                <i class="fas fa-user-graduate"></i>
                                طالب
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- محتوى القائمة: الروابط والأقسام -->
    <main class="new-sidebar-content">
        <nav class="new-sidebar-nav" role="navigation">
            <ul class="nav-list">
                
                <!-- لوحة التحكم (مشتركة) -->
                <li class="nav-item">
                    <a href="{% url 'dashboard' %}" 
                       class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}nav-link-active{% endif %}"
                       aria-current="{% if request.resolver_match.url_name == 'dashboard' %}page{% else %}false{% endif %}">
                        <div class="nav-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <span class="nav-text">لوحة التحكم</span>
                    </a>
                </li>

                {% if user.is_admin %}
                    <!-- أقسام المدير -->
                    
                    <!-- الإدارة التعليمية -->
                    <li class="nav-section">
                        <div class="nav-section-header">
                            <span class="nav-section-title">الإدارة التعليمية</span>
                        </div>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'admin_lessons' %}" class="nav-link">
                            <div class="nav-icon nav-icon-purple">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <span class="nav-text">مراقبة الحصص</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'admin_live_lessons' %}" class="nav-link">
                            <div class="nav-icon nav-icon-green">
                                <i class="fas fa-broadcast-tower"></i>
                            </div>
                            <span class="nav-text">الحصص المباشرة</span>
                            <span class="nav-indicator nav-indicator-live"></span>
                        </a>
                    </li>

                    <!-- إدارة النظام -->
                    <li class="nav-section">
                        <div class="nav-section-header">
                            <span class="nav-section-title">إدارة النظام</span>
                        </div>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'admin_user_verifications' %}" class="nav-link">
                            <div class="nav-icon nav-icon-yellow">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <span class="nav-text">طلبات التحقق</span>
                            {% if pending_verifications_count > 0 %}
                                <span class="nav-badge">{{ pending_verifications_count }}</span>
                            {% endif %}
                        </a>
                    </li>

                    <!-- الإدارة المالية -->
                    <li class="nav-section">
                        <div class="nav-section-header">
                            <span class="nav-section-title">الإدارة المالية</span>
                        </div>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'admin_subscriptions' %}" class="nav-link">
                            <div class="nav-icon nav-icon-purple">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <span class="nav-text">الاشتراكات والباقات</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'admin_invoices' %}" class="nav-link">
                            <div class="nav-icon nav-icon-green">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <span class="nav-text">الفواتير</span>
                        </a>
                    </li>

                    <!-- إدارة الجودة -->
                    <li class="nav-section">
                        <div class="nav-section-header">
                            <span class="nav-section-title">إدارة الجودة</span>
                        </div>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'admin_reports' %}" class="nav-link">
                            <div class="nav-icon nav-icon-yellow">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <span class="nav-text">التقارير</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'admin_ratings_view' %}" class="nav-link">
                            <div class="nav-icon nav-icon-orange">
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="nav-text">التقييمات</span>
                        </a>
                    </li>

                    <!-- إدارة الدعم الفني -->
                    <li class="nav-section">
                        <div class="nav-section-header">
                            <span class="nav-section-title">إدارة الدعم الفني</span>
                        </div>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'admin_support' %}" class="nav-link">
                            <div class="nav-icon nav-icon-red">
                                <i class="fas fa-headset"></i>
                            </div>
                            <span class="nav-text">مركز الدعم</span>
                            {% if unread_tickets_count > 0 %}
                                <span class="nav-badge nav-badge-pulse">{{ unread_tickets_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'ticket_list' %}" class="nav-link">
                            <div class="nav-icon nav-icon-green">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <span class="nav-text">تذاكر الدعم</span>
                            {% if unread_tickets_count > 0 %}
                                <span class="nav-badge nav-badge-pulse">{{ unread_tickets_count }}</span>
                            {% endif %}
                        </a>
                    </li>

                {% elif user.is_teacher %}
                    <!-- أقسام المعلم -->
                    
                    <!-- المعلم -->
                    <li class="nav-section">
                        <div class="nav-section-header">
                            <span class="nav-section-title">المعلم</span>
                        </div>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'teacher_schedule' %}" class="nav-link">
                            <div class="nav-icon nav-icon-blue">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <span class="nav-text">جدول الحصص</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'teacher_students' %}" class="nav-link">
                            <div class="nav-icon nav-icon-green">
                                <i class="fas fa-user-graduate"></i>
                            </div>
                            <span class="nav-text">طلابي</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'teacher_ratings' %}" class="nav-link">
                            <div class="nav-icon nav-icon-purple">
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="nav-text">التقييمات</span>
                        </a>
                    </li>

                    <!-- حصص مباشرة ومجدولة (ديناميكية) -->
                    {% if live_lessons or scheduled_live_lessons or sidebar_subscription_lessons %}
                    <li class="nav-section">
                        <div class="nav-section-header nav-section-live">
                            <span class="nav-section-title">حصص مباشرة ومجدولة</span>
                        </div>
                    </li>
                    
                    <!-- الحصص المباشرة -->
                    {% for lesson in live_lessons %}
                    <li class="nav-item nav-item-live">
                        <a href="{% url 'teacher_live_lesson' lesson.id %}" class="nav-link nav-link-live">
                            <div class="nav-icon nav-icon-live">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="nav-text-group">
                                <span class="nav-text">{{ lesson.title|truncatechars:20 }}</span>
                                <span class="nav-subtext">مع {{ lesson.student.get_full_name|truncatechars:15 }}</span>
                            </div>
                            <span class="nav-badge nav-badge-live">مباشرة</span>
                        </a>
                    </li>
                    {% endfor %}
                    
                    <!-- الحصص المجدولة -->
                    {% for lesson in scheduled_live_lessons|slice:":2" %}
                    <li class="nav-item nav-item-scheduled">
                        <a href="{% url 'teacher_live_lesson' lesson.id %}" class="nav-link nav-link-scheduled">
                            <div class="nav-icon nav-icon-scheduled">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="nav-text-group">
                                <span class="nav-text">{{ lesson.title|truncatechars:20 }}</span>
                                <span class="nav-subtext">{{ lesson.scheduled_date|date:"H:i" }}</span>
                            </div>
                            <span class="nav-badge nav-badge-scheduled">قريباً</span>
                        </a>
                    </li>
                    {% endfor %}
                    
                    <!-- حصص الاشتراكات -->
                    {% for lesson in sidebar_subscription_lessons %}
                    <li class="nav-item nav-item-subscription">
                        <a href="#" onclick="startSubscriptionLesson({{ lesson.id }}, '{{ lesson.subscription.student.get_full_name }}', '{{ lesson.lesson_number }}')" 
                           class="nav-link nav-link-subscription">
                            <div class="nav-icon nav-icon-subscription">
                                <i class="fas fa-chalkboard-teacher"></i>
                            </div>
                            <div class="nav-text-group">
                                <span class="nav-text">🎯 حصة رقم {{ lesson.lesson_number }}</span>
                                <span class="nav-subtext">👨‍🎓 {{ lesson.subscription.student.get_full_name|truncatechars:15 }}</span>
                                <span class="nav-subtext">⏰ {{ lesson.scheduled_date|date:"H:i" }}</span>
                            </div>
                            <span class="nav-badge nav-badge-subscription">ابدأ الآن</span>
                        </a>
                    </li>
                    {% endfor %}
                    {% endif %}

                {% elif user.is_student %}
                    <!-- أقسام الطالب -->
                    
                    <!-- الطالب -->
                    <li class="nav-section">
                        <div class="nav-section-header">
                            <span class="nav-section-title">الطالب</span>
                        </div>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'student_subscriptions' %}" class="nav-link">
                            <div class="nav-icon nav-icon-purple">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <span class="nav-text">الاشتراكات والباقات</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'student_lessons' %}" class="nav-link">
                            <div class="nav-icon nav-icon-green">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <span class="nav-text">حصصي</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{% url 'student_progress' %}" class="nav-link">
                            <div class="nav-icon nav-icon-blue">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <span class="nav-text">تقدمي</span>
                        </a>
                    </li>

                    <!-- حصص مباشرة ومجدولة (ديناميكية) -->
                    {% if live_lessons or scheduled_live_lessons or subscription_scheduled_lessons %}
                    <li class="nav-section">
                        <div class="nav-section-header nav-section-live">
                            <span class="nav-section-title">حصص مباشرة ومجدولة</span>
                        </div>
                    </li>
                    
                    <!-- الحصص المباشرة -->
                    {% for lesson in live_lessons %}
                    <li class="nav-item nav-item-live">
                        <a href="{% url 'student_live_lesson' lesson.id %}" class="nav-link nav-link-live-student">
                            <div class="nav-icon nav-icon-live-student">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="nav-text-group">
                                <span class="nav-text">{{ lesson.title|truncatechars:20 }}</span>
                                <span class="nav-subtext">مع {{ lesson.teacher.get_full_name|truncatechars:15 }}</span>
                            </div>
                            <span class="nav-badge nav-badge-live-student">مباشرة</span>
                        </a>
                    </li>
                    {% endfor %}
                    
                    <!-- الحصص المجدولة -->
                    {% for lesson in scheduled_live_lessons|slice:":2" %}
                    <li class="nav-item nav-item-scheduled">
                        <a href="{% url 'student_live_lesson' lesson.id %}" class="nav-link nav-link-scheduled">
                            <div class="nav-icon nav-icon-scheduled">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="nav-text-group">
                                <span class="nav-text">{{ lesson.title|truncatechars:20 }}</span>
                                <span class="nav-subtext">{{ lesson.scheduled_date|date:"H:i" }}</span>
                            </div>
                            <span class="nav-badge nav-badge-scheduled">قريباً</span>
                        </a>
                    </li>
                    {% endfor %}
                    
                    <!-- حصص الاشتراكات -->
                    {% for lesson in subscription_scheduled_lessons %}
                    <li class="nav-item nav-item-subscription-student">
                        <a href="#" onclick="joinSubscriptionLesson({{ lesson.id }}, '{{ lesson.lesson_number }}')" 
                           class="nav-link nav-link-subscription-student">
                            <div class="nav-icon nav-icon-subscription-student">
                                <i class="fas fa-user-graduate"></i>
                            </div>
                            <div class="nav-text-group">
                                <span class="nav-text">📚 حصة رقم {{ lesson.lesson_number }}</span>
                                {% if lesson.teacher %}
                                    <span class="nav-subtext">👨‍🏫 {{ lesson.teacher.get_full_name|truncatechars:15 }}</span>
                                {% else %}
                                    <span class="nav-subtext">⚠️ لم يتم تعيين معلم</span>
                                {% endif %}
                                <span class="nav-subtext">⏰ {{ lesson.scheduled_date|date:"H:i" }}</span>
                            </div>
                            <span class="nav-badge nav-badge-subscription-student">انضم الآن</span>
                        </a>
                    </li>
                    {% endfor %}
                    {% endif %}

                {% endif %}

                <!-- الأقسام العامة (جميع المستخدمين) -->
                <li class="nav-section">
                    <div class="nav-section-header">
                        <span class="nav-section-title">عام</span>
                    </div>
                </li>
                
                <li class="nav-item">
                    <a href="{% url 'notifications' %}" class="nav-link">
                        <div class="nav-icon nav-icon-yellow">
                            <i class="fas fa-bell"></i>
                        </div>
                        <span class="nav-text">الإشعارات</span>
                        {% if unread_notifications_count > 0 %}
                            <span class="nav-badge nav-badge-pulse">{{ unread_notifications_count }}</span>
                        {% endif %}
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="{% url 'conversations_list' %}" class="nav-link">
                        <div class="nav-icon nav-icon-blue">
                            <i class="fas fa-comments"></i>
                        </div>
                        <span class="nav-text">الرسائل</span>
                        {% if unread_messages_count > 0 %}
                            <span class="nav-badge nav-badge-pulse">{{ unread_messages_count }}</span>
                        {% endif %}
                    </a>
                </li>

                {% if not user.is_admin %}
                <li class="nav-item">
                    <a href="{% url 'system_messages' %}" class="nav-link">
                        <div class="nav-icon nav-icon-orange">
                            <i class="fas fa-broadcast-tower"></i>
                        </div>
                        <span class="nav-text">رسائل النظام</span>
                        {% if unread_system_messages_count > 0 %}
                            <span class="nav-badge nav-badge-pulse">{{ unread_system_messages_count }}</span>
                        {% endif %}
                    </a>
                </li>
                {% endif %}

                <!-- إعدادات عامة (للمدير فقط) -->
                {% if user.user_type == 'admin' %}
                <li class="nav-section">
                    <div class="nav-section-header">
                        <span class="nav-section-title">إعدادات عامة</span>
                    </div>
                </li>
                
                <li class="nav-item">
                    <a href="{% url 'technical_settings' %}" class="nav-link">
                        <div class="nav-icon nav-icon-gold">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <span class="nav-text">الإعدادات التقنية</span>
                    </a>
                </li>
                {% endif %}

                <!-- الدعم الفني (للمعلم والطالب) -->
                {% if not user.is_admin %}
                <li class="nav-section">
                    <div class="nav-section-header">
                        <span class="nav-section-title">الدعم الفني</span>
                    </div>
                </li>
                
                <li class="nav-item">
                    <a href="{% url 'ticket_list' %}" class="nav-link">
                        <div class="nav-icon nav-icon-green">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <span class="nav-text">تذاكر الدعم</span>
                        {% if user_unread_tickets_count > 0 %}
                            <span class="nav-badge nav-badge-pulse">{{ user_unread_tickets_count }}</span>
                        {% endif %}
                    </a>
                </li>
                {% endif %}

            </ul>
        </nav>
    </main>

    <!-- تذييل القائمة: الإعدادات وتسجيل الخروج -->
    <footer class="new-sidebar-footer">
        <div class="footer-nav">
            <ul class="footer-nav-list">
                {% if user.is_admin %}
                <li class="footer-nav-item">
                    <a href="{% url 'admin_academy_settings' %}" class="footer-nav-link">
                        <div class="footer-nav-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="footer-nav-text">إعدادات الأكاديمية</span>
                    </a>
                </li>
                {% endif %}

                <li class="footer-nav-item">
                    <a href="{% url 'profile' %}" class="footer-nav-link">
                        <div class="footer-nav-icon">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <span class="footer-nav-text">الملف الشخصي</span>
                    </a>
                </li>

                <li class="footer-nav-item">
                    <a href="{% url 'logout' %}" class="footer-nav-link footer-nav-link-logout">
                        <div class="footer-nav-icon">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <span class="footer-nav-text">تسجيل الخروج</span>
                    </a>
                </li>
            </ul>
        </div>
    </footer>

</aside>

</aside>
