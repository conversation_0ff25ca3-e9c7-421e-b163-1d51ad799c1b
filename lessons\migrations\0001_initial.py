# Generated manually after removing monitoring models

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('subscriptions', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Lesson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الحصة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الحصة')),
                ('scheduled_date', models.DateTimeField(verbose_name='تاريخ الحصة المجدولة')),
                ('duration_minutes', models.PositiveIntegerField(default=60, verbose_name='مدة الحصة (بالدقائق)')),
                ('status', models.CharField(choices=[('scheduled', 'مجدولة'), ('completed', 'مكتملة'), ('cancelled', 'ملغية'), ('rescheduled', 'معاد جدولتها')], default='scheduled', max_length=20, verbose_name='حالة الحصة')),
                ('jitsi_room_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='معرف غرفة Jitsi')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('student', models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='lessons', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
                ('teacher', models.ForeignKey(limit_choices_to={'user_type': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='teaching_lessons', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'حصة',
                'verbose_name_plural': 'الحصص',
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='LiveLesson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الحصة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الحصة')),
                ('scheduled_date', models.DateTimeField(verbose_name='تاريخ الحصة المجدولة')),
                ('duration_minutes', models.PositiveIntegerField(default=60, verbose_name='مدة الحصة (بالدقائق)')),
                ('status', models.CharField(choices=[('scheduled', 'مجدولة'), ('live', 'جارية الآن'), ('completed', 'مكتملة'), ('cancelled', 'ملغية')], default='scheduled', max_length=20, verbose_name='حالة الحصة')),
                ('jitsi_room_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='معرف غرفة Jitsi')),
                ('jitsi_room_url', models.URLField(blank=True, null=True, verbose_name='رابط غرفة Jitsi')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت بدء الحصة')),
                ('ended_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت انتهاء الحصة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('student', models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='live_lessons', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
                ('teacher', models.ForeignKey(limit_choices_to={'user_type': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='teaching_live_lessons', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'حصة مباشرة',
                'verbose_name_plural': 'الحصص المباشرة',
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='UnifiedLessonRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lesson_type', models.CharField(choices=[('scheduled', 'حصة مجدولة'), ('live', 'حصة مباشرة')], max_length=20, verbose_name='نوع الحصة')),
                ('overall_rating', models.IntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='التقييم العام')),
                ('lesson_quality', models.IntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='جودة الحصة')),
                ('teacher_interaction', models.IntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='تفاعل المعلم')),
                ('technical_quality', models.IntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='الجودة التقنية')),
                ('punctuality', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='الالتزام بالوقت')),
                ('lesson_preparation', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='التحضير للحصة')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='التعليق')),
                ('attendance_quality_score', models.FloatField(blank=True, null=True, verbose_name='نقاط جودة الحضور')),
                ('lesson_duration_minutes', models.PositiveIntegerField(blank=True, null=True, verbose_name='مدة الحصة الفعلية')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('submitted', 'تم الإرسال'), ('reviewed', 'تمت المراجعة'), ('disputed', 'محل نزاع')], default='submitted', max_length=20, verbose_name='حالة التقييم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('live_lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='unified_ratings', to='lessons.livelesson', verbose_name='الحصة المباشرة')),
                ('scheduled_lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='unified_ratings', to='subscriptions.scheduledlesson', verbose_name='الحصة المجدولة')),
                ('student', models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='given_unified_ratings', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_unified_ratings', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'تقييم موحد للحصة',
                'verbose_name_plural': 'التقييمات الموحدة للحصص',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TeacherLessonReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_performance', models.PositiveSmallIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='أداء الطالب')),
                ('student_participation', models.PositiveSmallIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='مستوى التفاعل')),
                ('student_understanding', models.PositiveSmallIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='مستوى الفهم')),
                ('strengths', models.TextField(verbose_name='نقاط القوة')),
                ('areas_for_improvement', models.TextField(verbose_name='نقاط تحتاج تحسين')),
                ('lesson_summary', models.TextField(verbose_name='ملخص الحصة')),
                ('homework_assigned', models.TextField(blank=True, null=True, verbose_name='الواجبات المطلوبة')),
                ('recommendations', models.TextField(blank=True, null=True, verbose_name='توصيات للحصص القادمة')),
                ('overall_lesson_rating', models.PositiveSmallIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='تقييم عام للحصة')),
                ('additional_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات إضافية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ إنشاء التقرير')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('lesson', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_report', to='lessons.livelesson', verbose_name='الحصة')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_reports_received', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_reports', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'تقرير المعلم عن الحصة',
                'verbose_name_plural': 'تقارير المعلمين عن الحصص',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LessonRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveIntegerField(choices=[(1, '⭐'), (2, '⭐⭐'), (3, '⭐⭐⭐'), (4, '⭐⭐⭐⭐'), (5, '⭐⭐⭐⭐⭐')], verbose_name='التقييم')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='تعليق')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقييم')),
                ('lesson', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='lessons.lesson', verbose_name='الحصة')),
                ('student', models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='lesson_ratings', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
            ],
            options={
                'verbose_name': 'تقييم الحصة',
                'verbose_name_plural': 'تقييمات الحصص',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LessonContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المحتوى')),
                ('content_type', models.CharField(choices=[('text', 'نص'), ('video', 'فيديو'), ('audio', 'صوت'), ('document', 'مستند'), ('link', 'رابط')], max_length=20, verbose_name='نوع المحتوى')),
                ('content', models.TextField(blank=True, null=True, verbose_name='المحتوى')),
                ('file', models.FileField(blank=True, null=True, upload_to='lesson_content/', verbose_name='ملف')),
                ('url', models.URLField(blank=True, null=True, verbose_name='رابط')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('lesson', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content', to='lessons.lesson', verbose_name='الحصة')),
            ],
            options={
                'verbose_name': 'محتوى الحصة',
                'verbose_name_plural': 'محتويات الحصص',
                'ordering': ['order'],
            },
        ),
    ]
