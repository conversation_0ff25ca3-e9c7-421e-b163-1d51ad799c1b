"""
أمر Django لإعداد إعدادات البريد الإلكتروني الافتراضية
يتم تشغيله تلقائياً عند النشر على الإنتاج
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from users.email_models import SMTPSettings


class Command(BaseCommand):
    help = 'إعداد إعدادات البريد الإلكتروني الافتراضية'

    def handle(self, *args, **options):
        self.stdout.write('🚀 بدء إعداد إعدادات البريد الإلكتروني الافتراضية...')
        
        try:
            # التحقق من وجود إعدادات SMTP
            smtp_settings = SMTPSettings.objects.first()
            
            if not smtp_settings:
                # إنشاء إعدادات SMTP افتراضية
                smtp_settings = SMTPSettings.objects.create(
                    host='smtp.gmail.com',
                    port=587,
                    username='',  # سيتم تعبئته من قبل المدير
                    password='',  # سيتم تعبئته من قبل المدير
                    use_tls=True,
                    use_ssl=False,
                    from_email='',  # سيتم تعبئته من قبل المدير
                    from_name='نظام قرآنيا التعليمي',
                    is_active=False,  # غير نشط حتى يتم تعبئة البيانات
                    created_at=timezone.now(),
                    updated_at=timezone.now()
                )
                self.stdout.write('✅ تم إنشاء إعدادات SMTP الافتراضية')
                self.stdout.write('⚠️ يرجى تعبئة بيانات SMTP من لوحة الإدارة لتفعيل النظام')
            else:
                self.stdout.write('ℹ️ إعدادات SMTP موجودة مسبقاً')
                
            # عرض معلومات الإعدادات
            self.stdout.write(f'📧 خادم SMTP: {smtp_settings.host}:{smtp_settings.port}')
            self.stdout.write(f'🔐 TLS: {"مفعل" if smtp_settings.use_tls else "معطل"}')
            self.stdout.write(f'🔐 SSL: {"مفعل" if smtp_settings.use_ssl else "معطل"}')
            self.stdout.write(f'📤 البريد المرسل: {smtp_settings.from_email or "غير محدد"}')
            self.stdout.write(f'👤 اسم المرسل: {smtp_settings.from_name}')
            self.stdout.write(f'✅ الحالة: {"نشط" if smtp_settings.is_active else "غير نشط"}')
            
            if not smtp_settings.is_active:
                self.stdout.write(
                    self.style.WARNING(
                        '\n⚠️ تنبيه: نظام البريد الإلكتروني غير نشط!'
                        '\nيرجى الذهاب إلى: الإعدادات التقنية > إعدادات البريد الإلكتروني'
                        '\nوتعبئة بيانات SMTP وتفعيل النظام.'
                    )
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        '🎉 نظام البريد الإلكتروني جاهز للاستخدام!'
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في إعداد إعدادات البريد الإلكتروني: {str(e)}')
            )

        self.stdout.write(
            self.style.SUCCESS('✅ تم الانتهاء من إعداد إعدادات البريد الإلكتروني')
        )
