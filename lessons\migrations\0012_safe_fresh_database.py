# Safe migration for fresh database deployment
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0011_safe_migration_fix'),
    ]

    operations = [
        # This migration ensures that the database is in a consistent state
        # for fresh deployments without the problematic old tables
        migrations.RunSQL(
            "-- Safe migration for fresh database",
            reverse_sql="-- Reverse safe migration"
        ),
    ]
