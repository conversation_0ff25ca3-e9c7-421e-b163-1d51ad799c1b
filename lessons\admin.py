from django.contrib import admin
from .models import (
    Lesson, StudentEvaluation, MakeupRequest,
    LessonContent, LiveLesson, UnifiedLessonRating, LessonAttendance,
    ComprehensiveLessonReport
)

# ===== النظام الجديد الموحد =====

@admin.register(Lesson)
class LessonAdmin(admin.ModelAdmin):
    """إدارة الحصص الموحدة"""
    list_display = [
        'id', 'title', 'lesson_type', 'status',
        'teacher', 'student', 'scheduled_date',
        'duration_minutes', 'lesson_number'
    ]
    list_filter = [
        'lesson_type', 'status', 'created_at',
        'teacher', 'duration_minutes'
    ]
    search_fields = [
        'title', 'description',
        'teacher__first_name', 'teacher__last_name',
        'student__first_name', 'student__last_name'
    ]
    readonly_fields = [
        'jitsi_room_id', 'jitsi_room_password',
        'created_at', 'updated_at'
    ]
    fieldsets = (
        ('معلومات أساسية', {
            'fields': (
                'title', 'description', 'lesson_type',
                'teacher', 'student', 'created_by'
            )
        }),
        ('التوقيت', {
            'fields': (
                'scheduled_date', 'duration_minutes',
                'started_at', 'ended_at'
            )
        }),
        ('الحالة', {
            'fields': (
                'status', 'cancelled_at', 'cancelled_by',
                'cancellation_reason', 'cancellation_note'
            )
        }),
        ('الاشتراك والترقيم', {
            'fields': (
                'subscription', 'lesson_number', 'original_lesson'
            )
        }),
        ('التقارير والتقييمات', {
            'fields': (
                'teacher_report', 'teacher_report_submitted',
                'student_evaluation_submitted'
            )
        }),
        ('Jitsi', {
            'fields': (
                'jitsi_room_id', 'jitsi_room_password'
            ),
            'classes': ('collapse',)
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'teacher', 'student', 'subscription', 'created_by'
        )


@admin.register(StudentEvaluation)
class StudentEvaluationAdmin(admin.ModelAdmin):
    """إدارة تقييمات الطلاب"""
    list_display = [
        'id', 'lesson', 'student', 'teacher',
        'overall_rating', 'average_rating', 'created_at'
    ]
    list_filter = [
        'overall_rating', 'teaching_quality', 'lesson_content',
        'would_recommend', 'interested_in_subscription', 'created_at'
    ]
    search_fields = [
        'lesson__title', 'student__first_name', 'student__last_name',
        'teacher__first_name', 'teacher__last_name', 'comment'
    ]
    readonly_fields = ['created_at', 'average_rating']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('lesson', 'student', 'teacher')
        }),
        ('التقييمات', {
            'fields': (
                'overall_rating', 'teaching_quality', 'lesson_content',
                'interaction_quality', 'punctuality', 'average_rating'
            )
        }),
        ('تقييمات خاصة', {
            'fields': (
                'would_recommend', 'interested_in_subscription'
            )
        }),
        ('التعليق', {
            'fields': ('comment',)
        }),
        ('التاريخ', {
            'fields': ('created_at',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'lesson', 'student', 'teacher'
        )


@admin.register(MakeupRequest)
class MakeupRequestAdmin(admin.ModelAdmin):
    """إدارة طلبات الحصص التعويضية"""
    list_display = [
        'id', 'original_lesson', 'requested_by',
        'status', 'request_date', 'reviewed_by'
    ]
    list_filter = [
        'status', 'request_date', 'reviewed_at'
    ]
    search_fields = [
        'original_lesson__title', 'requested_by__first_name',
        'requested_by__last_name', 'reason', 'admin_notes'
    ]
    readonly_fields = ['request_date', 'reviewed_at']

    fieldsets = (
        ('معلومات الطلب', {
            'fields': (
                'original_lesson', 'requested_by', 'reason', 'request_date'
            )
        }),
        ('حالة الطلب', {
            'fields': (
                'status', 'reviewed_by', 'reviewed_at', 'admin_notes'
            )
        }),
        ('الحصة التعويضية', {
            'fields': ('makeup_lesson',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'original_lesson', 'requested_by', 'reviewed_by', 'makeup_lesson'
        )


# ===== النماذج القديمة =====

@admin.register(LiveLesson)
class LiveLessonAdmin(admin.ModelAdmin):
    list_display = ['title', 'teacher', 'student', 'meeting_platform', 'scheduled_date', 'status', 'created_at']
    list_filter = ['status', 'meeting_platform', 'created_at', 'scheduled_date']
    search_fields = ['title', 'teacher__first_name', 'student__first_name']
    readonly_fields = ['jitsi_room_id', 'jitsi_room_password', 'google_meet_url', 'google_meet_id', 'created_at', 'updated_at']

    fieldsets = (
        ('معلومات الحصة', {
            'fields': ('title', 'description', 'teacher', 'student')
        }),
        ('إعدادات الجلسة', {
            'fields': ('scheduled_date', 'duration_minutes', 'status')
        }),
        ('معلومات Jitsi', {
            'fields': ('jitsi_room_id', 'jitsi_room_password'),
            'classes': ('collapse',)
        }),
        ('التوقيتات', {
            'fields': ('started_at', 'ended_at', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(UnifiedLessonRating)
class UnifiedLessonRatingAdmin(admin.ModelAdmin):
    list_display = ['live_lesson', 'student', 'teacher', 'overall_rating', 'student_performance', 'status', 'created_at']
    list_filter = ['overall_rating', 'student_performance', 'status', 'created_at']
    search_fields = ['live_lesson__title', 'student__first_name', 'teacher__first_name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('معلومات الحصة', {
            'fields': ('live_lesson', 'student', 'teacher')
        }),
        ('تقييم الطالب للمعلم', {
            'fields': ('overall_rating', 'lesson_quality', 'teacher_interaction', 'comment'),
            'classes': ('collapse',)
        }),
        ('تقرير المعلم عن الطالب', {
            'fields': ('student_performance', 'student_participation', 'student_understanding',
                      'lesson_summary', 'strengths', 'areas_for_improvement',
                      'homework_assigned', 'recommendations', 'teacher_additional_notes'),
            'classes': ('collapse',)
        }),
        ('معلومات إضافية', {
            'fields': ('status', 'lesson_duration_minutes', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

# تم دمج TeacherLessonReportAdmin مع UnifiedLessonRatingAdmin

@admin.register(LessonAttendance)
class LessonAttendanceAdmin(admin.ModelAdmin):
    list_display = ['lesson', 'user', 'joined_at', 'left_at', 'duration_minutes']
    list_filter = ['joined_at', 'left_at']
    search_fields = ['lesson__title', 'user__first_name', 'user__last_name']
    readonly_fields = ['joined_at', 'left_at', 'duration_minutes']

@admin.register(LessonContent)
class LessonContentAdmin(admin.ModelAdmin):
    list_display = ['lesson', 'content_type', 'surah_name', 'quality_score', 'recorded_at']
    list_filter = ['content_type', 'quality_score', 'recorded_at']


@admin.register(ComprehensiveLessonReport)
class ComprehensiveLessonReportAdmin(admin.ModelAdmin):
    """إدارة التقارير الشاملة للحصص"""
    list_display = [
        'id', 'lesson_type', 'teacher', 'student',
        'teacher_report_submitted', 'student_evaluation_submitted',
        'get_teacher_average_rating', 'get_student_average_rating', 'created_at'
    ]
    list_filter = [
        'lesson_type', 'teacher_report_submitted', 'student_evaluation_submitted',
        'created_at', 'teacher', 'would_recommend', 'interested_in_subscription'
    ]
    search_fields = [
        'teacher__first_name', 'teacher__last_name',
        'student__first_name', 'student__last_name',
        'lesson_summary', 'strengths', 'areas_for_improvement'
    ]
    readonly_fields = [
        'created_at', 'updated_at', 'teacher_report_date', 'student_evaluation_date'
    ]

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('lesson_type', 'lesson', 'live_lesson', 'teacher', 'student')
        }),
        ('تقرير المعلم', {
            'fields': (
                'teacher_report_submitted', 'teacher_report_date',
                'student_performance', 'student_participation',
                'student_understanding', 'overall_lesson_rating',
                'lesson_summary', 'strengths', 'areas_for_improvement', 'additional_notes'
            )
        }),
        ('تقييم الطالب', {
            'fields': (
                'student_evaluation_submitted', 'student_evaluation_date',
                'overall_rating', 'teaching_quality', 'lesson_content',
                'interaction_quality', 'punctuality', 'student_comment'
            )
        }),
        ('خاص بالحصص التجريبية', {
            'fields': ('would_recommend', 'interested_in_subscription')
        }),
        ('التوقيتات', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'teacher', 'student', 'lesson', 'live_lesson'
        )


