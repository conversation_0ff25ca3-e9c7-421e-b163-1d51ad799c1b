{% extends 'base.html' %}
{% load static %}

{% block title %}رسائل النظام{% endblock %}

{% block extra_css %}
<style>
    .messages-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .message-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .message-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .message-card.unread {
        border-left: 4px solid #3b82f6;
        background: rgba(255, 255, 255, 1);
    }

    .message-card.urgent {
        border-left: 4px solid #ef4444;
        background: rgba(254, 242, 242, 0.95);
    }

    .message-type-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }

    .priority-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }

    .priority-low { background: #f3f4f6; color: #6b7280; }
    .priority-medium { background: #dbeafe; color: #1d4ed8; }
    .priority-high { background: #fed7aa; color: #ea580c; }
    .priority-urgent { background: #fecaca; color: #dc2626; }

    .type-announcement { background: #dbeafe; color: #1d4ed8; }
    .type-warning { background: #fecaca; color: #dc2626; }
    .type-reminder { background: #fef3c7; color: #d97706; }
    .type-update { background: #d1fae5; color: #059669; }
    .type-maintenance { background: #e9d5ff; color: #7c3aed; }
    .type-policy { background: #e0e7ff; color: #4338ca; }
    .type-feature { background: #ccfbf1; color: #0d9488; }
    .type-urgent { background: #fecaca; color: #dc2626; }

    .filter-tabs {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .filter-tab {
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .filter-tab:hover, .filter-tab.active {
        background: rgba(255, 255, 255, 0.9);
        color: #374151;
        text-decoration: none;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.9);
        padding: 1.5rem;
        border-radius: 0.75rem;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #3b82f6;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 1rem;
        margin: 2rem 0;
    }

    .empty-icon {
        font-size: 4rem;
        color: #d1d5db;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="messages-container">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">
                <i class="fas fa-broadcast-tower ml-3"></i>
                رسائل النظام
            </h1>
            <p class="text-white text-opacity-90 text-lg">رسائل مهمة من مركز الدعم الفني</p>
        </div>

        <!-- Statistics -->
        <div class="max-w-6xl mx-auto mb-8">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ total_messages }}</div>
                    <div class="stat-label">
                        <i class="fas fa-envelope text-blue-500 ml-1"></i>
                        إجمالي الرسائل
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ unread_messages }}</div>
                    <div class="stat-label">
                        <i class="fas fa-envelope-open text-orange-500 ml-1"></i>
                        رسائل غير مقروءة
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ read_messages }}</div>
                    <div class="stat-label">
                        <i class="fas fa-check-circle text-green-500 ml-1"></i>
                        رسائل مقروءة
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="max-w-6xl mx-auto mb-6">
            <div class="filter-tabs">
                <a href="{% url 'system_messages' %}"
                   class="filter-tab {% if not current_type and not current_read_status %}active{% endif %}">
                    <i class="fas fa-list ml-1"></i>
                    جميع الرسائل
                </a>
                <a href="{% url 'system_messages' %}?read=unread"
                   class="filter-tab {% if current_read_status == 'unread' %}active{% endif %}">
                    <i class="fas fa-envelope ml-1"></i>
                    غير مقروءة
                </a>
                <a href="{% url 'system_messages' %}?read=read"
                   class="filter-tab {% if current_read_status == 'read' %}active{% endif %}">
                    <i class="fas fa-envelope-open ml-1"></i>
                    مقروءة
                </a>

                <!-- Message Type Filters -->
                {% for value, label in message_types %}
                <a href="{% url 'system_messages' %}?type={{ value }}"
                   class="filter-tab {% if current_type == value %}active{% endif %}">
                    {{ label }}
                </a>
                {% endfor %}
            </div>
        </div>

        <!-- Messages List -->
        <div class="max-w-6xl mx-auto">
            {% if message_receipts %}
                {% for receipt in message_receipts %}
                <div class="message-card {% if not receipt.is_read %}unread{% endif %} {% if receipt.system_message.priority == 'urgent' %}urgent{% endif %}"
                     onclick="window.location.href='{% url 'system_message_detail' receipt.system_message.id %}'">

                    <!-- Message Header -->
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">
                                {% if not receipt.is_read %}
                                    <i class="fas fa-circle text-blue-500 text-xs ml-2"></i>
                                {% endif %}
                                {{ receipt.system_message.title }}
                            </h3>

                            <!-- Badges -->
                            <div class="flex items-center gap-2 mb-2">
                                <span class="message-type-badge type-{{ receipt.system_message.message_type }}">
                                    {{ receipt.system_message.get_message_type_display }}
                                </span>
                                <span class="priority-badge priority-{{ receipt.system_message.priority }}">
                                    {{ receipt.system_message.get_priority_display }}
                                </span>
                                {% if receipt.system_message.is_expired %}
                                    <span class="priority-badge priority-urgent">
                                        <i class="fas fa-clock ml-1"></i>
                                        منتهية الصلاحية
                                    </span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="text-left">
                            <div class="text-sm text-gray-600">
                                <i class="fas fa-calendar ml-1"></i>
                                {{ receipt.system_message.created_at|date:"Y/m/d H:i" }}
                            </div>
                            {% if receipt.is_read %}
                                <div class="text-sm text-green-600 mt-1">
                                    <i class="fas fa-check-circle ml-1"></i>
                                    مقروءة في {{ receipt.read_at|date:"Y/m/d H:i" }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Message Preview -->
                    <div class="text-gray-700 mb-3">
                        {{ receipt.system_message.content|striptags|truncatewords:30 }}
                    </div>

                    <!-- Message Footer -->
                    <div class="flex items-center justify-between text-sm text-gray-600">
                        <div>
                            <i class="fas fa-user-shield ml-1"></i>
                            من: {{ receipt.system_message.sent_by.get_full_name }}
                        </div>
                        <div class="flex items-center">
                            {% if receipt.system_message.expires_at %}
                                <span class="ml-4">
                                    <i class="fas fa-clock ml-1"></i>
                                    ينتهي: {{ receipt.system_message.expires_at|date:"Y/m/d" }}
                                </span>
                            {% endif %}
                            <span class="text-blue-600">
                                <i class="fas fa-arrow-left ml-1"></i>
                                اقرأ المزيد
                            </span>
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- Pagination -->
                {% if message_receipts.has_other_pages %}
                <div class="flex justify-center mt-8">
                    <nav class="flex items-center space-x-2 space-x-reverse">
                        {% if message_receipts.has_previous %}
                            <a href="?page={{ message_receipts.previous_page_number }}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_read_status %}&read={{ current_read_status }}{% endif %}"
                               class="px-3 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50">
                                السابق
                            </a>
                        {% endif %}

                        <span class="px-4 py-2 bg-blue-600 text-white rounded-lg">
                            {{ message_receipts.number }} من {{ message_receipts.paginator.num_pages }}
                        </span>

                        {% if message_receipts.has_next %}
                            <a href="?page={{ message_receipts.next_page_number }}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_read_status %}&read={{ current_read_status }}{% endif %}"
                               class="px-3 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50">
                                التالي
                            </a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}

            {% else %}
                <!-- Empty State -->
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">لا توجد رسائل</h3>
                    <p class="text-gray-600">
                        {% if current_type or current_read_status %}
                            لا توجد رسائل تطابق الفلتر المحدد.
                        {% else %}
                            لم تستلم أي رسائل من النظام بعد.
                        {% endif %}
                    </p>
                    {% if current_type or current_read_status %}
                        <a href="{% url 'system_messages' %}" class="inline-block mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            عرض جميع الرسائل
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Auto-refresh every 30 seconds for new messages
setInterval(function() {
    if (document.visibilityState === 'visible') {
        // Only refresh if there are unread messages
        if ({{ unread_messages }} > 0) {
            location.reload();
        }
    }
}, 30000);

// Mark message as read when clicked
document.querySelectorAll('.message-card').forEach(card => {
    card.addEventListener('click', function(e) {
        // Add visual feedback
        this.style.opacity = '0.7';
    });
});
</script>
{% endblock %}
