from django.contrib.auth import get_user_model
from notifications.models import Notification
from support.models import SupportTicket, SystemMessageRecipient
from messaging.models import Conversation
from django.db.models import Q
from datetime import datetime
from users.models import AcademySettings

User = get_user_model()


def _send_lesson_notification(user, lesson, user_type):
    """إرسال إشعار عند ظهور الحصة المجدولة"""
    try:
        from notifications.models import Notification
        from django.utils import timezone

        # التحقق من عدم إرسال إشعار مكرر
        existing_notification = Notification.objects.filter(
            user=user,
            title__contains=f"حصة رقم {lesson.lesson_number}",
            created_at__gte=timezone.now() - timezone.timedelta(hours=1)
        ).exists()

        if not existing_notification:
            if user_type == 'teacher':
                title = f"🎯 حصة رقم {lesson.lesson_number} جاهزة للبدء"
                message = f"حصة مع الطالب {lesson.subscription.student.get_full_name()} في {lesson.scheduled_date.strftime('%H:%M')}"
            else:  # student
                teacher_name = lesson.teacher.get_full_name() if lesson.teacher else "المعلم"
                title = f"📚 حصة رقم {lesson.lesson_number} جاهزة للانضمام"
                message = f"حصة مع {teacher_name} في {lesson.scheduled_date.strftime('%H:%M')}"

            Notification.objects.create(
                user=user,
                title=title,
                message=message,
                notification_type='lesson_ready',
                is_read=False
            )
    except Exception as e:
        # تجاهل أخطاء الإشعارات لعدم تعطيل النظام
        pass


def global_context(request):
    """إضافة متغيرات عامة لجميع القوالب"""
    context = {}

    # إضافة إعدادات الأكاديمية
    academy_settings = AcademySettings.get_settings()
    context['ACADEMY_SETTINGS'] = academy_settings
    context['SITE_NAME'] = academy_settings.academy_name
    context['SITE_VERSION'] = '1.0.0'
    context['CURRENT_YEAR'] = datetime.now().year

    # إضافة متغيرات مختصرة لإعدادات الأكاديمية
    context['ACADEMY_EMAIL'] = academy_settings.academy_email
    context['ACADEMY_PHONE'] = academy_settings.academy_phone
    context['ACADEMY_WHATSAPP'] = academy_settings.academy_whatsapp
    context['ACADEMY_SUPPORT_EMAIL'] = academy_settings.academy_support_email
    context['ACADEMY_ADDRESS'] = academy_settings.academy_address
    context['ACADEMY_DESCRIPTION'] = academy_settings.academy_description
    context['ACADEMY_SLOGAN'] = academy_settings.academy_slogan
    context['ACADEMY_LOGO'] = academy_settings.academy_logo
    context['ACADEMY_WORKING_HOURS'] = academy_settings.academy_working_hours

    if request.user.is_authenticated:
        user = request.user

        # 1. إحصائيات الإشعارات العامة
        unread_notifications = Notification.objects.filter(
            recipient=user,
            is_read=False
        ).count()
        context['unread_notifications_count'] = unread_notifications

        # 2. إحصائيات تذاكر الدعم
        if user.is_admin():
            # للمدير - التذاكر غير المقروءة
            unread_tickets = SupportTicket.objects.filter(
                is_read_by_admin=False
            ).count()
            context['unread_tickets_count'] = unread_tickets
        else:
            # للمستخدمين - تذاكرهم غير المقروءة
            user_unread_tickets = SupportTicket.objects.filter(
                created_by=user,
                is_read_by_user=False
            ).count()
            context['user_unread_tickets_count'] = user_unread_tickets

        # 3. إحصائيات رسائل النظام (للمعلمين والطلاب فقط)
        if not user.is_admin():
            unread_system_messages = SystemMessageRecipient.objects.filter(
                recipient=user,
                system_message__is_active=True,
                is_read=False
            ).count()
            context['unread_system_messages_count'] = unread_system_messages

        # 4. إحصائيات الرسائل الشخصية
        if user.user_type == 'admin':
            conversations = Conversation.objects.filter(is_active=True)
        else:
            conversations = Conversation.objects.filter(
                Q(participant1=user) | Q(participant2=user) |
                Q(student=user) | Q(teacher=user),
                is_active=True
            )

        unread_messages = 0
        for conversation in conversations:
            unread_messages += conversation.get_unread_count_for_user(user)

        context['unread_messages_count'] = unread_messages

        # 5. إحصائيات طلبات التحقق للمدير
        if user.user_type == 'admin':
            pending_verifications = User.objects.filter(
                verification_status='pending'
            ).exclude(user_type='admin').count()
            context['pending_verifications_count'] = pending_verifications
        else:
            context['pending_verifications_count'] = 0

        # 6. الحصص المباشرة للقائمة الجانبية
        from lessons.models import LiveLesson
        from django.utils import timezone

        if user.is_teacher():
            now = timezone.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            today_end = now.replace(hour=23, minute=59, second=59, microsecond=999999)

            # الحصص المباشرة الجارية للمعلم - فقط المخصصة له
            live_lessons = LiveLesson.objects.filter(
                teacher=user,
                status='live'
            ).select_related('student').order_by('-started_at')

            # الحصص المباشرة المجدولة للمعلم - فقط الجارية والقادمة خلال اليوم
            scheduled_live_lessons = LiveLesson.objects.filter(
                teacher=user,
                status='scheduled',
                scheduled_date__gte=now,  # من الآن فصاعداً
                scheduled_date__lte=today_end  # حتى نهاية اليوم
            ).select_related('student').order_by('scheduled_date')[:3]

            # الحصص المجدولة من نظام الاشتراكات - فقط التي تم تعيين المعلم لها
            from subscriptions.models import ScheduledLesson

            # للقائمة الجانبية - حصة واحدة فقط خلال الـ 30 دقيقة القادمة
            sidebar_subscription_lessons = ScheduledLesson.objects.filter(
                teacher=user,  # المعلم المحدد لهذه الحصص
                subscription__status='active',  # الاشتراكات النشطة فقط
                status='scheduled',  # فقط المجدولة (ليس المكتملة)
                scheduled_date__gte=now,  # من الآن
                scheduled_date__lte=now + timezone.timedelta(minutes=30)  # خلال الـ 30 دقيقة القادمة
            ).select_related(
                'subscription__student',
                'subscription__plan'
            ).order_by('scheduled_date')[:1]  # حصة واحدة فقط

            # للبطاقة الرئيسية - الحصص القادمة خلال اليوم الحالي (ليس المكتملة)
            card_subscription_lessons = ScheduledLesson.objects.filter(
                teacher=user,  # المعلم المحدد لهذه الحصص
                subscription__status='active',  # الاشتراكات النشطة فقط
                status='scheduled',  # فقط المجدولة (ليس المكتملة)
                scheduled_date__gte=now,  # من الآن
                scheduled_date__lte=today_end  # حتى نهاية اليوم
            ).select_related(
                'subscription__student',
                'subscription__plan'
            ).order_by('scheduled_date')[:2]  # حصتين كحد أقصى

            # إرسال إشعار للمعلم عند ظهور الحصة
            if sidebar_subscription_lessons.exists():
                lesson = sidebar_subscription_lessons.first()
                _send_lesson_notification(user, lesson, 'teacher')

            context['live_lessons'] = live_lessons
            context['scheduled_live_lessons'] = scheduled_live_lessons
            context['subscription_scheduled_lessons'] = card_subscription_lessons  # للبطاقة الرئيسية
            context['sidebar_subscription_lessons'] = sidebar_subscription_lessons  # للقائمة الجانبية

        elif user.is_student():
            # الحصص المباشرة الجارية للطالب
            live_lessons = LiveLesson.objects.filter(
                student=user,
                status='live'
            ).select_related('teacher').order_by('-started_at')

            # الحصص المجدولة للطالب (تشمل التي حان موعدها ولم تبدأ بعد)
            now = timezone.now()
            scheduled_live_lessons = LiveLesson.objects.filter(
                student=user,
                status='scheduled',
                scheduled_date__gte=now - timezone.timedelta(hours=2)
            ).select_related('teacher').order_by('scheduled_date')[:3]

            # الحصص المجدولة من نظام الاشتراكات للطالب (حصة واحدة فقط قبل موعدها بـ 30 دقيقة)
            from subscriptions.models import ScheduledLesson
            subscription_scheduled_lessons = ScheduledLesson.objects.filter(
                subscription__student=user,
                status='scheduled',
                scheduled_date__gte=now,  # من الآن
                scheduled_date__lte=now + timezone.timedelta(minutes=30)  # خلال الـ 30 دقيقة القادمة فقط
            ).select_related('teacher', 'subscription__plan').order_by('scheduled_date')[:1]  # حصة واحدة فقط

            # إرسال إشعار للطالب عند ظهور الحصة
            if subscription_scheduled_lessons.exists():
                lesson = subscription_scheduled_lessons.first()
                _send_lesson_notification(user, lesson, 'student')

            context['live_lessons'] = live_lessons
            context['scheduled_live_lessons'] = scheduled_live_lessons
            context['subscription_scheduled_lessons'] = subscription_scheduled_lessons

    return context
