from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class Conversation(models.Model):
    """نموذج المحادثة بين المستخدمين"""

    # المشارك الأول في المحادثة
    participant1 = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='conversations_as_participant1',
        verbose_name=_('المشارك الأول'),
        null=True,
        blank=True
    )

    # المشارك الثاني في المحادثة
    participant2 = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='conversations_as_participant2',
        verbose_name=_('المشارك الثاني'),
        null=True,
        blank=True
    )

    # الحقول القديمة للتوافق
    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='student_conversations',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب'),
        null=True,
        blank=True
    )

    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='teacher_conversations',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم'),
        null=True,
        blank=True
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشطة')
    )

    class Meta:
        verbose_name = _('محادثة')
        verbose_name_plural = _('المحادثات')
        ordering = ['-updated_at']

    def __str__(self):
        p1, p2 = self.get_participants()
        if p1 and p2:
            return f"محادثة بين {p1.get_full_name()} و {p2.get_full_name()}"
        return f"محادثة #{self.id}"

    def get_participants(self):
        """الحصول على المشاركين في المحادثة"""
        # النظام الجديد
        if self.participant1 and self.participant2:
            return self.participant1, self.participant2
        # النظام القديم
        elif self.student and self.teacher:
            return self.student, self.teacher
        return None, None

    def get_last_message(self):
        """الحصول على آخر رسالة في المحادثة"""
        return self.chat_messages.order_by('-created_at').first()

    def get_unread_count_for_user(self, user):
        """عدد الرسائل غير المقروءة للمستخدم"""
        return self.chat_messages.filter(is_read=False).exclude(sender=user).count()

    def mark_as_read_for_user(self, user):
        """تمييز جميع الرسائل كمقروءة للمستخدم وإخفاء الإشعارات فوراً"""
        # تمييز الرسائل كمقروءة
        unread_messages = self.chat_messages.filter(is_read=False).exclude(sender=user)
        unread_messages.update(is_read=True)

        # إخفاء جميع الإشعارات المرتبطة بهذه المحادثة فوراً
        from notifications.models import Notification
        notifications_updated = Notification.objects.filter(
            recipient=user,
            notification_type='new_message',
            action_url=f'/messages/conversation/{self.id}/',
            is_read=False
        ).update(is_read=True)

        print(f"تم إخفاء {notifications_updated} إشعار للمستخدم {user.get_full_name()} في المحادثة {self.id}")

    def can_user_access(self, user):
        """التحقق من إمكانية وصول المستخدم للمحادثة"""
        p1, p2 = self.get_participants()
        return user == p1 or user == p2 or user.user_type == 'admin'

    def get_other_participant(self, user):
        """الحصول على المشارك الآخر في المحادثة"""
        p1, p2 = self.get_participants()
        if user == p1:
            return p2
        elif user == p2:
            return p1
        return None

    def get_unread_count_for_user(self, user):
        """الحصول على عدد الرسائل غير المقروءة للمستخدم"""
        return self.chat_messages.filter(
            is_read=False
        ).exclude(sender=user).count()


class ChatMessage(models.Model):
    """نموذج رسالة المحادثة"""

    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='chat_messages',
        verbose_name=_('المحادثة')
    )

    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_chat_messages',
        verbose_name=_('المرسل')
    )

    content = models.TextField(
        verbose_name=_('محتوى الرسالة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإرسال')
    )

    is_read = models.BooleanField(
        default=False,
        verbose_name=_('مقروءة')
    )

    attachment = models.FileField(
        upload_to='message_attachments/',
        blank=True,
        null=True,
        verbose_name=_('مرفق')
    )

    deleted_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الحذف')
    )

    deleted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='deleted_messages',
        verbose_name=_('محذوف بواسطة')
    )

    class Meta:
        verbose_name = _('رسالة')
        verbose_name_plural = _('الرسائل')
        ordering = ['created_at']

    def __str__(self):
        return f"رسالة من {self.sender.get_full_name()} في {self.created_at.strftime('%Y-%m-%d %H:%M')}"

    def save(self, *args, **kwargs):
        """حفظ الرسالة مع تحديث وقت المحادثة"""
        super().save(*args, **kwargs)

        # تحديث وقت المحادثة
        self.conversation.updated_at = timezone.now()
        self.conversation.save()

        # تم تعطيل الإشعار التلقائي نهائ<|im_start|> لمنع التكرار
        # سيتم إرسال الإشعار يدوياً من views.py فقط

    def send_notification(self):
        """إرسال إشعار للمستقبل"""
        from notifications.utils import NotificationService

        # تحديد المستقبل
        recipient = self.conversation.get_other_participant(self.sender)

        if recipient:
            # إرسال الإشعار
            NotificationService.notify_new_message(self, recipient)

    def get_recipient(self):
        """الحصول على مستقبل الرسالة"""
        return self.conversation.get_other_participant(self.sender)

    def get_created_at_display(self):
        """عرض وقت الإنشاء بصيغة مناسبة"""
        from django.utils import timezone
        from datetime import timedelta

        now = timezone.now()
        diff = now - self.created_at

        if diff < timedelta(minutes=1):
            return "الآن"
        elif diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"منذ {minutes} دقيقة"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"منذ {hours} ساعة"
        elif diff < timedelta(days=7):
            days = diff.days
            return f"منذ {days} يوم"
        else:
            return self.created_at.strftime("%Y/%m/%d %H:%M")

    def is_deleted(self):
        """التحقق من حذف الرسالة"""
        return self.deleted_at is not None

    def soft_delete(self, user):
        """حذف ناعم للرسالة"""
        self.deleted_at = timezone.now()
        self.deleted_by = user
        self.save()

    def can_delete(self, user):
        """التحقق من إمكانية حذف الرسالة"""
        # يمكن للمرسل حذف رسالته أو للمدير حذف أي رسالة
        return self.sender == user or user.user_type == 'admin'

    def get_display_content(self):
        """الحصول على محتوى الرسالة للعرض"""
        if self.is_deleted():
            return "تم حذف هذه الرسالة"
        return self.content
