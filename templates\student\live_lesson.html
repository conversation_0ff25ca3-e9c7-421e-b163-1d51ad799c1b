{% extends 'base.html' %}
{% load static %}

{% block title %}الحصة المباشرة - {{ live_lesson.title }} - طالب - قرآنيا{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Lesson Information Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 p-8">
            <div class="text-center mb-6">
                <h1 class="text-4xl font-bold text-islamic-dark mb-4">{{ live_lesson.title }}</h1>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-islamic-light bg-opacity-10 rounded-lg p-4">
                        <div class="text-sm text-gray-600 mb-1">المعلم</div>
                        <div class="font-semibold text-islamic-dark">{{ live_lesson.teacher.get_full_name }}</div>
                    </div>
                    <div class="bg-islamic-light bg-opacity-10 rounded-lg p-4">
                        <div class="text-sm text-gray-600 mb-1">المدة</div>
                        <div class="font-semibold text-islamic-dark">{{ live_lesson.duration_minutes }} دقيقة</div>
                    </div>
                    <div class="bg-islamic-light bg-opacity-10 rounded-lg p-4">
                        <div class="text-sm text-gray-600 mb-1">الموعد</div>
                        <div class="font-semibold text-islamic-dark">{{ live_lesson.scheduled_date|date:"Y/m/d H:i" }}</div>
                    </div>
                    <div class="bg-islamic-light bg-opacity-10 rounded-lg p-4">
                        <div class="text-sm text-gray-600 mb-1">الحالة</div>
                        <div class="font-semibold">
                            {% if live_lesson.status == 'scheduled' %}
                                <span class="text-yellow-600">في الانتظار</span>
                            {% elif live_lesson.status == 'live' %}
                                <span class="text-green-600">مباشرة</span>
                            {% elif live_lesson.status == 'completed' %}
                                <span class="text-blue-600">مكتملة</span>
                            {% else %}
                                <span class="text-gray-600">{{ live_lesson.get_status_display }}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lesson Control Section -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 p-8">
            {% if live_lesson.status == 'scheduled' %}
                <!-- Waiting for Teacher -->
                <div class="text-center">
                    <i class="fas fa-clock text-6xl text-yellow-400 mb-6"></i>
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">في انتظار بدء الحصة</h2>
                    <p class="text-gray-600 text-lg mb-6">المعلم {{ live_lesson.teacher.get_full_name }} لم يبدأ الحصة بعد</p>
                    <p class="text-gray-500">سيتم تحديث الصفحة تلقائياً عند بدء الحصة</p>
                    
                    <div class="mt-8 bg-blue-50 rounded-lg p-4">
                        <h3 class="font-semibold text-blue-900 mb-2">نصائح للاستعداد:</h3>
                        <ul class="text-blue-800 text-sm space-y-1">
                            <li>• تأكد من اتصال الإنترنت</li>
                            <li>• جهز المصحف أو المواد المطلوبة</li>
                            <li>• اختر مكاناً هادئاً للدراسة</li>
                        </ul>
                    </div>
                </div>

            {% elif live_lesson.status == 'live' %}
                <!-- Live Lesson Controls -->
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-green-600 mb-4 flex items-center justify-center">
                        <i class="fas fa-circle text-red-500 ml-2 animate-pulse"></i>
                        الحصة مباشرة الآن
                    </h2>
                    
                    <!-- Countdown Timer -->
                    <div class="bg-gray-50 rounded-xl p-6 mb-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">الوقت المتبقي</h3>
                        <div id="countdown-display" class="text-4xl font-bold text-green-600 mb-4">{{ live_lesson.duration_minutes }}:00</div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="progress-bar" class="bg-green-600 h-2 rounded-full transition-all duration-1000" style="width: 100%"></div>
                        </div>
                    </div>

                    <!-- Join Lesson Button -->
                    <button id="join-lesson-btn" class="bg-blue-600 text-white px-8 py-4 rounded-xl text-xl font-semibold hover:bg-blue-700 transition-colors duration-200 flex items-center mx-auto mb-6">
                        <i class="fas fa-video ml-3"></i>
                        دخول الحصة
                    </button>
                </div>

                <!-- Action Buttons -->
                <div class="text-center">
                    <button id="rate-lesson-btn" class="bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200 flex items-center justify-center mx-auto disabled:bg-gray-400 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-star ml-2"></i>
                        تقييم الحصة
                    </button>
                </div>

            {% elif live_lesson.status == 'completed' %}
                <!-- Post Lesson Actions -->
                <div class="text-center">
                    <h2 class="text-2xl font-bold text-blue-600 mb-6 flex items-center justify-center">
                        <i class="fas fa-check-circle ml-2"></i>
                        تم إكمال الحصة
                    </h2>
                    
                    {% if not live_lesson.student_evaluation_submitted %}
                        <button id="rate-lesson-btn" class="bg-purple-600 text-white px-8 py-4 rounded-xl text-xl font-semibold hover:bg-purple-700 transition-colors duration-200 flex items-center mx-auto">
                            <i class="fas fa-star ml-3"></i>
                            تقييم الحصة
                        </button>
                        <p class="text-gray-600 mt-4">يرجى تقييم الحصة والمعلم</p>
                    {% else %}
                        <div class="bg-green-50 rounded-xl p-6">
                            <i class="fas fa-check-circle text-green-600 text-4xl mb-4"></i>
                            <p class="text-green-800 font-semibold text-lg">تم إرسال التقييم بنجاح ✓</p>
                        </div>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Back Button -->
        <div class="text-center">
            <a href="{% url 'student_dashboard' %}" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors duration-200 inline-flex items-center">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<!-- Student Rating Modal -->
<div id="studentRatingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[9999]" style="display: none;">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/5 shadow-lg rounded-md bg-white z-[10000] max-h-[90vh] overflow-y-auto">
        <div class="mt-3">
            <!-- Header -->
            <div class="flex items-center justify-between pb-4 border-b">
                <h3 class="text-xl font-bold text-islamic-primary">
                    <i class="fas fa-star ml-2"></i>
                    تقييم الحصة والمعلم
                </h3>
                <button type="button" onclick="closeStudentRatingModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Rating Form -->
            <form id="studentRatingForm" class="space-y-6 mt-6">
                {% csrf_token %}
                <input type="hidden" id="lesson-id" name="lesson_id" value="{{ live_lesson.id }}">

                <!-- Rating Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="rating-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">التقييم العام</label>
                        <div class="flex space-x-1 space-x-reverse" data-rating="overall_rating">
                            {% for i in "12345" %}
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-value="{{ forloop.counter }}">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="overall_rating" id="overall_rating">
                    </div>

                    <div class="rating-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">جودة التدريس</label>
                        <div class="flex space-x-1 space-x-reverse" data-rating="teaching_quality">
                            {% for i in "12345" %}
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-value="{{ forloop.counter }}">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="teaching_quality" id="teaching_quality">
                    </div>

                    <div class="rating-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">محتوى الحصة</label>
                        <div class="flex space-x-1 space-x-reverse" data-rating="lesson_content">
                            {% for i in "12345" %}
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-value="{{ forloop.counter }}">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="lesson_content" id="lesson_content">
                    </div>

                    <div class="rating-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">جودة التفاعل</label>
                        <div class="flex space-x-1 space-x-reverse" data-rating="interaction_quality">
                            {% for i in "12345" %}
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-value="{{ forloop.counter }}">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="interaction_quality" id="interaction_quality">
                    </div>

                    <div class="rating-group md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">الالتزام بالوقت</label>
                        <div class="flex space-x-1 space-x-reverse justify-center" data-rating="punctuality">
                            {% for i in "12345" %}
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-value="{{ forloop.counter }}">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="punctuality" id="punctuality">
                    </div>
                </div>

                <!-- Comment Field -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">تعليقك على الحصة (اختياري)</label>
                    <textarea name="student_comment" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary" placeholder="شاركنا رأيك في الحصة والمعلم..."></textarea>
                </div>

                <!-- Trial Lesson Specific Questions -->
                {% if live_lesson.lesson_type == 'trial' %}
                <div class="bg-yellow-50 rounded-lg p-4 space-y-4">
                    <h4 class="font-semibold text-gray-900">أسئلة خاصة بالحصة التجريبية:</h4>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">هل تنصح بهذا المعلم؟</label>
                        <div class="flex space-x-4 space-x-reverse">
                            <label class="flex items-center">
                                <input type="radio" name="would_recommend" value="true" class="ml-2">
                                <span>نعم</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="would_recommend" value="false" class="ml-2">
                                <span>لا</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">هل أنت مهتم بالاشتراك؟</label>
                        <div class="flex space-x-4 space-x-reverse">
                            <label class="flex items-center">
                                <input type="radio" name="interested_in_subscription" value="true" class="ml-2">
                                <span>نعم</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="interested_in_subscription" value="false" class="ml-2">
                                <span>لا</span>
                            </label>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4 space-x-reverse pt-4 border-t">
                    <button type="button" onclick="closeStudentRatingModal()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-islamic-primary text-white px-6 py-2 rounded-lg hover:bg-islamic-dark transition-colors duration-200">
                        إرسال التقييم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const lessonId = {{ live_lesson.id }};
    const duration = {{ live_lesson.duration_minutes }};
    let countdownInterval;
    let lessonEndTime;
    let totalDuration;

    // Initialize based on lesson status
    {% if live_lesson.status == 'live' %}
        startCountdown();
    {% elif live_lesson.status == 'scheduled' %}
        // Check lesson status every 30 seconds
        setInterval(checkLessonStatus, 30000);
    {% endif %}

    // Join Lesson Button
    const joinBtn = document.getElementById('join-lesson-btn');
    if (joinBtn) {
        joinBtn.onclick = function() {
            joinLessonExternal();
        };
    }

    // Rate Lesson Button
    const rateBtn = document.getElementById('rate-lesson-btn');
    if (rateBtn) {
        rateBtn.onclick = function() {
            if (rateBtn.disabled) {
                alert('يجب انتظار انتهاء الحصة لتقييمها');
                return;
            }
            openRatingModal();
        };
    }

    function joinLessonExternal() {
        const studentName = encodeURIComponent('{{ user.get_full_name }} (طالب)');
        const jitsiUrl = `https://meet.ffmuc.net/{{ live_lesson.jitsi_room_id }}#userInfo.displayName=${studentName}&config.startWithAudioMuted=true&config.startWithVideoMuted=false`;
        
        window.open(jitsiUrl, 'jitsi_lesson', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        
        alert('تم فتح نافذة الحصة. يرجى عدم إغلاق هذه الصفحة للمتابعة مع العداد التنازلي.');
    }

    function checkLessonStatus() {
        fetch(`/api/live-lessons/${lessonId}/status/`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'live') {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error checking lesson status:', error);
            });
    }

    function startCountdown() {
        {% if live_lesson.started_at %}
            const startTime = new Date('{{ live_lesson.started_at|date:"c" }}');
        {% else %}
            const startTime = new Date();
        {% endif %}
        
        lessonEndTime = new Date(startTime.getTime() + (duration * 60 * 1000));
        totalDuration = duration * 60 * 1000;
        
        countdownInterval = setInterval(updateCountdown, 1000);
        updateCountdown();
    }

    function updateCountdown() {
        const now = new Date();
        const timeLeft = lessonEndTime - now;
        
        if (timeLeft <= 0) {
            clearInterval(countdownInterval);
            document.getElementById('countdown-display').textContent = '00:00';
            document.getElementById('progress-bar').style.width = '0%';
            
            // Enable rating button
            const rateBtn = document.getElementById('rate-lesson-btn');
            if (rateBtn) {
                rateBtn.disabled = false;
                rateBtn.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                rateBtn.classList.add('bg-purple-600', 'hover:bg-purple-700');
            }
            
            alert('انتهت الحصة! يمكنك الآن تقييم الحصة والمعلم.');
            return;
        }
        
        // Update display
        const minutes = Math.floor(timeLeft / 60000);
        const seconds = Math.floor((timeLeft % 60000) / 1000);
        document.getElementById('countdown-display').textContent = 
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        // Update progress bar
        const elapsed = totalDuration - timeLeft;
        const progress = (elapsed / totalDuration) * 100;
        document.getElementById('progress-bar').style.width = `${100 - progress}%`;
    }

    function openRatingModal() {
        document.getElementById('studentRatingModal').style.display = 'block';
    }

    // Star rating functionality
    document.querySelectorAll('.star-btn').forEach(star => {
        star.addEventListener('click', function() {
            const ratingGroup = this.closest('[data-rating]');
            const ratingName = ratingGroup.getAttribute('data-rating');
            const value = parseInt(this.getAttribute('data-value'));
            
            // Update hidden input
            document.getElementById(ratingName).value = value;
            
            // Update star display
            const stars = ratingGroup.querySelectorAll('.star-btn');
            stars.forEach((s, index) => {
                if (index < value) {
                    s.classList.remove('text-gray-300');
                    s.classList.add('text-yellow-400');
                } else {
                    s.classList.remove('text-yellow-400');
                    s.classList.add('text-gray-300');
                }
            });
        });
    });

    // Rating form submission
    document.getElementById('studentRatingForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        fetch(`/api/live-lessons/${lessonId}/student-rating/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال التقييم بنجاح!');
                closeStudentRatingModal();
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في إرسال التقييم');
        });
    });
});

function closeStudentRatingModal() {
    document.getElementById('studentRatingModal').style.display = 'none';
}
</script>
{% endblock %}
