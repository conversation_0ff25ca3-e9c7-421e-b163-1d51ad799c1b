# Generated by Django 4.2.7 on 2025-06-01 18:13

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BulkMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('announcement', 'إعلان عام'), ('update', 'تحديث النظام'), ('maintenance', 'إشعار صيانة'), ('policy', 'تحديث السياسات'), ('training', 'دورة تدريبية'), ('event', 'فعالية أو مناسبة'), ('urgent', 'إشعار عاجل'), ('welcome', 'رسالة ترحيب'), ('reminder', 'تذكير'), ('congratulations', 'تهنئة'), ('newsletter', 'نشرة إخبارية'), ('survey', 'استطلاع رأي')], default='announcement', max_length=20, verbose_name='نوع الرسالة')),
                ('recipient_type', models.CharField(choices=[('all', 'جميع المستخدمين'), ('teachers', 'المعلمين فقط'), ('students', 'الطلاب فقط'), ('active_users', 'المستخدمين النشطين'), ('new_users', 'المستخدمين الجدد'), ('custom', 'مستخدمين محددين')], default='all', max_length=20, verbose_name='نوع المستقبلين')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('urgent', 'عاجل')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('send_email', models.BooleanField(default=False, verbose_name='إرسال إيميل أيضاً')),
                ('schedule_send', models.DateTimeField(blank=True, null=True, verbose_name='جدولة الإرسال')),
                ('is_sent', models.BooleanField(default=False, verbose_name='تم الإرسال')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الإرسال')),
                ('recipients_count', models.PositiveIntegerField(default=0, verbose_name='عدد المستقبلين')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'رسالة جماعية',
                'verbose_name_plural': 'الرسائل الجماعية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('private', 'رسالة خاصة'), ('support', 'رسالة دعم'), ('announcement', 'إعلان عام'), ('update', 'تحديث النظام'), ('maintenance', 'إشعار صيانة'), ('policy', 'تحديث السياسات'), ('training', 'دورة تدريبية'), ('event', 'فعالية أو مناسبة'), ('urgent', 'إشعار عاجل'), ('welcome', 'رسالة ترحيب'), ('reminder', 'تذكير'), ('congratulations', 'تهنئة')], default='private', max_length=15, verbose_name='نوع الرسالة')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
            ],
            options={
                'verbose_name': 'رسالة',
                'verbose_name_plural': 'الرسائل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('lesson_reminder', 'تذكير بالحصة'), ('lesson_cancelled', 'إلغاء الحصة'), ('lesson_rescheduled', 'إعادة جدولة الحصة'), ('lesson_completed', 'اكتمال حصة'), ('lesson_created', 'إنشاء حصة جديدة'), ('lesson_starting_soon', 'الحصة تبدأ قريباً'), ('payment_received', 'استلام دفعة'), ('payment_confirmed', 'تأكيد دفعة'), ('payment_reminder', 'تذكير بالدفع'), ('payment_failed', 'فشل في الدفع'), ('payment_status_changed', 'تغيير حالة الدفع'), ('subscription_created', 'إنشاء اشتراك جديد'), ('subscription_payment_received', 'استلام دفعة اشتراك'), ('subscription_activated', 'تفعيل اشتراك'), ('subscription_cancelled', 'إلغاء اشتراك'), ('subscription_expiring_soon', 'اشتراك على وشك الانتهاء'), ('subscription_lessons_exhausted', 'انتهاء حصص الاشتراك'), ('subscription_needs_approval', 'اشتراك يحتاج موافقة'), ('new_enrollment', 'تسجيل جديد'), ('enrollment_activated', 'تفعيل التسجيل'), ('course_completed', 'اكتمال الدورة'), ('rating_received', 'استلام تقييم'), ('rating_request', 'طلب تقييم'), ('lesson_report', 'تقرير حصة'), ('new_student_assigned', 'تعيين طالب جديد'), ('teacher_earnings_updated', 'تحديث أرباح المعلم'), ('teacher_rate_changed', 'تغيير أسعار المعلم'), ('new_message', 'رسالة جديدة'), ('support_ticket', 'تذكرة دعم'), ('support_ticket_updated', 'تحديث تذكرة دعم'), ('system_announcement', 'إعلان النظام'), ('admin_new_user', 'مستخدم جديد'), ('admin_payment_pending', 'دفعة في الانتظار'), ('admin_low_rating', 'تقييم منخفض')], max_length=30, verbose_name='نوع الإشعار')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('urgent', 'عاجل')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت القراءة')),
                ('action_url', models.URLField(blank=True, null=True, verbose_name='رابط الإجراء')),
                ('action_text', models.CharField(blank=True, max_length=100, null=True, verbose_name='نص الإجراء')),
                ('email_sent', models.BooleanField(default=False, verbose_name='تم إرسال إيميل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
            },
        ),
    ]
