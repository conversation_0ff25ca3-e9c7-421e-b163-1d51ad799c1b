from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Count
from django.utils import timezone
from django.core.paginator import Paginator
from django.contrib.auth import get_user_model

from .models import SupportTicket, SupportTicketResponse, SystemMessage, SystemMessageRecipient

User = get_user_model()


@login_required
def support_dashboard(request):
    """لوحة تحكم الدعم الفني للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # إحصائيات التذاكر
    tickets_stats = {
        'total': SupportTicket.objects.count(),
        'open': SupportTicket.objects.filter(status='open').count(),
        'in_progress': SupportTicket.objects.filter(status='in_progress').count(),
        'waiting_response': SupportTicket.objects.filter(status='waiting_response').count(),
        'resolved': SupportTicket.objects.filter(status='resolved').count(),
        'closed': SupportTicket.objects.filter(status='closed').count(),
    }

    # التذاكر الحديثة
    recent_tickets = SupportTicket.objects.filter(
        is_read_by_admin=False
    ).order_by('-created_at')[:10]

    # التذاكر العاجلة
    urgent_tickets = SupportTicket.objects.filter(
        priority='urgent',
        status__in=['open', 'in_progress']
    ).order_by('-created_at')[:5]

    # إحصائيات المستخدمين
    user_stats = {
        'students': User.objects.filter(user_type='student').count(),
        'teachers': User.objects.filter(user_type='teacher').count(),
        'active_tickets_by_students': SupportTicket.objects.filter(
            created_by__user_type='student',
            status__in=['open', 'in_progress']
        ).count(),
        'active_tickets_by_teachers': SupportTicket.objects.filter(
            created_by__user_type='teacher',
            status__in=['open', 'in_progress']
        ).count(),
    }

    context = {
        'tickets_stats': tickets_stats,
        'recent_tickets': recent_tickets,
        'urgent_tickets': urgent_tickets,
        'user_stats': user_stats,
    }

    return render(request, 'support/admin_dashboard.html', context)


@login_required
def ticket_list(request):
    """قائمة تذاكر الدعم"""
    if request.user.is_admin():
        # المدير يرى جميع التذاكر
        tickets = SupportTicket.objects.all()
    else:
        # المستخدمون يرون تذاكرهم فقط
        tickets = SupportTicket.objects.filter(created_by=request.user)

    # فلترة حسب الحالة
    status_filter = request.GET.get('status')
    if status_filter:
        tickets = tickets.filter(status=status_filter)

    # فلترة حسب الأولوية
    priority_filter = request.GET.get('priority')
    if priority_filter:
        tickets = tickets.filter(priority=priority_filter)

    # فلترة حسب الفئة
    category_filter = request.GET.get('category')
    if category_filter:
        tickets = tickets.filter(category=category_filter)

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        tickets = tickets.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(ticket_number__icontains=search_query)
        )

    # ترتيب
    tickets = tickets.order_by('-created_at')

    # تقسيم الصفحات
    paginator = Paginator(tickets, 10)
    page_number = request.GET.get('page')
    tickets = paginator.get_page(page_number)

    context = {
        'tickets': tickets,
        'status_filter': status_filter,
        'priority_filter': priority_filter,
        'category_filter': category_filter,
        'search_query': search_query,
    }

    return render(request, 'support/ticket_list.html', context)


@login_required
def create_ticket(request):
    """إنشاء تذكرة دعم جديدة"""
    if request.method == 'POST':
        title = request.POST.get('title')
        description = request.POST.get('description')
        category = request.POST.get('category', 'general')
        priority = request.POST.get('priority', 'medium')

        if title and description:
            ticket = SupportTicket.objects.create(
                title=title,
                description=description,
                category=category,
                priority=priority,
                created_by=request.user
            )

            # إرسال إشعار للمدير
            from notifications.utils import NotificationService
            NotificationService.notify_new_support_ticket(ticket)

            messages.success(request, f"تم إنشاء تذكرة الدعم #{ticket.ticket_number} بنجاح.")
            return redirect('ticket_detail', ticket_id=ticket.id)
        else:
            messages.error(request, "يرجى ملء جميع الحقول المطلوبة.")

    return render(request, 'support/create_ticket.html')


@login_required
def ticket_detail(request, ticket_id):
    """تفاصيل تذكرة الدعم"""
    ticket = get_object_or_404(SupportTicket, id=ticket_id)

    # التحقق من الصلاحيات
    if not request.user.is_admin() and ticket.created_by != request.user:
        messages.error(request, "ليس لديك صلاحية لعرض هذه التذكرة.")
        return redirect('ticket_list')

    # تحديث حالة القراءة
    if request.user.is_admin():
        ticket.is_read_by_admin = True
        ticket.save()
    else:
        ticket.is_read_by_user = True
        ticket.save()

    # معالجة إضافة رد
    if request.method == 'POST':
        message = request.POST.get('message')
        if message:
            # التحقق من إمكانية إضافة رد
            if not ticket.can_add_response():
                messages.error(request, "لا يمكن إضافة ردود على هذه التذكرة.")
                return redirect('ticket_detail', ticket_id=ticket.id)

            response = SupportTicketResponse.objects.create(
                ticket=ticket,
                message=message,
                created_by=request.user
            )

            # إرسال إشعار
            from notifications.utils import NotificationService
            NotificationService.notify_support_response_added(response)

            messages.success(request, "تم إضافة ردك بنجاح.")
            return redirect('ticket_detail', ticket_id=ticket.id)

    # جلب الردود
    responses = ticket.responses.all().order_by('created_at')

    context = {
        'ticket': ticket,
        'responses': responses,
    }

    return render(request, 'support/ticket_detail.html', context)


@login_required
def update_ticket_status(request, ticket_id):
    """تحديث حالة التذكرة (للمدير فقط)"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'error': 'غير مصرح'})

    ticket = get_object_or_404(SupportTicket, id=ticket_id)

    if request.method == 'POST':
        new_status = request.POST.get('status')
        admin_notes = request.POST.get('admin_notes', '')

        # منع تغيير حالة التذاكر المغلقة والمحلولة
        if ticket.status in ['closed', 'resolved']:
            return JsonResponse({
                'success': False,
                'error': 'لا يمكن تغيير حالة التذاكر المغلقة أو المحلولة'
            })

        if new_status in dict(SupportTicket.STATUS_CHOICES):
            old_status = ticket.status
            ticket.status = new_status
            ticket.admin_notes = admin_notes
            ticket.assigned_to = request.user

            # إعادة تعيين حالة القراءة للمستخدم عند أي تغيير
            ticket.is_read_by_user = False
            ticket.is_read_by_admin = False

            ticket.save()

            # إرسال إشعار للمستخدم
            from notifications.utils import NotificationService
            NotificationService.notify_ticket_status_changed(ticket, old_status)



            return JsonResponse({
                'success': True,
                'message': f'تم تحديث حالة التذكرة إلى {ticket.get_status_display()}',
                'new_status': new_status,
                'ticket_id': ticket.id
            })

    return JsonResponse({'success': False, 'error': 'طلب غير صحيح'})


# تم حذف دالة send_support_message - استخدم send_system_message بدلاً منها


@login_required
def support_messages(request):
    """عرض رسائل الدعم للمستخدم - تم إعادة توجيهها لرسائل النظام"""
    # إعادة توجيه لرسائل النظام الجديدة
    return redirect('system_messages')


# ==================== نظام رسائل النظام ====================

@login_required
def send_system_message(request):
    """إرسال رسالة نظام من المدير للمعلمين والطلاب"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    if request.method == 'POST':
        title = request.POST.get('title')
        content = request.POST.get('content')
        message_type = request.POST.get('message_type', 'announcement')
        priority = request.POST.get('priority', 'medium')
        recipient_type = request.POST.get('recipient_type', 'all')
        expires_at = request.POST.get('expires_at')

        if title and content:
            # إنشاء رسالة النظام
            system_message = SystemMessage.objects.create(
                title=title,
                content=content,
                message_type=message_type,
                priority=priority,
                sent_by=request.user,
                expires_at=expires_at if expires_at else None
            )

            # تحديد المستقبلين
            recipients = []
            if recipient_type == 'all':
                recipients = User.objects.filter(
                    user_type__in=['student', 'teacher'],
                    is_active=True
                )
            elif recipient_type == 'students':
                recipients = User.objects.filter(
                    user_type='student',
                    is_active=True
                )
            elif recipient_type == 'teachers':
                recipients = User.objects.filter(
                    user_type='teacher',
                    is_active=True
                )

            # إضافة المستقبلين مع إنشاء سجلات تتبع القراءة
            for recipient in recipients:
                SystemMessageRecipient.objects.create(
                    system_message=system_message,
                    recipient=recipient
                )

            # إرسال إشعارات
            from notifications.utils import NotificationService
            for recipient in recipients:
                NotificationService.notify_system_message_received(system_message, recipient)

            messages.success(request, f"تم إرسال رسالة النظام إلى {recipients.count()} مستخدم بنجاح.")
            return redirect('admin_support')
        else:
            messages.error(request, "يرجى ملء جميع الحقول المطلوبة.")

    # جلب إحصائيات المستخدمين
    students_count = User.objects.filter(user_type='student', is_active=True).count()
    teachers_count = User.objects.filter(user_type='teacher', is_active=True).count()

    context = {
        'students_count': students_count,
        'teachers_count': teachers_count,
        'message_types': SystemMessage.MESSAGE_TYPES,
        'priority_levels': SystemMessage.PRIORITY_LEVELS,
    }

    return render(request, 'support/send_system_message.html', context)


@login_required
def system_messages(request):
    """عرض رسائل النظام للمعلمين والطلاب"""
    # التأكد من أن المستخدم ليس مدير
    if request.user.is_admin():
        messages.error(request, "هذه الصفحة مخصصة للمعلمين والطلاب فقط.")
        return redirect('admin_support')

    # جلب رسائل النظام للمستخدم الحالي
    message_receipts = SystemMessageRecipient.objects.filter(
        recipient=request.user,
        system_message__is_active=True
    ).select_related('system_message').order_by('-system_message__created_at')

    # فلترة حسب النوع إذا تم تحديده
    message_type = request.GET.get('type')
    if message_type:
        message_receipts = message_receipts.filter(
            system_message__message_type=message_type
        )

    # فلترة حسب حالة القراءة
    read_status = request.GET.get('read')
    if read_status == 'unread':
        message_receipts = message_receipts.filter(is_read=False)
    elif read_status == 'read':
        message_receipts = message_receipts.filter(is_read=True)

    # تقسيم الصفحات
    paginator = Paginator(message_receipts, 10)
    page_number = request.GET.get('page')
    message_receipts = paginator.get_page(page_number)

    # إحصائيات
    total_messages = SystemMessageRecipient.objects.filter(
        recipient=request.user,
        system_message__is_active=True
    ).count()

    unread_messages = SystemMessageRecipient.objects.filter(
        recipient=request.user,
        system_message__is_active=True,
        is_read=False
    ).count()

    read_messages = total_messages - unread_messages

    context = {
        'message_receipts': message_receipts,
        'total_messages': total_messages,
        'unread_messages': unread_messages,
        'read_messages': read_messages,
        'message_types': SystemMessage.MESSAGE_TYPES,
        'current_type': message_type,
        'current_read_status': read_status,
    }

    return render(request, 'support/system_messages.html', context)


@login_required
def system_message_detail(request, message_id):
    """عرض تفاصيل رسالة النظام وتمييزها كمقروءة"""
    # التأكد من أن المستخدم ليس مدير
    if request.user.is_admin():
        messages.error(request, "هذه الصفحة مخصصة للمعلمين والطلاب فقط.")
        return redirect('admin_support')

    # جلب سجل استلام الرسالة
    try:
        message_receipt = SystemMessageRecipient.objects.select_related(
            'system_message'
        ).get(
            system_message_id=message_id,
            recipient=request.user
        )
    except SystemMessageRecipient.DoesNotExist:
        messages.error(request, "الرسالة غير موجودة أو ليس لديك صلاحية لعرضها.")
        return redirect('system_messages')

    # تمييز الرسالة كمقروءة
    message_receipt.mark_as_read()

    context = {
        'message_receipt': message_receipt,
        'system_message': message_receipt.system_message,
    }

    return render(request, 'support/system_message_detail.html', context)


@login_required
def mark_system_message_read(request, message_id):
    """تمييز رسالة النظام كمقروءة عبر AJAX"""
    if request.method == 'POST':
        try:
            message_receipt = SystemMessageRecipient.objects.get(
                system_message_id=message_id,
                recipient=request.user
            )
            message_receipt.mark_as_read()
            return JsonResponse({'success': True})
        except SystemMessageRecipient.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'الرسالة غير موجودة'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مسموحة'})


@login_required
def close_ticket(request, ticket_id):
    """إغلاق تذكرة (للمستخدم أو المدير)"""
    ticket = get_object_or_404(SupportTicket, id=ticket_id)

    # التحقق من الصلاحيات
    if not request.user.is_admin() and ticket.created_by != request.user:
        return JsonResponse({'success': False, 'error': 'غير مصرح'})

    # التحقق من إمكانية الإغلاق
    if request.user.is_admin() or ticket.can_be_closed_by_user():
        ticket.status = 'closed'
        ticket.closed_at = timezone.now()
        ticket.save()

        return JsonResponse({
            'success': True,
            'message': 'تم إغلاق التذكرة بنجاح'
        })

    return JsonResponse({
        'success': False,
        'error': 'لا يمكن إغلاق هذه التذكرة في الوقت الحالي'
    })
