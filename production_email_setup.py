#!/usr/bin/env python3
"""
🚀 إعداد نظام البريد الإلكتروني لبيئة الإنتاج
تشغيل: python production_email_setup.py
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania_lms.settings')
django.setup()

def setup_production_email():
    """إعداد نظام البريد الإلكتروني للإنتاج"""
    
    print("🚀 إعداد نظام البريد الإلكتروني لبيئة الإنتاج")
    print("="*60)
    
    # 1. فحص إعدادات SMTP
    print("📧 1. فحص إعدادات SMTP...")
    
    from users.email_models import EmailSettings
    
    email_settings = EmailSettings.objects.first()
    if not email_settings:
        print("❌ لا توجد إعدادات SMTP! يرجى إعدادها من لوحة التحكم")
        return False
    
    if not email_settings.is_active:
        print("❌ إعدادات SMTP غير نشطة!")
        return False
    
    if not email_settings.smtp_password:
        print("❌ كلمة مرور SMTP غير محددة!")
        return False
    
    print(f"✅ إعدادات SMTP جاهزة: {email_settings.smtp_host}:{email_settings.smtp_port}")
    
    # 2. اختبار الاتصال
    print("\n🔌 2. اختبار الاتصال بخادم SMTP...")
    
    try:
        import smtplib
        server = smtplib.SMTP(email_settings.smtp_host, email_settings.smtp_port)
        if email_settings.use_tls:
            server.starttls()
        server.quit()
        print("✅ الاتصال بخادم SMTP نجح")
    except Exception as e:
        print(f"❌ فشل الاتصال: {str(e)}")
        return False
    
    # 3. إنشاء ملف متغيرات البيئة
    print("\n📝 3. إنشاء ملف متغيرات البيئة...")
    
    env_content = f"""# متغيرات البيئة لنظام البريد الإلكتروني
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST={email_settings.smtp_host}
EMAIL_PORT={email_settings.smtp_port}
EMAIL_USE_TLS={'True' if email_settings.use_tls else 'False'}
EMAIL_USE_SSL={'True' if email_settings.use_ssl else 'False'}
EMAIL_HOST_USER={email_settings.smtp_username}
EMAIL_HOST_PASSWORD={email_settings.smtp_password}
DEFAULT_FROM_EMAIL={email_settings.from_email}
"""
    
    with open('.env.email', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ تم إنشاء ملف .env.email")
    
    # 4. إنشاء ملف Cron Jobs
    print("\n⏰ 4. إنشاء ملف Cron Jobs...")
    
    cron_content = """# Cron Jobs لنظام البريد الإلكتروني
# تشغيل كل دقيقة - معالجة طابور البريد
* * * * * cd /path/to/your/project && python manage.py process_scheduled_emails >> /var/log/email_cron.log 2>&1

# تشغيل يومياً الساعة 6:00 صباحاً - التقارير الإدارية
0 6 * * * cd /path/to/your/project && python manage.py send_admin_reports >> /var/log/admin_reports.log 2>&1

# تنظيف السجلات أسبوعياً
0 2 * * 0 cd /path/to/your/project && python manage.py cleanup_email_logs --days=30 >> /var/log/cleanup.log 2>&1
"""
    
    with open('email_crontab.txt', 'w', encoding='utf-8') as f:
        f.write(cron_content)
    
    print("✅ تم إنشاء ملف email_crontab.txt")
    
    # 5. اختبار إرسال رسالة
    print("\n📧 5. اختبار إرسال رسالة...")
    
    from users.email_service import EmailService
    from users.email_models import EmailTemplate
    from django.contrib.auth import get_user_model
    
    User = get_user_model()
    email_service = EmailService()
    
    # البحث عن مستخدم للاختبار
    test_user = User.objects.filter(is_superuser=True).first()
    if not test_user:
        test_user = User.objects.first()
    
    if test_user:
        welcome_template = EmailTemplate.objects.filter(
            template_type='welcome',
            is_active=True
        ).first()
        
        if welcome_template:
            context = {
                'user_name': test_user.get_full_name() or test_user.username,
                'role': 'مدير النظام',
            }
            
            success = email_service.send_email(
                recipient=test_user,
                template=welcome_template,
                context=context,
                immediate=True
            )
            
            if success:
                print(f"✅ تم إرسال رسالة اختبار إلى: {test_user.email}")
            else:
                print("❌ فشل في إرسال رسالة الاختبار")
        else:
            print("⚠️ لا يوجد قالب ترحيب للاختبار")
    else:
        print("⚠️ لا يوجد مستخدمين للاختبار")
    
    # 6. عرض الإحصائيات
    print("\n📊 6. إحصائيات النظام:")
    
    from users.email_models import EmailQueue, EmailLog
    
    queue_count = EmailQueue.objects.count()
    log_count = EmailLog.objects.count()
    sent_count = EmailLog.objects.filter(status='sent').count()
    
    print(f"   📬 رسائل في الطابور: {queue_count}")
    print(f"   📋 إجمالي السجلات: {log_count}")
    print(f"   ✅ رسائل مرسلة: {sent_count}")
    
    if log_count > 0:
        success_rate = (sent_count / log_count) * 100
        print(f"   📈 معدل النجاح: {success_rate:.1f}%")
    
    print("\n🎉 تم إعداد النظام بنجاح!")
    print("="*60)
    
    return True

def show_production_instructions():
    """عرض تعليمات التشغيل في الإنتاج"""
    
    print("\n📋 تعليمات التشغيل في بيئة الإنتاج:")
    print("="*60)
    
    instructions = [
        "1. نسخ ملف .env.email إلى خادم الإنتاج",
        "2. تحديث مسارات المشروع في email_crontab.txt",
        "3. إضافة Cron Jobs: crontab email_crontab.txt",
        "4. تحديث settings.py لقراءة متغيرات البيئة",
        "5. إعادة تشغيل الخادم",
        "6. مراقبة ملفات السجلات"
    ]
    
    for instruction in instructions:
        print(f"   📌 {instruction}")
    
    print(f"\n🔍 ملفات مهمة للمراقبة:")
    print("   📄 /var/log/email_cron.log - سجل معالجة البريد")
    print("   📄 /var/log/admin_reports.log - سجل التقارير الإدارية")
    print("   📄 /var/log/cleanup.log - سجل تنظيف البيانات")
    
    print(f"\n🛠️ أوامر مفيدة للمراقبة:")
    print("   🔧 python manage.py process_scheduled_emails --dry-run")
    print("   🔧 python manage.py send_admin_reports --dry-run")
    print("   🔧 python manage.py cleanup_email_logs --days=30 --dry-run")

if __name__ == "__main__":
    try:
        success = setup_production_email()
        if success:
            show_production_instructions()
        else:
            print("\n❌ فشل في إعداد النظام!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ: {str(e)}")
        sys.exit(1)
