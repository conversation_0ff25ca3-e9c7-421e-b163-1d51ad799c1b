{% extends 'base.html' %}
{% load static %}

{% block title %}الحصة المباشرة - {{ live_lesson.title }} - معلم - قرآنيا{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Lesson Information Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 p-8">
            <div class="text-center mb-6">
                <h1 class="text-4xl font-bold text-islamic-dark mb-4">{{ live_lesson.title }}</h1>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-islamic-light bg-opacity-10 rounded-lg p-4">
                        <div class="text-sm text-gray-600 mb-1">الطالب</div>
                        <div class="font-semibold text-islamic-dark">{{ live_lesson.student.get_full_name }}</div>
                    </div>
                    <div class="bg-islamic-light bg-opacity-10 rounded-lg p-4">
                        <div class="text-sm text-gray-600 mb-1">المدة</div>
                        <div class="font-semibold text-islamic-dark">{{ live_lesson.duration_minutes }} دقيقة</div>
                    </div>
                    <div class="bg-islamic-light bg-opacity-10 rounded-lg p-4">
                        <div class="text-sm text-gray-600 mb-1">الموعد</div>
                        <div class="font-semibold text-islamic-dark">{{ live_lesson.scheduled_date|date:"Y/m/d H:i" }}</div>
                    </div>
                    <div class="bg-islamic-light bg-opacity-10 rounded-lg p-4">
                        <div class="text-sm text-gray-600 mb-1">الحالة</div>
                        <div class="font-semibold">
                            {% if live_lesson.status == 'scheduled' %}
                                <span class="text-yellow-600">مجدولة</span>
                            {% elif live_lesson.status == 'live' %}
                                <span class="text-green-600">مباشرة</span>
                            {% elif live_lesson.status == 'completed' %}
                                <span class="text-blue-600">مكتملة</span>
                            {% else %}
                                <span class="text-gray-600">{{ live_lesson.get_status_display }}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lesson Control Section -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 p-8">
            {% if live_lesson.status == 'scheduled' %}
                <!-- Start Lesson Button -->
                <div class="text-center">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">بدء الحصة</h2>
                    <button id="start-lesson-btn" class="bg-green-600 text-white px-8 py-4 rounded-xl text-xl font-semibold hover:bg-green-700 transition-colors duration-200 flex items-center mx-auto">
                        <i class="fas fa-play ml-3"></i>
                        بدء الحصة
                    </button>
                    <p class="text-gray-600 mt-4">اضغط لبدء الحصة وفتح نافذة الفيديو</p>
                </div>

            {% elif live_lesson.status == 'live' %}
                <!-- Live Lesson Controls -->
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-green-600 mb-4 flex items-center justify-center">
                        <i class="fas fa-circle text-red-500 ml-2 animate-pulse"></i>
                        الحصة مباشرة الآن
                    </h2>
                    
                    <!-- Countdown Timer -->
                    <div class="bg-gray-50 rounded-xl p-6 mb-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">الوقت المتبقي</h3>
                        <div id="countdown-display" class="text-4xl font-bold text-green-600 mb-4">{{ live_lesson.duration_minutes }}:00</div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="progress-bar" class="bg-green-600 h-2 rounded-full transition-all duration-1000" style="width: 100%"></div>
                        </div>
                    </div>

                    <!-- Open Jitsi Button -->
                    <button id="open-jitsi-btn" class="bg-blue-600 text-white px-8 py-4 rounded-xl text-xl font-semibold hover:bg-blue-700 transition-colors duration-200 flex items-center mx-auto mb-6">
                        <i class="fas fa-video ml-3"></i>
                        فتح نافذة الحصة
                    </button>
                </div>

                <!-- Action Buttons -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button id="write-report-btn" class="bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200 flex items-center justify-center disabled:bg-gray-400 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-clipboard-list ml-2"></i>
                        كتابة التقرير
                    </button>
                    <button id="end-lesson-btn" class="bg-red-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors duration-200 flex items-center justify-center">
                        <i class="fas fa-stop ml-2"></i>
                        إنهاء الحصة
                    </button>
                </div>

            {% elif live_lesson.status == 'completed' %}
                <!-- Post Lesson Actions -->
                <div class="text-center">
                    <h2 class="text-2xl font-bold text-blue-600 mb-6 flex items-center justify-center">
                        <i class="fas fa-check-circle ml-2"></i>
                        تم إكمال الحصة
                    </h2>
                    
                    {% if not live_lesson.teacher_report_submitted %}
                        <button id="write-report-btn" class="bg-purple-600 text-white px-8 py-4 rounded-xl text-xl font-semibold hover:bg-purple-700 transition-colors duration-200 flex items-center mx-auto">
                            <i class="fas fa-clipboard-list ml-3"></i>
                            كتابة التقرير
                        </button>
                        <p class="text-gray-600 mt-4">يرجى كتابة تقرير عن الحصة</p>
                    {% else %}
                        <div class="bg-green-50 rounded-xl p-6">
                            <i class="fas fa-check-circle text-green-600 text-4xl mb-4"></i>
                            <p class="text-green-800 font-semibold text-lg">تم إرسال التقرير بنجاح ✓</p>
                        </div>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Back Button -->
        <div class="text-center">
            <a href="{% url 'teacher_dashboard' %}" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors duration-200 inline-flex items-center">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<!-- Teacher Report Modal -->
<div id="teacherReportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[9999]" style="display: none;">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/5 shadow-lg rounded-md bg-white z-[10000] max-h-[90vh] overflow-y-auto">
        <div class="mt-3">
            <!-- Header -->
            <div class="flex items-center justify-between pb-4 border-b">
                <h3 class="text-xl font-bold text-islamic-primary">
                    <i class="fas fa-clipboard-list ml-2"></i>
                    تقرير الحصة
                </h3>
                <button type="button" onclick="closeTeacherReportModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Report Form -->
            <form id="teacherReportForm" class="space-y-6 mt-6">
                {% csrf_token %}
                <input type="hidden" id="lesson-id" name="lesson_id" value="{{ live_lesson.id }}">

                <!-- Rating Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="rating-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">أداء الطالب</label>
                        <div class="flex space-x-1 space-x-reverse" data-rating="student_performance">
                            {% for i in "12345" %}
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-value="{{ forloop.counter }}">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="student_performance" id="student_performance">
                    </div>

                    <div class="rating-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">مشاركة الطالب</label>
                        <div class="flex space-x-1 space-x-reverse" data-rating="student_participation">
                            {% for i in "12345" %}
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-value="{{ forloop.counter }}">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="student_participation" id="student_participation">
                    </div>

                    <div class="rating-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">فهم الطالب</label>
                        <div class="flex space-x-1 space-x-reverse" data-rating="student_understanding">
                            {% for i in "12345" %}
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-value="{{ forloop.counter }}">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="student_understanding" id="student_understanding">
                    </div>

                    <div class="rating-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">تقييم الحصة العام</label>
                        <div class="flex space-x-1 space-x-reverse" data-rating="overall_lesson_rating">
                            {% for i in "12345" %}
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors" data-value="{{ forloop.counter }}">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="overall_lesson_rating" id="overall_lesson_rating">
                    </div>
                </div>

                <!-- Text Fields -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">ملخص الحصة</label>
                        <textarea name="lesson_summary" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary" placeholder="اكتب ملخصاً عن محتوى الحصة..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نقاط القوة</label>
                        <textarea name="strengths" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary" placeholder="اذكر نقاط القوة لدى الطالب..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نقاط التحسين</label>
                        <textarea name="areas_for_improvement" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary" placeholder="اذكر النقاط التي تحتاج تحسين..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                        <textarea name="additional_notes" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4 space-x-reverse pt-4 border-t">
                    <button type="button" onclick="closeTeacherReportModal()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-islamic-primary text-white px-6 py-2 rounded-lg hover:bg-islamic-dark transition-colors duration-200">
                        إرسال التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const lessonId = {{ live_lesson.id }};
    const duration = {{ live_lesson.duration_minutes }};
    let countdownInterval;
    let lessonEndTime;
    let totalDuration;

    // Initialize based on lesson status
    {% if live_lesson.status == 'live' %}
        startCountdown();
    {% endif %}

    // Start Lesson Button
    const startBtn = document.getElementById('start-lesson-btn');
    if (startBtn) {
        startBtn.onclick = function() {
            startLesson();
        };
    }

    // Open Jitsi Button
    const jitsiBtn = document.getElementById('open-jitsi-btn');
    if (jitsiBtn) {
        jitsiBtn.onclick = function() {
            openJitsiExternal();
        };
    }

    // Write Report Button
    const reportBtn = document.getElementById('write-report-btn');
    if (reportBtn) {
        reportBtn.onclick = function() {
            if (reportBtn.disabled) {
                alert('يجب انتظار انتهاء الحصة لكتابة التقرير');
                return;
            }
            openReportModal();
        };
    }

    // End Lesson Button
    const endBtn = document.getElementById('end-lesson-btn');
    if (endBtn) {
        endBtn.onclick = function() {
            if (confirm('هل أنت متأكد من إنهاء الحصة؟')) {
                endLesson();
            }
        };
    }

    function startLesson() {
        fetch(`/api/live-lessons/${lessonId}/start/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في بدء الحصة');
        });
    }

    function openJitsiExternal() {
        const teacherName = encodeURIComponent('{{ user.get_full_name }} (معلم)');
        const jitsiUrl = `https://meet.ffmuc.net/{{ live_lesson.jitsi_room_id }}#userInfo.displayName=${teacherName}&config.startWithAudioMuted=false&config.startWithVideoMuted=false`;
        
        window.open(jitsiUrl, 'jitsi_lesson', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        
        alert('تم فتح نافذة الحصة. يرجى عدم إغلاق هذه الصفحة للمتابعة مع العداد التنازلي.');
    }

    function startCountdown() {
        {% if live_lesson.started_at %}
            const startTime = new Date('{{ live_lesson.started_at|date:"c" }}');
        {% else %}
            const startTime = new Date();
        {% endif %}
        
        lessonEndTime = new Date(startTime.getTime() + (duration * 60 * 1000));
        totalDuration = duration * 60 * 1000;
        
        countdownInterval = setInterval(updateCountdown, 1000);
        updateCountdown();
    }

    function updateCountdown() {
        const now = new Date();
        const timeLeft = lessonEndTime - now;
        
        if (timeLeft <= 0) {
            clearInterval(countdownInterval);
            document.getElementById('countdown-display').textContent = '00:00';
            document.getElementById('progress-bar').style.width = '0%';
            
            // Enable report button
            const reportBtn = document.getElementById('write-report-btn');
            if (reportBtn) {
                reportBtn.disabled = false;
                reportBtn.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                reportBtn.classList.add('bg-purple-600', 'hover:bg-purple-700');
            }
            
            alert('انتهت الحصة! يمكنك الآن كتابة التقرير.');
            notifyServerTimeUp();
            return;
        }
        
        // Update display
        const minutes = Math.floor(timeLeft / 60000);
        const seconds = Math.floor((timeLeft % 60000) / 1000);
        document.getElementById('countdown-display').textContent = 
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        // Update progress bar
        const elapsed = totalDuration - timeLeft;
        const progress = (elapsed / totalDuration) * 100;
        document.getElementById('progress-bar').style.width = `${100 - progress}%`;
    }

    function notifyServerTimeUp() {
        fetch(`/api/live-lessons/${lessonId}/time-up/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        });
    }

    function endLesson() {
        fetch(`/api/live-lessons/${lessonId}/end/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }

    function openReportModal() {
        document.getElementById('teacherReportModal').style.display = 'block';
    }

    // Star rating functionality
    document.querySelectorAll('.star-btn').forEach(star => {
        star.addEventListener('click', function() {
            const ratingGroup = this.closest('[data-rating]');
            const ratingName = ratingGroup.getAttribute('data-rating');
            const value = parseInt(this.getAttribute('data-value'));
            
            // Update hidden input
            document.getElementById(ratingName).value = value;
            
            // Update star display
            const stars = ratingGroup.querySelectorAll('.star-btn');
            stars.forEach((s, index) => {
                if (index < value) {
                    s.classList.remove('text-gray-300');
                    s.classList.add('text-yellow-400');
                } else {
                    s.classList.remove('text-yellow-400');
                    s.classList.add('text-gray-300');
                }
            });
        });
    });

    // Report form submission
    document.getElementById('teacherReportForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        fetch(`/api/live-lessons/${lessonId}/teacher-report/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال التقرير بنجاح!');
                closeTeacherReportModal();
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في إرسال التقرير');
        });
    });
});

function closeTeacherReportModal() {
    document.getElementById('teacherReportModal').style.display = 'none';
}
</script>
{% endblock %}
