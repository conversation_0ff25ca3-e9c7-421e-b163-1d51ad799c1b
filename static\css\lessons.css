/* ===== نظام الحصص الموحد - CSS ===== */

/* متغيرات الألوان */
:root {
    --islamic-primary: #059669;
    --islamic-secondary: #10b981;
    --islamic-gold: #d97706;
    --islamic-light: #ecfdf5;
    --trial-color: #f59e0b;
    --makeup-color: #ec4899;
    --completed-color: #10b981;
    --cancelled-color: #ef4444;
    --live-color: #22c55e;
}

/* تحسينات عامة */
.lesson-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e5e7eb;
    background: white;
    position: relative;
    overflow: hidden;
}

.lesson-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--islamic-primary);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.lesson-card:hover::before {
    transform: scaleY(1);
}

.lesson-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--islamic-primary);
}

/* أنواع الحصص */
.lesson-card[data-lesson-type="trial"] {
    border-left: 4px solid var(--trial-color);
}

.lesson-card[data-lesson-type="subscription"] {
    border-left: 4px solid var(--islamic-primary);
}

.lesson-card[data-lesson-type="makeup"] {
    border-left: 4px solid var(--makeup-color);
}

/* حالات الحصص */
.lesson-card[data-lesson-status="live"] {
    background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
    border-color: var(--live-color);
    animation: pulse-border 2s infinite;
}

.lesson-card[data-lesson-status="completed"] {
    background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
}

.lesson-card[data-lesson-status="cancelled_by_student"],
.lesson-card[data-lesson-status="cancelled_by_teacher"],
.lesson-card[data-lesson-status="cancelled_by_admin"] {
    background: linear-gradient(135deg, #fef2f2, #fef2f2);
    opacity: 0.8;
}

@keyframes pulse-border {
    0%, 100% {
        border-color: var(--live-color);
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
    }
    50% {
        border-color: #16a34a;
        box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
    }
}

/* أزرار الإجراءات */
.lesson-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lesson-actions button,
.lesson-actions a {
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    font-weight: 500;
}

.lesson-actions button:hover,
.lesson-actions a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* شارات الحالة */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-scheduled {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-live {
    background-color: #dcfce7;
    color: #166534;
    animation: pulse-glow 2s infinite;
}

.status-completed {
    background-color: #f0fdf4;
    color: #15803d;
}

.status-rated {
    background-color: #faf5ff;
    color: #7c3aed;
}

.status-cancelled {
    background-color: #fef2f2;
    color: #dc2626;
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stats-card:hover::before {
    transform: scaleX(1);
}

.stats-card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* نظام الفلترة */
.filter-section {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #f3f4f6;
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--islamic-primary);
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.form-control:hover {
    border-color: #9ca3af;
}

/* أزرار */
.btn-primary {
    background: linear-gradient(135deg, var(--islamic-primary), var(--islamic-secondary));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #047857, var(--islamic-primary));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563, #374151);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

/* القوائم المنسدلة */
.dropdown-menu {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 0.5rem 0;
    min-width: 12rem;
    z-index: 50;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    background: none;
    text-align: right;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: #f3f4f6;
    color: var(--islamic-primary);
}

/* التقويم */
.calendar-container {
    background: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.calendar-header {
    background: var(--islamic-primary);
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 600;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e5e7eb;
}

.calendar-day {
    background: white;
    padding: 0.75rem;
    min-height: 4rem;
    border: none;
    transition: all 0.2s ease;
    cursor: pointer;
}

.calendar-day:hover {
    background: var(--islamic-light);
}

.calendar-day.has-lesson {
    background: #dbeafe;
    position: relative;
}

.calendar-day.has-lesson::after {
    content: '';
    position: absolute;
    bottom: 0.25rem;
    right: 0.25rem;
    width: 0.5rem;
    height: 0.5rem;
    background: var(--islamic-primary);
    border-radius: 50%;
}

/* الحالة الفارغة */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* الإشعارات */
.notification {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 100;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 500;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    transform: translateX(-100%);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: var(--completed-color);
}

.notification.error {
    background: var(--cancelled-color);
}

.notification.info {
    background: #3b82f6;
}

/* التجاوب */
@media (max-width: 768px) {
    .lesson-card {
        padding: 1rem;
    }
    
    .stats-card {
        padding: 1rem;
    }
    
    .filter-section {
        padding: 1rem;
    }
    
    .btn-primary,
    .btn-secondary {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .lesson-actions {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .lesson-actions button,
    .lesson-actions a {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 640px) {
    .calendar-grid {
        grid-template-columns: repeat(7, 1fr);
        gap: 0;
    }
    
    .calendar-day {
        padding: 0.5rem;
        min-height: 3rem;
        font-size: 0.75rem;
    }
    
    .stats-card {
        text-align: center;
    }
    
    .form-control {
        padding: 0.5rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .lesson-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .btn-primary,
    .btn-secondary,
    .dropdown-menu {
        display: none;
    }
    
    .stats-card {
        box-shadow: none;
        border: 1px solid #000;
    }
}
