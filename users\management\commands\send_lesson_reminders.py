"""
أمر Django لإرسال تذكيرات الحصص
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from users.email_service import EmailService
from users.email_models import EmailTemplate
from subscriptions.models import ScheduledLesson
from lessons.models import Lesson
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'إرسال تذكيرات الحصص للمعلمين والطلاب'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reminder-type',
            type=str,
            choices=['24h', '30min', '5min'],
            default='24h',
            help='نوع التذكير (24h, 30min, 5min)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='تشغيل تجريبي بدون إرسال فعلي'
        )

    def handle(self, *args, **options):
        reminder_type = options['reminder_type']
        dry_run = options['dry_run']
        
        self.stdout.write(f"🔔 بدء إرسال تذكيرات الحصص ({reminder_type})")
        
        # تحديد الوقت المناسب للتذكير
        now = timezone.now()
        if reminder_type == '24h':
            target_time = now + timedelta(hours=24)
            time_range = timedelta(minutes=30)  # نافذة 30 دقيقة
        elif reminder_type == '30min':
            target_time = now + timedelta(minutes=30)
            time_range = timedelta(minutes=5)   # نافذة 5 دقائق
        elif reminder_type == '5min':
            target_time = now + timedelta(minutes=5)
            time_range = timedelta(minutes=2)   # نافذة دقيقتين
        
        start_time = target_time - time_range
        end_time = target_time + time_range
        
        # البحث عن الحصص المجدولة
        scheduled_lessons = ScheduledLesson.objects.filter(
            scheduled_date__gte=start_time,
            scheduled_date__lte=end_time,
            status='scheduled'
        ).select_related('subscription__student', 'teacher')
        
        # البحث عن الحصص الموحدة
        unified_lessons = Lesson.objects.filter(
            scheduled_date__gte=start_time,
            scheduled_date__lte=end_time,
            status='scheduled'
        ).select_related('student', 'teacher')
        
        # الحصول على قالب التذكير
        reminder_template = EmailTemplate.objects.filter(
            template_type='lesson_reminder',
            is_active=True
        ).first()
        
        if not reminder_template:
            self.stdout.write(
                self.style.ERROR('❌ لا يوجد قالب تذكير نشط')
            )
            return
        
        email_service = EmailService()
        sent_count = 0
        failed_count = 0
        
        # معالجة الحصص المجدولة
        for lesson in scheduled_lessons:
            sent_count += self._send_lesson_reminder(
                email_service, reminder_template, lesson, 
                'scheduled', reminder_type, dry_run
            )
        
        # معالجة الحصص الموحدة
        for lesson in unified_lessons:
            sent_count += self._send_lesson_reminder(
                email_service, reminder_template, lesson, 
                'unified', reminder_type, dry_run
            )
        
        # عرض النتائج
        total_lessons = len(scheduled_lessons) + len(unified_lessons)
        self.stdout.write(
            self.style.SUCCESS(
                f'✅ تم العثور على {total_lessons} حصة'
            )
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'🧪 تشغيل تجريبي: كان سيتم إرسال {sent_count * 2} تذكير'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'📧 تم إرسال {sent_count * 2} تذكير بنجاح'
                )
            )
            if failed_count > 0:
                self.stdout.write(
                    self.style.ERROR(
                        f'❌ فشل في إرسال {failed_count} تذكير'
                    )
                )

    def _send_lesson_reminder(self, email_service, template, lesson, lesson_type, reminder_type, dry_run):
        """إرسال تذكير لحصة واحدة"""
        try:
            # تحديد بيانات الحصة حسب النوع
            if lesson_type == 'scheduled':
                student = lesson.subscription.student
                teacher = lesson.teacher
                lesson_title = f"حصة رقم {lesson.lesson_number}"
                lesson_date = lesson.scheduled_date.strftime('%Y-%m-%d')
                lesson_time = lesson.scheduled_date.strftime('%H:%M')
                duration = f"{lesson.duration_minutes} دقيقة"
                lesson_id = lesson.id
                lesson_url = f"/subscriptions/join-lesson/{lesson.id}/"
            else:  # unified
                student = lesson.student
                teacher = lesson.teacher
                lesson_title = lesson.title
                lesson_date = lesson.scheduled_date.strftime('%Y-%m-%d')
                lesson_time = lesson.scheduled_date.strftime('%H:%M')
                duration = f"{lesson.duration_minutes} دقيقة"
                lesson_id = lesson.id
                lesson_url = f"/lessons/room/{lesson.id}/"
            
            # السياق المشترك
            base_context = {
                'lesson_title': lesson_title,
                'lesson_date': lesson_date,
                'lesson_time': lesson_time,
                'lesson_duration': duration,
                'lesson_id': lesson_id,
                'lesson_url': lesson_url,
                'reminder_type': reminder_type,
            }
            
            sent_count = 0
            
            # إرسال تذكير للطالب
            if student:
                student_context = base_context.copy()
                student_context.update({
                    'teacher_name': teacher.get_full_name() if teacher else 'غير محدد',
                    'recipient_type': 'student',
                })
                
                if not dry_run:
                    success = email_service.send_email(
                        recipient=student,
                        template=template,
                        context=student_context,
                        immediate=False
                    )
                    if success:
                        sent_count += 1
                else:
                    sent_count += 1
                    self.stdout.write(f"  📧 سيتم إرسال تذكير للطالب: {student.email}")
            
            # إرسال تذكير للمعلم
            if teacher:
                teacher_context = base_context.copy()
                teacher_context.update({
                    'student_name': student.get_full_name() if student else 'غير محدد',
                    'recipient_type': 'teacher',
                })
                
                if not dry_run:
                    success = email_service.send_email(
                        recipient=teacher,
                        template=template,
                        context=teacher_context,
                        immediate=False
                    )
                    if success:
                        sent_count += 1
                else:
                    sent_count += 1
                    self.stdout.write(f"  📧 سيتم إرسال تذكير للمعلم: {teacher.email}")
            
            return 1 if sent_count > 0 else 0
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في إرسال تذكير الحصة {lesson.id}: {str(e)}')
            )
            return 0
