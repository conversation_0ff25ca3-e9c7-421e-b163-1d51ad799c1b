/* Enhanced Styles for Qurania LMS */

/* ========================================
   🎨 متغيرات الألوان المحسنة
======================================== */
:root {
    --islamic-primary: #2D5016;
    --islamic-secondary: #4A7C59;
    --islamic-gold: #d4af37;
    --islamic-light-gold: #f4e4a6;
    --islamic-mint: #4ade80;
    --islamic-light: #ecfdf5;
    --islamic-dark: #1A3009;
    --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 15px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.2);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========================================
   🌟 Enhanced Cards
======================================== */
.enhanced-card {
    background: linear-gradient(135deg, var(--islamic-primary), var(--islamic-secondary));
    border-radius: 16px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.enhanced-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
}

.enhanced-card-header {
    padding: 2rem;
    background: linear-gradient(135deg, #2D5016, #1A3009);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
}

.enhanced-card-body {
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    color: #1f2937;
}

/* ========================================
   📊 Stats Cards
======================================== */
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition-smooth);
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--islamic-gold), var(--islamic-light-gold));
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--islamic-primary);
}

.stats-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* ========================================
   🎯 Quick Actions
======================================== */
.quick-action-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition-smooth);
    border: 2px solid transparent;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.quick-action-card:hover {
    transform: translateY(-4px);
    border-color: var(--islamic-primary);
    box-shadow: var(--shadow-medium);
    text-decoration: none;
    color: inherit;
}

.quick-action-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin: 0 auto 1rem;
}

.quick-action-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.quick-action-desc {
    font-size: 0.875rem;
    color: #6b7280;
}

/* ========================================
   🔔 Notifications
======================================== */
.notification-item {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid var(--islamic-gold);
    margin-bottom: 0.75rem;
    transition: var(--transition-smooth);
}

.notification-item:hover {
    transform: translateX(-4px);
    box-shadow: var(--shadow-light);
}

.notification-unread {
    background: #fef3c7;
    border-left-color: var(--islamic-gold);
}

.notification-read {
    background: #f9fafb;
    border-left-color: #d1d5db;
}

/* ========================================
   📱 Responsive Enhancements
======================================== */
@media (max-width: 768px) {
    .enhanced-card-header {
        padding: 1.5rem;
    }
    
    .enhanced-card-body {
        padding: 1.5rem;
    }
    
    .stats-card {
        padding: 1rem;
    }
    
    .stats-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
    
    .quick-action-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }
}

/* ========================================
   🎨 Animation Classes
======================================== */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ========================================
   🎯 Utility Classes
======================================== */
.text-islamic-primary { color: var(--islamic-primary); }
.text-islamic-gold { color: var(--islamic-gold); }
.text-islamic-mint { color: var(--islamic-mint); }
.bg-islamic-primary { background-color: var(--islamic-primary); }
.bg-islamic-gold { background-color: var(--islamic-gold); }
.bg-islamic-mint { background-color: var(--islamic-mint); }

.border-islamic-primary { border-color: var(--islamic-primary); }
.border-islamic-gold { border-color: var(--islamic-gold); }

.shadow-islamic { box-shadow: var(--shadow-medium); }
.transition-islamic { transition: var(--transition-smooth); }

/* ========================================
   🔧 Form Enhancements
======================================== */
.form-control-enhanced {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem;
    transition: var(--transition-smooth);
    font-size: 0.875rem;
}

.form-control-enhanced:focus {
    border-color: var(--islamic-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.btn-enhanced {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: var(--transition-smooth);
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary-enhanced {
    background: linear-gradient(135deg, var(--islamic-primary), var(--islamic-secondary));
    color: white;
}

.btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
    text-decoration: none;
}

.btn-gold-enhanced {
    background: linear-gradient(135deg, var(--islamic-gold), var(--islamic-light-gold));
    color: white;
}

.btn-gold-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
    text-decoration: none;
}
