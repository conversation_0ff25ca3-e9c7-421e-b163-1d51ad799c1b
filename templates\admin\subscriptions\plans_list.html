{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الباقات{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-4xl font-bold text-islamic-primary mb-2 flex items-center">
                    <i class="fas fa-box text-islamic-gold ml-3"></i>
                    إدارة الباقات
                </h1>
                <p class="text-gray-600">إدارة وتعديل جميع باقات الاشتراك</p>
            </div>
            <div class="flex space-x-4 space-x-reverse">
                <a href="{% url 'admin_subscriptions' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للوحة الرئيسية
                </a>
                <a href="{% url 'admin_plan_create' %}" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء باقة جديدة
                </a>
            </div>
        </div>

        <!-- Plans Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
            {% for plan in plans %}
            <div class="relative bg-white rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-all duration-300 hover:shadow-2xl {% if plan.is_featured %}border-3 border-islamic-gold ring-4 ring-islamic-gold ring-opacity-20{% endif %}">
                {% if plan.is_featured %}
                <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div class="bg-gradient-to-r from-islamic-gold to-yellow-500 text-white px-6 py-2 rounded-full shadow-lg">
                        <span class="font-bold text-sm flex items-center">
                            <i class="fas fa-crown ml-2"></i>
                            باقة مميزة
                        </span>
                    </div>
                </div>
                {% endif %}

                <!-- Plan Status Badge -->
                <div class="absolute top-4 right-4">
                    {% if plan.is_active %}
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                            <i class="fas fa-check-circle ml-1"></i>نشطة
                        </span>
                    {% else %}
                        <span class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                            <i class="fas fa-times-circle ml-1"></i>غير نشطة
                        </span>
                    {% endif %}
                </div>

                <!-- Plan Type Badge -->
                <div class="absolute top-4 left-4">
                    <span class="bg-islamic-primary text-white px-3 py-1 rounded-full text-xs font-semibold">
                        {{ plan.get_plan_type_display }}
                    </span>
                </div>

                <div class="p-8 {% if plan.is_featured %}pt-12{% endif %}">
                    <!-- Plan Header -->
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-box text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-islamic-primary mb-3">{{ plan.name }}</h3>

                        <!-- Price Display -->
                        <div class="mb-4">
                            {% if plan.discount_percentage > 0 %}
                                <div class="flex items-center justify-center space-x-2 space-x-reverse mb-2">
                                    <span class="text-lg line-through text-gray-400">{{ plan.get_formatted_price }}</span>
                                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full animate-pulse">
                                        خصم {{ plan.discount_percentage }}%
                                    </span>
                                </div>
                                <div class="text-4xl font-bold text-green-600 mb-1">
                                    {{ plan.get_formatted_discounted_price }}
                                </div>
                            {% else %}
                                <div class="text-4xl font-bold text-islamic-primary mb-1">
                                    {{ plan.get_formatted_price }}
                                </div>
                            {% endif %}
                            <div class="flex items-center justify-center">
                                <span class="text-gray-600 text-sm">{{ plan.get_currency_display }}</span>
                                <span class="text-gray-400 text-sm mx-2">/</span>
                                <span class="text-gray-600 text-sm">{{ plan.get_duration_type_display }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Stats -->
                    <div class="mb-8">
                        <div class="bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-6">
                            <h4 class="font-semibold text-gray-800 mb-4 text-center">إحصائيات الباقة</h4>
                            <div class="grid grid-cols-2 gap-6">
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-users text-blue-600"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-blue-600">{{ plan.subscriptions_count }}</div>
                                    <div class="text-xs text-gray-600">إجمالي الاشتراكات</div>
                                </div>
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-check-circle text-green-600"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-green-600">{{ plan.active_subscriptions_count }}</div>
                                    <div class="text-xs text-gray-600">اشتراكات نشطة</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Features -->
                    <div class="mb-8">
                        <div class="bg-gray-50 rounded-xl p-6">
                            <h4 class="font-semibold text-gray-800 mb-4 text-center">مميزات الباقة</h4>
                            <ul class="space-y-3">
                                <li class="flex items-center text-gray-700">
                                    <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-graduation-cap text-green-600 text-xs"></i>
                                    </div>
                                    <span class="font-medium">{{ plan.lessons_count }} حصة تعليمية</span>
                                </li>
                                <li class="flex items-center text-gray-700">
                                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-clock text-blue-600 text-xs"></i>
                                    </div>
                                    <span class="font-medium">{{ plan.lesson_duration }} دقيقة لكل حصة</span>
                                </li>
                                <li class="flex items-center text-gray-700">
                                    <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-calendar-alt text-purple-600 text-xs"></i>
                                    </div>
                                    <span class="font-medium">صالحة لمدة {{ plan.duration_days }} يوم</span>
                                </li>
                                <li class="flex items-center text-gray-700">
                                    <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-calculator text-orange-600 text-xs"></i>
                                    </div>
                                    <span class="font-medium">{{ plan.get_formatted_price_per_lesson }} {{ plan.get_currency_display }} للحصة الواحدة</span>
                                </li>
                                {% if plan.features %}
                                    {% for feature in plan.features %}
                                    <li class="flex items-center text-gray-700">
                                        <div class="w-6 h-6 bg-islamic-gold bg-opacity-20 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-star text-islamic-gold text-xs"></i>
                                        </div>
                                        <span class="font-medium">{{ feature }}</span>
                                    </li>
                                    {% endfor %}
                                {% endif %}
                            </ul>
                        </div>
                    </div>

                    <!-- Plan Description -->
                    {% if plan.description %}
                    <div class="mb-6 p-4 bg-islamic-mint bg-opacity-10 rounded-lg">
                        <p class="text-gray-700 text-sm text-center italic">{{ plan.description }}</p>
                    </div>
                    {% endif %}

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <div class="grid grid-cols-2 gap-3">
                            <a href="{% url 'admin_plan_detail' plan.id %}" class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-center">
                                <i class="fas fa-eye ml-2"></i>
                                عرض التفاصيل
                            </a>
                            <a href="{% url 'admin_plan_edit' plan.id %}" class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-center">
                                <i class="fas fa-edit ml-2"></i>
                                تعديل الباقة
                            </a>
                        </div>
                        <a href="{% url 'admin_plan_delete' plan.id %}" class="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-center block">
                            <i class="fas fa-trash ml-2"></i>
                            حذف الباقة
                        </a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-span-full text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-box text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد باقات</h3>
                <p class="text-gray-500 mb-6">لم يتم إنشاء أي باقات بعد</p>
                <a href="{% url 'admin_plan_create' %}" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء أول باقة
                </a>
            </div>
            {% endfor %}
        </div>

        <!-- Summary Statistics -->
        {% if plans %}
        <div class="mt-12 bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-islamic-primary mb-4">
                <i class="fas fa-chart-bar ml-2"></i>
                ملخص الإحصائيات
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-box text-blue-600 text-xl"></i>
                    </div>
                    <div class="text-3xl font-bold text-blue-600">{{ total_plans }}</div>
                    <div class="text-sm text-gray-600">إجمالي الباقات</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="text-3xl font-bold text-green-600">{{ active_plans }}</div>
                    <div class="text-sm text-gray-600">باقات نشطة</div>
                </div>
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-users text-yellow-600 text-xl"></i>
                    </div>
                    <div class="text-3xl font-bold text-yellow-600">{{ total_subscriptions }}</div>
                    <div class="text-sm text-gray-600">إجمالي الاشتراكات</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-user-check text-purple-600 text-xl"></i>
                    </div>
                    <div class="text-3xl font-bold text-purple-600">{{ active_subscriptions }}</div>
                    <div class="text-sm text-gray-600">اشتراكات نشطة</div>
                </div>
            </div>

            <!-- إحصائيات إضافية -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="text-center p-4 bg-indigo-50 rounded-lg">
                        <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-dollar-sign text-indigo-600 text-xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-indigo-600">${{ total_revenue|floatformat:0 }}</div>
                        <div class="text-sm text-gray-600">إجمالي الإيرادات</div>
                    </div>
                    <div class="text-center p-4 bg-teal-50 rounded-lg">
                        <div class="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-percentage text-teal-600 text-xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-teal-600">
                            {% if total_subscriptions > 0 %}
                                {% widthratio active_subscriptions total_subscriptions 100 %}%
                            {% else %}
                                0%
                            {% endif %}
                        </div>
                        <div class="text-sm text-gray-600">معدل الاشتراكات النشطة</div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
