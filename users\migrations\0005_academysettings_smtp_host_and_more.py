# Generated by Django 4.2.7 on 2025-06-07 00:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0004_update_attachment_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='academysettings',
            name='smtp_host',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='خادم SMTP'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='smtp_password',
            field=models.TextField(blank=True, null=True, verbose_name='كلمة مرور SMTP (مشفرة)'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='smtp_port',
            field=models.IntegerField(blank=True, default=587, null=True, verbose_name='منفذ SMTP'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='smtp_use_ssl',
            field=models.<PERSON>oleanField(default=False, verbose_name='استخدام SSL'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='smtp_use_tls',
            field=models.BooleanField(default=True, verbose_name='استخدام TLS'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='smtp_username',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم مستخدم SMTP'),
        ),
        migrations.AlterField(
            model_name='academysettings',
            name='email_service_provider',
            field=models.CharField(choices=[('sendinblue', 'SendinBlue (Brevo)'), ('amazon_ses', 'Amazon SES'), ('sendgrid', 'SendGrid'), ('mailgun', 'Mailgun'), ('postmark', 'Postmark'), ('mailjet', 'Mailjet'), ('sparkpost', 'SparkPost'), ('mandrill', 'Mandrill (Mailchimp)'), ('elastic_email', 'Elastic Email'), ('pepipost', 'Pepipost (Netcore)'), ('socketlabs', 'SocketLabs'), ('mailersend', 'MailerSend'), ('resend', 'Resend'), ('loops', 'Loops'), ('plunk', 'Plunk'), ('emailjs', 'EmailJS'), ('smtp2go', 'SMTP2GO'), ('turbosmtp', 'turboSMTP'), ('mailpace', 'Mailpace'), ('courier', 'Courier'), ('smtp', 'SMTP (عام)')], default='smtp', max_length=50, verbose_name='مزود خدمة البريد الإلكتروني'),
        ),
    ]
