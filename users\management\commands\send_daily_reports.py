"""
Django management command لإرسال التقارير اليومية للمديرين
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth import get_user_model
from users.email_service import EmailService
from users.email_models import EmailTemplate, EmailEvent
from users.models import User, Subscription, Lesson
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = 'إرسال التقارير اليومية للمديرين'

    def add_arguments(self, parser):
        parser.add_argument(
            '--date',
            type=str,
            help='تاريخ التقرير (YYYY-MM-DD) - افتراضي: أمس'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='تشغيل تجريبي بدون إرسال فعلي للتقارير'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # تحديد تاريخ التقرير
        if options['date']:
            try:
                report_date = datetime.strptime(options['date'], '%Y-%m-%d').date()
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD')
                )
                return
        else:
            # افتراضي: أمس
            report_date = (timezone.now() - timedelta(days=1)).date()
        
        self.stdout.write(
            self.style.SUCCESS(f'إنشاء التقرير اليومي لتاريخ: {report_date}')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('تشغيل تجريبي - لن يتم إرسال تقارير فعلية')
            )
        
        try:
            # جمع إحصائيات اليوم
            stats = self.collect_daily_stats(report_date)
            
            # عرض الإحصائيات
            self.display_stats(stats)
            
            if not dry_run:
                # إرسال التقارير للمديرين
                self.send_reports_to_admins(stats, report_date)
            else:
                self.stdout.write(
                    self.style.WARNING('تم تخطي الإرسال (تشغيل تجريبي)')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في إنشاء التقرير: {str(e)}')
            )
            logger.error(f'خطأ في إرسال التقارير اليومية: {str(e)}')
            raise

    def collect_daily_stats(self, report_date):
        """جمع إحصائيات اليوم"""
        start_date = datetime.combine(report_date, datetime.min.time())
        end_date = datetime.combine(report_date, datetime.max.time())
        
        # تسجيلات جديدة
        new_registrations = User.objects.filter(
            date_joined__range=[start_date, end_date]
        ).count()
        
        # اشتراكات جديدة
        try:
            new_subscriptions = Subscription.objects.filter(
                created_at__range=[start_date, end_date]
            ).count()
        except:
            new_subscriptions = 0
        
        # حصص مكتملة
        try:
            completed_lessons = Lesson.objects.filter(
                date__date=report_date,
                status='completed'
            ).count()
        except:
            completed_lessons = 0
        
        # طلاب نشطين
        try:
            active_students = User.objects.filter(
                role='student',
                last_login__date=report_date
            ).count()
        except:
            active_students = 0
        
        # معلمين نشطين
        try:
            active_teachers = User.objects.filter(
                role='teacher',
                last_login__date=report_date
            ).count()
        except:
            active_teachers = 0
        
        # اشتراكات تنتهي قريباً
        try:
            expiry_date = timezone.now().date() + timedelta(days=7)
            expiring_subscriptions = Subscription.objects.filter(
                end_date__lte=expiry_date,
                is_active=True
            ).count()
        except:
            expiring_subscriptions = 0
        
        return {
            'report_date': report_date,
            'new_registrations': new_registrations,
            'new_subscriptions': new_subscriptions,
            'completed_lessons': completed_lessons,
            'active_students': active_students,
            'active_teachers': active_teachers,
            'expiring_subscriptions': expiring_subscriptions,
            'total_revenue': 0,  # سيتم حسابه لاحقاً
            'pending_evaluations': 0,  # سيتم حسابه لاحقاً
        }

    def display_stats(self, stats):
        """عرض الإحصائيات في الكونسول"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(f'📊 إحصائيات يوم {stats["report_date"]}')
        self.stdout.write('='*50)
        self.stdout.write(f'👥 تسجيلات جديدة: {stats["new_registrations"]}')
        self.stdout.write(f'📝 اشتراكات جديدة: {stats["new_subscriptions"]}')
        self.stdout.write(f'📚 حصص مكتملة: {stats["completed_lessons"]}')
        self.stdout.write(f'🎓 طلاب نشطين: {stats["active_students"]}')
        self.stdout.write(f'👨‍🏫 معلمين نشطين: {stats["active_teachers"]}')
        self.stdout.write(f'⚠️ اشتراكات تنتهي قريباً: {stats["expiring_subscriptions"]}')
        self.stdout.write('='*50 + '\n')

    def send_reports_to_admins(self, stats, report_date):
        """إرسال التقارير للمديرين"""
        # البحث عن قالب التقرير الإداري
        try:
            template = EmailTemplate.objects.get(
                template_type='admin_report',
                is_active=True,
                is_default=True
            )
        except EmailTemplate.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('لم يتم العثور على قالب التقرير الإداري')
            )
            return
        
        # البحث عن المديرين
        admins = User.objects.filter(role='admin', is_active=True)
        
        if not admins.exists():
            self.stdout.write(
                self.style.WARNING('لا يوجد مديرين نشطين لإرسال التقارير إليهم')
            )
            return
        
        # إعداد بيانات التقرير
        context = {
            'report_date': report_date.strftime('%Y-%m-%d'),
            'current_date': report_date.strftime('%Y-%m-%d'),
            'current_time': timezone.now().strftime('%H:%M'),
            'new_registrations': stats['new_registrations'],
            'new_subscriptions': stats['new_subscriptions'],
            'completed_lessons': stats['completed_lessons'],
            'active_students': stats['active_students'],
            'active_teachers': stats['active_teachers'],
            'expiring_subscriptions': stats['expiring_subscriptions'],
            'total_revenue': stats['total_revenue'],
            'pending_evaluations': stats['pending_evaluations'],
            'admin_notes': 'تقرير تلقائي من النظام',
        }
        
        # إرسال التقارير
        email_service = EmailService()
        sent_count = 0
        failed_count = 0
        
        for admin in admins:
            try:
                # إضافة معلومات المدير للسياق
                admin_context = context.copy()
                admin_context.update({
                    'user_name': admin.get_full_name() or admin.username,
                    'user_email': admin.email,
                })
                
                success = email_service.send_email(
                    recipient=admin,
                    template=template,
                    context=admin_context,
                    immediate=True
                )
                
                if success:
                    sent_count += 1
                    self.stdout.write(f'✅ تم إرسال التقرير إلى: {admin.email}')
                else:
                    failed_count += 1
                    self.stdout.write(f'❌ فشل إرسال التقرير إلى: {admin.email}')
                    
            except Exception as e:
                failed_count += 1
                self.stdout.write(f'❌ خطأ في إرسال التقرير إلى {admin.email}: {str(e)}')
                logger.error(f'خطأ في إرسال التقرير إلى {admin.email}: {str(e)}')
        
        # ملخص النتائج
        self.stdout.write(
            self.style.SUCCESS(
                f'\n📧 ملخص الإرسال:\n'
                f'- تم الإرسال بنجاح: {sent_count}\n'
                f'- فشل في الإرسال: {failed_count}\n'
                f'- إجمالي المديرين: {admins.count()}'
            )
        )
