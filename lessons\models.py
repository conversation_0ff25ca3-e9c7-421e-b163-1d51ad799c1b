from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

import secrets
import string
import uuid


# ===== النظام الجديد الموحد للحصص =====

class Lesson(models.Model):
    """النموذج الموحد لجميع أنواع الحصص"""

    LESSON_TYPES = (
        ('subscription', _('حصة من الباقة')),
        ('trial', _('حصة تجريبية')),
        ('makeup', _('حصة تعويضية')),
    )

    STATUS_CHOICES = (
        ('scheduled', _('مجدولة')),
        ('live', _('جارية')),
        ('completed', _('مكتملة')),
        ('rated', _('تم تقييمها')),
        ('cancelled_by_student', _('ألغاها الطالب')),
        ('cancelled_by_teacher', _('ألغاها المعلم')),
        ('cancelled_by_admin', _('ألغاها المدير')),
        ('rescheduled', _('تم إعادة جدولتها')),
        ('no_show_student', _('غياب الطالب')),
        ('no_show_teacher', _('غياب المعلم')),
    )

    CANCELLATION_REASONS = (
        ('emergency', _('ظرف طارئ')),
        ('illness', _('مرض')),
        ('technical_issue', _('مشكلة تقنية')),
        ('schedule_conflict', _('تضارب في المواعيد')),
        ('personal_reason', _('سبب شخصي')),
        ('other', _('أخرى')),
    )

    # النوع والمصدر
    lesson_type = models.CharField(
        max_length=20,
        choices=LESSON_TYPES,
        verbose_name=_('نوع الحصة')
    )

    subscription = models.ForeignKey(
        'subscriptions.StudentSubscription',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='unified_lessons',
        verbose_name=_('الاشتراك')
    )

    lesson_number = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('رقم الحصة في الباقة')
    )

    original_lesson = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='makeup_lessons',
        verbose_name=_('الحصة الأصلية')
    )

    # المعلومات الأساسية
    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الحصة')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف الحصة')
    )

    teacher = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='lessons_as_teacher',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    student = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='lessons_as_student',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='created_lessons',
        verbose_name=_('منشئ الحصة')
    )

    # التوقيت
    scheduled_date = models.DateTimeField(
        verbose_name=_('موعد الحصة')
    )

    duration_minutes = models.PositiveIntegerField(
        choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة'), (90, '90 دقيقة')],
        default=45,
        verbose_name=_('مدة الحصة (بالدقائق)')
    )

    started_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت البداية الفعلي')
    )

    ended_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت النهاية الفعلي')
    )

    # الحالة
    status = models.CharField(
        max_length=25,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_('حالة الحصة')
    )

    # معلومات الإلغاء/إعادة الجدولة
    cancelled_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت الإلغاء')
    )

    cancelled_by = models.ForeignKey(
        'users.User',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='cancelled_lessons',
        verbose_name=_('ألغيت بواسطة')
    )

    cancellation_reason = models.CharField(
        max_length=20,
        choices=CANCELLATION_REASONS,
        blank=True,
        verbose_name=_('سبب الإلغاء')
    )

    cancellation_note = models.TextField(
        blank=True,
        verbose_name=_('ملاحظة الإلغاء')
    )

    # إعادة الجدولة
    rescheduled_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت إعادة الجدولة')
    )

    rescheduled_by = models.ForeignKey(
        'users.User',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='rescheduled_lessons',
        verbose_name=_('أعيد جدولتها بواسطة')
    )

    reschedule_reason = models.TextField(
        blank=True,
        verbose_name=_('سبب إعادة الجدولة')
    )

    # Jitsi Integration
    jitsi_room_id = models.CharField(
        max_length=100,
        unique=True,
        blank=True,
        verbose_name=_('معرف غرفة Jitsi')
    )

    jitsi_room_password = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_('كلمة مرور الغرفة')
    )

    # التقارير والتقييمات
    teacher_report = models.TextField(
        blank=True,
        verbose_name=_('تقرير المعلم')
    )

    teacher_report_submitted = models.BooleanField(
        default=False,
        verbose_name=_('تم إرسال تقرير المعلم')
    )

    student_evaluation_submitted = models.BooleanField(
        default=False,
        verbose_name=_('تم إرسال تقييم الطالب')
    )

    # التواريخ
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('حصة')
        verbose_name_plural = _('الحصص')
        ordering = ['scheduled_date']
        indexes = [
            models.Index(fields=['lesson_type', 'status']),
            models.Index(fields=['teacher', 'scheduled_date']),
            models.Index(fields=['student', 'scheduled_date']),
            models.Index(fields=['subscription', 'lesson_number']),
        ]

    def __str__(self):
        if self.lesson_type == 'trial':
            return f"حصة تجريبية - {self.teacher.get_full_name()} مع {self.student.get_full_name()}"
        elif self.lesson_type == 'makeup':
            return f"حصة تعويضية - {self.teacher.get_full_name()} مع {self.student.get_full_name()}"
        else:
            return f"حصة رقم {self.lesson_number} - {self.teacher.get_full_name()} مع {self.student.get_full_name()}"

    def save(self, *args, **kwargs):
        """حفظ الحصة مع توليد معرف Jitsi فريد"""
        if not self.jitsi_room_id:
            self.jitsi_room_id = self._generate_unique_jitsi_room_id()

        if not self.jitsi_room_password:
            self.jitsi_room_password = ''.join(secrets.choice(string.digits) for _ in range(6))

        # تحديد العنوان تلقائياً إذا لم يتم تحديده
        if not self.title:
            if self.lesson_type == 'trial':
                self.title = f"حصة تجريبية - {self.student.get_full_name()}"
            elif self.lesson_type == 'makeup':
                self.title = f"حصة تعويضية - {self.student.get_full_name()}"
            else:
                self.title = f"حصة رقم {self.lesson_number} - {self.student.get_full_name()}"

        super().save(*args, **kwargs)

    def _generate_unique_jitsi_room_id(self):
        """توليد معرف غرفة Jitsi فريد"""
        max_attempts = 10

        for attempt in range(max_attempts):
            timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
            uuid_part = str(uuid.uuid4()).replace('-', '')[:8]
            attempt_suffix = f"_{attempt}" if attempt > 0 else ""

            room_id = f"qurania_{self.lesson_type}_{timestamp}_{uuid_part}{attempt_suffix}"

            if not Lesson.objects.filter(jitsi_room_id=room_id).exists():
                return room_id

        # fallback
        return f"qurania_{self.lesson_type}_{str(uuid.uuid4()).replace('-', '')}"

    def can_be_cancelled_by(self, user):
        """تحديد إمكانية الإلغاء حسب المستخدم والوقت"""
        if self.status not in ['scheduled']:
            return False, "لا يمكن إلغاء الحصة في هذه الحالة"

        # المدير يمكنه الإلغاء دائماً
        if hasattr(user, 'is_admin') and user.is_admin():
            return True, "المدير يمكنه الإلغاء في أي وقت"

        # التحقق من الوقت المتبقي
        now = timezone.now()
        time_until_lesson = self.scheduled_date - now

        if time_until_lesson.total_seconds() < 3600:  # أقل من ساعة
            return False, "لا يمكن الإلغاء قبل الحصة بأقل من ساعة"

        return True, "يمكن الإلغاء"

    def can_be_rescheduled_by(self):
        """تحديد إمكانية إعادة الجدولة"""
        if self.status not in ['scheduled', 'cancelled_by_student', 'cancelled_by_teacher']:
            return False, "لا يمكن إعادة جدولة الحصة في هذه الحالة"
        return True, "يمكن إعادة الجدولة"

    def cancel_lesson(self, cancelled_by, reason, note=""):
        """إلغاء الحصة"""
        if cancelled_by.user_type == 'student':
            self.status = 'cancelled_by_student'
        elif cancelled_by.user_type == 'teacher':
            self.status = 'cancelled_by_teacher'
        else:
            self.status = 'cancelled_by_admin'

        self.cancelled_at = timezone.now()
        self.cancelled_by = cancelled_by
        self.cancellation_reason = reason
        self.cancellation_note = note
        self.save()

        # إرجاع الحصة للباقة إذا كانت من الاشتراك
        if self.lesson_type == 'subscription' and self.subscription:
            self.subscription.return_lesson()

    def reschedule_lesson(self, new_date, rescheduled_by, reason=""):
        """إعادة جدولة الحصة"""
        old_date = self.scheduled_date
        self.scheduled_date = new_date
        self.rescheduled_at = timezone.now()
        self.rescheduled_by = rescheduled_by
        self.reschedule_reason = reason
        self.status = 'scheduled'  # إعادة تعيين الحالة
        self.save()

        return old_date

    def start_lesson(self):
        """بدء الحصة"""
        self.status = 'live'
        self.started_at = timezone.now()
        self.save()

    def end_lesson(self):
        """إنهاء الحصة"""
        self.status = 'completed'
        self.ended_at = timezone.now()
        self.save()

    def submit_teacher_report(self, report):
        """إرسال تقرير المعلم"""
        self.teacher_report = report
        self.teacher_report_submitted = True
        self.save()

    def submit_student_evaluation(self):
        """تسجيل إرسال تقييم الطالب"""
        self.student_evaluation_submitted = True
        if self.teacher_report_submitted:
            self.status = 'rated'
        self.save()

    def get_jitsi_url(self):
        """الحصول على رابط Jitsi"""
        from django.conf import settings
        domain = getattr(settings, 'JITSI_DOMAIN', 'meet.jit.si')
        return f"https://{domain}/{self.jitsi_room_id}"

    def get_teacher_jitsi_url(self):
        """رابط Jitsi للمعلم مع صلاحيات المشرف"""
        base_url = self.get_jitsi_url()
        return f"{base_url}#config.startWithVideoMuted=false&config.startWithAudioMuted=false&userInfo.displayName={self.teacher.get_full_name()}&userInfo.role=moderator"

    def get_student_jitsi_url(self):
        """رابط Jitsi للطالب"""
        base_url = self.get_jitsi_url()
        return f"{base_url}#config.startWithVideoMuted=true&config.startWithAudioMuted=true&userInfo.displayName={self.student.get_full_name()}&userInfo.role=participant"

    def is_live(self):
        """التحقق من كون الحصة مباشرة"""
        return self.status == 'live'

    def is_completed(self):
        """التحقق من كون الحصة مكتملة"""
        return self.status in ['completed', 'rated']

    def is_cancelled(self):
        """التحقق من كون الحصة ملغية"""
        return self.status in ['cancelled_by_student', 'cancelled_by_teacher', 'cancelled_by_admin']

    def get_status_display_arabic(self):
        """عرض الحالة بالعربية"""
        status_map = {
            'scheduled': 'مجدولة',
            'live': 'جارية',
            'completed': 'مكتملة',
            'rated': 'مقيمة',
            'cancelled_by_student': 'ألغاها الطالب',
            'cancelled_by_teacher': 'ألغاها المعلم',
            'cancelled_by_admin': 'ألغاها المدير',
            'rescheduled': 'معاد جدولتها',
            'no_show_student': 'غياب الطالب',
            'no_show_teacher': 'غياب المعلم',
        }
        return status_map.get(self.status, self.status)


class MakeupRequest(models.Model):
    """نموذج طلبات الحصص التعويضية"""

    REQUEST_STATUS = (
        ('pending', _('في الانتظار')),
        ('approved', _('موافق عليه')),
        ('rejected', _('مرفوض')),
        ('completed', _('مكتمل')),
    )

    original_lesson = models.ForeignKey(
        Lesson,
        on_delete=models.CASCADE,
        related_name='makeup_requests',
        verbose_name=_('الحصة الأصلية')
    )

    requested_by = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='makeup_requests',
        verbose_name=_('طالب الحصة التعويضية')
    )

    reason = models.TextField(
        verbose_name=_('سبب الطلب')
    )

    request_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الطلب')
    )

    status = models.CharField(
        max_length=15,
        choices=REQUEST_STATUS,
        default='pending',
        verbose_name=_('حالة الطلب')
    )

    reviewed_by = models.ForeignKey(
        'users.User',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='reviewed_makeup_requests',
        verbose_name=_('راجعه')
    )

    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ المراجعة')
    )

    admin_notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات الإدارة')
    )

    makeup_lesson = models.ForeignKey(
        Lesson,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='makeup_request',
        verbose_name=_('الحصة التعويضية')
    )

    class Meta:
        verbose_name = _('طلب حصة تعويضية')
        verbose_name_plural = _('طلبات الحصص التعويضية')
        ordering = ['-request_date']

    def __str__(self):
        return f"طلب حصة تعويضية - {self.requested_by.get_full_name()}"

    def approve(self, reviewed_by, admin_notes=""):
        """الموافقة على الطلب"""
        self.status = 'approved'
        self.reviewed_by = reviewed_by
        self.reviewed_at = timezone.now()
        self.admin_notes = admin_notes
        self.save()

    def reject(self, reviewed_by, admin_notes=""):
        """رفض الطلب"""
        self.status = 'rejected'
        self.reviewed_by = reviewed_by
        self.reviewed_at = timezone.now()
        self.admin_notes = admin_notes
        self.save()


class StudentEvaluation(models.Model):
    """نموذج تقييمات الطلاب الموحد"""

    lesson = models.OneToOneField(
        Lesson,
        on_delete=models.CASCADE,
        related_name='evaluation',
        verbose_name=_('الحصة')
    )

    student = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='submitted_evaluations',
        verbose_name=_('الطالب')
    )

    teacher = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='received_evaluations',
        verbose_name=_('المعلم')
    )

    # التقييمات
    overall_rating = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('التقييم العام')
    )

    teaching_quality = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('جودة التدريس')
    )

    lesson_content = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('محتوى الحصة')
    )

    interaction_quality = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('جودة التفاعل')
    )

    punctuality = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('الالتزام بالوقت')
    )

    # تقييمات خاصة بالحصص التجريبية
    would_recommend = models.BooleanField(
        null=True,
        blank=True,
        verbose_name=_('هل تنصح بالمعلم؟')
    )

    interested_in_subscription = models.BooleanField(
        null=True,
        blank=True,
        verbose_name=_('مهتم بالاشتراك؟')
    )

    # التعليق
    comment = models.TextField(
        blank=True,
        default='',
        verbose_name=_('تعليق')
    )

    # التوقيت
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التقييم')
    )

    class Meta:
        verbose_name = _('تقييم الطالب')
        verbose_name_plural = _('تقييمات الطلاب')
        ordering = ['-created_at']

    def __str__(self):
        return f"تقييم {self.student.get_full_name()} للمعلم {self.teacher.get_full_name()}"

    @property
    def average_rating(self):
        """حساب متوسط التقييم"""
        ratings = [
            self.overall_rating,
            self.teaching_quality,
            self.lesson_content,
            self.interaction_quality,
            self.punctuality
        ]
        return sum(ratings) / len(ratings)


class LessonAttendance(models.Model):
    """نموذج حضور الحصص - محدث للنظام الجديد"""

    lesson = models.ForeignKey(
        Lesson,
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('الحصة')
    )

    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='lesson_attendance',
        verbose_name=_('المستخدم')
    )

    joined_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('وقت الدخول')
    )

    left_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت الخروج')
    )

    duration_minutes = models.PositiveIntegerField(
        default=0,
        verbose_name=_('مدة الحضور (بالدقائق)')
    )

    class Meta:
        verbose_name = _('حضور الحصة')
        verbose_name_plural = _('حضور الحصص')
        unique_together = ['lesson', 'user']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.lesson.title}"

    def calculate_duration(self):
        """حساب مدة الحضور"""
        if self.left_at:
            duration = self.left_at - self.joined_at
            self.duration_minutes = int(duration.total_seconds() / 60)
            self.save()


class LessonContent(models.Model):
    """نموذج محتوى الحصة - محدث للنظام الجديد"""

    CONTENT_TYPES = (
        ('memorization', _('حفظ')),
        ('review', _('مراجعة')),
        ('recitation', _('تلاوة')),
        ('correction', _('تصحيح')),
    )

    lesson = models.ForeignKey(
        Lesson,
        on_delete=models.CASCADE,
        related_name='content_records',
        verbose_name=_('الحصة')
    )

    content_type = models.CharField(
        max_length=15,
        choices=CONTENT_TYPES,
        verbose_name=_('نوع المحتوى')
    )

    surah_name = models.CharField(
        max_length=100,
        verbose_name=_('اسم السورة')
    )

    from_verse = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_('من الآية')
    )

    to_verse = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_('إلى الآية')
    )

    quality_score = models.PositiveIntegerField(
        choices=[(i, f"{i}/10") for i in range(1, 11)],
        blank=True,
        null=True,
        verbose_name=_('تقييم الجودة')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    recorded_by = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='recorded_lesson_content',
        blank=True,
        null=True,
        verbose_name=_('مسجل بواسطة')
    )

    recorded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التسجيل')
    )

    class Meta:
        verbose_name = _('محتوى الحصة')
        verbose_name_plural = _('محتوى الحصص')
        ordering = ['-recorded_at']

    def __str__(self):
        return f"{self.lesson.title} - {self.surah_name} ({self.from_verse}-{self.to_verse})"


# ===== النماذج القديمة - للتوافق المؤقت =====

class ComprehensiveLessonReport(models.Model):
    """نموذج موحد لتقارير جميع أنواع الحصص"""

    # ربط مع أنواع الحصص المختلفة
    lesson = models.ForeignKey(
        'Lesson',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='comprehensive_reports',
        verbose_name=_('الحصة المجدولة')
    )

    live_lesson = models.ForeignKey(
        'LiveLesson',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='comprehensive_reports',
        verbose_name=_('الحصة المباشرة')
    )

    # معلومات أساسية
    lesson_type = models.CharField(
        max_length=20,
        choices=[
            ('trial', _('حصة تجريبية')),
            ('subscription', _('حصة اشتراك')),
            ('scheduled', _('حصة مجدولة')),
        ],
        verbose_name=_('نوع الحصة')
    )

    teacher = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='comprehensive_teacher_reports',
        verbose_name=_('المعلم')
    )

    student = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='comprehensive_student_reports',
        verbose_name=_('الطالب')
    )

    # تقرير المعلم
    teacher_report_submitted = models.BooleanField(default=False, verbose_name=_('تم إرسال تقرير المعلم'))
    teacher_report_date = models.DateTimeField(null=True, blank=True, verbose_name=_('تاريخ تقرير المعلم'))

    # تقييمات المعلم للطالب
    student_performance = models.PositiveIntegerField(
        null=True, blank=True,
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('أداء الطالب')
    )
    student_participation = models.PositiveIntegerField(
        null=True, blank=True,
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('مشاركة الطالب')
    )
    student_understanding = models.PositiveIntegerField(
        null=True, blank=True,
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('فهم الطالب')
    )
    overall_lesson_rating = models.PositiveIntegerField(
        null=True, blank=True,
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('تقييم الحصة العام')
    )

    # نصوص تقرير المعلم
    lesson_summary = models.TextField(blank=True, verbose_name=_('ملخص الحصة'))
    strengths = models.TextField(blank=True, verbose_name=_('نقاط القوة'))
    areas_for_improvement = models.TextField(blank=True, verbose_name=_('نقاط التحسين'))
    additional_notes = models.TextField(blank=True, verbose_name=_('ملاحظات إضافية'))

    # تقييم الطالب
    student_evaluation_submitted = models.BooleanField(default=False, verbose_name=_('تم إرسال تقييم الطالب'))
    student_evaluation_date = models.DateTimeField(null=True, blank=True, verbose_name=_('تاريخ تقييم الطالب'))

    # تقييمات الطالب للمعلم
    overall_rating = models.PositiveIntegerField(
        null=True, blank=True,
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('التقييم العام')
    )
    teaching_quality = models.PositiveIntegerField(
        null=True, blank=True,
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('جودة التدريس')
    )
    lesson_content = models.PositiveIntegerField(
        null=True, blank=True,
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('محتوى الحصة')
    )
    interaction_quality = models.PositiveIntegerField(
        null=True, blank=True,
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('جودة التفاعل')
    )
    punctuality = models.PositiveIntegerField(
        null=True, blank=True,
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('الالتزام بالوقت')
    )

    student_comment = models.TextField(blank=True, verbose_name=_('تعليق الطالب'))

    # خاص بالحصص التجريبية
    would_recommend = models.BooleanField(null=True, blank=True, verbose_name=_('هل ينصح بالمعلم؟'))
    interested_in_subscription = models.BooleanField(null=True, blank=True, verbose_name=_('مهتم بالاشتراك؟'))

    # التوقيتات
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('تاريخ الإنشاء'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('تاريخ آخر تحديث'))

    class Meta:
        verbose_name = _('تقرير حصة شامل')
        verbose_name_plural = _('تقارير الحصص الشاملة')
        ordering = ['-created_at']

    def __str__(self):
        lesson_obj = self.get_lesson_object()
        return f"تقرير {lesson_obj.title if lesson_obj else 'حصة'} - {self.teacher.get_full_name()} - {self.student.get_full_name()}"

    def get_lesson_object(self):
        """الحصول على كائن الحصة الفعلي"""
        return self.lesson or self.live_lesson

    def is_complete(self):
        """التحقق من اكتمال التقرير والتقييم"""
        return self.teacher_report_submitted and self.student_evaluation_submitted

    def get_teacher_average_rating(self):
        """متوسط تقييم المعلم للطالب"""
        ratings = [
            self.student_performance,
            self.student_participation,
            self.student_understanding,
            self.overall_lesson_rating
        ]
        valid_ratings = [r for r in ratings if r is not None]
        return sum(valid_ratings) / len(valid_ratings) if valid_ratings else 0

    def get_student_average_rating(self):
        """متوسط تقييم الطالب للمعلم"""
        ratings = [
            self.overall_rating,
            self.teaching_quality,
            self.lesson_content,
            self.interaction_quality,
            self.punctuality
        ]
        valid_ratings = [r for r in ratings if r is not None]
        return sum(valid_ratings) / len(valid_ratings) if valid_ratings else 0

    def get_completion_status(self):
        """حالة اكتمال التقرير"""
        if self.teacher_report_submitted and self.student_evaluation_submitted:
            return 'complete'
        elif self.teacher_report_submitted:
            return 'teacher_only'
        elif self.student_evaluation_submitted:
            return 'student_only'
        else:
            return 'incomplete'

    def get_lesson_date(self):
        """تاريخ الحصة"""
        lesson_obj = self.get_lesson_object()
        if lesson_obj:
            return lesson_obj.scheduled_date
        return self.created_at


# سيتم الاحتفاظ بـ LiveLesson مؤقتاً للتوافق مع النظام القديم
class LiveLesson(models.Model):
    """نموذج الحصص المباشرة مع Jitsi Meet"""

    STATUS_CHOICES = (
        ('scheduled', _('مجدولة')),
        ('live', _('مباشرة')),
        ('completed', _('مكتملة')),
        ('rated', _('تم تقييمها')),
        ('cancelled', _('ملغية')),
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الحصة')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف الحصة')
    )

    teacher = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='live_lessons_as_teacher',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    student = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='live_lessons_as_student',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    jitsi_room_id = models.CharField(
        max_length=100,
        unique=True,
        blank=True,
        null=False,
        verbose_name=_('معرف غرفة Jitsi')
    )

    jitsi_room_password = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('كلمة مرور الغرفة')
    )

    # Google Meet integration
    google_meet_url = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('رابط Google Meet')
    )

    google_meet_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('معرف Google Meet')
    )

    meeting_platform = models.CharField(
        max_length=20,
        choices=[
            ('jitsi', 'Jitsi Meet'),
            ('google_meet', 'Google Meet'),
        ],
        default='jitsi',
        verbose_name=_('منصة الاجتماع')
    )

    scheduled_date = models.DateTimeField(
        default=timezone.now,
        verbose_name=_('موعد الحصة')
    )

    duration_minutes = models.PositiveIntegerField(
        choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة'), (90, '90 دقيقة')],
        default=45,
        verbose_name=_('مدة الحصة (بالدقائق)')
    )

    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_('حالة الحصة')
    )

    started_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت البداية الفعلي')
    )

    ended_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت النهاية الفعلي')
    )

    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='created_live_lessons',
        blank=True,
        null=True,
        verbose_name=_('منشئ الحصة')
    )

    # حالة التقارير والتقييمات
    teacher_report_submitted = models.BooleanField(default=False, verbose_name=_('تم إرسال تقرير المعلم'))
    student_evaluation_submitted = models.BooleanField(default=False, verbose_name=_('تم إرسال تقييم الطالب'))
    time_up_notified = models.BooleanField(default=False, verbose_name=_('تم إشعار انتهاء الوقت'))

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('حصة')
        verbose_name_plural = _('الحصص')
        ordering = ['scheduled_date']

    def __str__(self):
        return f"{self.title} - {self.teacher.get_full_name()} مع {self.student.get_full_name()}"

    def save(self, *args, **kwargs):
        """إنشاء معرف غرفة Jitsi فريد وكلمة مرور"""
        is_new = self.pk is None

        if not self.jitsi_room_id:
            # إنشاء معرف فريد للغرفة مع ضمان عدم التكرار
            self.jitsi_room_id = self._generate_unique_jitsi_room_id()

        if not self.jitsi_room_password:
            # إنشاء كلمة مرور عشوائية
            self.jitsi_room_password = ''.join(secrets.choice(string.digits) for _ in range(6))

        super().save(*args, **kwargs)

        # إرسال إشعارات عند إنشاء حصة جديدة
        if is_new:
            from notifications.utils import NotificationService
            NotificationService.notify_live_lesson_created(self)

    def _generate_unique_jitsi_room_id(self):
        """توليد معرف غرفة Jitsi فريد مع ضمان عدم التكرار"""
        import uuid
        max_attempts = 10

        for attempt in range(max_attempts):
            # إنشاء معرف فريد باستخدام timestamp + UUID + محاولة
            timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
            uuid_part = str(uuid.uuid4()).replace('-', '')[:8]
            attempt_suffix = f"_{attempt}" if attempt > 0 else ""

            room_id = f"qurania_live_{timestamp}_{uuid_part}{attempt_suffix}"

            # التحقق من عدم وجود هذا المعرف
            if not LiveLesson.objects.filter(jitsi_room_id=room_id).exists():
                return room_id

        # في حالة فشل جميع المحاولات، استخدم UUID كامل
        fallback_id = f"qurania_live_{str(uuid.uuid4()).replace('-', '')}"
        return fallback_id

    def get_jitsi_url(self):
        """إنشاء رابط غرفة Jitsi مع دعم JaaS"""
        from django.conf import settings

        # استخدام JaaS (Jitsi as a Service) للإنتاج
        if hasattr(settings, 'JITSI_JAAS_DOMAIN') and settings.JITSI_JAAS_DOMAIN:
            domain = settings.JITSI_JAAS_DOMAIN
        else:
            # fallback للخادم المحلي أو العام
            domain = getattr(settings, 'JITSI_DOMAIN', 'meet.jit.si')

        return f"https://{domain}/{self.jitsi_room_id}"

    def get_teacher_jitsi_url(self):
        """رابط Jitsi للمعلم مع صلاحيات المشرف"""
        from .jitsi_service import JitsiService

        # استخدام JaaS مع JWT token إذا كان متاحاً
        if JitsiService.is_jaas_enabled():
            return JitsiService.get_jitsi_url_with_token(
                room_id=self.jitsi_room_id,
                user=self.teacher,
                is_moderator=True
            )
        else:
            # fallback للطريقة القديمة
            base_url = self.get_jitsi_url()
            return f"{base_url}#config.startWithVideoMuted=false&config.startWithAudioMuted=false&userInfo.displayName={self.teacher.get_full_name()}&userInfo.role=moderator"

    def get_student_jitsi_url(self):
        """رابط Jitsi للطالب مع صلاحيات محدودة"""
        from .jitsi_service import JitsiService

        # استخدام JaaS مع JWT token إذا كان متاحاً
        if JitsiService.is_jaas_enabled():
            return JitsiService.get_jitsi_url_with_token(
                room_id=self.jitsi_room_id,
                user=self.student,
                is_moderator=False
            )
        else:
            # fallback للطريقة القديمة
            base_url = self.get_jitsi_url()
            return f"{base_url}#config.startWithVideoMuted=true&config.startWithAudioMuted=true&userInfo.displayName={self.student.get_full_name()}&userInfo.role=participant"

    def generate_google_meet_url(self):
        """إنشاء رابط Google Meet"""
        import uuid
        # إنشاء معرف فريد للاجتماع
        meeting_id = str(uuid.uuid4())[:12].replace('-', '')
        self.google_meet_id = meeting_id
        return f"https://meet.google.com/{meeting_id}"

    def get_meeting_url(self):
        """الحصول على رابط الاجتماع - Jitsi فقط"""
        return self.get_jitsi_url()

    def get_teacher_meeting_url(self):
        """رابط الاجتماع للمعلم - Jitsi فقط"""
        return self.get_teacher_jitsi_url()

    def get_student_meeting_url(self):
        """رابط الاجتماع للطالب - Jitsi فقط"""
        return self.get_student_jitsi_url()

    def start_lesson(self):
        """بدء الحصة"""
        self.status = 'live'
        self.started_at = timezone.now()
        self.save()

    def record_user_join(self, user):
        """تسجيل دخول مستخدم للحصة"""
        try:
            # التحقق من عدم وجود سجل حضور نشط
            existing_attendance = LessonAttendance.objects.filter(
                lesson=self,
                user=user,
                left_at__isnull=True
            ).first()

            if not existing_attendance:
                # إنشاء سجل حضور جديد
                LessonAttendance.objects.create(
                    lesson=self,
                    user=user
                )
                print(f"تم تسجيل دخول {user.get_full_name()} للحصة {self.id}")
            else:
                print(f"المستخدم {user.get_full_name()} موجود بالفعل في الحصة {self.id}")

        except Exception as e:
            print(f"خطأ في تسجيل دخول المستخدم للحصة: {str(e)}")

    def end_lesson(self):
        """إنهاء الحصة"""
        self.status = 'completed'
        self.ended_at = timezone.now()
        self.save()

        # تحديث الحصة المجدولة المرتبطة إذا وجدت
        self._complete_related_scheduled_lesson()

    def cancel_lesson(self):
        """إلغاء الحصة"""
        self.status = 'cancelled'
        self.save()

    def is_live(self):
        """التحقق من كون الحصة مباشرة"""
        return self.status == 'live'

    def is_scheduled(self):
        """التحقق من كون الحصة مجدولة"""
        return self.status == 'scheduled'

    def get_duration_display(self):
        """عرض مدة الحصة"""
        if self.started_at and self.ended_at:
            duration = self.ended_at - self.started_at
            minutes = int(duration.total_seconds() / 60)
            return f"{minutes} دقيقة"
        return f"{self.duration_minutes} دقيقة (مخطط)"

    def should_auto_end(self):
        """التحقق من ضرورة إنهاء الحصة تلقائياً"""
        if self.status != 'live' or not self.started_at:
            return False

        # حساب الوقت المنقضي منذ بداية الحصة
        now = timezone.now()
        elapsed_time = now - self.started_at
        elapsed_minutes = elapsed_time.total_seconds() / 60

        # إنهاء الحصة إذا تجاوزت المدة المحددة + 15 دقيقة إضافية
        return elapsed_minutes > (self.duration_minutes + 15)

    def auto_end_if_needed(self):
        """إنهاء الحصة تلقائياً إذا لزم الأمر"""
        if self.should_auto_end():
            self.end_lesson()
            return True
        return False

    def _complete_related_scheduled_lesson(self):
        """إكمال الحصة المجدولة المرتبطة بهذه الحصة المباشرة"""
        try:
            # تجنب الاستيراد الدائري
            from django.apps import apps
            ScheduledLesson = apps.get_model('subscriptions', 'ScheduledLesson')

            # البحث عن الحصة المجدولة المرتبطة
            scheduled_lesson = ScheduledLesson.objects.filter(
                live_lesson_id=self.id,
                status='converted_to_live'
            ).first()

            if scheduled_lesson:
                # تحديد الحصة المجدولة كمكتملة
                scheduled_lesson.mark_completed()
                print(f"تم إكمال الحصة المجدولة {scheduled_lesson.id} المرتبطة بالحصة المباشرة {self.id}")

        except Exception as e:
            print(f"خطأ في إكمال الحصة المجدولة المرتبطة: {str(e)}")
            # لا نريد أن يفشل إنهاء الحصة بسبب هذا الخطأ
            pass

    def get_related_scheduled_lesson(self):
        """الحصول على الحصة المجدولة المرتبطة"""
        try:
            # تجنب الاستيراد الدائري
            from django.apps import apps
            ScheduledLesson = apps.get_model('subscriptions', 'ScheduledLesson')
            return ScheduledLesson.objects.filter(
                live_lesson_id=self.id,
                status='converted_to_live'
            ).first()
        except Exception:
            return None

    def is_lesson_counted(self):
        """التحقق من احتساب الحصة"""
        # الحصة تُحتسب إذا:
        # 1. تم إكمالها (completed أو rated)
        # 2. يوجد تقرير من المعلم (إجباري)
        # 3. تقييم الطالب اختياري لكن الحصة تُحتسب بدونه

        if self.status not in ['completed', 'rated']:
            return False

        # التحقق من وجود تقرير المعلم
        return hasattr(self, 'teacher_report')

    def get_lesson_completion_status(self):
        """الحصول على حالة اكتمال الحصة"""
        has_teacher_report = hasattr(self, 'teacher_report')
        has_student_rating = self.ratings.exists()

        return {
            'is_counted': self.is_lesson_counted(),
            'has_teacher_report': has_teacher_report,
            'has_student_rating': has_student_rating,
            'status': self.status,
            'completion_percentage': self.get_completion_percentage()
        }

    def get_completion_percentage(self):
        """حساب نسبة اكتمال الحصة"""
        if self.status == 'scheduled':
            return 0
        elif self.status == 'live':
            return 25
        elif self.status == 'completed':
            # 50% للإكمال + 25% لتقرير المعلم + 25% لتقييم الطالب
            percentage = 50
            if hasattr(self, 'teacher_report'):
                percentage += 25
            if self.ratings.exists():
                percentage += 25
            return percentage
        elif self.status == 'rated':
            return 100
        else:
            return 0

    def can_be_rated(self):
        """التحقق من إمكانية تقييم الحصة"""
        return self.status in ['completed', 'rated']

    def mark_as_completed_by_teacher(self):
        """تحديد الحصة كمكتملة من قبل المعلم"""
        if self.status == 'live':
            self.status = 'completed'
            self.ended_at = timezone.now()
            self.save()
            return True
        return False

    def mark_as_rated(self):
        """تحديد الحصة كمقيمة"""
        if self.status == 'completed':
            self.status = 'rated'
            self.save()
            return True
        return False


# تم حذف LiveLessonRating - استخدم UnifiedLessonRating بدلاً منه


# تم دمج TeacherLessonReport مع UnifiedLessonRating


# ===== نظام التقييم الموحد =====

class UnifiedLessonRating(models.Model):
    """نظام تقييم موحد لجميع أنواع الحصص - النظام الوحيد للتقييمات"""

    # ربط مع الحصة المباشرة
    live_lesson = models.ForeignKey(
        'LiveLesson',
        on_delete=models.CASCADE,
        related_name='ratings',
        verbose_name=_('الحصة المباشرة')
    )

    # أطراف التقييم
    teacher = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='received_ratings',
        verbose_name=_('المعلم')
    )

    student = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='given_ratings',
        verbose_name=_('الطالب')
    )

    # معايير التقييم الموحدة
    overall_rating = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('التقييم العام'),
        help_text=_('التقييم العام للحصة من 1 إلى 5 نجوم')
    )

    lesson_quality = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('جودة المحتوى'),
        help_text=_('تقييم جودة محتوى الحصة ووضوح الشرح')
    )

    teacher_interaction = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('تفاعل المعلم'),
        help_text=_('تقييم تفاعل المعلم واستجابته للأسئلة')
    )

    technical_quality = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('الجودة التقنية'),
        help_text=_('تقييم جودة الصوت والصورة والاتصال')
    )

    # تقييمات إضافية اختيارية
    punctuality = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        null=True, blank=True,
        verbose_name=_('الالتزام بالوقت'),
        help_text=_('تقييم التزام المعلم بوقت الحصة')
    )

    lesson_preparation = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        null=True, blank=True,
        verbose_name=_('التحضير للحصة'),
        help_text=_('تقييم مدى تحضير المعلم للحصة')
    )

    # تعليق الطالب
    comment = models.TextField(
        blank=True,
        default='',
        verbose_name=_('تعليق الطالب'),
        help_text=_('تعليق اختياري من الطالب')
    )

    # ===== تقرير المعلم (مدمج) =====

    # تقييم أداء الطالب من المعلم (إجباري)
    student_performance = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        null=True, blank=True,
        verbose_name=_('أداء الطالب'),
        help_text=_('تقييم المعلم لأداء الطالب')
    )

    student_participation = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        null=True, blank=True,
        verbose_name=_('مستوى التفاعل'),
        help_text=_('تقييم المعلم لتفاعل الطالب')
    )

    student_understanding = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        null=True, blank=True,
        verbose_name=_('مستوى الفهم'),
        help_text=_('تقييم المعلم لفهم الطالب')
    )

    # تقرير المعلم النصي
    lesson_summary = models.TextField(
        blank=True,
        verbose_name=_('ملخص الحصة'),
        help_text=_('ملخص مختصر عما تم تدريسه في الحصة')
    )

    strengths = models.TextField(
        blank=True,
        verbose_name=_('نقاط القوة'),
        help_text=_('نقاط القوة التي لاحظها المعلم على الطالب')
    )

    areas_for_improvement = models.TextField(
        blank=True,
        verbose_name=_('نقاط تحتاج تحسين'),
        help_text=_('النقاط التي يحتاج الطالب لتحسينها')
    )

    homework_assigned = models.TextField(
        blank=True,
        verbose_name=_('الواجبات المطلوبة'),
        help_text=_('الواجبات أو المهام المطلوبة من الطالب')
    )

    recommendations = models.TextField(
        blank=True,
        verbose_name=_('توصيات للحصص القادمة'),
        help_text=_('توصيات لتحسين الحصص القادمة')
    )

    teacher_additional_notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات إضافية من المعلم'),
        help_text=_('ملاحظات إضافية من المعلم')
    )

    # معلومات إضافية
    lesson_duration_minutes = models.PositiveIntegerField(
        null=True, blank=True,
        verbose_name=_('مدة الحصة الفعلية'),
        help_text=_('المدة الفعلية للحصة بالدقائق')
    )

    # حالة التقييم والتقرير
    STATUS_CHOICES = [
        ('student_only', _('تقييم طالب فقط')),
        ('teacher_only', _('تقرير معلم فقط')),
        ('complete', _('مكتمل (تقييم + تقرير)')),
        ('reviewed', _('تمت المراجعة')),
    ]

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='student_only',
        verbose_name=_('حالة التقييم والتقرير')
    )

    # تواريخ النظام
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ آخر تحديث')
    )

    class Meta:
        verbose_name = _('تقييم الحصة')
        verbose_name_plural = _('تقييمات الحصص')
        ordering = ['-created_at']
        # ضمان عدم تكرار التقييم لنفس الطالب في نفس الحصة
        unique_together = [
            ['live_lesson', 'student'],      # طالب واحد لكل حصة مباشرة
        ]

    def __str__(self):
        lesson_info = self.get_lesson_info()
        return f"تقييم {lesson_info['title']} - {self.student.get_full_name()} ({self.overall_rating} نجوم)"

    @property
    def lesson_type(self):
        """تحديد نوع الحصة"""
        if self.live_lesson:
            return 'live'
        return None

    @property
    def lesson_object(self):
        """الحصول على كائن الحصة"""
        if self.live_lesson:
            return self.live_lesson
        return None

    def get_lesson_info(self):
        """الحصول على معلومات الحصة"""
        lesson = self.lesson_object
        if not lesson:
            return {'title': 'حصة غير محددة', 'date': None}

        if self.lesson_type == 'live':
            return {
                'title': lesson.title,
                'date': lesson.scheduled_date,
                'duration': lesson.duration_minutes
            }

        return {'title': 'حصة غير محددة', 'date': None}

    def calculate_average_rating(self):
        """حساب متوسط التقييم"""
        ratings = [
            self.overall_rating,
            self.lesson_quality,
            self.teacher_interaction,
            self.technical_quality
        ]

        # إضافة التقييمات الاختيارية إذا كانت موجودة
        if self.punctuality:
            ratings.append(self.punctuality)
        if self.lesson_preparation:
            ratings.append(self.lesson_preparation)

        return round(sum(ratings) / len(ratings), 2)

    @property
    def average_rating(self):
        """حساب متوسط التقييم - للتوافق مع النظام القديم"""
        return self.calculate_average_rating()

    def get_rating_breakdown(self):
        """الحصول على تفصيل التقييمات"""
        return {
            'overall': self.overall_rating,
            'lesson_quality': self.lesson_quality,
            'teacher_interaction': self.teacher_interaction,
            'technical_quality': self.technical_quality,
            'punctuality': self.punctuality,
            'lesson_preparation': self.lesson_preparation,
            'average': self.calculate_average_rating()
        }

    def is_positive_rating(self):
        """فحص ما إذا كان التقييم إيجابي (4 نجوم أو أكثر)"""
        return self.calculate_average_rating() >= 4.0

    def get_quality_level(self):
        """تحديد مستوى الجودة"""
        avg = self.calculate_average_rating()
        if avg >= 4.5:
            return 'excellent'
        elif avg >= 4.0:
            return 'very_good'
        elif avg >= 3.0:
            return 'good'
        elif avg >= 2.0:
            return 'fair'
        else:
            return 'poor'

    def has_teacher_report(self):
        """التحقق من وجود تقرير المعلم"""
        return bool(self.student_performance and self.lesson_summary)

    def has_student_rating(self):
        """التحقق من وجود تقييم الطالب"""
        return bool(self.overall_rating)

    def is_complete(self):
        """التحقق من اكتمال التقييم والتقرير"""
        return self.has_teacher_report() and self.has_student_rating()

    def update_status(self):
        """تحديث حالة التقييم بناءً على المحتوى"""
        if self.is_complete():
            self.status = 'complete'
        elif self.has_teacher_report():
            self.status = 'teacher_only'
        elif self.has_student_rating():
            self.status = 'student_only'

    def get_teacher_average_rating(self):
        """حساب متوسط تقييم المعلم للطالب"""
        if not self.has_teacher_report():
            return None

        ratings = [self.student_performance, self.student_participation, self.student_understanding]
        return round(sum(ratings) / len(ratings), 2)

    def save(self, *args, **kwargs):
        """حفظ التقييم مع تحديث حالة الحصة"""
        is_new = self.pk is None

        # تحديث حالة التقييم
        self.update_status()

        super().save(*args, **kwargs)

        # تحديث حالة الحصة
        if self.live_lesson.status == 'completed':
            if self.has_teacher_report():  # تقرير المعلم إجباري لاحتساب الحصة
                self.live_lesson.status = 'rated'
                self.live_lesson.save()

        # إرسال إشعارات
        if is_new:
            try:
                from notifications.utils import NotificationService
                if self.has_student_rating():
                    NotificationService.notify_lesson_rated(self)
                if self.has_teacher_report():
                    NotificationService.notify_teacher_report_submitted(self)
            except Exception as e:
                print(f"خطأ في إرسال إشعار التقييم: {e}")


# للتوافق مع النظام القديم - alias للنموذج الموحد
LessonRating = UnifiedLessonRating


# ===== نماذج نظام مراقبة الجودة =====














