{% extends 'base.html' %}
{% load static %}

{% block title %}{{ message.subject }}{% endblock %}

{% block extra_css %}
<style>
    .message-detail {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
    }
    .message-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .reply-card {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        border-right: 4px solid #3b82f6;
    }
    .sender-avatar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="message-detail">
    <div class="container mx-auto px-4 py-8">

        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="{% url 'notifications:message_list' %}"
                       class="bg-gray-100 text-gray-600 p-2 rounded-lg hover:bg-gray-200 transition-colors ml-4">
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <div>
                        <h1 class="text-2xl font-bold text-islamic-primary">{{ message.subject }}</h1>
                        <p class="text-gray-600">تفاصيل الرسالة</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2 space-x-reverse">
                    {% if message.message_type == 'support' %}
                        <span class="bg-orange-100 text-orange-800 text-sm px-3 py-1 rounded-full">رسالة دعم</span>
                    {% elif message.message_type == 'announcement' %}
                        <span class="bg-red-100 text-red-800 text-sm px-3 py-1 rounded-full">إعلان</span>
                    {% else %}
                        <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full">رسالة خاصة</span>
                    {% endif %}

                    <a href="{% url 'notifications:reply_message' message.id %}"
                       class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-secondary transition-colors">
                        <i class="fas fa-reply ml-2"></i>
                        رد
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Message -->
        <div class="message-card rounded-xl shadow-lg p-8 mb-8">
            <!-- Sender Info -->
            <div class="flex items-center mb-6 pb-6 border-b border-gray-200">
                <div class="sender-avatar w-16 h-16 rounded-full flex items-center justify-center text-white ml-4">
                    {% if message.sender.user_type == 'admin' %}
                        <i class="fas fa-crown text-2xl"></i>
                    {% elif message.sender.user_type == 'teacher' %}
                        <i class="fas fa-chalkboard-teacher text-2xl"></i>
                    {% else %}
                        <i class="fas fa-user-graduate text-2xl"></i>
                    {% endif %}
                </div>

                <div class="flex-1">
                    <h3 class="text-xl font-bold text-gray-900">{{ message.sender.get_full_name }}</h3>
                    <p class="text-gray-600">
                        {% if message.sender.user_type == 'admin' %}
                            مدير النظام
                        {% elif message.sender.user_type == 'teacher' %}
                            معلم
                        {% else %}
                            طالب
                        {% endif %}
                    </p>
                    <p class="text-sm text-gray-500 mt-1">
                        <i class="fas fa-clock ml-1"></i>
                        {{ message.created_at|date:"Y/m/d H:i" }} ({{ message.created_at|timesince }} مضت)
                    </p>
                </div>

                <div class="text-left">
                    {% if message.is_read %}
                        <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full">
                            <i class="fas fa-check-double ml-1"></i>
                            مقروءة
                        </span>
                    {% else %}
                        <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                            <i class="fas fa-envelope ml-1"></i>
                            جديدة
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- Message Content -->
            <div class="prose max-w-none">
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="text-gray-800 leading-relaxed text-lg">{{ message.content|safe }}</div>
                </div>
            </div>

            <!-- Message Actions -->
            <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-sm text-gray-500">
                        <i class="fas fa-envelope ml-1"></i>
                        رقم الرسالة: #{{ message.id }}
                    </span>
                    {% if message.read_at %}
                        <span class="text-sm text-gray-500">
                            <i class="fas fa-eye ml-1"></i>
                            تم القراءة: {{ message.read_at|date:"Y/m/d H:i" }}
                        </span>
                    {% endif %}
                </div>

                <div class="flex items-center space-x-2 space-x-reverse">
                    <button onclick="window.print()"
                            class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-print ml-2"></i>
                        طباعة
                    </button>

                    <a href="{% url 'notifications:reply_message' message.id %}"
                       class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-secondary transition-colors">
                        <i class="fas fa-reply ml-2"></i>
                        رد على الرسالة
                    </a>
                </div>
            </div>
        </div>

        <!-- Replies -->
        {% if replies %}
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-6">
                <i class="fas fa-comments text-blue-600 ml-2"></i>
                الردود ({{ replies|length }})
            </h2>

            <div class="space-y-6">
                {% for reply in replies %}
                <div class="reply-card rounded-lg p-6">
                    <!-- Reply Header -->
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full flex items-center justify-center text-white ml-4
                                    {% if reply.sender.user_type == 'admin' %}bg-yellow-500{% endif %}
                                    {% if reply.sender.user_type == 'teacher' %}bg-green-500{% endif %}
                                    {% if reply.sender.user_type == 'student' %}bg-blue-500{% endif %}">
                            {% if reply.sender.user_type == 'admin' %}
                                <i class="fas fa-crown"></i>
                            {% elif reply.sender.user_type == 'teacher' %}
                                <i class="fas fa-chalkboard-teacher"></i>
                            {% else %}
                                <i class="fas fa-user-graduate"></i>
                            {% endif %}
                        </div>

                        <div class="flex-1">
                            <h4 class="font-bold text-gray-900">{{ reply.sender.get_full_name }}</h4>
                            <p class="text-sm text-gray-600">{{ reply.created_at|date:"Y/m/d H:i" }}</p>
                        </div>

                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">رد</span>
                    </div>

                    <!-- Reply Content -->
                    <div class="bg-white rounded-lg p-4">
                        <p class="text-gray-800 leading-relaxed whitespace-pre-line">{{ reply.content }}</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Quick Reply -->
        <div class="bg-white rounded-xl shadow-lg p-6 mt-8">
            <h3 class="text-lg font-bold text-gray-900 mb-4">
                <i class="fas fa-reply text-green-600 ml-2"></i>
                رد سريع
            </h3>

            <form method="post" action="{% url 'notifications:reply_message' message.id %}">
                {% csrf_token %}
                <div class="mb-4">
                    <textarea name="content"
                              rows="4"
                              placeholder="اكتب ردك هنا..."
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent resize-none"
                              required></textarea>
                </div>

                <div class="flex items-center justify-between">
                    <p class="text-sm text-gray-500">
                        <i class="fas fa-info-circle ml-1"></i>
                        سيتم إرسال إشعار للمرسل الأصلي
                    </p>

                    <button type="submit"
                            class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال الرد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Mark message as read automatically after 3 seconds
setTimeout(function() {
    // You can add AJAX call here to mark as read
}, 3000);

// Auto-expand textarea
document.querySelector('textarea[name="content"]').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
</script>
{% endblock %}
