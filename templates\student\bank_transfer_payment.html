{% extends 'base.html' %}
{% load static %}

{% block title %}دفع بالتحويل البنكي{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-islamic-primary mb-4">
                <i class="fas fa-university text-islamic-gold ml-3"></i>
                دفع بالتحويل البنكي
            </h1>
            <p class="text-gray-600">قم بتحويل المبلغ ثم ارفع إثبات التحويل</p>
        </div>

        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Bank Information -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold text-islamic-primary mb-4">
                        <i class="fas fa-info-circle ml-2"></i>
                        معلومات الحساب البنكي
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-700 font-medium">اسم البنك:</span>
                                <span class="font-semibold">{{ bank_info.bank_name }}</span>
                            </div>
                            
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-700 font-medium">رقم الحساب:</span>
                                <span class="font-semibold font-mono">{{ bank_info.account_number }}</span>
                                <button onclick="copyToClipboard('{{ bank_info.account_number }}')" 
                                        class="text-islamic-primary hover:text-islamic-light">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-700 font-medium">اسم الحساب:</span>
                                <span class="font-semibold">{{ bank_info.account_name }}</span>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 font-medium">IBAN:</span>
                                <span class="font-semibold font-mono">{{ bank_info.iban }}</span>
                                <button onclick="copyToClipboard('{{ bank_info.iban }}')" 
                                        class="text-islamic-primary hover:text-islamic-light">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h4 class="font-semibold text-yellow-800 mb-2">
                                <i class="fas fa-exclamation-triangle ml-2"></i>
                                تعليمات مهمة
                            </h4>
                            <ul class="text-yellow-700 text-sm space-y-1">
                                <li>• قم بتحويل المبلغ المطلوب بالضبط: <strong>{{ payment.amount }} ريال</strong></li>
                                <li>• احتفظ بإيصال التحويل الأصلي</li>
                                <li>• تأكد من وضوح جميع البيانات في الإيصال</li>
                                <li>• سيتم تفعيل اشتراكك خلال 24 ساعة من التحقق</li>
                            </ul>
                        </div>
                        
                        <div class="text-center">
                            <div class="text-2xl font-bold text-islamic-primary">
                                المبلغ المطلوب: {{ payment.amount }} ريال
                            </div>
                            <p class="text-gray-600 text-sm mt-1">
                                للباقة: {{ payment.subscription.plan.name }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Upload Transfer Proof -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold text-islamic-primary mb-4">
                        <i class="fas fa-upload ml-2"></i>
                        رفع إثبات التحويل
                    </h3>
                    
                    <form method="POST" enctype="multipart/form-data" class="space-y-4">
                        {% csrf_token %}
                        
                        <!-- Transfer Receipt -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                إيصال التحويل <span class="text-red-500">*</span>
                            </label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-islamic-primary transition-colors">
                                <input type="file" name="transfer_receipt" accept="image/*" required 
                                       class="hidden" id="receipt-upload">
                                <label for="receipt-upload" class="cursor-pointer">
                                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-600">اضغط لرفع صورة الإيصال</p>
                                    <p class="text-sm text-gray-500">PNG, JPG, JPEG (حد أقصى 5MB)</p>
                                </label>
                            </div>
                            <div id="file-preview" class="mt-2 hidden">
                                <img id="preview-image" class="max-w-full h-32 object-cover rounded-lg">
                            </div>
                        </div>
                        
                        <!-- Sender Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                اسم المرسل <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="sender_name" required 
                                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="اسم صاحب الحساب المرسل">
                        </div>
                        
                        <!-- Transfer Amount -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                مبلغ التحويل <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="transfer_amount" step="0.01" required 
                                   value="{{ payment.amount }}"
                                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="المبلغ المحول">
                        </div>
                        
                        <!-- Transfer Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                تاريخ التحويل <span class="text-red-500">*</span>
                            </label>
                            <input type="date" name="transfer_date" required 
                                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        </div>
                        
                        <!-- Bank Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                اسم البنك المرسل <span class="text-red-500">*</span>
                            </label>
                            <select name="bank_name" required 
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="">اختر البنك</option>
                                <option value="البنك الأهلي السعودي">البنك الأهلي السعودي</option>
                                <option value="بنك الراجحي">بنك الراجحي</option>
                                <option value="بنك الرياض">بنك الرياض</option>
                                <option value="البنك السعودي للاستثمار">البنك السعودي للاستثمار</option>
                                <option value="البنك السعودي الفرنسي">البنك السعودي الفرنسي</option>
                                <option value="بنك ساب">بنك ساب</option>
                                <option value="البنك العربي الوطني">البنك العربي الوطني</option>
                                <option value="بنك الجزيرة">بنك الجزيرة</option>
                                <option value="بنك الإنماء">بنك الإنماء</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        
                        <!-- Reference Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                رقم المرجع (اختياري)
                            </label>
                            <input type="text" name="reference_number" 
                                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="رقم المرجع إن وجد">
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="space-y-3">
                            <button type="submit" 
                                    class="w-full bg-islamic-primary hover:bg-islamic-light text-white py-3 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-check ml-2"></i>
                                إرسال إثبات التحويل
                            </button>
                            
                            <a href="{% url 'student_subscriptions' %}" 
                               class="block w-full text-center bg-gray-300 hover:bg-gray-400 text-gray-700 py-3 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-arrow-right ml-2"></i>
                                العودة للاشتراكات
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Process Timeline -->
        <div class="max-w-4xl mx-auto mt-8">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-islamic-primary mb-4">
                    <i class="fas fa-clock ml-2"></i>
                    خطوات معالجة الدفع
                </h3>
                
                <div class="flex items-center justify-between">
                    <div class="flex flex-col items-center text-center">
                        <div class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-university"></i>
                        </div>
                        <span class="text-sm font-medium">تحويل المبلغ</span>
                    </div>
                    
                    <div class="flex-1 h-1 bg-green-500 mx-2"></div>
                    
                    <div class="flex flex-col items-center text-center">
                        <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-upload"></i>
                        </div>
                        <span class="text-sm font-medium">رفع الإثبات</span>
                    </div>
                    
                    <div class="flex-1 h-1 bg-gray-300 mx-2"></div>
                    
                    <div class="flex flex-col items-center text-center">
                        <div class="w-10 h-10 bg-gray-300 text-white rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-search"></i>
                        </div>
                        <span class="text-sm font-medium">مراجعة الطلب</span>
                    </div>
                    
                    <div class="flex-1 h-1 bg-gray-300 mx-2"></div>
                    
                    <div class="flex flex-col items-center text-center">
                        <div class="w-10 h-10 bg-gray-300 text-white rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-check"></i>
                        </div>
                        <span class="text-sm font-medium">تفعيل الاشتراك</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
        toast.textContent = 'تم نسخ النص';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    });
}

// File preview
document.getElementById('receipt-upload').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-image').src = e.target.result;
            document.getElementById('file-preview').classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
});

// Set today's date as default
document.querySelector('input[name="transfer_date"]').value = new Date().toISOString().split('T')[0];
</script>
{% endblock %}
