# Generated by Django 4.2.7 on 2025-06-07 02:26

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_academysettings_smtp_host_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='academysettings',
            name='email_api_key',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='email_api_region',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='email_api_secret',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='email_from_email',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='email_from_name',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='email_reply_to',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='email_service_enabled',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='email_service_provider',
        ),
    ]
