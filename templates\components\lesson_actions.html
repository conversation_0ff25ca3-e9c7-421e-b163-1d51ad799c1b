<!-- أزرار إجراءات الحصة -->
<div class="flex items-center space-x-2 space-x-reverse">
    {% load math_filters %}
    
    <!-- حساب الوقت المتبقي -->
    {% now "Y-m-d H:i:s" as current_time %}
    {% if lesson.scheduled_date|date:"Y-m-d H:i:s" > current_time %}
        {% with time_diff=lesson.scheduled_date|timeuntil %}
            <!-- إجراءات الحصص المجدولة -->
            {% if lesson.status == 'scheduled' %}
                
                <!-- دخول الحصة (إذا حان الوقت أو قريب) -->
                {% if lesson.scheduled_date|time_until_minutes <= 5 %}
                    <a href="{% url 'lesson_room' lesson.id %}" 
                       class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-lg text-xs transition-colors">
                        <i class="fas fa-video ml-1"></i>
                        دخول الحصة
                    </a>
                {% elif lesson.scheduled_date|time_until_minutes <= 15 %}
                    <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-lg text-xs">
                        <i class="fas fa-clock ml-1"></i>
                        {{ lesson.scheduled_date|time_until_minutes }} دقيقة
                    </span>
                {% endif %}
                
                <!-- إجراءات حسب نوع المستخدم -->
                {% if user_type == 'admin' %}
                    <!-- إجراءات المدير -->
                    <div class="relative inline-block">
                        <button onclick="toggleDropdown('actions-{{ lesson.id }}')" 
                                class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-xs transition-colors">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div id="actions-{{ lesson.id }}" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
                            <a href="{% url 'admin_edit_lesson' lesson.id %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-edit ml-2"></i>
                                تعديل الحصة
                            </a>
                            <button onclick="cancelLesson({{ lesson.id }})" class="block w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-times ml-2"></i>
                                إلغاء الحصة
                            </button>
                            <button onclick="rescheduleLesson({{ lesson.id }})" class="block w-full text-right px-4 py-2 text-sm text-blue-600 hover:bg-blue-50">
                                <i class="fas fa-calendar-alt ml-2"></i>
                                إعادة جدولة
                            </button>
                        </div>
                    </div>
                    
                {% elif user_type == 'teacher' %}
                    <!-- إجراءات المعلم -->
                    {% if lesson.scheduled_date|time_until_minutes <= 30 %}
                        <button onclick="prepareForLesson({{ lesson.id }})" 
                                class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg text-xs transition-colors">
                            <i class="fas fa-clipboard-check ml-1"></i>
                            استعداد
                        </button>
                    {% endif %}
                    
                    <div class="relative inline-block">
                        <button onclick="toggleDropdown('teacher-actions-{{ lesson.id }}')" 
                                class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-xs transition-colors">
                            <i class="fas fa-cog"></i>
                        </button>
                        <div id="teacher-actions-{{ lesson.id }}" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
                            <button onclick="requestReschedule({{ lesson.id }})" class="block w-full text-right px-4 py-2 text-sm text-blue-600 hover:bg-blue-50">
                                <i class="fas fa-calendar-alt ml-2"></i>
                                طلب إعادة جدولة
                            </button>
                            <button onclick="requestCancel({{ lesson.id }})" class="block w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-times ml-2"></i>
                                طلب إلغاء
                            </button>
                        </div>
                    </div>
                    
                {% elif user_type == 'student' %}
                    <!-- إجراءات الطالب -->
                    <div class="relative inline-block">
                        <button onclick="toggleDropdown('student-actions-{{ lesson.id }}')" 
                                class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-xs transition-colors">
                            <i class="fas fa-user-cog"></i>
                        </button>
                        <div id="student-actions-{{ lesson.id }}" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
                            <button onclick="requestReschedule({{ lesson.id }})" class="block w-full text-right px-4 py-2 text-sm text-blue-600 hover:bg-blue-50">
                                <i class="fas fa-calendar-alt ml-2"></i>
                                طلب إعادة جدولة
                            </button>
                            <button onclick="requestCancel({{ lesson.id }})" class="block w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-times ml-2"></i>
                                طلب إلغاء
                            </button>
                            {% if lesson.lesson_type == 'subscription' %}
                                <button onclick="requestMakeup({{ lesson.id }})" class="block w-full text-right px-4 py-2 text-sm text-green-600 hover:bg-green-50">
                                    <i class="fas fa-redo ml-2"></i>
                                    طلب حصة تعويضية
                                </button>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
                
            {% endif %}
        {% endwith %}
    {% endif %}
    
    <!-- إجراءات الحصص المكتملة -->
    {% if lesson.status == 'completed' %}
        {% if user_type == 'teacher' and not lesson.teacher_report_submitted %}
            <button onclick="submitTeacherReport({{ lesson.id }})" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg text-xs transition-colors">
                <i class="fas fa-file-alt ml-1"></i>
                كتابة التقرير
            </button>
        {% elif user_type == 'student' and not lesson.student_evaluation_submitted %}
            <button onclick="submitStudentEvaluation({{ lesson.id }})" 
                    class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-lg text-xs transition-colors">
                <i class="fas fa-star ml-1"></i>
                تقييم الحصة
            </button>
        {% endif %}
    {% endif %}
    
    <!-- إجراءات الحصص الجارية -->
    {% if lesson.status == 'live' %}
        {% if user_type == 'teacher' %}
            <button onclick="endLesson({{ lesson.id }})" 
                    class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-xs transition-colors">
                <i class="fas fa-stop ml-1"></i>
                إنهاء الحصة
            </button>
        {% endif %}
        
        <a href="{% url 'lesson_room' lesson.id %}" 
           class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-lg text-xs transition-colors">
            <i class="fas fa-video ml-1"></i>
            دخول الحصة
        </a>
    {% endif %}
    
    <!-- عرض التفاصيل -->
    <button onclick="showLessonDetails({{ lesson.id }})" 
            class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-xs transition-colors">
        <i class="fas fa-eye ml-1"></i>
        التفاصيل
    </button>
</div>
