"""
خدمة إدارة بوابات الدفع
"""
from django.core.cache import cache
from .models import PaymentGatewaySettings


class PaymentGatewayService:
    """خدمة موحدة لإدارة بوابات الدفع"""
    
    CACHE_KEY = 'payment_gateway_settings'
    CACHE_TIMEOUT = 300  # 5 دقائق
    
    def __init__(self):
        self.settings = self.get_payment_settings()
    
    def get_payment_settings(self):
        """الحصول على إعدادات الدفع مباشرة من قاعدة البيانات"""
        try:
            # قراءة مباشرة من قاعدة البيانات بدون تخزين مؤقت
            settings = PaymentGatewaySettings.objects.get(pk=1)
            # إعادة تحميل البيانات من قاعدة البيانات للتأكد من الحداثة
            settings.refresh_from_db()
            return settings
        except PaymentGatewaySettings.DoesNotExist:
            # إنشاء إعدادات جديدة إذا لم تكن موجودة
            settings = PaymentGatewaySettings.objects.create(
                pk=1,
                paypal_enabled=False,
                stripe_enabled=False,
                bank_transfer_enabled=True
            )
            return settings
        except Exception:
            return None
    
    def clear_cache(self):
        """مسح التخزين المؤقت للإعدادات وإعادة تحميل البيانات"""
        cache.delete(self.CACHE_KEY)
        # إعادة تحميل الإعدادات من قاعدة البيانات
        self.settings = self.get_payment_settings()
    
    def is_paypal_enabled(self):
        """التحقق من تفعيل PayPal"""
        if not self.settings:
            return False
        # التحقق من التفعيل فقط، بدون التحقق من البيانات الإضافية
        return self.settings.paypal_enabled
    
    def is_stripe_enabled(self):
        """التحقق من تفعيل Stripe"""
        if not self.settings:
            return False
        # التحقق من التفعيل فقط، بدون التحقق من البيانات الإضافية
        return self.settings.stripe_enabled
    
    def is_bank_transfer_enabled(self):
        """التحقق من تفعيل التحويل البنكي"""
        if not self.settings:
            return False
        # التحقق من التفعيل فقط، بدون التحقق من البيانات الإضافية
        return self.settings.bank_transfer_enabled

    def is_paypal_configured(self):
        """التحقق من اكتمال إعدادات PayPal"""
        if not self.settings:
            return False
        return (self.settings.paypal_enabled and
                self.settings.paypal_client_id and
                self.settings.paypal_client_secret)

    def is_stripe_configured(self):
        """التحقق من اكتمال إعدادات Stripe"""
        if not self.settings:
            return False
        return (self.settings.stripe_enabled and
                self.settings.stripe_publishable_key and
                self.settings.stripe_secret_key)

    def is_bank_transfer_configured(self):
        """التحقق من اكتمال إعدادات التحويل البنكي"""
        if not self.settings:
            return False
        return (self.settings.bank_transfer_enabled and
                self.settings.bank_account_number)

    def is_payment_method_enabled(self, method):
        """التحقق من تفعيل وسيلة دفع محددة"""
        if method == 'paypal':
            return self.is_paypal_enabled()
        elif method == 'stripe':
            return self.is_stripe_enabled()
        elif method == 'bank_transfer':
            return self.is_bank_transfer_enabled()
        return False
    
    def get_available_payment_methods(self):
        """الحصول على وسائل الدفع المتاحة"""
        methods = []
        
        if self.is_paypal_enabled():
            methods.append({
                'id': 'paypal',
                'name': 'PayPal',
                'icon': 'fab fa-paypal',
                'color': 'blue',
                'enabled': True
            })
        
        if self.is_stripe_enabled():
            methods.append({
                'id': 'stripe',
                'name': 'Stripe',
                'icon': 'fab fa-stripe',
                'color': 'purple',
                'enabled': True
            })
        
        if self.is_bank_transfer_enabled():
            methods.append({
                'id': 'bank_transfer',
                'name': 'التحويل البنكي',
                'icon': 'fas fa-university',
                'color': 'green',
                'enabled': True
            })
        
        return methods
    
    def get_all_payment_methods(self):
        """الحصول على جميع وسائل الدفع مع حالة التفعيل"""
        all_methods = [
            {
                'id': 'paypal',
                'name': 'PayPal',
                'icon': 'fab fa-paypal',
                'color': 'blue',
                'enabled': self.is_paypal_enabled()
            },
            {
                'id': 'stripe',
                'name': 'Stripe',
                'icon': 'fab fa-stripe',
                'color': 'purple',
                'enabled': self.is_stripe_enabled()
            },
            {
                'id': 'bank_transfer',
                'name': 'التحويل البنكي',
                'icon': 'fas fa-university',
                'color': 'green',
                'enabled': self.is_bank_transfer_enabled()
            }
        ]
        return all_methods
    
    def get_bank_details(self):
        """الحصول على تفاصيل البنك"""
        if not self.settings or not self.is_bank_transfer_enabled():
            return None
            
        return {
            'bank_name': self.settings.bank_name,
            'account_number': self.settings.bank_account_number,
            'account_name': self.settings.bank_account_name,
            'iban': self.settings.bank_iban,
            'swift_code': self.settings.bank_swift_code,
            'branch': self.settings.bank_branch
        }
    
    def get_paypal_config(self):
        """الحصول على إعدادات PayPal"""
        if not self.settings or not self.is_paypal_enabled():
            return None
            
        return {
            'client_id': self.settings.paypal_client_id,
            'client_secret': self.settings.paypal_client_secret,
            'sandbox_mode': self.settings.paypal_sandbox_mode,
            'webhook_id': self.settings.paypal_webhook_id
        }
    
    def get_stripe_config(self):
        """الحصول على إعدادات Stripe"""
        if not self.settings or not self.is_stripe_enabled():
            return None
            
        return {
            'publishable_key': self.settings.stripe_publishable_key,
            'secret_key': self.settings.stripe_secret_key,
            'webhook_secret': self.settings.stripe_webhook_secret
        }
    
    def get_disabled_payment_message(self, method):
        """الحصول على رسالة التعطيل لوسيلة دفع محددة"""
        messages = {
            'paypal': 'الدفع عن طريق PayPal غير متاح في الوقت الحالي. يرجى اختيار وسيلة دفع أخرى أو التواصل مع الإدارة.',
            'stripe': 'الدفع عن طريق Stripe غير متاح في الوقت الحالي. يرجى اختيار وسيلة دفع أخرى أو التواصل مع الإدارة.',
            'bank_transfer': 'التحويل البنكي غير متاح في الوقت الحالي. يرجى اختيار وسيلة دفع أخرى أو التواصل مع الإدارة.'
        }
        return messages.get(method, 'وسيلة الدفع هذه غير متاحة في الوقت الحالي.')


# إنشاء instance عام للاستخدام
payment_service = PaymentGatewayService()
