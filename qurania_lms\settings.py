"""
Django settings for qurania_lms project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-$&p2ktluat5awmss_$s*+11l+(339r_*9x1s01=b4q4=ykfc0p'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True  # سيتم تغييره في settings_production.py

ALLOWED_HOSTS = ['127.0.0.1', 'localhost', '***********', '************', '0.0.0.0', '*']  # للتطوير المحلي


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third party apps
    'crispy_forms',
    'crispy_bootstrap5',
    'django_extensions',
    # 'django_celery_beat',  # Disabled for production
    # 'django_celery_results',  # Disabled for production

    # Local apps
    'users',
    'lessons',
    'notifications',
    'support',
    'messaging',  # سيتم دمجه مع notifications لاحقاً
    'reports',
    'subscriptions',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',  # تم تقديمه ليكون قبل middleware المخصصة
    'users.middleware.DatabaseLockMiddleware',  # معالجة مشاكل قفل قاعدة البيانات
    'users.middleware.UserStatusMiddleware',  # تتبع حالة المستخدمين
    'users.middleware.UserVerificationMiddleware',  # التحقق من حالة التحقق
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'qurania_lms.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'qurania_lms.context_processors.global_context',
            ],
        },
    },
]

WSGI_APPLICATION = 'qurania_lms.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
        'OPTIONS': {
            'timeout': 30,  # زيادة مهلة الانتظار لتجنب قفل قاعدة البيانات
        },
    }
}

# تحسينات قاعدة البيانات معطلة للإنتاج


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'ar'

TIME_ZONE = 'Asia/Riyadh'

USE_I18N = True

USE_TZ = True


# Jitsi Meet Configuration
# =========================

# خوادم Jitsi المجانية البديلة (بدون حد زمني للـ embedding)
JITSI_ALTERNATIVE_DOMAINS = [
    'meet.ffmuc.net',      # خادم ألماني مجاني
    'meet.golem.de',       # خادم ألماني آخر
    'jitsi.riot.im',       # خادم Element/Matrix
    'meet.element.io',     # خادم Element الجديد
    'jitsi.member.fsf.org', # خادم Free Software Foundation
]

# الخادم الأساسي (بدون حد زمني)
JITSI_DOMAIN = 'meet.ffmuc.net'  # خادم ألماني مجاني وموثوق

# للإنتاج: استخدام JaaS (Jitsi as a Service) - اختياري
JITSI_JAAS_DOMAIN = None  # مثال: 'your-tenant.jaas-8x8.vc'
JITSI_JAAS_APP_ID = None  # App ID من JaaS
JITSI_JAAS_PRIVATE_KEY = None  # Private Key من JaaS

# إعدادات إضافية للـ embedding
JITSI_CONFIG = {
    'startWithAudioMuted': True,
    'startWithVideoMuted': False,
    'enableWelcomePage': False,
    'enableUserRolesBasedOnToken': True,
    'enableNoAudioSignal': True,
    'enableNoisyMicDetection': True,
    'disableThirdPartyRequests': True,
    'enableLayerSuspension': True,
    'channelLastN': -1,  # عرض جميع المشاركين
    'prejoinPageEnabled': False,  # تخطي صفحة ما قبل الانضمام
    'requireDisplayName': False,  # عدم طلب اسم العرض
    'enableInsecureRoomNameWarning': False,  # إزالة تحذيرات الأمان
    'disableProfile': False,  # السماح بالملف الشخصي
    'enableAutomaticUrlCopy': False,  # منع نسخ الرابط تلقائياً
}

# إعدادات Celery معطلة للإنتاج

# إعدادات التشفير للخدمات الخارجية
EMAIL_ENCRYPTION_KEY = b'ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg='  # مفتاح تشفير صالح
# للتوافق مع النظام القديم
SMTP_ENCRYPTION_KEY = EMAIL_ENCRYPTION_KEY

# ==================== إعدادات WhatsApp ====================
# سيتم إضافة إعدادات WhatsApp الجديدة هنا

# URL الموقع للتتبع
SITE_URL = 'http://localhost:8000'

# Internationalization
LANGUAGES = [
    ('ar', 'العربية'),
    ('en', 'English'),
]

LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = '/static/'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]
STATIC_ROOT = BASE_DIR / 'staticfiles'

# إعدادات إضافية للإنتاج
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# ضغط الملفات الثابتة
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.ManifestStaticFilesStorage'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5 MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5 MB
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom User Model
AUTH_USER_MODEL = 'users.User'

# Crispy Forms
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# Login/Logout URLs
LOGIN_URL = '/login/'
LOGIN_REDIRECT_URL = '/dashboard/'
LOGOUT_REDIRECT_URL = '/login/'

# Authentication Backends
AUTHENTICATION_BACKENDS = [
    'users.backends.EmailOrUsernameModelBackend',
    'django.contrib.auth.backends.ModelBackend',
]

# Email Configuration (for notifications)
# استخدام خدمة SMTP البسيطة لجميع رسائل Django
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

# إعدادات إضافية لإعادة تعيين كلمة المرور
PASSWORD_RESET_TIMEOUT = 86400  # 24 ساعة

# معرف التطبيق
JITSI_APP_ID = 'qurania_lms'

# Session Configuration
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_EXPIRE_AT_BROWSER_CLOSE = True

# User Status Configuration
USER_ONLINE_TIMEOUT = 5  # minutes
USER_STATUS_CLEANUP_INTERVAL = 60  # seconds

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
    },
    'loggers': {
        'django.request': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
