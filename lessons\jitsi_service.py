"""
خدمة Jitsi Meet مع دعم JaaS (Jitsi as a Service)
"""

import jwt
import json
import requests
from datetime import datetime, timedelta, timezone
from django.conf import settings


class JitsiService:
    """خدمة إدارة Jitsi Meet مع دعم JaaS"""

    @staticmethod
    def generate_jwt_token(room_name, user_name, user_email, is_moderator=False):
        """إنشاء JWT token لـ JaaS"""

        if not all([
            getattr(settings, 'JITSI_JAAS_APP_ID', None),
            getattr(settings, 'JITSI_JAAS_PRIVATE_KEY', None)
        ]):
            return None

        # إعداد payload
        now = datetime.now(timezone.utc)
        payload = {
            "iss": "chat",
            "aud": "jitsi",
            "exp": int((now + timedelta(hours=2)).timestamp()),  # انتهاء بعد ساعتين
            "nbf": int((now - timedelta(minutes=5)).timestamp()),  # صالح من 5 دقائق مضت
            "sub": settings.JITSI_JAAS_APP_ID,
            "room": room_name,
            "context": {
                "user": {
                    "id": user_email,
                    "name": user_name,
                    "email": user_email,
                    "moderator": str(is_moderator).lower(),
                    "avatar": "",
                },
                "features": {
                    "livestreaming": str(is_moderator).lower(),
                    "recording": str(is_moderator).lower(),
                    "transcription": str(is_moderator).lower(),
                    "outbound-call": str(is_moderator).lower(),
                }
            }
        }

        # إنشاء التوكن
        try:
            token = jwt.encode(
                payload,
                settings.JITSI_JAAS_PRIVATE_KEY,
                algorithm="RS256",
                headers={"kid": settings.JITSI_JAAS_APP_ID}
            )
            return token
        except Exception as e:
            print(f"خطأ في إنشاء JWT token: {e}")
            return None

    @staticmethod
    def get_jitsi_url_with_token(room_id, user, is_moderator=False):
        """الحصول على رابط Jitsi مع JWT token"""
        
        # التحقق من استخدام JaaS
        if not getattr(settings, 'JITSI_JAAS_DOMAIN', None):
            # استخدام الخادم العادي بدون token
            domain = getattr(settings, 'JITSI_DOMAIN', 'meet.jit.si')
            return f"https://{domain}/{room_id}"

        # إنشاء JWT token
        token = JitsiService.generate_jwt_token(
            room_name=room_id,
            user_name=user.get_full_name(),
            user_email=user.email,
            is_moderator=is_moderator
        )

        if token:
            return f"https://{settings.JITSI_JAAS_DOMAIN}/{room_id}?jwt={token}"
        else:
            # fallback بدون token
            return f"https://{settings.JITSI_JAAS_DOMAIN}/{room_id}"

    @staticmethod
    def get_iframe_config(room_id, user, is_moderator=False):
        """الحصول على إعدادات iframe لـ Jitsi"""
        
        config = getattr(settings, 'JITSI_CONFIG', {})
        
        # إعدادات أساسية
        base_config = {
            'roomName': room_id,
            'width': '100%',
            'height': '100%',
            'parentNode': None,  # سيتم تحديده في JavaScript
            'configOverwrite': {
                'startWithAudioMuted': config.get('startWithAudioMuted', True),
                'startWithVideoMuted': config.get('startWithVideoMuted', False),
                'enableWelcomePage': config.get('enableWelcomePage', False),
                'enableUserRolesBasedOnToken': config.get('enableUserRolesBasedOnToken', True),
                'enableNoAudioSignal': config.get('enableNoAudioSignal', True),
                'enableNoisyMicDetection': config.get('enableNoisyMicDetection', True),
                'disableThirdPartyRequests': True,
                'disableLocalVideoFlip': False,
                'backgroundAlpha': 0.5,
            },
            'interfaceConfigOverwrite': {
                'DISABLE_JOIN_LEAVE_NOTIFICATIONS': True,
                'DISABLE_PRESENCE_STATUS': True,
                'HIDE_INVITE_MORE_HEADER': True,
                'SHOW_JITSI_WATERMARK': False,
                'SHOW_WATERMARK_FOR_GUESTS': False,
                'SHOW_BRAND_WATERMARK': False,
                'BRAND_WATERMARK_LINK': '',
                'SHOW_POWERED_BY': False,
                'DISPLAY_WELCOME_PAGE_CONTENT': False,
                'DISPLAY_WELCOME_PAGE_TOOLBAR_ADDITIONAL_CONTENT': False,
                'APP_NAME': 'أكاديمية القرآنيا',
                'NATIVE_APP_NAME': 'أكاديمية القرآنيا',
                'DEFAULT_BACKGROUND': '#1a1a1a',
                'TOOLBAR_BUTTONS': [
                    'microphone', 'camera', 'closedcaptions', 'desktop',
                    'fullscreen', 'fodeviceselection', 'hangup', 'profile',
                    'chat', 'recording', 'livestreaming', 'etherpad',
                    'sharedvideo', 'settings', 'raisehand', 'videoquality',
                    'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',
                    'tileview', 'videobackgroundblur', 'download', 'help',
                    'mute-everyone', 'security'
                ] if is_moderator else [
                    'microphone', 'camera', 'closedcaptions', 'desktop',
                    'fullscreen', 'fodeviceselection', 'hangup', 'profile',
                    'chat', 'settings', 'raisehand', 'videoquality',
                    'filmstrip', 'feedback', 'stats', 'shortcuts',
                    'tileview', 'videobackgroundblur', 'help'
                ]
            },
            'userInfo': {
                'displayName': user.get_full_name(),
                'email': user.email,
            }
        }

        # إضافة JWT token إذا كان متاحاً
        if getattr(settings, 'JITSI_JAAS_DOMAIN', None):
            token = JitsiService.generate_jwt_token(
                room_name=room_id,
                user_name=user.get_full_name(),
                user_email=user.email,
                is_moderator=is_moderator
            )
            if token:
                base_config['jwt'] = token

        return base_config

    @staticmethod
    def get_domain():
        """الحصول على domain المستخدم مع التحقق من التوفر"""
        if getattr(settings, 'JITSI_JAAS_DOMAIN', None):
            return settings.JITSI_JAAS_DOMAIN

        # التحقق من الخادم الأساسي
        primary_domain = getattr(settings, 'JITSI_DOMAIN', 'meet.ffmuc.net')
        if JitsiService.check_domain_availability(primary_domain):
            return primary_domain

        # التحقق من الخوادم البديلة
        alternative_domains = getattr(settings, 'JITSI_ALTERNATIVE_DOMAINS', [])
        for domain in alternative_domains:
            if JitsiService.check_domain_availability(domain):
                return domain

        # العودة للخادم الأساسي كحل أخير
        return primary_domain

    @staticmethod
    def check_domain_availability(domain):
        """التحقق من توفر خادم Jitsi"""
        try:
            response = requests.get(f"https://{domain}/external_api.js", timeout=5)
            return response.status_code == 200
        except:
            return False

    @staticmethod
    def is_jaas_enabled():
        """التحقق من تفعيل JaaS"""
        return bool(getattr(settings, 'JITSI_JAAS_DOMAIN', None))

    @staticmethod
    def get_api_url():
        """الحصول على رابط Jitsi API"""
        domain = JitsiService.get_domain()
        return f"https://{domain}/external_api.js"


class JitsiEmbedService:
    """خدمة embedding Jitsi في الصفحات مع تجاوز حدود meet.jit.si"""

    @staticmethod
    def generate_smart_embed_code(live_lesson, user, is_moderator=False):
        """إنشاء كود HTML ذكي لـ embedding Jitsi مع تجاوز حد الـ 5 دقائق"""

        config = JitsiService.get_iframe_config(
            room_id=live_lesson.jitsi_room_id,
            user=user,
            is_moderator=is_moderator
        )

        api_url = JitsiService.get_api_url()
        domain = JitsiService.get_domain()
        is_public_jitsi = False  # الخوادم الجديدة لا تحتاج مراقبة زمنية

        embed_code = f"""
        <div id="jitsi-container" style="width: 100%; height: 500px; position: relative;">
            <div id="jitsi-loading" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; z-index: 1000;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحضير الحصة...</p>
            </div>
        </div>

        <script src="{api_url}"></script>
        <script>
            class SmartJitsiManager {{
                constructor() {{
                    this.domain = '{domain}';
                    this.isPublicJitsi = {str(is_public_jitsi).lower()};
                    this.options = {json.dumps(config, ensure_ascii=False)};
                    this.api = null;
                    this.reconnectAttempts = 0;
                    this.maxReconnectAttempts = 3;
                    this.sessionStartTime = Date.now();
                    this.warningShown = false;
                    this.autoReconnectEnabled = true;

                    this.init();
                }}

                init() {{
                    this.options.parentNode = document.querySelector('#jitsi-container');
                    this.createJitsiInstance();
                    this.setupEventListeners();

                    // إخفاء شاشة التحميل بعد 3 ثوان
                    setTimeout(() => {{
                        const loading = document.getElementById('jitsi-loading');
                        if (loading) loading.style.display = 'none';
                    }}, 3000);

                    // مراقبة حد الـ 5 دقائق للخادم العام
                    if (this.isPublicJitsi) {{
                        this.startTimeMonitoring();
                    }}
                }}

                createJitsiInstance() {{
                    try {{
                        // استخدام معرف الغرفة الثابت
                        // this.options.roomName يحتوي بالفعل على معرف الغرفة الصحيح

                        this.api = new JitsiMeetExternalAPI(this.domain, this.options);
                        console.log('✅ تم إنشاء جلسة Jitsi جديدة');

                        // حفظ مرجع عام
                        window.jitsiApi = this.api;

                    }} catch (error) {{
                        console.error('❌ خطأ في إنشاء Jitsi:', error);
                        this.showError('فشل في تحميل الحصة. جاري المحاولة مرة أخرى...');
                        setTimeout(() => this.reconnect(), 2000);
                    }}
                }}

                setupEventListeners() {{
                    if (!this.api) return;

                    this.api.addEventListener('videoConferenceJoined', (data) => {{
                        console.log('✅ تم الانضمام للمؤتمر:', data);
                        this.onJoined();
                    }});

                    this.api.addEventListener('videoConferenceLeft', (data) => {{
                        console.log('👋 تم مغادرة المؤتمر:', data);
                        this.onLeft();
                    }});

                    this.api.addEventListener('participantJoined', (data) => {{
                        console.log('👤 انضم مشارك جديد:', data);
                    }});

                    this.api.addEventListener('participantLeft', (data) => {{
                        console.log('👤 غادر مشارك:', data);
                    }});

                    // مراقبة أخطاء الاتصال
                    this.api.addEventListener('connectionFailed', () => {{
                        console.warn('⚠️ فشل الاتصال');
                        if (this.autoReconnectEnabled) {{
                            this.reconnect();
                        }}
                    }});
                }}

                startTimeMonitoring() {{
                    // تحذير بعد 4 دقائق
                    setTimeout(() => {{
                        if (!this.warningShown) {{
                            this.showTimeWarning();
                            this.warningShown = true;
                        }}
                    }}, 4 * 60 * 1000);

                    // إعادة الاتصال التلقائي بعد 4.5 دقيقة
                    setTimeout(() => {{
                        if (this.isPublicJitsi && this.autoReconnectEnabled) {{
                            this.smartReconnect();
                        }}
                    }}, 4.5 * 60 * 1000);
                }}

                showTimeWarning() {{
                    const message = `
                        <div class="alert alert-warning alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
                            <strong>⏰ تنبيه:</strong> ستنتهي الجلسة خلال دقيقة واحدة. سيتم إعادة الاتصال تلقائياً.
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    `;
                    document.body.insertAdjacentHTML('beforeend', message);
                }}

                smartReconnect() {{
                    console.log('🔄 بدء إعادة الاتصال الذكي...');

                    // حفظ حالة الصوت والفيديو
                    let audioMuted = false;
                    let videoMuted = false;

                    try {{
                        audioMuted = this.api.isAudioMuted();
                        videoMuted = this.api.isVideoMuted();
                    }} catch (e) {{
                        console.warn('لا يمكن قراءة حالة الصوت/الفيديو');
                    }}

                    // إغلاق الجلسة الحالية
                    this.dispose();

                    // إنشاء جلسة جديدة بعد ثانيتين
                    setTimeout(() => {{
                        this.reconnectAttempts++;
                        this.sessionStartTime = Date.now();
                        this.warningShown = false;

                        // الحفاظ على معرف الغرفة الثابت
                        this.options.roomName = '{live_lesson.jitsi_room_id}';

                        this.createJitsiInstance();

                        // استعادة حالة الصوت والفيديو بعد الانضمام
                        setTimeout(() => {{
                            try {{
                                if (audioMuted) this.api.executeCommand('toggleAudio');
                                if (videoMuted) this.api.executeCommand('toggleVideo');
                            }} catch (e) {{
                                console.warn('لا يمكن استعادة حالة الصوت/الفيديو');
                            }}
                        }}, 2000);

                        this.showReconnectMessage();

                        // بدء مراقبة جديدة
                        if (this.isPublicJitsi) {{
                            this.startTimeMonitoring();
                        }}

                    }}, 2000);
                }}

                showReconnectMessage() {{
                    const message = `
                        <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
                            <strong>✅ تم إعادة الاتصال:</strong> جلسة جديدة (المحاولة #${{this.reconnectAttempts}})
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    `;
                    document.body.insertAdjacentHTML('beforeend', message);

                    // إزالة الرسالة بعد 5 ثوان
                    setTimeout(() => {{
                        const alerts = document.querySelectorAll('.alert');
                        alerts.forEach(alert => {{
                            if (alert.textContent.includes('تم إعادة الاتصال')) {{
                                alert.remove();
                            }}
                        }});
                    }}, 5000);
                }}

                reconnect() {{
                    if (this.reconnectAttempts >= this.maxReconnectAttempts) {{
                        this.showError('فشل في إعادة الاتصال. يرجى تحديث الصفحة.');
                        return;
                    }}

                    this.smartReconnect();
                }}

                showError(message) {{
                    const errorDiv = `
                        <div class="alert alert-danger" role="alert" style="margin: 20px;">
                            <strong>خطأ:</strong> ${{message}}
                            <button class="btn btn-primary btn-sm ms-2" onclick="location.reload()">تحديث الصفحة</button>
                        </div>
                    `;
                    document.getElementById('jitsi-container').innerHTML = errorDiv;
                }}

                onJoined() {{
                    // إرسال إشعار للخادم
                    fetch('/api/live-lessons/{live_lesson.id}/join/', {{
                        method: 'POST',
                        headers: {{
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                            'Content-Type': 'application/json'
                        }}
                    }}).catch(console.error);
                }}

                onLeft() {{
                    // إرسال إشعار للخادم
                    fetch('/api/live-lessons/{live_lesson.id}/leave/', {{
                        method: 'POST',
                        headers: {{
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                            'Content-Type': 'application/json'
                        }}
                    }}).catch(console.error);
                }}

                dispose() {{
                    if (this.api) {{
                        try {{
                            this.api.dispose();
                        }} catch (e) {{
                            console.warn('خطأ في إغلاق API:', e);
                        }}
                        this.api = null;
                    }}
                }}

                // وظائف التحكم العامة
                toggleAudio() {{
                    if (this.api) this.api.executeCommand('toggleAudio');
                }}

                toggleVideo() {{
                    if (this.api) this.api.executeCommand('toggleVideo');
                }}

                hangUp() {{
                    if (this.api) this.api.executeCommand('hangup');
                }}
            }}

            // إنشاء مدير Jitsi الذكي
            const jitsiManager = new SmartJitsiManager();

            // حفظ مرجع عام
            window.jitsiManager = jitsiManager;

            // تنظيف عند إغلاق الصفحة
            window.addEventListener('beforeunload', () => {{
                jitsiManager.dispose();
            }});
        </script>
        """

        return embed_code

    @staticmethod
    def generate_embed_code(live_lesson, user, is_moderator=False):
        """إنشاء كود HTML عادي لـ embedding Jitsi (للتوافق مع النسخة القديمة)"""
        return JitsiEmbedService.generate_smart_embed_code(live_lesson, user, is_moderator)

    @staticmethod
    def get_iframe_url(live_lesson, user, is_moderator=False):
        """الحصول على رابط iframe"""
        return JitsiService.get_jitsi_url_with_token(
            room_id=live_lesson.jitsi_room_id,
            user=user,
            is_moderator=is_moderator
        )
