<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - نظام قرآنيا التعليمي</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #2D5016;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 30px;
        }
        .button {
            display: inline-block;
            background-color: #2D5016;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕌 نظام قرآنيا التعليمي</h1>
            <p>إعادة تعيين كلمة المرور</p>
        </div>

        <div class="content">
            <h2>السلام عليكم {{ user.get_full_name|default:user.username }}</h2>

            <p>لقد تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في {{ ACADEMY_SLOGAN }}.</p>

            <p>إذا كنت قد طلبت إعادة تعيين كلمة المرور، يرجى النقر على الرابط أدناه:</p>

            <div style="text-align: center;">
                <a href="{{ protocol }}://{{ domain }}{% url 'password_reset_confirm' uidb64=uid token=token %}" class="button">
                    إعادة تعيين كلمة المرور
                </a>
            </div>

            <p>أو يمكنك نسخ الرابط التالي ولصقه في متصفحك:</p>
            <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                {{ protocol }}://{{ domain }}{% url 'password_reset_confirm' uidb64=uid token=token %}
            </p>

            <div class="warning">
                <strong>⚠️ تنبيه أمني:</strong>
                <ul>
                    <li>هذا الرابط صالح لمدة محدودة فقط</li>
                    <li>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذه الرسالة</li>
                    <li>لا تشارك هذا الرابط مع أي شخص آخر</li>
                </ul>
            </div>

            <p>إذا كنت تواجه أي مشاكل، يرجى التواصل مع فريق الدعم الفني.</p>

            <p>بارك الله فيك،<br>
            فريق {{ ACADEMY_SLOGAN }}</p>
        </div>

        <div class="footer">
            <p>هذه رسالة تلقائية، يرجى عدم الرد عليها</p>
            <p>© {{ current_year }} {{ ACADEMY_SLOGAN }} - جميع الحقوق محفوظة</p>
        </div>
    </div>
</body>
</html>
