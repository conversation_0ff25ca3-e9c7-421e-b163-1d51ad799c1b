# Generated by Django 4.2.7 on 2025-06-01 18:13

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ChatMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='محتوى الرسالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='message_attachments/', verbose_name='مرفق')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحذف')),
            ],
            options={
                'verbose_name': 'رسالة',
                'verbose_name_plural': 'الرسائل',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
            ],
            options={
                'verbose_name': 'محادثة',
                'verbose_name_plural': 'المحادثات',
                'ordering': ['-updated_at'],
            },
        ),
    ]
