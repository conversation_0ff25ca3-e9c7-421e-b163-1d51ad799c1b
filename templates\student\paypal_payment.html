{% extends 'base.html' %}
{% load static %}

{% block title %}الدفع عبر PayPal{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-islamic-primary mb-2">
                <i class="fab fa-paypal text-blue-600 ml-3"></i>
                الدفع عبر PayPal
            </h1>
            <p class="text-gray-600">أكمل عملية الدفع بأمان عبر PayPal</p>
        </div>

        <div class="max-w-2xl mx-auto">
            <!-- Payment Summary -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">
                    <i class="fas fa-receipt ml-2"></i>
                    ملخص الدفع
                </h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-2 border-b">
                        <span class="text-gray-600">الباقة:</span>
                        <span class="font-semibold">{{ payment.subscription.plan.name }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-2 border-b">
                        <span class="text-gray-600">نوع الباقة:</span>
                        <span class="font-semibold">{{ payment.subscription.plan.get_plan_type_display }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-2 border-b">
                        <span class="text-gray-600">عدد الحصص:</span>
                        <span class="font-semibold">{{ payment.subscription.plan.lessons_count }} حصة</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-2 border-b">
                        <span class="text-gray-600">مدة كل حصة:</span>
                        <span class="font-semibold">{{ payment.subscription.plan.lesson_duration }} دقيقة</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-2 border-b">
                        <span class="text-gray-600">صالحة لمدة:</span>
                        <span class="font-semibold">{{ payment.subscription.plan.duration_days }} يوم</span>
                    </div>
                    
                    {% if payment.subscription.plan.discount_percentage > 0 %}
                    <div class="flex justify-between items-center py-2 border-b">
                        <span class="text-gray-600">السعر الأصلي:</span>
                        <span class="line-through text-gray-500">{{ payment.subscription.plan.price }} {{ payment.subscription.plan.get_currency_symbol }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-2 border-b">
                        <span class="text-green-600">الخصم ({{ payment.subscription.plan.discount_percentage }}%):</span>
                        <span class="text-green-600 font-semibold">-{{ payment.subscription.plan.get_discount_amount }} {{ payment.subscription.plan.get_currency_symbol }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="flex justify-between items-center py-4 bg-blue-50 px-4 rounded-lg">
                        <span class="text-lg font-bold text-gray-900">المبلغ الإجمالي:</span>
                        <span class="text-2xl font-bold text-blue-600">{{ payment.amount }} {{ payment.subscription.plan.get_currency_symbol }}</span>
                    </div>
                </div>
            </div>

            <!-- PayPal Payment Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="text-center mb-6">
                    <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fab fa-paypal text-blue-600 text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">الدفع الآمن عبر PayPal</h3>
                    <p class="text-gray-600">ستتم إعادة توجيهك إلى PayPal لإكمال عملية الدفع بأمان</p>
                </div>

                <!-- Payment Features -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <i class="fas fa-shield-alt text-blue-600 text-2xl mb-2"></i>
                        <h4 class="font-semibold text-gray-900 mb-1">آمان عالي</h4>
                        <p class="text-sm text-gray-600">حماية كاملة لبياناتك المالية</p>
                    </div>
                    
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <i class="fas fa-bolt text-green-600 text-2xl mb-2"></i>
                        <h4 class="font-semibold text-gray-900 mb-1">سريع وسهل</h4>
                        <p class="text-sm text-gray-600">دفع فوري وتفعيل مباشر</p>
                    </div>
                    
                    <div class="text-center p-4 bg-purple-50 rounded-lg">
                        <i class="fas fa-globe text-purple-600 text-2xl mb-2"></i>
                        <h4 class="font-semibold text-gray-900 mb-1">عالمي</h4>
                        <p class="text-sm text-gray-600">مقبول في جميع أنحاء العالم</p>
                    </div>
                </div>

                <!-- PayPal SDK Integration -->
                {% if paypal_settings.paypal_enabled and paypal_settings.paypal_client_id %}
                <div id="paypal-button-container" class="mb-6"></div>

                <!-- Fallback Form -->
                <div id="fallback-form" style="display: none;">
                    <form method="post" class="space-y-6">
                        {% csrf_token %}

                        <!-- Payment Confirmation -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <i class="fas fa-info-circle text-yellow-600 text-xl ml-3 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-yellow-800 mb-2">تأكيد الدفع</h4>
                                    <p class="text-yellow-700 text-sm mb-3">
                                        بالنقر على "ادفع الآن"، ستتم إعادة توجيهك إلى PayPal لإكمال عملية الدفع.
                                        سيتم تفعيل اشتراكك فور نجاح عملية الدفع.
                                    </p>
                                    <ul class="text-yellow-700 text-sm space-y-1">
                                        <li>• سيتم خصم {{ payment.amount }} {{ payment.subscription.plan.get_currency_symbol }} من حسابك</li>
                                        <li>• ستحصل على {{ payment.subscription.plan.lessons_count }} حصة</li>
                                        <li>• الاشتراك صالح لمدة {{ payment.subscription.plan.duration_days }} يوم</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-4 space-x-reverse">
                            <button type="submit"
                                    class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-4 px-6 rounded-lg font-semibold text-lg transition-colors flex items-center justify-center">
                                <i class="fab fa-paypal ml-2"></i>
                                ادفع الآن عبر PayPal
                            </button>

                            <a href="{% url 'student_subscriptions' %}"
                               class="bg-gray-500 hover:bg-gray-600 text-white py-4 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center">
                                <i class="fas fa-arrow-right ml-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
                {% else %}
                <!-- PayPal Not Available -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                    <i class="fas fa-exclamation-triangle text-red-600 text-3xl mb-4"></i>
                    <h3 class="text-lg font-semibold text-red-800 mb-2">PayPal غير متاح</h3>
                    <p class="text-red-700 mb-4">عذراً، خدمة PayPal غير متاحة في الوقت الحالي.</p>
                    <a href="{% url 'student_subscriptions' %}"
                       class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors">
                        العودة لاختيار وسيلة دفع أخرى
                    </a>
                </div>
                {% endif %}

                <!-- Security Notice -->
                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-500">
                        <i class="fas fa-lock ml-1"></i>
                        جميع المعاملات محمية بتشفير SSL 256-bit
                    </p>
                </div>
            </div>

            <!-- Help Section -->
            <div class="mt-8 bg-gray-50 rounded-lg p-6">
                <h4 class="font-semibold text-gray-900 mb-3">
                    <i class="fas fa-question-circle ml-2"></i>
                    هل تحتاج مساعدة؟
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">كيفية الدفع عبر PayPal:</h5>
                        <ul class="text-gray-600 space-y-1">
                            <li>1. انقر على "ادفع الآن"</li>
                            <li>2. سجل دخولك إلى PayPal</li>
                            <li>3. أكد عملية الدفع</li>
                            <li>4. ستتم إعادة توجيهك للموقع</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">في حالة وجود مشكلة:</h5>
                        <ul class="text-gray-600 space-y-1">
                            <li>• تأكد من رصيد حسابك</li>
                            <li>• تحقق من بيانات الدفع</li>
                            <li>• تواصل مع الدعم الفني</li>
                            <li>• جرب طريقة دفع أخرى</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if paypal_settings.paypal_enabled and paypal_settings.paypal_client_id %}
<!-- PayPal SDK -->
<script src="https://www.paypal.com/sdk/js?client-id={{ paypal_settings.paypal_client_id }}&currency={{ payment.subscription.plan.currency }}{% if paypal_settings.paypal_sandbox_mode %}&disable-funding=credit,card{% endif %}"></script>

<script>
// دالة إرسال الرسائل للنافذة الأصلية
function notifyParentWindow(status, data) {
    if (window.opener) {
        try {
            window.opener.postMessage({
                type: 'payment_result',
                status: status,
                data: data
            }, '*');

            // إغلاق النافذة بعد إرسال الرسالة
            setTimeout(() => {
                window.close();
            }, 1000);
        } catch (error) {
            console.error('خطأ في إرسال الرسالة للنافذة الأصلية:', error);
            // في حالة فشل الإرسال، استخدم الطريقة التقليدية
            if (status === 'success') {
                window.location.href = '/dashboard/student/thank-you/' + data.payment_id + '/';
            } else {
                window.location.href = '{% url "student_subscriptions" %}';
            }
        }
    } else {
        // إذا لم تكن نافذة منبثقة، استخدم الطريقة التقليدية
        if (status === 'success') {
            window.location.href = '/dashboard/student/thank-you/' + data.payment_id + '/';
        } else {
            window.location.href = '{% url "student_subscriptions" %}';
        }
    }
}

// إرسال إشعار جاهزية النافذة
function notifyWindowReady() {
    if (window.opener) {
        try {
            window.opener.postMessage({
                type: 'window_ready',
                data: { payment_type: 'paypal' }
            }, '*');
        } catch (error) {
            console.error('خطأ في إرسال إشعار الجاهزية:', error);
        }
    }
}

// إضافة تنبيه للنافذة المنبثقة
function addPopupNotification() {
    const notification = document.createElement('div');
    notification.innerHTML = `
        <div class="fixed top-0 left-0 right-0 bg-blue-600 text-white p-3 text-center z-50 shadow-lg">
            <div class="flex items-center justify-center">
                <i class="fas fa-info-circle ml-2"></i>
                <span class="font-semibold">نافذة دفع آمنة - لا تغلق هذه النافذة حتى اكتمال العملية</span>
                <i class="fas fa-shield-alt mr-2 text-yellow-300"></i>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('afterbegin', notification.innerHTML);

    // إضافة مساحة للمحتوى لتجنب التداخل
    document.body.style.paddingTop = '60px';
}

// إعداد حماية النافذة المنبثقة
function setupPopupProtection() {
    let paymentInProgress = false;

    // تتبع بداية عملية الدفع
    document.addEventListener('click', function(e) {
        if (e.target.closest('#paypal-button-container') || e.target.closest('#fallback-form')) {
            paymentInProgress = true;
        }
    });

    // منع إغلاق النافذة أثناء الدفع
    window.addEventListener('beforeunload', function(e) {
        if (paymentInProgress) {
            e.preventDefault();
            e.returnValue = 'عملية الدفع جارية. هل أنت متأكد من أنك تريد إغلاق النافذة؟';
            return e.returnValue;
        }
    });

    // إيقاف الحماية عند اكتمال الدفع
    window.addEventListener('message', function(e) {
        if (e.data && e.data.type === 'payment_completed') {
            paymentInProgress = false;
        }
    });
}

// PayPal Integration
document.addEventListener('DOMContentLoaded', function() {
    // إرسال إشعار جاهزية النافذة
    notifyWindowReady();

    // إذا كانت نافذة منبثقة، أضف تنبيه للمستخدم
    if (window.opener) {
        addPopupNotification();
        setupPopupProtection();
    }
    // تحقق من وجود PayPal SDK
    if (typeof paypal !== 'undefined') {
        paypal.Buttons({
            createOrder: function(data, actions) {
                return actions.order.create({
                    purchase_units: [{
                        amount: {
                            value: '{{ payment.amount }}',
                            currency_code: '{{ payment.subscription.plan.currency }}'
                        },
                        description: 'اشتراك {{ payment.subscription.plan.name }} - {{ payment.subscription.plan.lessons_count }} حصة'
                    }]
                });
            },
            onApprove: function(data, actions) {
                return actions.order.capture().then(function(details) {
                    // إرسال تفاصيل الدفع للخادم
                    fetch('{% url "paypal_payment" payment.id %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: JSON.stringify({
                            orderID: data.orderID,
                            payerID: data.payerID,
                            paymentID: data.paymentID,
                            details: details
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // إرسال رسالة نجاح للنافذة الأصلية
                            notifyParentWindow('success', {
                                payment_id: data.payment_id,
                                amount: '{{ payment.amount }}',
                                currency: '{{ payment.subscription.plan.currency }}',
                                payment_method: 'paypal'
                            });
                        } else {
                            // إرسال رسالة فشل للنافذة الأصلية
                            notifyParentWindow('failed', {
                                message: data.message || 'حدث خطأ أثناء معالجة الدفع'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // إرسال رسالة خطأ للنافذة الأصلية
                        notifyParentWindow('failed', {
                            message: 'حدث خطأ أثناء التواصل مع الخادم'
                        });
                    });
                });
            },
            onError: function(err) {
                console.error('PayPal Error:', err);
                // إرسال رسالة خطأ للنافذة الأصلية
                notifyParentWindow('failed', {
                    message: 'حدث خطأ أثناء معالجة الدفع عبر PayPal'
                });
            },
            onCancel: function(data) {
                // إرسال رسالة إلغاء للنافذة الأصلية
                notifyParentWindow('cancelled', {
                    message: 'تم إلغاء عملية الدفع'
                });
            }
        }).render('#paypal-button-container');
    } else {
        // إظهار النموذج البديل إذا فشل تحميل PayPal SDK
        document.getElementById('fallback-form').style.display = 'block';

        const payButton = document.querySelector('#fallback-form button[type="submit"]');
        if (payButton) {
            payButton.addEventListener('click', function(e) {
                e.preventDefault();

                this.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري التوجيه إلى PayPal...';
                this.disabled = true;

                // إرسال النموذج وإعادة التوجيه
                const form = this.closest('form');
                const formData = new FormData(form);

                fetch(form.action, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (response.ok) {
                        // إذا كانت نافذة منبثقة، أغلقها وأرسل رسالة نجاح
                        if (window.opener) {
                            notifyParentWindow('success', {
                                payment_id: 'fallback_payment',
                                amount: '{{ payment.amount }}',
                                currency: '{{ payment.subscription.plan.currency }}',
                                payment_method: 'paypal'
                            });
                        } else {
                            // إعادة توجيه عادية
                            window.location.href = '{% url "student_subscriptions" %}';
                        }
                    } else {
                        throw new Error('فشل في معالجة الطلب');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (window.opener) {
                        notifyParentWindow('failed', {
                            message: 'حدث خطأ أثناء معالجة الدفع'
                        });
                    } else {
                        alert('حدث خطأ أثناء معالجة الدفع');
                        this.innerHTML = '<i class="fab fa-paypal ml-2"></i>ادفع الآن عبر PayPal';
                        this.disabled = false;
                    }
                });
            });
        }
    }
});
</script>
{% else %}
<script>
// إذا كان PayPal معطل، إظهار رسالة
document.addEventListener('DOMContentLoaded', function() {
    console.log('PayPal is disabled or not configured');
});
</script>
{% endif %}

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{% endblock %}
