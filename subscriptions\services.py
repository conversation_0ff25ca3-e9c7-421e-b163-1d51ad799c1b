from datetime import datetime, timedelta
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import SubscriptionPlan, StudentSubscription, ScheduledLesson

User = get_user_model()


class LessonSchedulingService:
    """خدمة الجدولة التلقائية للحصص"""

    @staticmethod
    def create_automatic_schedule(subscription):
        """إنشاء جدولة تلقائية للحصص بناءً على الاشتراك"""
        
        if not subscription.plan.auto_schedule:
            return False, "الجدولة التلقائية غير مفعلة لهذه الباقة"
        
        plan = subscription.plan
        start_date = subscription.start_date
        end_date = subscription.end_date
        
        # التحقق من وجود إعدادات الجدولة
        if not plan.preferred_days or not plan.preferred_times:
            return False, "لم يتم تحديد أيام أو أوقات مفضلة للباقة"
        
        # حذف أي حصص مجدولة سابقة لهذا الاشتراك
        ScheduledLesson.objects.filter(subscription=subscription).delete()
        
        # إعدادات الجدولة
        lessons_to_schedule = subscription.remaining_lessons
        lessons_per_week = plan.lessons_per_week
        preferred_days = plan.preferred_days  # [0, 2, 4] مثلاً (أحد، ثلاثاء، خميس)
        preferred_times = plan.preferred_times  # ["16:00", "18:00"] مثلاً
        
        # البدء من تاريخ بداية الاشتراك
        current_date = start_date
        lesson_number = 1
        scheduled_lessons = []
        
        # حساب عدد الأسابيع المطلوبة
        weeks_needed = (lessons_to_schedule + lessons_per_week - 1) // lessons_per_week
        
        for week in range(weeks_needed):
            if lesson_number > lessons_to_schedule:
                break
                
            # حساب تاريخ بداية الأسبوع
            week_start = current_date + timedelta(weeks=week)
            
            # جدولة حصص هذا الأسبوع
            lessons_this_week = 0
            
            for day_offset in preferred_days:
                if lesson_number > lessons_to_schedule or lessons_this_week >= lessons_per_week:
                    break
                
                # حساب تاريخ الحصة
                lesson_date = week_start + timedelta(days=day_offset)
                
                # التأكد من أن التاريخ لا يتجاوز نهاية الاشتراك
                if lesson_date > end_date:
                    break
                
                # اختيار وقت من الأوقات المفضلة (دوري)
                time_index = (lesson_number - 1) % len(preferred_times)
                preferred_time = preferred_times[time_index]
                
                # تحويل الوقت إلى datetime
                try:
                    hour, minute = map(int, preferred_time.split(':'))
                    lesson_datetime = datetime.combine(lesson_date, datetime.min.time().replace(hour=hour, minute=minute))
                    
                    # تحويل إلى timezone-aware datetime
                    lesson_datetime = timezone.make_aware(lesson_datetime)
                    
                except (ValueError, IndexError):
                    # في حالة خطأ في تنسيق الوقت، استخدم وقت افتراضي
                    lesson_datetime = datetime.combine(lesson_date, datetime.min.time().replace(hour=16, minute=0))
                    lesson_datetime = timezone.make_aware(lesson_datetime)
                
                # إنشاء الحصة المجدولة
                scheduled_lesson = ScheduledLesson(
                    subscription=subscription,
                    lesson_number=lesson_number,
                    scheduled_date=lesson_datetime,
                    duration_minutes=plan.lesson_duration,
                    status='scheduled'
                )
                
                scheduled_lessons.append(scheduled_lesson)
                lesson_number += 1
                lessons_this_week += 1
        
        # حفظ جميع الحصص المجدولة
        if scheduled_lessons:
            ScheduledLesson.objects.bulk_create(scheduled_lessons)
            return True, f"تم جدولة {len(scheduled_lessons)} حصة بنجاح"
        else:
            return False, "لم يتم جدولة أي حصص"

    @staticmethod
    def assign_teacher_to_lessons(subscription, teacher):
        """تعيين معلم لجميع حصص الاشتراك"""
        
        if not teacher or teacher.user_type != 'teacher':
            return False, "المعلم غير صحيح"
        
        updated_count = ScheduledLesson.objects.filter(
            subscription=subscription,
            status='scheduled'
        ).update(teacher=teacher)
        
        return True, f"تم تعيين المعلم لـ {updated_count} حصة"

    @staticmethod
    def reschedule_lesson(lesson, new_datetime):
        """إعادة جدولة حصة معينة"""
        
        if lesson.status != 'scheduled':
            return False, "لا يمكن إعادة جدولة حصة غير مجدولة"
        
        # التحقق من أن التاريخ الجديد ضمن فترة الاشتراك
        if new_datetime.date() > lesson.subscription.end_date:
            return False, "التاريخ الجديد يتجاوز فترة الاشتراك"
        
        lesson.scheduled_date = new_datetime
        lesson.status = 'rescheduled'
        lesson.save()
        
        return True, "تم إعادة جدولة الحصة بنجاح"

    @staticmethod
    def get_schedule_summary(subscription):
        """الحصول على ملخص جدولة الاشتراك"""
        
        lessons = ScheduledLesson.objects.filter(subscription=subscription)
        
        summary = {
            'total_lessons': lessons.count(),
            'scheduled': lessons.filter(status='scheduled').count(),
            'completed': lessons.filter(status='completed').count(),
            'cancelled': lessons.filter(status='cancelled').count(),
            'upcoming': lessons.filter(
                status='scheduled',
                scheduled_date__gte=timezone.now()
            ).count(),
            'next_lesson': lessons.filter(
                status='scheduled',
                scheduled_date__gte=timezone.now()
            ).first()
        }
        
        return summary

    @staticmethod
    def get_available_teachers():
        """الحصول على قائمة المعلمين المتاحين"""
        return User.objects.filter(
            user_type='teacher',
            is_active=True,
            is_active_teacher=True
        ).order_by('first_name', 'last_name')

    @staticmethod
    def validate_schedule_settings(plan):
        """التحقق من صحة إعدادات الجدولة للباقة"""
        errors = []
        
        if not plan.preferred_days:
            errors.append("يجب تحديد أيام الأسبوع المفضلة")
        elif not all(isinstance(day, int) and 0 <= day <= 6 for day in plan.preferred_days):
            errors.append("أيام الأسبوع يجب أن تكون أرقام من 0 إلى 6")
        
        if not plan.preferred_times:
            errors.append("يجب تحديد الأوقات المفضلة")
        elif not all(isinstance(time, str) and ':' in time for time in plan.preferred_times):
            errors.append("الأوقات يجب أن تكون بصيغة HH:MM")
        
        if plan.lessons_per_week <= 0:
            errors.append("عدد الحصص أسبوعياً يجب أن يكون أكبر من صفر")
        
        if plan.lessons_per_week > len(plan.preferred_days):
            errors.append("عدد الحصص أسبوعياً لا يمكن أن يكون أكبر من عدد الأيام المفضلة")
        
        return len(errors) == 0, errors


class SubscriptionApprovalService:
    """خدمة الموافقة على الاشتراكات"""

    @staticmethod
    def approve_subscription(subscription, approved_by=None):
        """الموافقة على الاشتراك وإنشاء الجدولة التلقائية"""
        
        if subscription.status != 'pending_approval':
            return False, "الاشتراك ليس في حالة انتظار الموافقة"
        
        # تحديث حالة الاشتراك
        subscription.status = 'active'
        subscription.save()
        
        # إنشاء الجدولة التلقائية
        scheduling_message = ""
        if subscription.plan.auto_schedule:
            success, message = LessonSchedulingService.create_automatic_schedule(subscription)
            if not success:
                # في حالة فشل الجدولة، نبقي الاشتراك نشط لكن نسجل الخطأ
                scheduling_message = f" (فشلت الجدولة التلقائية: {message})"

        print(f"🔄 بدء إرسال إشعارات التفعيل للطالب {subscription.student.email}")
        
        # تم استبدال نظام البريد الإلكتروني بنظام WhatsApp
        print("تم استبدال إرسال الإشعارات بنظام WhatsApp")
        
        return True, "تم تفعيل الاشتراك وإنشاء الجدولة التلقائية بنجاح"

    @staticmethod
    def reject_subscription(subscription, reason="", rejected_by=None):
        """رفض الاشتراك"""
        
        if subscription.status != 'pending_approval':
            return False, "الاشتراك ليس في حالة انتظار الموافقة"
        
        subscription.status = 'cancelled'
        subscription.notes = f"مرفوض: {reason}" if reason else "مرفوض"
        subscription.save()
        
        # إرسال إشعار للطالب بإلغاء الاشتراك
        from notifications.utils import SubscriptionNotificationService

        SubscriptionNotificationService.notify_subscription_cancelled(subscription, reason)

        # تم استبدال نظام البريد الإلكتروني بنظام WhatsApp
        
        return True, "تم رفض الاشتراك"


class ScheduledLessonToLiveService:
    """خدمة تحويل الحصص المجدولة إلى حصص مباشرة"""

    @staticmethod
    def convert_scheduled_to_live():
        """تحويل الحصص المجدولة التي حان موعدها إلى حصص مباشرة"""
        from lessons.models import LiveLesson
        import uuid

        now = timezone.now()
        # البحث عن الحصص المجدولة التي حان موعدها (في آخر 15 دقيقة)
        ready_lessons = ScheduledLesson.objects.filter(
            status='scheduled',
            scheduled_date__lte=now + timedelta(minutes=15),  # 15 دقيقة قبل الموعد
            scheduled_date__gte=now - timedelta(minutes=30)   # حتى 30 دقيقة بعد الموعد
        ).select_related('subscription__student', 'teacher')

        converted_count = 0

        for scheduled_lesson in ready_lessons:
            try:
                # التحقق من عدم وجود حصة مباشرة مرتبطة بالفعل
                existing_live = LiveLesson.objects.filter(
                    student=scheduled_lesson.subscription.student,
                    teacher=scheduled_lesson.teacher,
                    scheduled_date=scheduled_lesson.scheduled_date
                ).first()

                if existing_live:
                    continue  # تخطي إذا كانت موجودة بالفعل

                # إنشاء حصة مباشرة جديدة
                live_lesson = LiveLesson.objects.create(
                    title=f"حصة رقم {scheduled_lesson.lesson_number} - {scheduled_lesson.subscription.plan.name}",
                    description=f"حصة من اشتراك {scheduled_lesson.subscription.student.get_full_name()}",
                    student=scheduled_lesson.subscription.student,
                    teacher=scheduled_lesson.teacher,
                    scheduled_date=scheduled_lesson.scheduled_date,
                    duration_minutes=scheduled_lesson.duration_minutes,
                    status='scheduled',  # ستبقى مجدولة حتى يبدأها المعلم
                    created_by=scheduled_lesson.teacher,
                    # ربط بالحصة المجدولة الأصلية
                    notes=f"تم إنشاؤها تلقائياً من الحصة المجدولة رقم {scheduled_lesson.id}"
                )

                # تحديث حالة الحصة المجدولة
                scheduled_lesson.status = 'converted_to_live'
                scheduled_lesson.notes = f"تم تحويلها إلى حصة مباشرة رقم {live_lesson.id}"
                scheduled_lesson.save()

                # إرسال إشعارات
                ScheduledLessonToLiveService._send_lesson_ready_notifications(
                    live_lesson, scheduled_lesson
                )

                converted_count += 1

            except Exception as e:
                # تسجيل الخطأ وتخطي هذه الحصة
                print(f"خطأ في تحويل الحصة المجدولة {scheduled_lesson.id}: {str(e)}")
                continue

        return converted_count

    @staticmethod
    def _send_lesson_ready_notifications(live_lesson, scheduled_lesson):
        """إرسال إشعارات للمعلم والطالب عن جاهزية الحصة"""
        # تم استبدال نظام البريد الإلكتروني بنظام WhatsApp
        from users.whatsapp_service import whatsapp_service

        try:
            # إشعار للمعلم
            # إشعار WhatsApp للمعلم
            teacher_message = f"""👨‍🏫 حصة جاهزة للبدء!

السلام عليكم أستاذ {live_lesson.teacher.get_full_name()},

حصتك مع الطالب {live_lesson.student.get_full_name()} جاهزة للبدء ✅

📋 تفاصيل الحصة:
• العنوان: {live_lesson.title}
• الموعد: {live_lesson.scheduled_date.strftime('%Y-%m-%d %H:%M')}
• المدة: {live_lesson.duration_minutes} دقيقة

يمكنك بدء الحصة من لوحة التحكم 🚀

---
إدارة المنصة 🕌"""

            whatsapp_service.send_message(
                recipient_user=live_lesson.teacher,
                message_type='lesson_ready',
                content=teacher_message
            )

            # إشعار للطالب
            # إشعار WhatsApp للطالب
            student_message = f"""📚 حصتك جاهزة للبدء!

السلام عليكم {live_lesson.student.get_full_name()},

حصتك مع الأستاذ {live_lesson.teacher.get_full_name()} جاهزة للبدء ✅

📋 تفاصيل الحصة:
• العنوان: {live_lesson.title}
• الموعد: {live_lesson.scheduled_date.strftime('%Y-%m-%d %H:%M')}
• المدة: {live_lesson.duration_minutes} دقيقة
• المعلم: {live_lesson.teacher.get_full_name()}

يمكنك الانضمام للحصة من لوحة التحكم 🚀

---
إدارة المنصة 🕌"""

            whatsapp_service.send_message(
                recipient_user=live_lesson.student,
                message_type='lesson_ready',
                content=student_message
            )

        except Exception as e:
            print(f"خطأ في إرسال الإشعارات للحصة {live_lesson.id}: {str(e)}")

    @staticmethod
    def check_and_convert_lessons():
        """فحص وتحويل الحصص - يتم استدعاؤها دورياً"""
        try:
            converted = ScheduledLessonToLiveService.convert_scheduled_to_live()
            if converted > 0:
                print(f"تم تحويل {converted} حصة مجدولة إلى حصص مباشرة")
            return converted
        except Exception as e:
            print(f"خطأ في فحص وتحويل الحصص: {str(e)}")
            return 0
