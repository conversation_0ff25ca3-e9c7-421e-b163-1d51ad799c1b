<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير شامل - تقييمات جميع الطلاب</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #2D5016, #4a7c59);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 20px;
            opacity: 0.9;
        }
        .info-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #2D5016;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #2D5016;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #2D5016;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .students-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 12px;
        }
        .students-table th,
        .students-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .students-table th {
            background: #2D5016;
            color: white;
            font-weight: bold;
            font-size: 11px;
        }
        .students-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .students-table tr:hover {
            background: #e9ecef;
        }
        .performance-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        .excellent { background: #28a745; }
        .very-good { background: #17a2b8; }
        .good { background: #007bff; }
        .average { background: #ffc107; color: #333; }
        .needs-improvement { background: #dc3545; }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            color: #666;
            font-size: 12px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
        }
        .info-value {
            color: #333;
            font-weight: bold;
        }
        @media print {
            body { background: white; }
            .info-section { box-shadow: none; border: 1px solid #ddd; }
            .stat-card { box-shadow: none; border: 1px solid #ddd; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>أكاديمية القرآنية</h1>
        <h2>تقرير شامل - تقييمات جميع الطلاب</h2>
    </div>

    <!-- معلومات التقرير -->
    <div class="info-section">
        <div class="section-title">معلومات التقرير</div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">تاريخ الإنشاء:</span>
                <span class="info-value">{{ generated_at|date:"d/m/Y H:i" }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">إجمالي الطلاب:</span>
                <span class="info-value">{{ total_students }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">متوسط التقييمات:</span>
                <span class="info-value">
                    {% if student_ratings %}
                        {{ student_ratings|length|floatformat:0 }} طالب
                    {% else %}
                        0
                    {% endif %}
                </span>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ total_students }}</div>
            <div class="stat-label">إجمالي الطلاب</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {% if student_ratings %}
                    {% for rating in student_ratings %}
                        {% if forloop.first %}{{ rating.total_ratings|add:0 }}{% endif %}
                    {% endfor %}
                {% else %}
                    0
                {% endif %}
            </div>
            <div class="stat-label">إجمالي التقييمات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {% if student_ratings %}
                    {{ student_ratings|length|floatformat:0 }}
                {% else %}
                    0
                {% endif %}
            </div>
            <div class="stat-label">طلاب نشطون</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {% if student_ratings %}
                    {% for rating in student_ratings %}
                        {% if forloop.first %}{{ rating.overall_average|floatformat:1 }}{% endif %}
                    {% endfor %}
                {% else %}
                    0.0
                {% endif %}
            </div>
            <div class="stat-label">متوسط عام</div>
        </div>
    </div>

    <!-- جدول الطلاب الرئيسي -->
    <div class="info-section">
        <div class="section-title">تفاصيل تقييمات الطلاب</div>
        
        {% if student_ratings %}
        <table class="students-table">
            <thead>
                <tr>
                    <th style="width: 20%;">اسم الطالب</th>
                    <th style="width: 10%;">إجمالي التقييمات</th>
                    <th style="width: 12%;">المتوسط العام</th>
                    <th style="width: 12%;">جودة الحصة</th>
                    <th style="width: 12%;">تفاعل الطالب</th>
                    <th style="width: 12%;">الجودة التقنية</th>
                    <th style="width: 10%;">حصص مجدولة</th>
                    <th style="width: 10%;">حصص مباشرة</th>
                    <th style="width: 12%;">مستوى الأداء</th>
                </tr>
            </thead>
            <tbody>
                {% for student_rating in student_ratings %}
                <tr>
                    <td style="text-align: right; font-weight: bold;">
                        {{ student_rating.student.get_full_name }}
                    </td>
                    <td>{{ student_rating.total_ratings }}</td>
                    <td>
                        <strong>{{ student_rating.overall_average|floatformat:1 }}</strong>
                    </td>
                    <td>{{ student_rating.avg_quality|floatformat:1 }}</td>
                    <td>{{ student_rating.avg_interaction|floatformat:1 }}</td>
                    <td>{{ student_rating.avg_technical|floatformat:1 }}</td>
                    <td>{{ student_rating.scheduled_count|default:0 }}</td>
                    <td>{{ student_rating.live_count|default:0 }}</td>
                    <td>
                        {% if student_rating.overall_average >= 4.5 %}
                            <span class="performance-badge excellent">ممتاز</span>
                        {% elif student_rating.overall_average >= 4.0 %}
                            <span class="performance-badge very-good">جيد جداً</span>
                        {% elif student_rating.overall_average >= 3.5 %}
                            <span class="performance-badge good">جيد</span>
                        {% elif student_rating.overall_average >= 2.5 %}
                            <span class="performance-badge average">متوسط</span>
                        {% else %}
                            <span class="performance-badge needs-improvement">يحتاج تحسين</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #666;">
            <h3>لا توجد بيانات تقييمات للطلاب</h3>
            <p>لم يتم العثور على أي تقييمات للطلاب في النظام.</p>
        </div>
        {% endif %}
    </div>

    <!-- ملخص الإحصائيات -->
    {% if student_ratings %}
    <div class="info-section">
        <div class="section-title">ملخص الإحصائيات</div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">أعلى متوسط:</span>
                <span class="info-value">
                    {% for rating in student_ratings %}
                        {% if forloop.first %}{{ rating.overall_average|floatformat:1 }}{% endif %}
                    {% endfor %}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">أقل متوسط:</span>
                <span class="info-value">
                    {% for rating in student_ratings %}
                        {% if forloop.last %}{{ rating.overall_average|floatformat:1 }}{% endif %}
                    {% endfor %}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">متوسط عام للأكاديمية:</span>
                <span class="info-value">
                    {% if student_ratings %}
                        {% for rating in student_ratings %}
                            {% if forloop.first %}{{ rating.overall_average|floatformat:1 }}{% endif %}
                        {% endfor %}
                    {% else %}
                        0.0
                    {% endif %}
                </span>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <strong>أكاديمية القرآنية</strong><br>
        تقرير شامل تم إنشاؤه تلقائياً في {{ generated_at|date:"d/m/Y H:i" }}<br>
        هذا التقرير يحتوي على معلومات سرية ومخصص للاستخدام الداخلي فقط<br>
        إجمالي الطلاب المدرجين: {{ total_students }}
    </div>
</body>
</html>
