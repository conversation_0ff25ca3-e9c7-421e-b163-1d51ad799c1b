import time
import csv
import io
import os
from datetime import datetime, timedelta
from django.db.models import Avg, Count, Sum, Q
from django.contrib.auth import get_user_model
from django.core.files.base import ContentFile
from django.utils import timezone

# تم إزالة التبعية على courses.models
from lessons.models import LiveLesson
from .models import GeneratedReport

# استيراد مكتبات PDF و Excel
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import openpyxl
    from openpyxl.styles import Font, Align<PERSON>, PatternFill, Border, Side
    from openpyxl.chart import BarChart, Reference
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

User = get_user_model()

class ReportGenerator:
    """مولد التقارير الرئيسي"""

    def __init__(self, custom_report):
        self.custom_report = custom_report
        self.filters = custom_report.filters or {}
        self.date_from = custom_report.date_from
        self.date_to = custom_report.date_to

    def generate_report(self, file_format='pdf', user=None):
        """توليد التقرير بالصيغة المطلوبة"""
        start_time = time.time()

        try:
            # جلب البيانات حسب فئة التقرير
            data = self._get_report_data()

            # توليد الملف حسب الصيغة
            if file_format == 'pdf':
                file_content = self._generate_pdf(data)
                file_extension = '.pdf'
            elif file_format == 'excel':
                file_content = self._generate_excel(data)
                file_extension = '.xlsx'
            elif file_format == 'csv':
                file_content = self._generate_csv(data)
                file_extension = '.csv'
            else:
                raise ValueError(f"صيغة غير مدعومة: {file_format}")

            # حفظ الملف
            filename = f"{self.custom_report.title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_extension}"

            generated_report = GeneratedReport.objects.create(
                custom_report=self.custom_report,
                generated_by=user,
                file_format=file_format,
                file_size=len(file_content),
                total_records=len(data) if isinstance(data, list) else data.count(),
                processing_time=time.time() - start_time,
                is_ready=True
            )

            # حفظ الملف
            generated_report.file_path.save(
                filename,
                ContentFile(file_content),
                save=True
            )

            return generated_report

        except Exception as e:
            # في حالة الخطأ، إنشاء سجل بحالة غير جاهز
            GeneratedReport.objects.create(
                custom_report=self.custom_report,
                generated_by=user,
                file_format=file_format,
                processing_time=time.time() - start_time,
                is_ready=False
            )
            raise e

    def _get_report_data(self):
        """جلب البيانات حسب فئة التقرير"""
        category = self.custom_report.category

        if category == 'teachers':
            return self._get_teachers_data()
        elif category == 'students':
            return self._get_students_data()
        elif category == 'courses':
            return self._get_courses_data()
        elif category == 'financial':
            return self._get_financial_data()
        else:
            return {
                'headers': ['البيانات'],
                'rows': [['لا توجد بيانات متاحة']],
                'summary': {'total': 0}
            }

    def _get_teachers_data(self):
        """جلب بيانات المعلمين"""
        queryset = User.objects.filter(user_type='teacher')

        # تطبيق الفلاتر
        if self.filters.get('is_active'):
            queryset = queryset.filter(is_active=True)

        if self.filters.get('is_verified'):
            queryset = queryset.filter(is_verified=True)

        if self.date_from and self.date_to:
            queryset = queryset.filter(
                created_at__date__range=[self.date_from, self.date_to]
            )

        # إضافة الإحصائيات
        teachers_data = []
        for teacher in queryset:
            # تم إزالة التبعية على Enrollment
            lessons = LiveLesson.objects.filter(teacher=teacher)

            teachers_data.append({
                'الاسم': teacher.get_full_name(),
                'البريد الإلكتروني': teacher.email,
                'تاريخ التسجيل': teacher.created_at.strftime('%Y-%m-%d'),
                'عدد الطلاب': 0,  # تم إزالة التبعية على enrollments
                'عدد الحصص': lessons.count(),
                'الحصص المكتملة': lessons.filter(status='completed').count(),
                'متوسط التقييم': 0,  # تم إزالة التبعية على teacher_ratings
                'الحالة': 'نشط' if teacher.is_active else 'غير نشط',
            })

        return teachers_data

    def _get_students_data(self):
        """جلب بيانات الطلاب"""
        queryset = User.objects.filter(user_type='student')

        # تطبيق الفلاتر
        if self.filters.get('is_active'):
            queryset = queryset.filter(is_active=True)

        if self.date_from and self.date_to:
            queryset = queryset.filter(
                created_at__date__range=[self.date_from, self.date_to]
            )

        # إضافة الإحصائيات
        students_data = []
        for student in queryset:
            # تم إزالة التبعية على Enrollment
            lessons = LiveLesson.objects.filter(student=student)

            students_data.append({
                'الاسم': student.get_full_name(),
                'البريد الإلكتروني': student.email,
                'تاريخ التسجيل': student.created_at.strftime('%Y-%m-%d'),
                'عدد الدورات': 0,  # تم إزالة التبعية على enrollments
                'عدد الحصص': lessons.count(),
                'الحصص المكتملة': lessons.filter(status='completed').count(),
                'نسبة الإكمال': f"{(lessons.filter(status='completed').count() / lessons.count() * 100) if lessons.count() > 0 else 0:.1f}%",
                'الحالة': 'نشط' if student.is_active else 'غير نشط',
            })

        return students_data

    def _get_courses_data(self):
        """جلب بيانات الدورات - تم تعطيلها بعد حذف courses app"""
        return []  # تم إزالة التبعية على Course

    def _get_financial_data(self):
        """جلب البيانات المالية - تم تعطيلها بعد حذف courses app"""
        return []  # تم إزالة التبعية على Enrollment

    def _generate_pdf(self, data):
        """توليد ملف PDF محسن"""
        if REPORTLAB_AVAILABLE:
            return self._generate_pdf_with_reportlab(data)
        else:
            # نسخة مبسطة بدون reportlab
            content = f"""
=== تقرير: {self.custom_report.title} ===
تاريخ التوليد: {datetime.now().strftime('%Y-%m-%d %H:%M')}
الوصف: {self.custom_report.description or 'غير محدد'}
"""

            if self.date_from and self.date_to:
                content += f"الفترة: من {self.date_from} إلى {self.date_to}\n"

            content += f"عدد السجلات: {len(data)}\n"
            content += "=" * 60 + "\n\n"

            if data:
                # إضافة البيانات في جدول منسق
                headers = list(data[0].keys())

                # حساب عرض الأعمدة
                col_widths = {}
                for header in headers:
                    col_widths[header] = max(len(str(header)),
                                           max(len(str(row.get(header, ''))) for row in data))

                # طباعة العناوين
                header_line = " | ".join([str(header).ljust(col_widths[header]) for header in headers])
                content += header_line + "\n"
                content += "-" * len(header_line) + "\n"

                # طباعة البيانات
                for row in data:
                    row_line = " | ".join([str(row.get(col, '')).ljust(col_widths[col]) for col in headers])
                    content += row_line + "\n"

                # إضافة ملخص
                content += "\n" + "=" * 60 + "\n"
                content += f"إجمالي السجلات: {len(data)}\n"

            else:
                content += "لا توجد بيانات للعرض\n"

            return content.encode('utf-8')

    def _generate_pdf_with_reportlab(self, data):
        """توليد PDF باستخدام ReportLab"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        story = []

        # الأنماط
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # وسط
        )

        # العنوان
        story.append(Paragraph(self.custom_report.title, title_style))
        story.append(Spacer(1, 12))

        # معلومات التقرير
        info_data = [
            ['تاريخ التوليد:', datetime.now().strftime('%Y-%m-%d %H:%M')],
            ['الوصف:', self.custom_report.description or 'غير محدد'],
        ]

        if self.date_from and self.date_to:
            info_data.append(['الفترة:', f'من {self.date_from} إلى {self.date_to}'])

        info_data.append(['عدد السجلات:', str(len(data))])

        info_table = Table(info_data, colWidths=[2*inch, 4*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.grey),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(info_table)
        story.append(Spacer(1, 20))

        # جدول البيانات
        if data:
            headers = list(data[0].keys())
            table_data = [headers]

            for row in data:
                table_data.append([str(row.get(col, '')) for col in headers])

            data_table = Table(table_data)
            data_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(data_table)
        else:
            story.append(Paragraph("لا توجد بيانات للعرض", styles['Normal']))

        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()

    def _generate_excel(self, data):
        """توليد ملف Excel محسن"""
        if OPENPYXL_AVAILABLE:
            return self._generate_excel_with_openpyxl(data)
        else:
            # إنشاء محتوى CSV كبديل للـ Excel
            return self._generate_csv(data)

    def _generate_excel_with_openpyxl(self, data):
        """توليد Excel باستخدام openpyxl"""
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "تقرير البيانات"

        # تنسيق العناوين
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        # معلومات التقرير
        ws['A1'] = 'تقرير:'
        ws['B1'] = self.custom_report.title
        ws['A2'] = 'تاريخ التوليد:'
        ws['B2'] = datetime.now().strftime('%Y-%m-%d %H:%M')
        ws['A3'] = 'الوصف:'
        ws['B3'] = self.custom_report.description or 'غير محدد'

        if self.date_from and self.date_to:
            ws['A4'] = 'الفترة:'
            ws['B4'] = f'من {self.date_from} إلى {self.date_to}'
            start_row = 6
        else:
            start_row = 5

        ws[f'A{start_row-1}'] = 'عدد السجلات:'
        ws[f'B{start_row-1}'] = len(data)

        # تنسيق معلومات التقرير
        for row in range(1, start_row):
            ws[f'A{row}'].font = Font(bold=True)
            ws[f'A{row}'].fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")

        if data:
            # العناوين
            headers = list(data[0].keys())
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=start_row, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

            # البيانات
            for row_idx, row_data in enumerate(data, start_row + 1):
                for col_idx, header in enumerate(headers, 1):
                    value = row_data.get(header, '')
                    cell = ws.cell(row=row_idx, column=col_idx, value=str(value))
                    cell.alignment = Alignment(horizontal="center")

                    # تلوين الصفوف بالتناوب
                    if row_idx % 2 == 0:
                        cell.fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")

            # تعديل عرض الأعمدة
            for col in range(1, len(headers) + 1):
                column_letter = openpyxl.utils.get_column_letter(col)
                ws.column_dimensions[column_letter].width = 15

            # إضافة حدود للجدول
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            for row in range(start_row, start_row + len(data) + 1):
                for col in range(1, len(headers) + 1):
                    ws.cell(row=row, column=col).border = thin_border
        else:
            ws.cell(row=start_row, column=1, value="لا توجد بيانات للعرض")

        # حفظ في buffer
        buffer = io.BytesIO()
        wb.save(buffer)
        buffer.seek(0)
        return buffer.getvalue()

    def _generate_csv(self, data):
        """توليد ملف CSV"""
        if not data:
            return b""

        output = io.StringIO()

        if data:
            headers = list(data[0].keys())
            writer = csv.writer(output)

            # كتابة العناوين
            writer.writerow(headers)

            # كتابة البيانات
            for row in data:
                writer.writerow([str(row[col]) for col in headers])

        csv_content = output.getvalue()
        output.close()

        return csv_content.encode('utf-8-sig')
