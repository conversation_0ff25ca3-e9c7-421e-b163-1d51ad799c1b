from django.urls import path
from . import api_views

urlpatterns = [
    # Notification API
    path('notifications/count/', api_views.notification_count, name='api_notification_count'),
    path('notifications/mark-read/<int:notification_id>/', api_views.mark_notification_read, name='api_mark_notification_read'),

    # User API
    path('users/search/', api_views.user_search, name='api_user_search'),
    path('users/profile/', api_views.user_profile, name='api_user_profile'),

    # Timezone API
    path('update-timezone/', api_views.update_timezone, name='api_update_timezone'),
    path('get-time-elements/', api_views.get_time_elements, name='api_get_time_elements'),

    # Live Lessons API
    path('live-lessons/<int:lesson_id>/start/', api_views.api_start_live_lesson, name='api_start_live_lesson'),
    path('live-lessons/<int:lesson_id>/end/', api_views.api_end_live_lesson, name='api_end_live_lesson'),
    path('live-lessons/<int:lesson_id>/auto-end/', api_views.api_auto_end_live_lesson, name='api_auto_end_live_lesson'),
    path('live-lessons/<int:lesson_id>/duration-update/', api_views.api_duration_update, name='api_duration_update'),
    path('live-lessons/<int:lesson_id>/status/', api_views.api_live_lesson_status, name='api_live_lesson_status'),
    path('live-lessons/<int:lesson_id>/rate/', api_views.api_rate_live_lesson, name='api_rate_live_lesson'),
    path('admin/live-lessons-status/', api_views.api_admin_live_lessons_status, name='api_admin_live_lessons_status'),
    path('live-lessons-sidebar/', api_views.api_live_lessons_sidebar, name='api_live_lessons_sidebar'),

    # Teacher Reports API
    path('teacher/lesson-report/', api_views.api_teacher_lesson_report, name='api_teacher_lesson_report'),

    # Student Subscription API
    path('student/subscription/<int:subscription_id>/plan-id/', api_views.api_get_subscription_plan_id, name='api_get_subscription_plan_id'),

    # Scheduled Lessons API
    path('teacher/start-scheduled-lesson/<int:lesson_id>/', api_views.api_start_scheduled_lesson, name='api_start_scheduled_lesson'),
    path('student/join-scheduled-lesson/<int:lesson_id>/', api_views.api_join_scheduled_lesson, name='api_join_scheduled_lesson'),

    # Live Lesson Attendance API
    path('live-lessons/<int:lesson_id>/join/', api_views.api_join_live_lesson, name='api_join_live_lesson'),
    path('live-lessons/<int:lesson_id>/leave/', api_views.api_leave_live_lesson, name='api_leave_live_lesson'),
    path('live-lessons/<int:lesson_id>/participants/', api_views.api_live_lesson_participants, name='api_live_lesson_participants'),

    # Payment Gateway API
    path('payment-gateway-status/', api_views.api_payment_gateway_status, name='api_payment_gateway_status'),
]
