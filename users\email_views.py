"""
Views لإدارة إعدادات البريد الإلكتروني
"""

import json
import logging
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from .email_models import EmailSettings, EmailTemplate, EmailEvent, EmailQueue, EmailLog, EmailSubscription
from .email_forms import EmailSettingsForm, EmailTemplateForm, EmailEventForm, TestEmailForm
from .email_service import EmailService

logger = logging.getLogger(__name__)


@login_required
def email_settings_dashboard(request):
    """صفحة إعدادات البريد الإلكتروني الرئيسية"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    
    # الحصول على الإعدادات الحالية
    email_settings = EmailSettings.get_active_settings()
    
    # الحصول على الإحصائيات
    email_service = EmailService()
    stats = email_service.get_statistics()
    
    # الحصول على آخر الرسائل
    recent_logs = EmailLog.objects.select_related('recipient').order_by('-sent_at')[:10]
    
    # عدد القوالب والأحداث
    templates_count = EmailTemplate.objects.filter(is_active=True).count()
    events_count = EmailEvent.objects.filter(is_active=True).count()
    
    context = {
        'email_settings': email_settings,
        'stats': stats,
        'recent_logs': recent_logs,
        'templates_count': templates_count,
        'events_count': events_count,
        'page_title': 'إعدادات إشعارات البريد الإلكتروني',
    }
    
    return render(request, 'admin/email_settings/dashboard.html', context)


@login_required
def email_smtp_settings(request):
    """صفحة إعدادات SMTP"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    
    email_settings = EmailSettings.get_active_settings()
    
    if request.method == 'POST':
        form = EmailSettingsForm(request.POST, instance=email_settings)
        if form.is_valid():
            form.save()
            messages.success(request, "تم حفظ إعدادات SMTP بنجاح.")
            return redirect('email_smtp_settings')
    else:
        form = EmailSettingsForm(instance=email_settings)
    
    context = {
        'form': form,
        'email_settings': email_settings,
        'page_title': 'إعدادات SMTP',
    }
    
    return render(request, 'admin/email_settings/smtp_settings.html', context)


@login_required
def email_templates_list(request):
    """قائمة قوالب البريد الإلكتروني"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    
    # البحث والتصفية
    search_query = request.GET.get('search', '')
    template_type = request.GET.get('type', '')
    
    templates = EmailTemplate.objects.all()
    
    if search_query:
        templates = templates.filter(
            Q(name__icontains=search_query) |
            Q(subject__icontains=search_query)
        )
    
    if template_type:
        templates = templates.filter(template_type=template_type)

    templates = templates.order_by('-created_at')

    # التصفح
    paginator = Paginator(templates, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الحصول على أنواع القوالب المتاحة
    template_types = EmailTemplate.TEMPLATE_TYPES

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'template_type': template_type,
        'template_types': template_types,
        'page_title': 'قوالب البريد الإلكتروني',
    }
    
    return render(request, 'admin/email_settings/templates_list.html', context)


@login_required
def email_template_create(request):
    """إنشاء قالب بريد إلكتروني جديد"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    
    if request.method == 'POST':
        form = EmailTemplateForm(request.POST)
        if form.is_valid():
            template = form.save()
            messages.success(request, f"تم إنشاء القالب '{template.name}' بنجاح.")
            return redirect('email_templates_list')
    else:
        form = EmailTemplateForm()
    
    context = {
        'form': form,
        'page_title': 'إنشاء قالب جديد',
        'is_create': True,
    }
    
    return render(request, 'admin/email_settings/template_form.html', context)


@login_required
def email_template_edit(request, template_id):
    """تعديل قالب بريد إلكتروني"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    
    template = get_object_or_404(EmailTemplate, id=template_id)
    
    if request.method == 'POST':
        form = EmailTemplateForm(request.POST, instance=template)
        if form.is_valid():
            form.save()
            messages.success(request, f"تم تحديث القالب '{template.name}' بنجاح.")
            return redirect('email_templates_list')
    else:
        form = EmailTemplateForm(instance=template)
    
    context = {
        'form': form,
        'template': template,
        'page_title': f'تعديل القالب: {template.name}',
        'is_create': False,
    }
    
    return render(request, 'admin/email_settings/template_form.html', context)


@login_required
def email_template_preview(request, template_id):
    """معاينة قالب البريد الإلكتروني"""
    if not request.user.is_admin():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    template = get_object_or_404(EmailTemplate, id=template_id)

    # متغيرات تجريبية للمعاينة
    sample_variables = {
        'ACADEMY_NAME': 'أكاديمية القرآنية',
        'ACADEMY_SLOGAN': 'نحو تعليم قرآني متميز',
        'ACADEMY_ADDRESS': 'الرياض، المملكة العربية السعودية',
        'ACADEMY_PHONE': '+966 11 123 4567',
        'ACADEMY_EMAIL': '<EMAIL>',
        'student_name': 'أحمد محمد',
        'teacher_name': 'الأستاذ عبدالله',
        'lesson_title': 'حفظ سورة البقرة',
        'lesson_date': '2025-01-03',
        'lesson_time': '10:00 صباحاً',
        'lesson_duration': '45',
        'subscription_plan': 'الباقة الذهبية',
        'expiry_date': '2025-02-01',
        'remaining_lessons': '15',
        'dashboard_url': request.build_absolute_uri('/dashboard/student/'),
        'subscription_url': request.build_absolute_uri('/dashboard/student/subscriptions/'),
    }

    # استبدال المتغيرات في المحتوى
    preview_subject = template.subject
    preview_html = template.html_content or ''
    preview_text = template.text_content or ''

    for key, value in sample_variables.items():
        placeholder = f'{{{{ {key} }}}}'
        preview_subject = preview_subject.replace(placeholder, str(value))
        preview_html = preview_html.replace(placeholder, str(value))
        preview_text = preview_text.replace(placeholder, str(value))

    return JsonResponse({
        'success': True,
        'template': {
            'name': template.name,
            'subject': preview_subject,
            'html_content': preview_html,
            'text_content': preview_text,
            'template_type': template.get_template_type_display(),
        }
    })


@login_required
def email_events_list(request):
    """قائمة أحداث البريد الإلكتروني"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    
    events = EmailEvent.objects.select_related('template').order_by('event_type')
    
    context = {
        'events': events,
        'page_title': 'أحداث البريد الإلكتروني',
    }
    
    return render(request, 'admin/email_settings/events_list.html', context)


@login_required
def email_logs_list(request):
    """سجلات البريد الإلكتروني"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    
    # البحث والتصفية
    search_query = request.GET.get('search', '')
    status = request.GET.get('status', '')
    
    logs = EmailLog.objects.select_related('recipient')
    
    if search_query:
        logs = logs.filter(
            Q(recipient__email__icontains=search_query) |
            Q(subject__icontains=search_query)
        )
    
    if status:
        logs = logs.filter(status=status)
    
    logs = logs.order_by('-sent_at')
    
    # التصفح
    paginator = Paginator(logs, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status': status,
        'status_choices': EmailLog.STATUS_CHOICES,
        'page_title': 'سجلات البريد الإلكتروني',
    }
    
    return render(request, 'admin/email_settings/logs_list.html', context)


@login_required
@require_http_methods(["POST"])
def test_smtp_connection(request):
    """اختبار الاتصال بخادم SMTP"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'غير مصرح'})
    
    try:
        email_service = EmailService()
        success, message = email_service.test_connection()
        
        return JsonResponse({
            'success': success,
            'message': message
        })
        
    except Exception as e:
        logger.error(f"خطأ في اختبار الاتصال: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'خطأ غير متوقع: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def send_test_email(request):
    """إرسال بريد إلكتروني تجريبي"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'غير مصرح'})
    
    try:
        form = TestEmailForm(request.POST)
        if form.is_valid():
            recipient_email = form.cleaned_data['recipient_email']
            template = form.cleaned_data['template']
            test_data = form.cleaned_data.get('test_data', '{}')
            
            # تحويل بيانات الاختبار
            try:
                context = json.loads(test_data) if test_data else {}
            except json.JSONDecodeError:
                context = {}
            
            # إضافة بيانات افتراضية للاختبار
            context.update({
                'user_name': context.get('user_name', 'المستخدم التجريبي'),
                'test_mode': True,
            })
            
            # إنشاء مستخدم وهمي للاختبار
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            # البحث عن مستخدم بهذا البريد أو إنشاء واحد مؤقت
            try:
                recipient = User.objects.get(email=recipient_email)
            except User.DoesNotExist:
                # إنشاء مستخدم مؤقت للاختبار
                recipient = User(email=recipient_email, username=recipient_email)
            
            # إرسال البريد
            email_service = EmailService()
            success = email_service.send_email(recipient, template, context, immediate=True)
            
            if success:
                return JsonResponse({
                    'success': True,
                    'message': f'تم إرسال البريد التجريبي إلى {recipient_email} بنجاح'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'فشل في إرسال البريد التجريبي'
                })
        else:
            errors = []
            for field, field_errors in form.errors.items():
                errors.extend(field_errors)
            return JsonResponse({
                'success': False,
                'message': 'أخطاء في النموذج: ' + ', '.join(errors)
            })
            
    except Exception as e:
        logger.error(f"خطأ في إرسال البريد التجريبي: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'خطأ غير متوقع: {str(e)}'
        })


@login_required
def get_email_statistics(request):
    """الحصول على إحصائيات البريد الإلكتروني"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'غير مصرح'})
    
    try:
        email_service = EmailService()
        stats = email_service.get_statistics()
        
        return JsonResponse({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'خطأ غير متوقع: {str(e)}'
        })
