# Generated by Django 4.2.7 on 2025-06-18 01:18

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0006_remove_old_email_fields'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='emailattachment',
            name='email_notification',
        ),
        migrations.RemoveField(
            model_name='emailnotification',
            name='recipient',
        ),
        migrations.AlterUniqueTogether(
            name='emailtemplate',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='emailtemplate',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='emailtemplate',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='emailtracking',
            name='email_notification',
        ),
        migrations.DeleteModel(
            name='EmailApprovalRequest',
        ),
        migrations.DeleteModel(
            name='EmailAttachment',
        ),
        migrations.DeleteModel(
            name='EmailNotification',
        ),
        migrations.DeleteModel(
            name='EmailTemplate',
        ),
        migrations.DeleteModel(
            name='EmailTracking',
        ),
    ]
