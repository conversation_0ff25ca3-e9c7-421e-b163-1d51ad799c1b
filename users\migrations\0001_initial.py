# Generated by Django 4.2.7 on 2025-06-01 18:13

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(error_messages={'unique': 'مستخدم بهذا البريد الإلكتروني موجود بالفعل.'}, max_length=254, unique=True, verbose_name='email address')),
                ('user_type', models.CharField(choices=[('admin', 'مدير'), ('teacher', 'معلم'), ('student', 'طالب')], default='student', max_length=10, verbose_name='نوع المستخدم')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('student_level', models.CharField(blank=True, choices=[('beginner', 'مبتدئ'), ('intermediate', 'متوسط'), ('advanced', 'متقدم')], max_length=15, null=True, verbose_name='مستوى الطالب')),
                ('hourly_rate_30', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='أجر الحصة 30 دقيقة')),
                ('hourly_rate_45', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='أجر الحصة 45 دقيقة')),
                ('hourly_rate_60', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='أجر الحصة 60 دقيقة')),
                ('commission_rate_30', models.DecimalField(decimal_places=2, default=70.0, max_digits=5, verbose_name='نسبة العمولة - 30 دقيقة %')),
                ('commission_rate_45', models.DecimalField(decimal_places=2, default=70.0, max_digits=5, verbose_name='نسبة العمولة - 45 دقيقة %')),
                ('commission_rate_60', models.DecimalField(decimal_places=2, default=70.0, max_digits=5, verbose_name='نسبة العمولة - 60 دقيقة %')),
                ('is_active_teacher', models.BooleanField(default=True, verbose_name='معلم نشط')),
                ('verification_status', models.CharField(choices=[('pending', 'في انتظار المراجعة'), ('approved', 'تم الموافقة'), ('rejected', 'تم الرفض'), ('under_review', 'قيد المراجعة')], default='pending', max_length=15, verbose_name='حالة التحقق')),
                ('verification_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات التحقق')),
                ('verified_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق')),
                ('rejection_reason', models.TextField(blank=True, null=True, verbose_name='سبب الرفض')),
                ('was_active', models.BooleanField(default=False, verbose_name='كان نشطًا سابقًا')),
                ('is_banned', models.BooleanField(default=False, verbose_name='محظور')),
                ('ban_type', models.CharField(blank=True, choices=[('temporary', 'مؤقت'), ('permanent', 'دائم')], max_length=20, null=True, verbose_name='نوع الحظر')),
                ('ban_reason', models.TextField(blank=True, null=True, verbose_name='سبب الحظر')),
                ('banned_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحظر')),
                ('banned_until', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء الحظر')),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='profile_pics/', verbose_name='صورة الملف الشخصي')),
                ('bio', models.TextField(blank=True, null=True, verbose_name='نبذة شخصية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('banned_by', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'admin'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='banned_users', to=settings.AUTH_USER_MODEL, verbose_name='تم الحظر بواسطة')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('verified_by', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'admin'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_users', to=settings.AUTH_USER_MODEL, verbose_name='تم التحقق بواسطة')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمون',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='AcademySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('academy_name', models.CharField(default='أكاديمية القرآنية', max_length=100, verbose_name='اسم الأكاديمية')),
                ('academy_email', models.EmailField(default='<EMAIL>', max_length=100, verbose_name='البريد الإلكتروني للأكاديمية')),
                ('academy_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم هاتف الأكاديمية')),
                ('academy_whatsapp', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم واتساب الأكاديمية')),
                ('academy_support_email', models.EmailField(blank=True, default='<EMAIL>', max_length=100, null=True, verbose_name='البريد الإلكتروني للدعم الفني')),
                ('academy_logo', models.ImageField(blank=True, null=True, upload_to='academy/', verbose_name='شعار الأكاديمية')),
                ('academy_address', models.TextField(blank=True, null=True, verbose_name='عنوان الأكاديمية')),
                ('academy_description', models.TextField(blank=True, null=True, verbose_name='وصف الأكاديمية')),
                ('academy_slogan', models.CharField(blank=True, default='نظام قرآنيا التعليمي', max_length=200, null=True, verbose_name='سلوجان الأكاديمية')),
                ('academy_working_hours', models.TextField(blank=True, null=True, verbose_name='ساعات العمل')),
                ('academy_website', models.URLField(blank=True, null=True, verbose_name='موقع الأكاديمية الإلكتروني')),
                ('commercial_register', models.CharField(blank=True, max_length=50, null=True, verbose_name='السجل التجاري')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم البنك')),
                ('bank_account', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحساب البنكي')),
                ('iban', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الآيبان (IBAN)')),
                ('swift_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='رمز السويفت (SWIFT)')),
                ('invoice_footer_text', models.TextField(blank=True, null=True, verbose_name='نص تذييل الفاتورة')),
                ('invoice_terms', models.TextField(blank=True, null=True, verbose_name='شروط وأحكام الفاتورة')),
                ('smtp_enabled', models.BooleanField(default=False, verbose_name='تفعيل SMTP')),
                ('smtp_provider', models.CharField(choices=[('gmail', 'Gmail'), ('outlook', 'Outlook/Hotmail'), ('yahoo', 'Yahoo Mail'), ('sendgrid', 'SendGrid'), ('mailgun', 'Mailgun'), ('namecheap', 'Namecheap'), ('hostinger', 'Hostinger'), ('godaddy', 'GoDaddy'), ('cpanel', 'cPanel/WHM'), ('custom', 'إعدادات مخصصة')], default='custom', max_length=50, verbose_name='مزود خدمة SMTP')),
                ('smtp_host', models.CharField(blank=True, max_length=255, verbose_name='خادم SMTP')),
                ('smtp_port', models.IntegerField(default=587, verbose_name='منفذ SMTP')),
                ('smtp_username', models.CharField(blank=True, max_length=255, verbose_name='اسم المستخدم')),
                ('smtp_password', models.CharField(blank=True, max_length=500, verbose_name='كلمة المرور')),
                ('smtp_use_tls', models.BooleanField(default=True, verbose_name='استخدام TLS')),
                ('smtp_use_ssl', models.BooleanField(default=False, verbose_name='استخدام SSL')),
                ('smtp_from_email', models.EmailField(blank=True, max_length=254, verbose_name='البريد المرسل')),
                ('smtp_from_name', models.CharField(blank=True, max_length=255, verbose_name='اسم المرسل')),
                ('smtp_reply_to', models.EmailField(blank=True, max_length=254, verbose_name='الرد على')),
                ('smtp_max_attachments', models.IntegerField(default=5, verbose_name='الحد الأقصى للمرفقات')),
                ('smtp_max_attachment_size', models.IntegerField(default=25, verbose_name='الحد الأقصى لحجم المرفق (MB)')),
                ('email_tracking_enabled', models.BooleanField(default=True, verbose_name='تفعيل تتبع الرسائل')),
                ('manual_approval_required', models.BooleanField(default=True, verbose_name='تتطلب موافقة يدوية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعدادات الأكاديمية',
                'verbose_name_plural': 'إعدادات الأكاديمية',
            },
        ),
        migrations.CreateModel(
            name='EmailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('lesson_reminder_1day', 'تذكير بالحصة - يوم واحد'), ('lesson_reminder_30min', 'تذكير بالحصة - 30 دقيقة'), ('manual_notification', 'إشعار يدوي'), ('welcome_message', 'رسالة ترحيب'), ('account_approved', 'تم الموافقة على الحساب'), ('account_rejected', 'تم رفض الحساب'), ('subscription_payment_success', 'نجح الدفع - إرسال فاتورة'), ('subscription_activated', 'تفعيل الاشتراك'), ('subscription_cancelled', 'إلغاء الاشتراك'), ('subscription_expiring_7days', 'تذكير انتهاء الاشتراك - 7 أيام'), ('subscription_expiring_3days', 'تذكير انتهاء الاشتراك - 3 أيام'), ('subscription_expired', 'انتهاء الاشتراك'), ('payment_received_admin', 'إشعار المدير - دفعة جديدة'), ('welcome', 'رسالة ترحيب للمستخدم الجديد'), ('account_suspended', 'إشعار حظر الحساب'), ('account_unsuspended', 'إشعار إلغاء حظر الحساب'), ('account_deleted', 'إشعار حذف الحساب'), ('under_review', 'إشعار وضع الحساب قيد المراجعة')], max_length=50, verbose_name='نوع الإشعار')),
                ('subject', models.CharField(max_length=255, verbose_name='الموضوع')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('html_message', models.TextField(blank=True, verbose_name='رسالة HTML')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=20, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('pending', 'في انتظار الإرسال'), ('sent', 'تم الإرسال'), ('failed', 'فشل الإرسال'), ('cancelled', 'تم الإلغاء')], default='pending', max_length=20, verbose_name='الحالة')),
                ('scheduled_send_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت الإرسال المجدول')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),

                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستلم')),
            ],
            options={
                'verbose_name': 'إشعار بريدي',
                'verbose_name_plural': 'الإشعارات البريدية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_online', models.BooleanField(default=False, verbose_name='متصل')),
                ('last_seen', models.DateTimeField(auto_now=True, verbose_name='آخر ظهور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='user_status', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'حالة المستخدم',
                'verbose_name_plural': 'حالات المستخدمين',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emergency_contact', models.CharField(blank=True, max_length=100, null=True, verbose_name='جهة الاتصال في حالات الطوارئ')),
                ('emergency_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='هاتف الطوارئ')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('preferred_language', models.CharField(choices=[('ar', 'العربية'), ('en', 'English')], default='ar', max_length=5, verbose_name='اللغة المفضلة')),
                ('timezone', models.CharField(default='Asia/Riyadh', max_length=50, verbose_name='المنطقة الزمنية')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف تعريف المستخدم',
                'verbose_name_plural': 'ملفات تعريف المستخدمين',
            },
        ),
        migrations.CreateModel(
            name='PaymentGatewaySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('paypal_client_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='PayPal Client ID')),
                ('paypal_client_secret', models.CharField(blank=True, max_length=255, null=True, verbose_name='PayPal Client Secret')),
                ('paypal_sandbox_mode', models.BooleanField(default=True, verbose_name='وضع التجربة PayPal')),
                ('paypal_webhook_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='PayPal Webhook ID')),
                ('paypal_enabled', models.BooleanField(default=False, verbose_name='تفعيل PayPal')),
                ('stripe_publishable_key', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Publishable Key')),
                ('stripe_secret_key', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Secret Key')),
                ('stripe_webhook_secret', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Webhook Secret')),
                ('stripe_enabled', models.BooleanField(default=False, verbose_name='تفعيل Stripe')),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم البنك')),
                ('bank_account_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحساب')),
                ('bank_account_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم صاحب الحساب')),
                ('bank_iban', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم IBAN')),
                ('bank_swift_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='رمز SWIFT')),
                ('bank_branch', models.CharField(blank=True, max_length=100, null=True, verbose_name='الفرع')),
                ('bank_transfer_enabled', models.BooleanField(default=True, verbose_name='تفعيل التحويل البنكي')),
                ('default_currency', models.CharField(default='SAR', max_length=3, verbose_name='العملة الافتراضية')),
                ('auto_approve_payments', models.BooleanField(default=False, verbose_name='الموافقة التلقائية على المدفوعات')),
                ('payment_timeout_minutes', models.PositiveIntegerField(default=30, verbose_name='مهلة انتظار الدفع (بالدقائق)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات بوابات الدفع',
                'verbose_name_plural': 'إعدادات بوابات الدفع',
            },
        ),
        migrations.CreateModel(
            name='EmailTracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='معرف التتبع')),
                ('is_opened', models.BooleanField(default=False, verbose_name='تم فتحه')),
                ('opened_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الفتح')),
                ('open_count', models.IntegerField(default=0, verbose_name='عدد مرات الفتح')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('links_clicked', models.JSONField(blank=True, default=list, verbose_name='الروابط المنقورة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('email_notification', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='tracking', to='users.emailnotification', verbose_name='الإشعار البريدي')),
            ],
            options={
                'verbose_name': 'تتبع بريدي',
                'verbose_name_plural': 'تتبع الرسائل البريدية',
            },
        ),
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='اسم القالب')),
                ('template_type', models.CharField(choices=[('lesson_reminder_1day', 'تذكير بالحصة - يوم واحد'), ('lesson_reminder_30min', 'تذكير بالحصة - 30 دقيقة'), ('welcome_message', 'رسالة ترحيب'), ('account_approved', 'تم الموافقة على الحساب'), ('account_rejected', 'تم رفض الحساب'), ('custom', 'قالب مخصص'), ('subscription_payment_success', 'نجح الدفع - إرسال فاتورة'), ('subscription_activated', 'تفعيل الاشتراك'), ('subscription_cancelled', 'إلغاء الاشتراك'), ('subscription_expiring_7days', 'تذكير انتهاء الاشتراك - 7 أيام'), ('subscription_expiring_3days', 'تذكير انتهاء الاشتراك - 3 أيام'), ('subscription_expired', 'انتهاء الاشتراك'), ('payment_received_admin', 'إشعار المدير - دفعة جديدة')], max_length=50, verbose_name='نوع القالب')),
                ('subject', models.CharField(max_length=255, verbose_name='الموضوع')),
                ('body', models.TextField(verbose_name='محتوى القالب')),
                ('html_body', models.TextField(blank=True, verbose_name='محتوى HTML')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'قالب بريدي',
                'verbose_name_plural': 'قوالب البريد',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EmailAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='email_attachments/%Y/%m/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'zip'])], verbose_name='الملف')),
                ('original_name', models.CharField(max_length=255, verbose_name='الاسم الأصلي')),
                ('file_size', models.IntegerField(verbose_name='حجم الملف (بايت)')),
                ('content_type', models.CharField(max_length=100, verbose_name='نوع المحتوى')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('email_notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='users.emailnotification', verbose_name='الإشعار البريدي')),
            ],
            options={
                'verbose_name': 'مرفق بريدي',
                'verbose_name_plural': 'مرفقات البريد',
            },
        ),
        migrations.CreateModel(
            name='EmailApprovalRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=255, verbose_name='الموضوع')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('html_message', models.TextField(blank=True, verbose_name='رسالة HTML')),
                ('status', models.CharField(choices=[('pending', 'في انتظار الموافقة'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('sent', 'تم الإرسال')], default='pending', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=20, verbose_name='الأولوية')),
                ('scheduled_send_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت الإرسال المجدول')),
                ('approval_notes', models.TextField(blank=True, verbose_name='ملاحظات الموافقة')),
                ('rejection_reason', models.TextField(blank=True, verbose_name='سبب الرفض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_emails', to=settings.AUTH_USER_MODEL, verbose_name='وافق عليه')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_requests', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('recipients', models.ManyToManyField(related_name='received_email_requests', to=settings.AUTH_USER_MODEL, verbose_name='المستلمون')),
            ],
            options={
                'verbose_name': 'طلب موافقة بريدي',
                'verbose_name_plural': 'طلبات الموافقة البريدية',
                'ordering': ['-created_at'],
            },
        ),
    ]
