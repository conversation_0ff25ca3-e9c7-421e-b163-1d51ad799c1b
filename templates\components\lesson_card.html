<!-- بطاقة الحصة الموحدة -->
<div class="lesson-card border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200" 
     data-lesson-id="{{ lesson.id }}" 
     data-lesson-type="{{ lesson.lesson_type }}"
     data-lesson-status="{{ lesson.status }}">
    
    <div class="flex items-center justify-between">
        <!-- معلومات الحصة -->
        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- أيقونة/رقم الحصة -->
            <div class="w-12 h-12 rounded-full flex items-center justify-center
                        {% if lesson.lesson_type == 'trial' %}
                            bg-orange-100 text-orange-600
                        {% elif lesson.lesson_type == 'makeup' %}
                            bg-pink-100 text-pink-600
                        {% else %}
                            bg-islamic-primary text-white
                        {% endif %}">
                {% if lesson.lesson_type == 'trial' %}
                    <i class="fas fa-flask"></i>
                {% elif lesson.lesson_type == 'makeup' %}
                    <i class="fas fa-redo"></i>
                {% else %}
                    <span class="font-bold">{{ lesson.lesson_number|default:'#' }}</span>
                {% endif %}
            </div>
            
            <!-- تفاصيل الحصة -->
            <div>
                <h5 class="font-medium text-gray-900">
                    {% if lesson.lesson_type == 'trial' %}
                        حصة تجريبية
                    {% elif lesson.lesson_type == 'makeup' %}
                        حصة تعويضية
                    {% else %}
                        حصة رقم {{ lesson.lesson_number }}
                    {% endif %}
                </h5>
                
                <!-- عرض المشاركين حسب نوع المستخدم -->
                <p class="text-sm text-gray-600">
                    {% if user_type == 'admin' %}
                        {{ lesson.teacher.get_full_name }} → {{ lesson.student.get_full_name }}
                    {% elif user_type == 'teacher' %}
                        مع {{ lesson.student.get_full_name }}
                    {% elif user_type == 'student' %}
                        مع {{ lesson.teacher.get_full_name }}
                    {% endif %}
                </p>
                
                <!-- التاريخ والوقت -->
                <p class="text-sm text-gray-500">
                    <i class="fas fa-calendar ml-1"></i>
                    {{ lesson.scheduled_date|date:"Y/m/d" }} - {{ lesson.scheduled_date|time:"H:i" }}
                    <span class="mr-2">
                        <i class="fas fa-clock ml-1"></i>
                        {{ lesson.duration_minutes }} دقيقة
                    </span>
                </p>
                
                <!-- معلومات الاشتراك -->
                {% if lesson.lesson_type == 'subscription' and lesson.subscription %}
                    <p class="text-xs text-islamic-primary">
                        <i class="fas fa-tag ml-1"></i>
                        {{ lesson.subscription.plan.name }}
                    </p>
                {% endif %}
                
                <!-- معلومات الحصة الأصلية للحصص التعويضية -->
                {% if lesson.lesson_type == 'makeup' and lesson.original_lesson %}
                    <p class="text-xs text-pink-600">
                        <i class="fas fa-link ml-1"></i>
                        تعويض عن حصة {{ lesson.original_lesson.scheduled_date|date:"Y/m/d" }}
                    </p>
                {% endif %}
            </div>
        </div>
        
        <!-- الحالة والإجراءات -->
        <div class="flex items-center space-x-2 space-x-reverse">
            <!-- شارة الحالة -->
            {% include 'components/lesson_status_badge.html' with lesson=lesson %}
            
            <!-- أزرار الإجراءات -->
            {% include 'components/lesson_actions.html' with lesson=lesson user_type=user_type %}
        </div>
    </div>
    
    <!-- معلومات إضافية قابلة للطي -->
    {% if lesson.description or lesson.teacher_report or lesson.cancellation_note %}
    <div class="mt-3 pt-3 border-t border-gray-100">
        {% if lesson.description %}
            <p class="text-sm text-gray-600 mb-2">
                <i class="fas fa-info-circle ml-1"></i>
                {{ lesson.description|truncatewords:15 }}
            </p>
        {% endif %}
        
        {% if lesson.teacher_report and lesson.teacher_report_submitted %}
            <p class="text-sm text-green-600 mb-2">
                <i class="fas fa-check-circle ml-1"></i>
                تم إرسال تقرير المعلم
            </p>
        {% endif %}
        
        {% if lesson.student_evaluation_submitted %}
            <p class="text-sm text-blue-600 mb-2">
                <i class="fas fa-star ml-1"></i>
                تم إرسال تقييم الطالب
            </p>
        {% endif %}
        
        {% if lesson.cancellation_note %}
            <p class="text-sm text-red-600">
                <i class="fas fa-exclamation-triangle ml-1"></i>
                {{ lesson.cancellation_note|truncatewords:10 }}
            </p>
        {% endif %}
    </div>
    {% endif %}
</div>
