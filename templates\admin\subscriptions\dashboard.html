{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الاشتراكات والباقات{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-islamic-primary mb-4 flex items-center justify-center">
                <i class="fas fa-credit-card text-islamic-gold ml-3"></i>
                إدارة الاشتراكات والباقات
            </h1>
            <p class="text-gray-600 text-lg">إدارة شاملة للباقات والاشتراكات والمدفوعات</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Plans -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-blue-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الباقات</p>
                        <p class="text-3xl font-bold text-blue-600">{{ total_plans }}</p>
                        <p class="text-sm text-gray-500">{{ active_plans }} نشطة</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-box text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Subscriptions -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-green-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الاشتراكات</p>
                        <p class="text-3xl font-bold text-green-600">{{ total_subscriptions }}</p>
                        <p class="text-sm text-gray-500">{{ active_subscriptions }} نشطة</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-yellow-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                        <p class="text-3xl font-bold text-yellow-600">{{ total_revenue|floatformat:0 }}</p>
                        <p class="text-sm text-gray-500">بعملات متنوعة</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Pending Payments -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-red-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">مدفوعات معلقة</p>
                        <p class="text-3xl font-bold text-red-600">{{ pending_payments }}</p>
                        <p class="text-sm text-gray-500">تحتاج مراجعة</p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-xl font-bold text-islamic-primary mb-4">
                <i class="fas fa-bolt ml-2"></i>
                إجراءات سريعة
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{% url 'admin_plan_create' %}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg flex items-center justify-center transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء باقة جديدة
                </a>
                <a href="{% url 'admin_plans_list' %}" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg flex items-center justify-center transition-colors">
                    <i class="fas fa-list ml-2"></i>
                    إدارة الباقات
                </a>
                <a href="{% url 'admin_subscriptions_list' %}" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg flex items-center justify-center transition-colors">
                    <i class="fas fa-users ml-2"></i>
                    إدارة الاشتراكات
                </a>
                <a href="{% url 'admin_payments_list' %}" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg flex items-center justify-center transition-colors">
                    <i class="fas fa-credit-card ml-2"></i>
                    إدارة المدفوعات
                </a>
            </div>
        </div>

        <!-- Plans with Statistics -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-xl font-bold text-islamic-primary mb-4">
                <i class="fas fa-box-open ml-2"></i>
                الباقات مع الإحصائيات
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباقة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاشتراكات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإيرادات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for plan_stat in plans_with_stats %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-islamic-primary rounded-lg flex items-center justify-center ml-3">
                                        <i class="fas fa-box text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ plan_stat.plan.name }}</div>
                                        <div class="text-sm text-gray-500">{{ plan_stat.plan.get_plan_type_display }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% if plan_stat.plan.discount_percentage > 0 %}
                                    <span class="line-through text-gray-500">{{ plan_stat.plan.get_formatted_price }} {{ plan_stat.plan.get_currency_display }}</span>
                                    <br>
                                    <span class="text-green-600 font-bold">{{ plan_stat.plan.get_formatted_discounted_price }} {{ plan_stat.plan.get_currency_display }}</span>
                                {% else %}
                                    {{ plan_stat.plan.get_formatted_price }} {{ plan_stat.plan.get_currency_display }}
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>إجمالي: {{ plan_stat.total_subscriptions }}</div>
                                <div class="text-green-600">نشطة: {{ plan_stat.active_subscriptions }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ plan_stat.revenue|floatformat:0 }} {{ plan_stat.plan.get_currency_symbol }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if plan_stat.plan.is_active %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        نشطة
                                    </span>
                                {% else %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        معطلة
                                    </span>
                                {% endif %}
                                {% if plan_stat.plan.is_featured %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 mr-1">
                                        مميزة
                                    </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pending Approvals Section -->
        {% if pending_approvals %}
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8 border-r-4 border-orange-500">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold text-orange-600">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    اشتراكات تحتاج موافقة ({{ pending_approvals|length }})
                </h3>
                <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    عاجل
                </span>
            </div>
            <div class="space-y-4">
                {% for subscription in pending_approvals %}
                <div class="flex items-center justify-between p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center ml-3">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ subscription.student.get_full_name }}</div>
                            <div class="text-sm text-gray-500">{{ subscription.plan.name }}</div>
                            <div class="text-xs text-orange-600">{{ subscription.created_at|date:"Y-m-d H:i" }}</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="text-left ml-4">
                            <div class="text-sm font-medium text-gray-900">{{ subscription.amount_paid|floatformat:2 }} {{ subscription.plan.get_currency_symbol }}</div>
                            <div class="text-xs text-gray-500">{{ subscription.plan.get_plan_type_display }}</div>
                        </div>
                        <a href="{% url 'admin_approve_subscription' subscription.id %}"
                           class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                            مراجعة
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Recent Subscriptions and Pending Transfers -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Subscriptions -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-islamic-primary mb-4">
                    <i class="fas fa-clock ml-2"></i>
                    أحدث الاشتراكات
                </h3>
                <div class="space-y-4">
                    {% for subscription in recent_subscriptions %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-islamic-primary rounded-full flex items-center justify-center ml-3">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ subscription.student.get_full_name }}</div>
                                <div class="text-sm text-gray-500">{{ subscription.plan.name }}</div>
                            </div>
                        </div>
                        <div class="text-left">
                            <div class="text-sm font-medium text-gray-900">{{ subscription.amount_paid|floatformat:2 }} {{ subscription.plan.get_currency_symbol }}</div>
                            <div class="text-sm text-gray-500">{{ subscription.created_at|date:"Y-m-d" }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Pending Bank Transfers -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-islamic-primary mb-4">
                    <i class="fas fa-university ml-2"></i>
                    تحويلات بنكية معلقة
                </h3>
                <div class="space-y-4">
                    {% for transfer in pending_transfers %}
                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center ml-3">
                                <i class="fas fa-exclamation text-white text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ transfer.payment.subscription.student.get_full_name }}</div>
                                <div class="text-sm text-gray-500">{{ transfer.payment.subscription.plan.name }}</div>
                            </div>
                        </div>
                        <div class="text-left">
                            <div class="text-sm font-medium text-gray-900">{{ transfer.transfer_amount|floatformat:2 }} {{ transfer.payment.subscription.plan.get_currency_symbol }}</div>
                            <div class="text-sm text-gray-500">{{ transfer.uploaded_at|date:"Y-m-d" }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
