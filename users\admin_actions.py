"""
إجراءات مخصصة لإدارة المستخدمين في Django Admin
تتضمن إرسال الإشعارات التلقائية
"""

from django.contrib import admin
from django.contrib.auth import get_user_model
from django.utils.html import format_html
from django.urls import reverse
from django.shortcuts import redirect
from django.contrib import messages
from django.utils import timezone
from datetime import datetime, timedelta
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class UserAdminActions:
    """إجراءات إدارة المستخدمين مع الإشعارات"""
    
    @admin.action(description='الموافقة على المستخدمين المحددين')
    def approve_users(self, request, queryset):
        """الموافقة على المستخدمين وإرسال إشعارات"""
        approved_count = 0
        failed_count = 0
        
        for user in queryset:
            try:
                if user.verification_status != 'approved':
                    user.approve_verification(request.user, "تمت الموافقة من لوحة الإدارة")
                    approved_count += 1
                    logger.info(f"تمت الموافقة على المستخدم {user.email} بواسطة {request.user.email}")
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في الموافقة على المستخدم {user.email}: {str(e)}")
        
        if approved_count > 0:
            messages.success(request, f'تمت الموافقة على {approved_count} مستخدم وإرسال الإشعارات')
        if failed_count > 0:
            messages.error(request, f'فشل في الموافقة على {failed_count} مستخدم')
    
    @admin.action(description='رفض المستخدمين المحددين')
    def reject_users(self, request, queryset):
        """رفض المستخدمين وإرسال إشعارات"""
        rejected_count = 0
        failed_count = 0
        
        for user in queryset:
            try:
                if user.verification_status != 'rejected':
                    user.reject_verification(
                        request.user, 
                        "تم رفض الحساب من لوحة الإدارة",
                        "لم يستوف الحساب المتطلبات المطلوبة"
                    )
                    rejected_count += 1
                    logger.info(f"تم رفض المستخدم {user.email} بواسطة {request.user.email}")
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في رفض المستخدم {user.email}: {str(e)}")
        
        if rejected_count > 0:
            messages.success(request, f'تم رفض {rejected_count} مستخدم وإرسال الإشعارات')
        if failed_count > 0:
            messages.error(request, f'فشل في رفض {failed_count} مستخدم')
    
    @admin.action(description='وضع قيد المراجعة')
    def set_under_review(self, request, queryset):
        """وضع المستخدمين قيد المراجعة وإرسال إشعارات"""
        updated_count = 0
        failed_count = 0
        
        for user in queryset:
            try:
                user.verification_status = 'under_review'
                user.verified_by = request.user
                user.verified_at = timezone.now()
                user.verification_notes = "تم وضع الحساب قيد المراجعة من لوحة الإدارة"
                user.save()
                
                # إرسال إشعار
                user._send_admin_action_notification('under_review', request.user, "حسابك قيد المراجعة")
                updated_count += 1
                logger.info(f"تم وضع المستخدم {user.email} قيد المراجعة بواسطة {request.user.email}")
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في وضع المستخدم {user.email} قيد المراجعة: {str(e)}")
        
        if updated_count > 0:
            messages.success(request, f'تم وضع {updated_count} مستخدم قيد المراجعة وإرسال الإشعارات')
        if failed_count > 0:
            messages.error(request, f'فشل في وضع {failed_count} مستخدم قيد المراجعة')
    
    @admin.action(description='حظر مؤقت (7 أيام)')
    def ban_temporary_7days(self, request, queryset):
        """حظر مؤقت لمدة 7 أيام"""
        banned_count = 0
        failed_count = 0
        banned_until = timezone.now() + timedelta(days=7)
        
        for user in queryset:
            try:
                user.ban_user(
                    request.user,
                    'temporary',
                    "حظر مؤقت لمدة 7 أيام من لوحة الإدارة",
                    banned_until
                )
                banned_count += 1
                logger.info(f"تم حظر المستخدم {user.email} مؤقتاً بواسطة {request.user.email}")
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في حظر المستخدم {user.email}: {str(e)}")
        
        if banned_count > 0:
            messages.success(request, f'تم حظر {banned_count} مستخدم مؤقتاً وإرسال الإشعارات')
        if failed_count > 0:
            messages.error(request, f'فشل في حظر {failed_count} مستخدم')
    
    @admin.action(description='حظر مؤقت (30 يوم)')
    def ban_temporary_30days(self, request, queryset):
        """حظر مؤقت لمدة 30 يوم"""
        banned_count = 0
        failed_count = 0
        banned_until = timezone.now() + timedelta(days=30)
        
        for user in queryset:
            try:
                user.ban_user(
                    request.user,
                    'temporary',
                    "حظر مؤقت لمدة 30 يوم من لوحة الإدارة",
                    banned_until
                )
                banned_count += 1
                logger.info(f"تم حظر المستخدم {user.email} مؤقتاً بواسطة {request.user.email}")
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في حظر المستخدم {user.email}: {str(e)}")
        
        if banned_count > 0:
            messages.success(request, f'تم حظر {banned_count} مستخدم مؤقتاً وإرسال الإشعارات')
        if failed_count > 0:
            messages.error(request, f'فشل في حظر {failed_count} مستخدم')
    
    @admin.action(description='حظر دائم')
    def ban_permanent(self, request, queryset):
        """حظر دائم"""
        banned_count = 0
        failed_count = 0
        
        for user in queryset:
            try:
                user.ban_user(
                    request.user,
                    'permanent',
                    "حظر دائم من لوحة الإدارة"
                )
                banned_count += 1
                logger.info(f"تم حظر المستخدم {user.email} دائماً بواسطة {request.user.email}")
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في حظر المستخدم {user.email}: {str(e)}")
        
        if banned_count > 0:
            messages.success(request, f'تم حظر {banned_count} مستخدم دائماً وإرسال الإشعارات')
        if failed_count > 0:
            messages.error(request, f'فشل في حظر {failed_count} مستخدم')
    
    @admin.action(description='إلغاء الحظر')
    def unban_users(self, request, queryset):
        """إلغاء حظر المستخدمين"""
        unbanned_count = 0
        failed_count = 0
        
        for user in queryset:
            try:
                if user.is_banned:
                    user.unban_user(request.user)
                    unbanned_count += 1
                    logger.info(f"تم إلغاء حظر المستخدم {user.email} بواسطة {request.user.email}")
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في إلغاء حظر المستخدم {user.email}: {str(e)}")
        
        if unbanned_count > 0:
            messages.success(request, f'تم إلغاء حظر {unbanned_count} مستخدم وإرسال الإشعارات')
        if failed_count > 0:
            messages.error(request, f'فشل في إلغاء حظر {failed_count} مستخدم')
    
    @admin.action(description='حذف المستخدمين (مع إشعار)')
    def delete_users_with_notification(self, request, queryset):
        """حذف المستخدمين مع إرسال إشعار مسبق"""
        deleted_count = 0
        failed_count = 0
        
        for user in queryset:
            try:
                # إرسال إشعار حذف قبل الحذف
                user._send_admin_action_notification('deleted', request.user, "تم حذف حسابك من لوحة الإدارة")
                
                # حذف المستخدم
                user_email = user.email
                user.delete()
                deleted_count += 1
                logger.info(f"تم حذف المستخدم {user_email} بواسطة {request.user.email}")
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في حذف المستخدم {user.email}: {str(e)}")
        
        if deleted_count > 0:
            messages.success(request, f'تم حذف {deleted_count} مستخدم وإرسال الإشعارات')
        if failed_count > 0:
            messages.error(request, f'فشل في حذف {failed_count} مستخدم')
    
    @admin.action(description='إرسال إشعار ترحيب')
    def send_welcome_notification(self, request, queryset):
        """إرسال إشعار ترحيب للمستخدمين"""
        sent_count = 0
        failed_count = 0
        
        for user in queryset:
            try:
                from users.email_service import UserEmailService
                email_service = UserEmailService()
                result = email_service.send_welcome_email(user)
                
                if result:
                    sent_count += 1
                    logger.info(f"تم إرسال إشعار ترحيب للمستخدم {user.email}")
                else:
                    failed_count += 1
            except Exception as e:
                failed_count += 1
                logger.error(f"فشل في إرسال إشعار ترحيب للمستخدم {user.email}: {str(e)}")
        
        if sent_count > 0:
            messages.success(request, f'تم إرسال إشعار ترحيب لـ {sent_count} مستخدم')
        if failed_count > 0:
            messages.error(request, f'فشل في إرسال إشعار ترحيب لـ {failed_count} مستخدم')
