{% extends 'lessons/base_lesson.html' %}
{% load static %}

{% block page_title %}إدارة الحصص{% endblock %}
{% block header_icon %}fas fa-cogs{% endblock %}
{% block page_description %}إدارة جميع أنواع الحصص من مكان واحد{% endblock %}

{% block header_actions %}
<div class="flex flex-col sm:flex-row gap-2 sm:gap-4">
    <a href="{% url 'lessons:admin_create_trial_lesson' %}"
       class="btn-primary flex items-center justify-center">
        <i class="fas fa-flask ml-2"></i>
        حصة تجريبية جديدة
    </a>
    <a href="{% url 'lessons:admin_create_subscription_lesson' %}"
       class="btn-secondary flex items-center justify-center">
        <i class="fas fa-calendar-plus ml-2"></i>
        حصة مجدولة جديدة
    </a>
    <div class="relative">
        <button onclick="toggleDropdown('admin-quick-actions')" 
                class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors flex items-center">
            <i class="fas fa-ellipsis-v ml-2"></i>
            إجراءات سريعة
        </button>
        <div id="admin-quick-actions" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
            <a href="{% url 'lessons:admin_bulk_reschedule' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <i class="fas fa-calendar-alt ml-2"></i>
                إعادة جدولة متعددة
            </a>
            <a href="{% url 'lessons:admin_export_lessons' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <i class="fas fa-download ml-2"></i>
                تصدير البيانات
            </a>
            <a href="{% url 'lessons:admin_lesson_reports' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <i class="fas fa-chart-bar ml-2"></i>
                التقارير
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block lesson_stats %}
{% include 'components/lesson_stats.html' with stats=admin_stats user_type='admin' %}
{% endblock %}

{% block lesson_filters %}
{% include 'components/lesson_filters.html' with user_type='admin' available_teachers=teachers available_students=students %}
{% endblock %}

{% block lesson_content %}
<div class="space-y-6">
    
    <!-- حصص اليوم -->
    {% if today_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-calendar-day text-blue-600 ml-2"></i>
                حصص اليوم ({{ today_lessons|length }})
            </h3>
            <span class="text-sm text-gray-500">{{ today_date|date:"Y/m/d" }}</span>
        </div>
        <div class="space-y-3">
            {% for lesson in today_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='admin' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- الحصص الجارية -->
    {% if live_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-green-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-green-900">
                <i class="fas fa-video text-green-600 ml-2"></i>
                الحصص الجارية ({{ live_lessons|length }})
            </h3>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 animate-pulse">
                <span class="w-2 h-2 bg-green-600 rounded-full ml-2"></span>
                مباشر
            </span>
        </div>
        <div class="space-y-3">
            {% for lesson in live_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='admin' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- الحصص القادمة -->
    {% if upcoming_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-blue-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-blue-900">
                <i class="fas fa-clock text-blue-600 ml-2"></i>
                الحصص القادمة ({{ upcoming_lessons|length }})
            </h3>
            <button onclick="toggleSection('upcoming-lessons')" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div id="upcoming-lessons" class="space-y-3">
            {% for lesson in upcoming_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='admin' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- جميع الحصص -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-list text-gray-600 ml-2"></i>
                جميع الحصص
            </h3>
            <div class="flex items-center space-x-2 space-x-reverse">
                <select id="sort-select" class="form-control text-sm" onchange="sortLessons(this.value)">
                    <option value="date_asc">التاريخ (الأقدم أولاً)</option>
                    <option value="date_desc">التاريخ (الأحدث أولاً)</option>
                    <option value="status">الحالة</option>
                    <option value="type">النوع</option>
                </select>
                <button onclick="toggleView('grid')" id="grid-view-btn" class="p-2 text-gray-600 hover:text-gray-800">
                    <i class="fas fa-th-large"></i>
                </button>
                <button onclick="toggleView('list')" id="list-view-btn" class="p-2 text-gray-600 hover:text-gray-800">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
        
        <div id="lessons-container" class="space-y-3">
            {% for lesson in lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='admin' %}
            {% empty %}
                <div class="text-center py-12">
                    <i class="fas fa-calendar-times text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد حصص</h3>
                    <p class="text-gray-500 mb-4">لا توجد حصص مطابقة للمعايير المحددة</p>
                    <a href="{% url 'lessons:admin_create_trial_lesson' %}" class="btn-primary">
                        <i class="fas fa-plus ml-2"></i>
                        إنشاء حصة جديدة
                    </a>
                </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if lessons.has_other_pages %}
        <div class="mt-6 flex justify-center">
            <nav class="flex items-center space-x-2 space-x-reverse">
                {% if lessons.has_previous %}
                    <a href="?page={{ lessons.previous_page_number }}" 
                       class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                {% endif %}
                
                {% for num in lessons.paginator.page_range %}
                    {% if lessons.number == num %}
                        <span class="px-3 py-2 text-sm bg-blue-600 text-white rounded">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}" 
                           class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}
                
                {% if lessons.has_next %}
                    <a href="?page={{ lessons.next_page_number }}" 
                       class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block lesson_modals %}
<!-- Modal لتفاصيل الحصة -->
<div id="lessonDetailsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <!-- سيتم إنشاؤه بـ JavaScript -->
</div>

<!-- Modal للإجراءات المتعددة -->
<div id="bulkActionsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-900">إجراءات متعددة</h3>
            <button onclick="hideBulkActionsModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="bulkActionsContent">
            <!-- سيتم ملؤها ديناميكياً -->
        </div>
    </div>
</div>
{% endblock %}

{% block lesson_js %}
<script>
// إضافة وظائف خاصة بصفحة المدير
document.body.dataset.userType = 'admin';

function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    const button = event.target;
    
    if (section.style.display === 'none') {
        section.style.display = 'block';
        button.innerHTML = '<i class="fas fa-chevron-down"></i>';
    } else {
        section.style.display = 'none';
        button.innerHTML = '<i class="fas fa-chevron-up"></i>';
    }
}

function toggleView(viewType) {
    const container = document.getElementById('lessons-container');
    const gridBtn = document.getElementById('grid-view-btn');
    const listBtn = document.getElementById('list-view-btn');
    
    if (viewType === 'grid') {
        container.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
        gridBtn.classList.add('text-blue-600');
        listBtn.classList.remove('text-blue-600');
    } else {
        container.className = 'space-y-3';
        listBtn.classList.add('text-blue-600');
        gridBtn.classList.remove('text-blue-600');
    }
}

function sortLessons(sortBy) {
    const params = new URLSearchParams(window.location.search);
    params.set('sort', sortBy);
    window.location.href = `${window.location.pathname}?${params.toString()}`;
}

// تحديث تلقائي كل 30 ثانية للحصص الجارية
setInterval(() => {
    if (document.querySelector('.animate-pulse')) {
        window.lessonsManager?.refreshLessonsData();
    }
}, 30000);
</script>
{% endblock %}
