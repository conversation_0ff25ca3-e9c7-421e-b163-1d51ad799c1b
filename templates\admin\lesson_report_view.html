{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير الحصة - {{ lesson.title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-islamic-primary">تقرير المعلم عن الحصة</h1>
                    <p class="text-gray-600 mt-1">{{ lesson.title }}</p>
                </div>
                <div class="flex space-x-4 space-x-reverse">
                    <a href="{% url 'admin_lessons' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>

        <!-- Lesson Information -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">معلومات الحصة</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Lesson Details -->
                <div class="bg-blue-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-blue-900 mb-4">تفاصيل الحصة</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600">عنوان الحصة:</span>
                            <p class="font-semibold">{{ lesson.title }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">تاريخ الحصة:</span>
                            <p class="font-semibold">{{ lesson.scheduled_date|date:"Y-m-d H:i" }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">المدة:</span>
                            <p class="font-semibold">{{ lesson.duration_minutes }} دقيقة</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">الحالة:</span>
                            <span class="px-2 py-1 rounded-full text-xs font-medium
                                {% if lesson.status == 'completed' %}bg-green-100 text-green-800
                                {% elif lesson.status == 'live' %}bg-red-100 text-red-800
                                {% elif lesson.status == 'scheduled' %}bg-blue-100 text-blue-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ lesson.get_status_display }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Teacher Information -->
                <div class="bg-green-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-green-900 mb-4">معلومات المعلم</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600">الاسم:</span>
                            <p class="font-semibold">{{ lesson.teacher.get_full_name }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">البريد الإلكتروني:</span>
                            <p class="font-semibold">{{ lesson.teacher.email }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">رقم الهاتف:</span>
                            <p class="font-semibold">{{ lesson.teacher.phone|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">تاريخ إنشاء التقرير:</span>
                            <p class="font-semibold">{{ report.created_at|date:"Y-m-d H:i" }}</p>
                        </div>
                    </div>
                </div>

                <!-- Student Information -->
                <div class="bg-purple-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-purple-900 mb-4">معلومات الطالب</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600">الاسم:</span>
                            <p class="font-semibold">{{ lesson.student.get_full_name }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">البريد الإلكتروني:</span>
                            <p class="font-semibold">{{ lesson.student.email }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">رقم الهاتف:</span>
                            <p class="font-semibold">{{ lesson.student.phone|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">تاريخ التسجيل:</span>
                            <p class="font-semibold">{{ lesson.student.date_joined|date:"Y-m-d" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher Report -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">تقرير المعلم</h2>

            <!-- Student Ratings -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">تقييم أداء الطالب</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="bg-blue-100 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-600 mb-2">أداء الطالب</h4>
                            <div class="flex justify-center mb-2">
                                {% for i in "12345" %}
                                    <i class="fas fa-star text-lg {% if forloop.counter <= report.student_performance %}text-yellow-400{% else %}text-gray-300{% endif %}"></i>
                                {% endfor %}
                            </div>
                            <p class="text-2xl font-bold text-blue-600">{{ report.student_performance }}/5</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="bg-green-100 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-600 mb-2">مستوى التفاعل</h4>
                            <div class="flex justify-center mb-2">
                                {% for i in "12345" %}
                                    <i class="fas fa-star text-lg {% if forloop.counter <= report.student_participation %}text-yellow-400{% else %}text-gray-300{% endif %}"></i>
                                {% endfor %}
                            </div>
                            <p class="text-2xl font-bold text-green-600">{{ report.student_participation }}/5</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="bg-purple-100 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-600 mb-2">مستوى الفهم</h4>
                            <div class="flex justify-center mb-2">
                                {% for i in "12345" %}
                                    <i class="fas fa-star text-lg {% if forloop.counter <= report.student_understanding %}text-yellow-400{% else %}text-gray-300{% endif %}"></i>
                                {% endfor %}
                            </div>
                            <p class="text-2xl font-bold text-purple-600">{{ report.student_understanding }}/5</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <div class="bg-gray-100 rounded-lg p-4 inline-block">
                        <h4 class="text-sm font-medium text-gray-600 mb-2">متوسط تقييم الطالب</h4>
                        <p class="text-3xl font-bold text-islamic-primary">{{ report.get_average_student_rating|floatformat:1 }}/5</p>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- Strengths -->
                <div class="bg-green-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-green-900 mb-3">
                        <i class="fas fa-thumbs-up text-green-600 ml-2"></i>
                        نقاط القوة
                    </h4>
                    <p class="text-gray-700 leading-relaxed">{{ report.strengths }}</p>
                </div>

                <!-- Areas for Improvement -->
                <div class="bg-orange-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-orange-900 mb-3">
                        <i class="fas fa-arrow-up text-orange-600 ml-2"></i>
                        نقاط تحتاج تحسين
                    </h4>
                    <p class="text-gray-700 leading-relaxed">{{ report.areas_for_improvement }}</p>
                </div>
            </div>

            <!-- Lesson Summary -->
            <div class="mb-6">
                <div class="bg-blue-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-blue-900 mb-3">
                        <i class="fas fa-book text-blue-600 ml-2"></i>
                        ملخص الحصة
                    </h4>
                    <p class="text-gray-700 leading-relaxed">{{ report.lesson_summary }}</p>
                </div>
            </div>

            <!-- Homework and Recommendations -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {% if report.homework_assigned %}
                <div class="bg-purple-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-purple-900 mb-3">
                        <i class="fas fa-tasks text-purple-600 ml-2"></i>
                        الواجبات المطلوبة
                    </h4>
                    <p class="text-gray-700 leading-relaxed">{{ report.homework_assigned }}</p>
                </div>
                {% endif %}

                {% if report.recommendations %}
                <div class="bg-yellow-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-yellow-900 mb-3">
                        <i class="fas fa-lightbulb text-yellow-600 ml-2"></i>
                        توصيات للحصص القادمة
                    </h4>
                    <p class="text-gray-700 leading-relaxed">{{ report.recommendations }}</p>
                </div>
                {% endif %}
            </div>

            <!-- Overall Rating and Additional Notes -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Overall Lesson Rating -->
                <div class="bg-indigo-50 rounded-lg p-4 text-center">
                    <h4 class="text-lg font-semibold text-indigo-900 mb-3">
                        <i class="fas fa-star text-indigo-600 ml-2"></i>
                        تقييم عام للحصة
                    </h4>
                    <div class="flex justify-center mb-2">
                        {% for i in "12345" %}
                            <i class="fas fa-star text-2xl {% if forloop.counter <= report.overall_lesson_rating %}text-yellow-400{% else %}text-gray-300{% endif %}"></i>
                        {% endfor %}
                    </div>
                    <p class="text-3xl font-bold text-indigo-600">{{ report.overall_lesson_rating }}/5</p>
                </div>

                <!-- Additional Notes -->
                {% if report.additional_notes %}
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">
                        <i class="fas fa-sticky-note text-gray-600 ml-2"></i>
                        ملاحظات إضافية
                    </h4>
                    <p class="text-gray-700 leading-relaxed">{{ report.additional_notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Report Summary -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-6">ملخص التقرير</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center bg-blue-50 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-600 mb-2">متوسط أداء الطالب</h4>
                    <p class="text-2xl font-bold text-blue-600">{{ report.get_average_student_rating|floatformat:1 }}/5</p>
                </div>
                <div class="text-center bg-green-50 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-600 mb-2">تقييم الحصة</h4>
                    <p class="text-2xl font-bold text-green-600">{{ report.overall_lesson_rating }}/5</p>
                </div>
                <div class="text-center bg-purple-50 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-600 mb-2">تاريخ التقرير</h4>
                    <p class="text-sm font-bold text-purple-600">{{ report.created_at|date:"Y-m-d" }}</p>
                </div>
                <div class="text-center bg-orange-50 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-600 mb-2">وقت التقرير</h4>
                    <p class="text-sm font-bold text-orange-600">{{ report.created_at|date:"H:i" }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
