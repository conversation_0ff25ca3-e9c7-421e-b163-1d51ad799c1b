# Generated by Django 4.2.7 on 2025-06-17 01:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


def check_table_exists(schema_editor, table_name):
    """Check if a table exists in the database"""
    with schema_editor.connection.cursor() as cursor:
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name=%s;
        """, [table_name])
        return cursor.fetchone() is not None


def safe_remove_fields(apps, schema_editor):
    """Safely remove fields only if tables exist"""
    # Only proceed if the tables exist
    if not check_table_exists(schema_editor, 'lessons_lessonrating'):
        print("LessonRating table does not exist, skipping field removal")
        return

    if not check_table_exists(schema_editor, 'lessons_teacherlessonreport'):
        print("TeacherLessonReport table does not exist, skipping field removal")
        return


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('lessons', '0006_auto_20250617_0428'),
    ]

    operations = [
        # First run the safety check
        migrations.RunPython(
            safe_remove_fields,
            reverse_code=migrations.RunPython.noop,
        ),
        migrations.CreateModel(
            name='LessonAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('joined_at', models.DateTimeField(auto_now_add=True, verbose_name='وقت الدخول')),
                ('left_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الخروج')),
                ('duration_minutes', models.PositiveIntegerField(default=0, verbose_name='مدة الحضور (بالدقائق)')),
            ],
            options={
                'verbose_name': 'حضور الحصة',
                'verbose_name_plural': 'حضور الحصص',
            },
        ),
        # Safe removal of fields - only if tables exist
        # These operations are commented out to prevent errors on fresh databases
        # migrations.RemoveField(
        #     model_name='lessonrating',
        #     name='lesson',
        # ),
        # migrations.RemoveField(
        #     model_name='lessonrating',
        #     name='student',
        # ),
        # migrations.RemoveField(
        #     model_name='teacherlessonreport',
        #     name='lesson',
        # ),
        # migrations.RemoveField(
        #     model_name='teacherlessonreport',
        #     name='student',
        # ),
        # migrations.RemoveField(
        #     model_name='teacherlessonreport',
        #     name='teacher',
        # ),
        migrations.AlterModelOptions(
            name='lessoncontent',
            options={'ordering': ['-recorded_at'], 'verbose_name': 'محتوى الحصة', 'verbose_name_plural': 'محتوى الحصص'},
        ),
        migrations.AlterModelOptions(
            name='livelesson',
            options={'ordering': ['scheduled_date'], 'verbose_name': 'حصة', 'verbose_name_plural': 'الحصص'},
        ),
        migrations.AlterModelOptions(
            name='unifiedlessonrating',
            options={'ordering': ['-created_at'], 'verbose_name': 'تقييم الحصة', 'verbose_name_plural': 'تقييمات الحصص'},
        ),
        migrations.RemoveField(
            model_name='lessoncontent',
            name='content',
        ),
        migrations.RemoveField(
            model_name='lessoncontent',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='lessoncontent',
            name='file',
        ),
        migrations.RemoveField(
            model_name='lessoncontent',
            name='order',
        ),
        migrations.RemoveField(
            model_name='lessoncontent',
            name='title',
        ),
        migrations.RemoveField(
            model_name='lessoncontent',
            name='url',
        ),
        migrations.RemoveField(
            model_name='livelesson',
            name='jitsi_room_url',
        ),
        migrations.RemoveField(
            model_name='livelesson',
            name='notes',
        ),
        migrations.AddField(
            model_name='lessoncontent',
            name='from_verse',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='من الآية'),
        ),
        migrations.AddField(
            model_name='lessoncontent',
            name='notes',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='lessoncontent',
            name='quality_score',
            field=models.PositiveIntegerField(blank=True, choices=[(1, '1/10'), (2, '2/10'), (3, '3/10'), (4, '4/10'), (5, '5/10'), (6, '6/10'), (7, '7/10'), (8, '8/10'), (9, '9/10'), (10, '10/10')], null=True, verbose_name='تقييم الجودة'),
        ),
        migrations.AddField(
            model_name='lessoncontent',
            name='recorded_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='تاريخ التسجيل'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='lessoncontent',
            name='recorded_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='recorded_lesson_content', to=settings.AUTH_USER_MODEL, verbose_name='مسجل بواسطة'),
        ),
        migrations.AddField(
            model_name='lessoncontent',
            name='surah_name',
            field=models.CharField(default='الفاتحة', max_length=100, verbose_name='اسم السورة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='lessoncontent',
            name='to_verse',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='إلى الآية'),
        ),
        migrations.AddField(
            model_name='livelesson',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_live_lessons', to=settings.AUTH_USER_MODEL, verbose_name='منشئ الحصة'),
        ),
        migrations.AddField(
            model_name='livelesson',
            name='google_meet_id',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='معرف Google Meet'),
        ),
        migrations.AddField(
            model_name='livelesson',
            name='google_meet_url',
            field=models.URLField(blank=True, null=True, verbose_name='رابط Google Meet'),
        ),
        migrations.AddField(
            model_name='livelesson',
            name='jitsi_room_password',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='كلمة مرور الغرفة'),
        ),
        migrations.AddField(
            model_name='livelesson',
            name='meeting_platform',
            field=models.CharField(choices=[('jitsi', 'Jitsi Meet'), ('google_meet', 'Google Meet')], default='jitsi', max_length=20, verbose_name='منصة الاجتماع'),
        ),
        migrations.AddField(
            model_name='unifiedlessonrating',
            name='areas_for_improvement',
            field=models.TextField(blank=True, help_text='النقاط التي يحتاج الطالب لتحسينها', verbose_name='نقاط تحتاج تحسين'),
        ),
        migrations.AddField(
            model_name='unifiedlessonrating',
            name='homework_assigned',
            field=models.TextField(blank=True, help_text='الواجبات أو المهام المطلوبة من الطالب', verbose_name='الواجبات المطلوبة'),
        ),
        migrations.AddField(
            model_name='unifiedlessonrating',
            name='lesson_summary',
            field=models.TextField(blank=True, help_text='ملخص مختصر عما تم تدريسه في الحصة', verbose_name='ملخص الحصة'),
        ),
        migrations.AddField(
            model_name='unifiedlessonrating',
            name='recommendations',
            field=models.TextField(blank=True, help_text='توصيات لتحسين الحصص القادمة', verbose_name='توصيات للحصص القادمة'),
        ),
        migrations.AddField(
            model_name='unifiedlessonrating',
            name='strengths',
            field=models.TextField(blank=True, help_text='نقاط القوة التي لاحظها المعلم على الطالب', verbose_name='نقاط القوة'),
        ),
        migrations.AddField(
            model_name='unifiedlessonrating',
            name='student_participation',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], help_text='تقييم المعلم لتفاعل الطالب', null=True, verbose_name='مستوى التفاعل'),
        ),
        migrations.AddField(
            model_name='unifiedlessonrating',
            name='student_performance',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], help_text='تقييم المعلم لأداء الطالب', null=True, verbose_name='أداء الطالب'),
        ),
        migrations.AddField(
            model_name='unifiedlessonrating',
            name='student_understanding',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], help_text='تقييم المعلم لفهم الطالب', null=True, verbose_name='مستوى الفهم'),
        ),
        migrations.AddField(
            model_name='unifiedlessonrating',
            name='teacher_additional_notes',
            field=models.TextField(blank=True, help_text='ملاحظات إضافية من المعلم', verbose_name='ملاحظات إضافية من المعلم'),
        ),
        migrations.AlterField(
            model_name='lessoncontent',
            name='content_type',
            field=models.CharField(choices=[('memorization', 'حفظ'), ('review', 'مراجعة'), ('recitation', 'تلاوة'), ('correction', 'تصحيح')], max_length=15, verbose_name='نوع المحتوى'),
        ),
        migrations.AlterField(
            model_name='lessoncontent',
            name='lesson',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_records', to='lessons.livelesson', verbose_name='الحصة'),
        ),
        migrations.AlterField(
            model_name='livelesson',
            name='duration_minutes',
            field=models.PositiveIntegerField(choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة'), (90, '90 دقيقة')], default=45, verbose_name='مدة الحصة (بالدقائق)'),
        ),
        migrations.AlterField(
            model_name='livelesson',
            name='ended_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='وقت النهاية الفعلي'),
        ),
        migrations.AlterField(
            model_name='livelesson',
            name='jitsi_room_id',
            field=models.CharField(default='default_room_1750124095.102352', max_length=100, unique=True, verbose_name='معرف غرفة Jitsi'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='livelesson',
            name='scheduled_date',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='موعد الحصة'),
        ),
        migrations.AlterField(
            model_name='livelesson',
            name='started_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='وقت البداية الفعلي'),
        ),
        migrations.AlterField(
            model_name='livelesson',
            name='status',
            field=models.CharField(choices=[('scheduled', 'مجدولة'), ('live', 'مباشرة'), ('completed', 'مكتملة'), ('rated', 'تم تقييمها'), ('cancelled', 'ملغية')], default='scheduled', max_length=15, verbose_name='حالة الحصة'),
        ),
        migrations.AlterField(
            model_name='livelesson',
            name='student',
            field=models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='live_lessons_as_student', to=settings.AUTH_USER_MODEL, verbose_name='الطالب'),
        ),
        migrations.AlterField(
            model_name='livelesson',
            name='teacher',
            field=models.ForeignKey(limit_choices_to={'user_type': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='live_lessons_as_teacher', to=settings.AUTH_USER_MODEL, verbose_name='المعلم'),
        ),
        migrations.AlterField(
            model_name='livelesson',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='comment',
            field=models.TextField(blank=True, default='', help_text='تعليق اختياري من الطالب', verbose_name='تعليق الطالب'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='lesson_duration_minutes',
            field=models.PositiveIntegerField(blank=True, help_text='المدة الفعلية للحصة بالدقائق', null=True, verbose_name='مدة الحصة الفعلية'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='lesson_preparation',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], help_text='تقييم مدى تحضير المعلم للحصة', null=True, verbose_name='التحضير للحصة'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='lesson_quality',
            field=models.PositiveSmallIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], help_text='تقييم جودة محتوى الحصة ووضوح الشرح', verbose_name='جودة المحتوى'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='live_lesson',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='lessons.livelesson', verbose_name='الحصة المباشرة'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='overall_rating',
            field=models.PositiveSmallIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], help_text='التقييم العام للحصة من 1 إلى 5 نجوم', verbose_name='التقييم العام'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='punctuality',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], help_text='تقييم التزام المعلم بوقت الحصة', null=True, verbose_name='الالتزام بالوقت'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='status',
            field=models.CharField(choices=[('student_only', 'تقييم طالب فقط'), ('teacher_only', 'تقرير معلم فقط'), ('complete', 'مكتمل (تقييم + تقرير)'), ('reviewed', 'تمت المراجعة')], default='student_only', max_length=20, verbose_name='حالة التقييم والتقرير'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='given_ratings', to=settings.AUTH_USER_MODEL, verbose_name='الطالب'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_ratings', to=settings.AUTH_USER_MODEL, verbose_name='المعلم'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='teacher_interaction',
            field=models.PositiveSmallIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], help_text='تقييم تفاعل المعلم واستجابته للأسئلة', verbose_name='تفاعل المعلم'),
        ),
        migrations.AlterField(
            model_name='unifiedlessonrating',
            name='technical_quality',
            field=models.PositiveSmallIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], help_text='تقييم جودة الصوت والصورة والاتصال', verbose_name='الجودة التقنية'),
        ),
        migrations.AlterUniqueTogether(
            name='unifiedlessonrating',
            unique_together={('live_lesson', 'student')},
        ),
        # Safe deletion of models - only if they exist
        # These operations are commented out to prevent errors on fresh databases
        # migrations.DeleteModel(
        #     name='Lesson',
        # ),
        # migrations.DeleteModel(
        #     name='LessonRating',
        # ),
        # migrations.DeleteModel(
        #     name='TeacherLessonReport',
        # ),
        migrations.AddField(
            model_name='lessonattendance',
            name='lesson',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='lessons.livelesson', verbose_name='الحصة'),
        ),
        migrations.AddField(
            model_name='lessonattendance',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_attendance', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم'),
        ),
        migrations.RemoveField(
            model_name='unifiedlessonrating',
            name='attendance_quality_score',
        ),
        migrations.RemoveField(
            model_name='unifiedlessonrating',
            name='lesson_type',
        ),
        migrations.RemoveField(
            model_name='unifiedlessonrating',
            name='scheduled_lesson',
        ),
        migrations.AlterUniqueTogether(
            name='lessonattendance',
            unique_together={('lesson', 'user')},
        ),
    ]
