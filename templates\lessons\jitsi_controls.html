<!-- أزر<PERSON>ر التحكم الذكية في Jitsi Meet -->
{% if error %}
    <div class="alert alert-danger">
        خطأ في تحميل أزرار التحكم: {{ error }}
    </div>
{% else %}
    <div class="jitsi-controls-container">
        <!-- معلومات الحصة -->
        <div class="lesson-info-card bg-white bg-opacity-10 backdrop-blur-md rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-white font-bold">{{ live_lesson.title }}</h3>
                    <p class="text-gray-300 text-sm">
                        <i class="fas fa-user-graduate mr-1"></i>
                        {% if is_moderator %}
                            الطالب: {{ live_lesson.student.get_full_name }}
                        {% else %}
                            المعلم: {{ live_lesson.teacher.get_full_name }}
                        {% endif %}
                    </p>
                </div>
                <div class="text-right">
                    {% load jitsi_tags %}
                    {% jitsi_status_badge live_lesson %}
                    <div class="text-sm text-gray-300 mt-1">
                        <i class="fas fa-clock mr-1"></i>
                        {{ live_lesson.duration_minutes }} دقيقة
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم الأساسية -->
        <div class="control-buttons grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
            <!-- زر كتم/إلغاء كتم الصوت -->
            <button onclick="window.jitsiManager?.toggleAudio()" 
                    class="control-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200">
                <i class="fas fa-microphone mr-1"></i>
                <span class="hidden md:inline">الصوت</span>
            </button>

            <!-- زر تشغيل/إيقاف الفيديو -->
            <button onclick="window.jitsiManager?.toggleVideo()" 
                    class="control-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200">
                <i class="fas fa-video mr-1"></i>
                <span class="hidden md:inline">الفيديو</span>
            </button>

            <!-- زر فتح في نافذة جديدة -->
            <button onclick="openJitsiInNewWindow()" 
                    class="control-btn bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-all duration-200">
                <i class="fas fa-external-link-alt mr-1"></i>
                <span class="hidden md:inline">نافذة جديدة</span>
            </button>

            <!-- زر إنهاء المكالمة -->
            <button onclick="window.jitsiManager?.hangUp()" 
                    class="control-btn bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-all duration-200">
                <i class="fas fa-phone-slash mr-1"></i>
                <span class="hidden md:inline">إنهاء</span>
            </button>
        </div>

        <!-- معلومات الاتصال -->
        <div class="connection-info bg-black bg-opacity-30 rounded-lg p-3">
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center text-green-400">
                    <i class="fas fa-wifi mr-2"></i>
                    <span id="connection-status-text">متصل</span>
                </div>
                <div class="flex items-center text-blue-400">
                    <i class="fas fa-server mr-2"></i>
                    <span>{{ domain }}</span>
                    {% if is_jaas %}
                        <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full mr-2">JaaS</span>
                    {% else %}
                        <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full mr-2">عام</span>
                    {% endif %}
                </div>
                <div class="flex items-center text-gray-400">
                    <i class="fas fa-users mr-2"></i>
                    <span id="participants-count-display">{{ live_lesson|jitsi_participant_count }}</span>
                </div>
            </div>
        </div>

        <!-- تحذير للخادم العام -->
        {% if not is_jaas %}
        <div class="warning-card bg-yellow-500 bg-opacity-20 border border-yellow-500 rounded-lg p-3 mt-4">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-yellow-400 mt-1 mr-2"></i>
                <div class="text-yellow-200 text-sm">
                    <strong>تنبيه:</strong> تستخدم الحصة خادم Jitsi العام مع حد زمني 5 دقائق للـ embedding.
                    سيتم إعادة الاتصال تلقائياً عند الحاجة.
                    <a href="#" class="text-yellow-300 underline hover:text-yellow-100 mr-2">
                        معرفة المزيد عن JaaS
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script>
        // وظائف التحكم الإضافية
        function openJitsiInNewWindow() {
            const url = '{{ jitsi_url }}';
            const windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no';
            const newWindow = window.open(url, 'jitsi_window', windowFeatures);
            
            if (newWindow) {
                newWindow.focus();
                showNotification('تم فتح الحصة في نافذة جديدة');
            } else {
                showNotification('فشل في فتح النافذة الجديدة. تحقق من إعدادات المتصفح.', 'error');
            }
        }

        // مراقبة حالة الاتصال
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connection-status-text');
            const participantsElement = document.getElementById('participants-count-display');
            
            if (window.jitsiManager && window.jitsiManager.api) {
                // تحديث حالة الاتصال
                try {
                    const connectionQuality = window.jitsiManager.api.getConnectionQuality();
                    if (connectionQuality) {
                        statusElement.textContent = 'متصل - جودة عالية';
                        statusElement.className = 'text-green-400';
                    }
                } catch (e) {
                    statusElement.textContent = 'متصل';
                    statusElement.className = 'text-green-400';
                }
                
                // تحديث عدد المشاركين
                fetch(`/api/live-lessons/{{ live_lesson.id }}/participants/`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && participantsElement) {
                            participantsElement.textContent = data.count;
                        }
                    })
                    .catch(console.error);
            } else {
                statusElement.textContent = 'غير متصل';
                statusElement.className = 'text-red-400';
            }
        }

        // تحديث دوري للحالة
        setInterval(updateConnectionStatus, 10000);
        
        // تحديث فوري عند تحميل الصفحة
        setTimeout(updateConnectionStatus, 3000);

        // إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : 'alert-info';
            const notification = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                     style="top: 20px; right: 20px; z-index: 9999; max-width: 400px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', notification);
            
            // إزالة الإشعار بعد 5 ثوان
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    if (alert.textContent.includes(message.substring(0, 20))) {
                        alert.remove();
                    }
                });
            }, 5000);
        }
    </script>
{% endif %}
