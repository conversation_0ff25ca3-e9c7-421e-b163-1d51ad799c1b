{% extends 'base.html' %}

{% block title %}إدارة طلبات التحقق - نظام قرآنيا التعليمي{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-6">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="{% url 'admin_dashboard' %}" class="text-gray-500 hover:text-gray-700 ml-4">
                        <i class="fas fa-arrow-right text-xl"></i>
                    </a>
                    <div>
                        <h1 class="text-2xl font-bold text-islamic-dark">إدارة طلبات التحقق</h1>
                        <p class="text-gray-600 mt-1">مراجعة والموافقة على طلبات التسجيل الجديدة</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                        {{ stats.pending }} طلب في الانتظار
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        <!-- حالات التحقق -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:col-span-1 lg:col-span-1">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">في الانتظار</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.pending }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:col-span-1 lg:col-span-1">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="fas fa-search text-blue-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">قيد المراجعة</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.under_review }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:col-span-1 lg:col-span-1">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <i class="fas fa-check text-green-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">تم القبول</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.approved }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:col-span-1 lg:col-span-1">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100">
                    <i class="fas fa-times text-red-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">تم الرفض</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.rejected }}</p>
                </div>
            </div>
        </div>

        <!-- حالات الحظر -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:col-span-1 lg:col-span-1">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100">
                    <i class="fas fa-clock text-orange-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">حظر مؤقت</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.temporary_banned }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:col-span-1 lg:col-span-1">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100">
                    <i class="fas fa-ban text-red-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">حظر دائم</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.permanent_banned }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">البحث والتصفية</h3>

        <form method="GET" action="{% url 'admin_user_verifications' %}">
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <!-- حالة التحقق -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">حالة التحقق</label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        {% for status_code, status_name in status_choices %}
                        <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>
                            {{ status_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- نوع المستخدم -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع المستخدم</label>
                    <select name="user_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in user_type_choices %}
                        <option value="{{ type_code }}" {% if user_type_filter == type_code %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- حالة الحظر -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">حالة الحظر</label>
                    <select name="ban_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">الكل</option>
                        <option value="not_banned" {% if ban_status_filter == 'not_banned' %}selected{% endif %}>غير محظور</option>
                        <option value="banned" {% if ban_status_filter == 'banned' %}selected{% endif %}>محظور</option>
                        <option value="temporary" {% if ban_status_filter == 'temporary' %}selected{% endif %}>حظر مؤقت</option>
                        <option value="permanent" {% if ban_status_filter == 'permanent' %}selected{% endif %}>حظر دائم</option>
                    </select>
                </div>

                <!-- فترة التسجيل -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">فترة التسجيل</label>
                    <select name="registration_period" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">الكل</option>
                        <option value="today" {% if registration_period_filter == 'today' %}selected{% endif %}>اليوم</option>
                        <option value="week" {% if registration_period_filter == 'week' %}selected{% endif %}>هذا الأسبوع</option>
                        <option value="month" {% if registration_period_filter == 'month' %}selected{% endif %}>هذا الشهر</option>
                        <option value="year" {% if registration_period_filter == 'year' %}selected{% endif %}>هذا العام</option>
                    </select>
                </div>

                <!-- تاريخ التسجيل من -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ التسجيل من</label>
                    <input type="date" name="date_from" value="{{ date_from|default:'' }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>

                <!-- تاريخ التسجيل إلى -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ التسجيل إلى</label>
                    <input type="date" name="date_to" value="{{ date_to|default:'' }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>

                <!-- بحث -->
                <div class="md:col-span-2 lg:col-span-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <input type="text" name="search" value="{{ search_query|default:'' }}"
                           placeholder="البحث بالاسم أو البريد الإلكتروني أو رقم الهاتف..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>

                <!-- أزرار البحث -->
                <div class="md:col-span-1 lg:col-span-2 flex items-end space-x-2 space-x-reverse">
                    <button type="submit" class="flex-1 bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-search ml-2"></i>
                        بحث
                    </button>
                    <a href="{% url 'admin_user_verifications' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">طلبات التحقق</h3>
        </div>

        <!-- قسم الإجراءات الجماعية -->
        <div id="bulk-actions-section" class="px-6 py-4 border-b border-gray-200 hidden">
            <form id="bulk-actions-form" method="POST">
                {% csrf_token %}
                <input type="hidden" name="action" id="bulk-action-type" value="">
                <input type="hidden" name="user_ids" id="selected-user-ids" value="">

                <div class="flex flex-wrap items-center gap-4">
                    <div class="flex-grow">
                        <span class="text-sm font-medium text-gray-700" id="selected-count">تم تحديد 0 حسابات</span>
                    </div>

                    <div class="flex items-center gap-2">
                        <select id="bulk-action-select" class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-islamic-primary focus:border-islamic-primary sm:text-sm">
                            <option value="">-- اختر إجراء --</option>
                            <option value="approve">الموافقة على الحسابات</option>
                            <option value="reject">رفض الحسابات</option>
                            <option value="under_review">وضع قيد المراجعة</option>
                            <option value="ban_temporary">حظر مؤقت</option>
                            <option value="ban_permanent">حظر نهائي</option>
                            <option value="delete">حذف الحسابات</option>
                        </select>

                        <button type="button" id="apply-bulk-action" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-islamic-primary hover:bg-islamic-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-islamic-primary">
                            تطبيق
                        </button>

                        <button type="button" id="cancel-bulk-action" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-islamic-primary">
                            إلغاء
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase">
                            <input type="checkbox" id="select-all-users" class="h-4 w-4 text-islamic-primary focus:ring-islamic-primary border-gray-300 rounded">
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ التسجيل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for user in users %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-3 py-4 whitespace-nowrap text-center">
                            <input type="checkbox" name="selected_users" value="{{ user.id }}" class="user-checkbox h-4 w-4 text-islamic-primary focus:ring-islamic-primary border-gray-300 rounded">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    {% if user.profile_picture %}
                                        <img class="h-10 w-10 rounded-full object-cover" src="{{ user.profile_picture.url }}" alt="{{ user.get_full_name }}">
                                    {% else %}
                                        <div class="h-10 w-10 rounded-full bg-islamic-light-blue flex items-center justify-center">
                                            <i class="fas fa-user text-islamic-primary"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ user.get_full_name|default:user.username }}
                                    </div>
                                    <div class="text-sm text-gray-500">{{ user.email }}</div>
                                    {% if user.phone %}
                                        <div class="text-xs text-gray-400">{{ user.phone }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if user.user_type == 'student' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    طالب
                                </span>
                            {% elif user.user_type == 'teacher' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                    معلم
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ user.created_at|date:"Y-m-d H:i" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if user.verification_status == 'pending' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    في الانتظار
                                </span>
                            {% elif user.verification_status == 'approved' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    تم القبول
                                </span>
                            {% elif user.verification_status == 'rejected' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    تم الرفض
                                </span>
                            {% elif user.verification_status == 'under_review' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    قيد المراجعة
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                {% if user.verification_status == 'pending' or user.verification_status == 'under_review' %}
                                    <button onclick="approveUser({{ user.id }}, '{{ user.get_full_name|default:user.username }}')"
                                            class="text-green-600 hover:text-green-900" title="قبول">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button onclick="rejectUser({{ user.id }}, '{{ user.get_full_name|default:user.username }}')"
                                            class="text-red-600 hover:text-red-900" title="رفض">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button onclick="reviewUser({{ user.id }}, '{{ user.get_full_name|default:user.username }}')"
                                            class="text-blue-600 hover:text-blue-900" title="وضع قيد المراجعة">
                                        <i class="fas fa-search"></i>
                                    </button>
                                {% endif %}

                                {% if not user.is_banned %}
                                    <button onclick="banUserTemporary({{ user.id }}, '{{ user.get_full_name|default:user.username }}')"
                                            class="text-orange-600 hover:text-orange-900" title="حظر مؤقت">
                                        <i class="fas fa-clock"></i>
                                    </button>
                                    <button onclick="banUserPermanent({{ user.id }}, '{{ user.get_full_name|default:user.username }}')"
                                            class="text-red-800 hover:text-red-900" title="حظر نهائي">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                {% else %}
                                    <button onclick="unbanUser({{ user.id }}, '{{ user.get_full_name|default:user.username }}')"
                                            class="text-purple-600 hover:text-purple-900" title="إلغاء الحظر">
                                        <i class="fas fa-unlock"></i>
                                    </button>
                                    <span class="text-xs text-red-600" title="{{ user.get_ban_status_display }}">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </span>
                                {% endif %}

                                <a href="{% url 'admin_user_detail' user.id %}" class="text-islamic-primary hover:text-islamic-light" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>

                                <!-- زر حذف الحساب -->
                                <button onclick="deleteUser({{ user.id }}, '{{ user.get_full_name|default:user.username }}')"
                                        class="text-red-700 hover:text-red-900" title="حذف الحساب">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            لا توجد طلبات تحقق مطابقة للبحث
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div id="approvalModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <i class="fas fa-check text-green-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">قبول الطلب</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="approvalText"></p>
                <form method="POST" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="approve">
                    <input type="hidden" name="user_id" id="approvalUserId">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات (اختيارية)</label>
                        <textarea name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="أضف ملاحظات للمستخدم..."></textarea>
                    </div>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                            قبول الطلب
                        </button>
                        <button type="button" onclick="closeModal('approvalModal')" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div id="rejectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-times text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">رفض الطلب</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="rejectionText"></p>
                <form method="POST" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="reject">
                    <input type="hidden" name="user_id" id="rejectionUserId">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب الرفض *</label>
                        <textarea name="reason" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="اذكر سبب رفض الطلب..."></textarea>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية (اختيارية)</label>
                        <textarea name="notes" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="ملاحظات إضافية..."></textarea>
                    </div>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                            رفض الطلب
                        </button>
                        <button type="button" onclick="closeModal('rejectionModal')" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Temporary Ban Modal -->
<div id="temporaryBanModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-orange-100">
                <i class="fas fa-clock text-orange-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">حظر مؤقت</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="temporaryBanText"></p>
                <form method="POST" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="ban_temporary">
                    <input type="hidden" name="user_id" id="temporaryBanUserId">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب الحظر *</label>
                        <textarea name="ban_reason" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="اذكر سبب الحظر..."></textarea>
                    </div>
                    <div class="mb-4 flex space-x-2 space-x-reverse">
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-2">مدة الحظر *</label>
                            <input type="number" name="ban_duration" min="1" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="المدة">
                        </div>
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-2">الوحدة</label>
                            <select name="ban_unit" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="hours">ساعات</option>
                                <option value="days" selected>أيام</option>
                                <option value="weeks">أسابيع</option>
                                <option value="months">شهور</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button type="submit" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700">
                            حظر مؤقت
                        </button>
                        <button type="button" onclick="closeModal('temporaryBanModal')" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Permanent Ban Modal -->
<div id="permanentBanModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-ban text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">حظر نهائي</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="permanentBanText"></p>
                <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-red-600 mt-1 ml-2"></i>
                        <div class="text-sm text-red-700">
                            <strong>تحذير:</strong> الحظر النهائي لا يمكن التراجع عنه إلا بتدخل المدير.
                        </div>
                    </div>
                </div>
                <form method="POST" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="ban_permanent">
                    <input type="hidden" name="user_id" id="permanentBanUserId">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب الحظر النهائي *</label>
                        <textarea name="ban_reason" rows="4" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="اذكر سبب الحظر النهائي بالتفصيل..."></textarea>
                    </div>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                            حظر نهائي
                        </button>
                        <button type="button" onclick="closeModal('permanentBanModal')" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div id="deleteUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-trash-alt text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">حذف الحساب</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="deleteUserText"></p>
                <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-red-600 mt-1 ml-2"></i>
                        <div class="text-sm text-red-700">
                            <strong>تحذير:</strong> حذف الحساب لا يمكن التراجع عنه. سيتم حذف جميع البيانات المرتبطة بهذا الحساب.
                        </div>
                    </div>
                </div>
                <form method="POST" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="delete_user">
                    <input type="hidden" name="user_id" id="deleteUserId">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب الحذف *</label>
                        <textarea name="delete_reason" rows="4" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="اذكر سبب حذف الحساب بالتفصيل..."></textarea>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">تأكيد الحذف *</label>
                        <div class="flex items-center">
                            <input type="checkbox" name="confirm_delete" required class="ml-2 h-4 w-4 text-islamic-primary focus:ring-islamic-primary border-gray-300 rounded">
                            <span class="text-sm text-gray-700">أؤكد أنني أريد حذف هذا الحساب نهائياً</span>
                        </div>
                    </div>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                            حذف الحساب
                        </button>
                        <button type="button" onclick="closeModal('deleteUserModal')" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Modal -->
<div id="bulkDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-trash-alt text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">حذف الحسابات المحددة</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="bulkDeleteText">هل أنت متأكد من حذف الحسابات المحددة؟</p>
                <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-red-600 mt-1 ml-2"></i>
                        <div class="text-sm text-red-700">
                            <strong>تحذير:</strong> حذف الحسابات لا يمكن التراجع عنه. سيتم حذف جميع البيانات المرتبطة بهذه الحسابات.
                        </div>
                    </div>
                </div>
                <form id="bulk-delete-form" method="POST" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="bulk_delete">
                    <input type="hidden" name="user_ids" id="bulk-delete-user-ids">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب الحذف *</label>
                        <textarea name="delete_reason" rows="4" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="اذكر سبب حذف الحسابات بالتفصيل..."></textarea>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">تأكيد الحذف *</label>
                        <div class="flex items-center">
                            <input type="checkbox" name="confirm_delete" required class="ml-2 h-4 w-4 text-islamic-primary focus:ring-islamic-primary border-gray-300 rounded">
                            <span class="text-sm text-gray-700">أؤكد أنني أريد حذف الحسابات المحددة نهائياً</span>
                        </div>
                    </div>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                            حذف الحسابات
                        </button>
                        <button type="button" onclick="closeModal('bulkDeleteModal')" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Approve Modal -->
<div id="bulkApproveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <i class="fas fa-check text-green-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">الموافقة على الحسابات المحددة</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="bulkApproveText">هل أنت متأكد من الموافقة على الحسابات المحددة؟</p>
                <form id="bulk-approve-form" method="POST" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="bulk_approve">
                    <input type="hidden" name="user_ids" id="bulk-approve-user-ids">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات (اختياري)</label>
                        <textarea name="notes" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="أضف ملاحظات حول الموافقة..."></textarea>
                    </div>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                            موافقة
                        </button>
                        <button type="button" onclick="closeModal('bulkApproveModal')" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Reject Modal -->
<div id="bulkRejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-times text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">رفض الحسابات المحددة</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="bulkRejectText">هل أنت متأكد من رفض الحسابات المحددة؟</p>
                <form id="bulk-reject-form" method="POST" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="bulk_reject">
                    <input type="hidden" name="user_ids" id="bulk-reject-user-ids">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب الرفض *</label>
                        <textarea name="reason" rows="4" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="اذكر سبب رفض الحسابات..."></textarea>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات (اختياري)</label>
                        <textarea name="notes" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="أضف ملاحظات إضافية..."></textarea>
                    </div>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                            رفض
                        </button>
                        <button type="button" onclick="closeModal('bulkRejectModal')" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Ban Modal -->
<div id="bulkBanModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-ban text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4" id="bulkBanTitle">حظر الحسابات المحددة</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="bulkBanText">هل أنت متأكد من حظر الحسابات المحددة؟</p>
                <form id="bulk-ban-form" method="POST" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="bulk_ban">
                    <input type="hidden" name="ban_type" id="bulk-ban-type" value="">
                    <input type="hidden" name="user_ids" id="bulk-ban-user-ids">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب الحظر *</label>
                        <textarea name="ban_reason" rows="4" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="اذكر سبب حظر الحسابات..."></textarea>
                    </div>
                    <div id="ban-duration-section" class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">مدة الحظر *</label>
                        <div class="flex space-x-2 space-x-reverse">
                            <input type="number" name="ban_duration" min="1" required class="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" placeholder="المدة">
                            <select name="ban_unit" class="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="hours">ساعات</option>
                                <option value="days" selected>أيام</option>
                                <option value="weeks">أسابيع</option>
                                <option value="months">شهور</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700" id="bulkBanButton">
                            حظر
                        </button>
                        <button type="button" onclick="closeModal('bulkBanModal')" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function approveUser(userId, userName) {
    document.getElementById('approvalUserId').value = userId;
    document.getElementById('approvalText').textContent = `هل أنت متأكد من قبول طلب ${userName}؟`;
    document.getElementById('approvalModal').classList.remove('hidden');
}

function rejectUser(userId, userName) {
    document.getElementById('rejectionUserId').value = userId;
    document.getElementById('rejectionText').textContent = `هل أنت متأكد من رفض طلب ${userName}؟`;
    document.getElementById('rejectionModal').classList.remove('hidden');
}

function reviewUser(userId, userName) {
    if (confirm(`هل تريد وضع طلب ${userName} قيد المراجعة؟`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            {% csrf_token %}
            <input type="hidden" name="action" value="under_review">
            <input type="hidden" name="user_id" value="${userId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function banUserTemporary(userId, userName) {
    document.getElementById('temporaryBanUserId').value = userId;
    document.getElementById('temporaryBanText').textContent = `هل أنت متأكد من حظر ${userName} مؤقتاً؟`;
    document.getElementById('temporaryBanModal').classList.remove('hidden');
}

function banUserPermanent(userId, userName) {
    document.getElementById('permanentBanUserId').value = userId;
    document.getElementById('permanentBanText').textContent = `هل أنت متأكد من حظر ${userName} نهائياً؟`;
    document.getElementById('permanentBanModal').classList.remove('hidden');
}

function unbanUser(userId, userName) {
    if (confirm(`هل أنت متأكد من إلغاء حظر ${userName}؟`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            {% csrf_token %}
            <input type="hidden" name="action" value="unban">
            <input type="hidden" name="user_id" value="${userId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteUser(userId, userName) {
    document.getElementById('deleteUserId').value = userId;
    document.getElementById('deleteUserText').textContent = `هل أنت متأكد من حذف حساب ${userName}؟`;
    document.getElementById('deleteUserModal').classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Close modal when clicking outside
window.onclick = function(event) {
    const approvalModal = document.getElementById('approvalModal');
    const rejectionModal = document.getElementById('rejectionModal');
    const temporaryBanModal = document.getElementById('temporaryBanModal');
    const permanentBanModal = document.getElementById('permanentBanModal');
    const deleteUserModal = document.getElementById('deleteUserModal');

    if (event.target === approvalModal) {
        approvalModal.classList.add('hidden');
    }
    if (event.target === rejectionModal) {
        rejectionModal.classList.add('hidden');
    }
    if (event.target === temporaryBanModal) {
        temporaryBanModal.classList.add('hidden');
    }
    if (event.target === permanentBanModal) {
        permanentBanModal.classList.add('hidden');
    }
    if (event.target === deleteUserModal) {
        deleteUserModal.classList.add('hidden');
    }
}

// متغيرات للإجراءات الجماعية
let selectedUsers = [];

// تحديد/إلغاء تحديد جميع المستخدمين
document.getElementById('select-all-users').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedUsers();
});

// تحديث قائمة المستخدمين المحددين
function updateSelectedUsers() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    selectedUsers = Array.from(checkboxes).map(cb => cb.value);

    const count = selectedUsers.length;
    document.getElementById('selected-count').textContent = `تم تحديد ${count} حسابات`;

    // إظهار/إخفاء قسم الإجراءات الجماعية
    const bulkSection = document.getElementById('bulk-actions-section');
    if (count > 0) {
        bulkSection.classList.remove('hidden');
    } else {
        bulkSection.classList.add('hidden');
    }

    // تحديث حالة خانة "تحديد الكل"
    const selectAllCheckbox = document.getElementById('select-all-users');
    const allCheckboxes = document.querySelectorAll('.user-checkbox');
    selectAllCheckbox.checked = allCheckboxes.length > 0 && checkboxes.length === allCheckboxes.length;
}

// إضافة مستمعين للأحداث لخانات الاختيار
document.addEventListener('DOMContentLoaded', function() {
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedUsers);
    });
});

// تطبيق الإجراء الجماعي
document.getElementById('apply-bulk-action').addEventListener('click', function() {
    const action = document.getElementById('bulk-action-select').value;
    if (!action) {
        alert('يرجى اختيار إجراء');
        return;
    }

    if (selectedUsers.length === 0) {
        alert('يرجى تحديد حساب واحد على الأقل');
        return;
    }

    // تنفيذ الإجراء المحدد
    switch(action) {
        case 'approve':
            showBulkApproveModal();
            break;
        case 'reject':
            showBulkRejectModal();
            break;
        case 'under_review':
            confirmBulkUnderReview();
            break;
        case 'ban_temporary':
            showBulkBanModal('temporary');
            break;
        case 'ban_permanent':
            showBulkBanModal('permanent');
            break;
        case 'delete':
            showBulkDeleteModal();
            break;
    }
});

// إلغاء الإجراء الجماعي
document.getElementById('cancel-bulk-action').addEventListener('click', function() {
    // إلغاء تحديد جميع الحسابات
    document.querySelectorAll('.user-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('select-all-users').checked = false;
    updateSelectedUsers();
    document.getElementById('bulk-action-select').value = '';
});

// عرض نافذة الموافقة الجماعية
function showBulkApproveModal() {
    document.getElementById('bulk-approve-user-ids').value = selectedUsers.join(',');
    document.getElementById('bulkApproveText').textContent = `هل أنت متأكد من الموافقة على ${selectedUsers.length} حسابات؟`;
    document.getElementById('bulkApproveModal').classList.remove('hidden');
}

// عرض نافذة الرفض الجماعي
function showBulkRejectModal() {
    document.getElementById('bulk-reject-user-ids').value = selectedUsers.join(',');
    document.getElementById('bulkRejectText').textContent = `هل أنت متأكد من رفض ${selectedUsers.length} حسابات؟`;
    document.getElementById('bulkRejectModal').classList.remove('hidden');
}

// عرض نافذة الحظر الجماعي
function showBulkBanModal(banType) {
    document.getElementById('bulk-ban-user-ids').value = selectedUsers.join(',');
    document.getElementById('bulk-ban-type').value = banType;

    if (banType === 'temporary') {
        document.getElementById('bulkBanTitle').textContent = 'حظر مؤقت للحسابات المحددة';
        document.getElementById('bulkBanText').textContent = `هل أنت متأكد من حظر ${selectedUsers.length} حسابات مؤقتاً؟`;
        document.getElementById('ban-duration-section').style.display = 'block';
        document.getElementById('bulkBanButton').textContent = 'حظر مؤقت';
    } else {
        document.getElementById('bulkBanTitle').textContent = 'حظر نهائي للحسابات المحددة';
        document.getElementById('bulkBanText').textContent = `هل أنت متأكد من حظر ${selectedUsers.length} حسابات نهائياً؟`;
        document.getElementById('ban-duration-section').style.display = 'none';
        document.getElementById('bulkBanButton').textContent = 'حظر نهائي';
    }

    document.getElementById('bulkBanModal').classList.remove('hidden');
}

// عرض نافذة الحذف الجماعي
function showBulkDeleteModal() {
    document.getElementById('bulk-delete-user-ids').value = selectedUsers.join(',');
    document.getElementById('bulkDeleteText').textContent = `هل أنت متأكد من حذف ${selectedUsers.length} حسابات؟`;
    document.getElementById('bulkDeleteModal').classList.remove('hidden');
}

// تأكيد وضع قيد المراجعة الجماعي
function confirmBulkUnderReview() {
    if (confirm(`هل أنت متأكد من وضع ${selectedUsers.length} حسابات قيد المراجعة؟`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrfmiddlewaretoken" value="${document.querySelector('[name=csrfmiddlewaretoken]').value}">
            <input type="hidden" name="action" value="bulk_under_review">
            <input type="hidden" name="user_ids" value="${selectedUsers.join(',')}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
