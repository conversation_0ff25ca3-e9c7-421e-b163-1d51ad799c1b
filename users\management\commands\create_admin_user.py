from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
import getpass

User = get_user_model()


class Command(BaseCommand):
    help = 'إنشاء مستخدم مدير بصلاحيات كاملة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='اسم المستخدم (اختياري)',
        )
        parser.add_argument(
            '--email',
            type=str,
            help='البريد الإلكتروني (اختياري)',
        )
        parser.add_argument(
            '--password',
            type=str,
            help='كلمة المرور (اختياري)',
        )
        parser.add_argument(
            '--first-name',
            type=str,
            help='الاسم الأول (اختياري)',
        )
        parser.add_argument(
            '--last-name',
            type=str,
            help='الاسم الأخير (اختياري)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔧 إنشاء مستخدم مدير جديد')
        )
        
        # جمع البيانات
        username = options.get('username') or input('اسم المستخدم: ')
        email = options.get('email') or input('البريد الإلكتروني: ')
        first_name = options.get('first_name') or input('الاسم الأول: ')
        last_name = options.get('last_name') or input('الاسم الأخير: ')
        
        # كلمة المرور
        if options.get('password'):
            password = options.get('password')
        else:
            password = getpass.getpass('كلمة المرور: ')
            password_confirm = getpass.getpass('تأكيد كلمة المرور: ')
            
            if password != password_confirm:
                self.stdout.write(
                    self.style.ERROR('❌ كلمات المرور غير متطابقة')
                )
                return
        
        # التحقق من وجود المستخدم
        if User.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.ERROR(f'❌ اسم المستخدم "{username}" موجود بالفعل')
            )
            return
            
        if User.objects.filter(email=email).exists():
            self.stdout.write(
                self.style.ERROR(f'❌ البريد الإلكتروني "{email}" موجود بالفعل')
            )
            return
        
        try:
            # إنشاء المستخدم المدير
            admin_user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                user_type='admin',  # مهم: تعيين نوع المستخدم كمدير
                is_staff=True,
                is_superuser=True,
                is_active=True,
                verification_status='approved',
                verified_at=timezone.now()
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ تم إنشاء المستخدم المدير بنجاح!')
            )
            self.stdout.write(f'👤 اسم المستخدم: {admin_user.username}')
            self.stdout.write(f'📧 البريد الإلكتروني: {admin_user.email}')
            self.stdout.write(f'👨‍💼 الاسم الكامل: {admin_user.get_full_name()}')
            self.stdout.write(f'🔑 نوع المستخدم: {admin_user.get_user_type_display()}')
            self.stdout.write(f'✅ حالة التحقق: {admin_user.verification_status}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في إنشاء المستخدم: {str(e)}')
            )
