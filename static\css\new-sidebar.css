/* 🚀 القائمة الجانبية الجديدة الموحدة والمتجاوبة */
/* تعمل على جميع الأجهزة: Mobile, Tablet, Desktop */

/* ========================================
   🎨 متغيرات CSS للألوان والأبعاد
======================================== */
:root {
  /* الأبعاد */
  --new-sidebar-width-desktop: 256px;
  --new-sidebar-width-tablet: 320px;
  --new-sidebar-width-mobile: 100vw;
  
  /* الألوان الأساسية */
  --new-sidebar-bg: var(--islamic-primary, #1a365d);
  --new-sidebar-bg-gradient: linear-gradient(135deg, var(--islamic-primary, #1a365d) 0%, var(--islamic-dark, #0f2a44) 100%);
  --new-sidebar-text: #ffffff;
  --new-sidebar-text-secondary: rgba(255, 255, 255, 0.8);
  --new-sidebar-border: var(--islamic-light, rgba(255, 255, 255, 0.1));
  --new-sidebar-gold: var(--islamic-gold, #d4af37);
  
  /* ألوان الأيقونات */
  --icon-purple: #a855f7;
  --icon-green: #10b981;
  --icon-blue: #3b82f6;
  --icon-yellow: #f59e0b;
  --icon-red: #ef4444;
  --icon-orange: #f97316;
  --icon-gold: var(--new-sidebar-gold);
  
  /* الانتقالات */
  --new-sidebar-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --new-sidebar-transition-fast: all 0.2s ease-out;
  
  /* الظلال */
  --new-sidebar-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  --new-sidebar-shadow-light: 0 4px 15px rgba(0, 0, 0, 0.1);
  
  /* Z-index */
  --z-sidebar: 9999;
  --z-overlay: 9998;
  --z-toggle: 10000;
}

/* ========================================
   🔧 إعدادات أساسية
======================================== */
* {
  box-sizing: border-box;
}

/* منع التمرير عند فتح القائمة على الموبايل */
body.new-sidebar-open {
  overflow: hidden;
}

/* ========================================
   📱 زر التحكم (Toggle Button)
======================================== */
.new-sidebar-toggle {
  display: none;
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: var(--z-toggle);
  width: 56px;
  height: 56px;
  background: var(--new-sidebar-bg-gradient);
  border: none;
  border-radius: 50%;
  box-shadow: var(--new-sidebar-shadow);
  cursor: pointer;
  transition: var(--new-sidebar-transition);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.new-sidebar-toggle:hover {
  transform: scale(1.05);
  box-shadow: var(--new-sidebar-shadow), 0 0 20px rgba(212, 175, 55, 0.3);
}

.new-sidebar-toggle:active {
  transform: scale(0.95);
}

/* خطوط الهامبرغر */
.hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--new-sidebar-text);
  border-radius: 1px;
  transition: var(--new-sidebar-transition);
}

/* تحويل الهامبرغر إلى X عند الفتح */
.new-sidebar-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.new-sidebar-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.new-sidebar-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* ========================================
   🌫️ الخلفية الشفافة (Overlay)
======================================== */
.new-sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-overlay);
  opacity: 0;
  visibility: hidden;
  transition: var(--new-sidebar-transition);
  backdrop-filter: blur(2px);
}

.new-sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* ========================================
   🏠 القائمة الجانبية الرئيسية
======================================== */
.new-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: var(--new-sidebar-width-desktop);
  height: 100vh;
  background: var(--new-sidebar-bg-gradient);
  z-index: var(--z-sidebar);
  display: flex;
  flex-direction: column;
  box-shadow: var(--new-sidebar-shadow);
  transition: var(--new-sidebar-transition);
  overflow: hidden;
}

/* تخصيص خاص للمدير */
body.admin-dashboard .new-sidebar,
.admin-dashboard .new-sidebar {
  background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body.admin-dashboard .enhanced-card-header,
.admin-dashboard .enhanced-card-header {
  background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body.admin-dashboard .enhanced-card,
.admin-dashboard .enhanced-card {
  background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* تخصيص خاص للطالب */
body.student-dashboard .new-sidebar,
.student-dashboard .new-sidebar {
  background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body.student-dashboard .enhanced-card-header,
.student-dashboard .enhanced-card-header {
  background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body.student-dashboard .enhanced-card,
.student-dashboard .enhanced-card {
  background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* زر الإغلاق */
.new-sidebar-close {
  display: none;
  position: absolute;
  top: 1rem;
  left: 1rem;
  z-index: 10;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: var(--new-sidebar-text);
  cursor: pointer;
  transition: var(--new-sidebar-transition-fast);
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.new-sidebar-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* ========================================
   📋 رأس القائمة (Header)
======================================== */
.new-sidebar-header {
  flex-shrink: 0;
  padding: 1.5rem;
  border-bottom: 1px solid var(--new-sidebar-border);
}

/* قسم شعار الأكاديمية */
.academy-logo-section {
  margin-bottom: 1rem;
}

.academy-logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.academy-logo-img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid var(--new-sidebar-gold);
  box-shadow: var(--new-sidebar-shadow-light);
}

.academy-logo-placeholder {
  width: 48px;
  height: 48px;
  background: var(--new-sidebar-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--new-sidebar-bg);
  font-size: 1.5rem;
  box-shadow: var(--new-sidebar-shadow-light);
}

.academy-info {
  flex: 1;
}

.academy-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--new-sidebar-text);
  margin: 0;
  line-height: 1.2;
}

.academy-slogan {
  font-size: 0.875rem;
  color: var(--new-sidebar-text-secondary);
  margin: 0.25rem 0 0 0;
  line-height: 1.3;
}

/* قسم معلومات المستخدم */
.user-info-section {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.user-info-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--new-sidebar-gold);
  box-shadow: var(--new-sidebar-shadow-light);
}

.user-avatar-placeholder {
  width: 40px;
  height: 40px;
  background: var(--new-sidebar-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--new-sidebar-bg);
  font-size: 1.1rem;
  box-shadow: var(--new-sidebar-shadow-light);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--new-sidebar-text);
  margin: 0;
  line-height: 1.2;
}

.user-role {
  margin-top: 0.25rem;
}

.role-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.role-admin {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #1f2937;
}

.role-teacher {
  background: linear-gradient(135deg, #34d399, #10b981);
  color: #1f2937;
}

.role-student {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  color: #ffffff;
}

/* ========================================
   📝 محتوى القائمة (Content)
======================================== */
.new-sidebar-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.new-sidebar-nav {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1rem 0;
  min-height: 0;
}

/* تخصيص شريط التمرير */
.new-sidebar-nav::-webkit-scrollbar,
.new-sidebar-footer::-webkit-scrollbar {
  width: 6px;
}

.new-sidebar-nav::-webkit-scrollbar-track,
.new-sidebar-footer::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.new-sidebar-nav::-webkit-scrollbar-thumb,
.new-sidebar-footer::-webkit-scrollbar-thumb {
  background: var(--new-sidebar-gold);
  border-radius: 3px;
}

.new-sidebar-nav::-webkit-scrollbar-thumb:hover,
.new-sidebar-footer::-webkit-scrollbar-thumb:hover {
  background: #e6c547;
}

/* قائمة التنقل */
.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* ========================================
   📂 أقسام التنقل (Sections)
======================================== */
.nav-section {
  margin: 1.5rem 0 0.5rem 0;
}

.nav-section:first-child {
  margin-top: 0.5rem;
}

.nav-section-header {
  padding: 0 1.5rem;
  margin-bottom: 0.5rem;
  position: relative;
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 700;
  color: var(--new-sidebar-gold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  display: inline-block;
  padding: 0 0.5rem;
  background: var(--new-sidebar-bg);
}

.nav-section-header::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 1.5rem;
  right: 1.5rem;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--new-sidebar-gold), transparent);
  z-index: 1;
}

.nav-section-title {
  z-index: 2;
  position: relative;
}

/* قسم الحصص المباشرة */
.nav-section-live .nav-section-title {
  color: #ef4444;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* ========================================
   🔗 عناصر التنقل (Navigation Items)
======================================== */
.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: var(--new-sidebar-text);
  text-decoration: none;
  transition: var(--new-sidebar-transition-fast);
  position: relative;
  gap: 0.75rem;
  border-radius: 0;
  border-right: 3px solid transparent;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--new-sidebar-text);
  border-right-color: var(--new-sidebar-gold);
}

.nav-link-active {
  background: rgba(255, 255, 255, 0.15);
  border-right-color: var(--new-sidebar-gold);
  box-shadow: inset 0 0 20px rgba(212, 175, 55, 0.1);
}

/* أيقونات التنقل */
.nav-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: var(--new-sidebar-gold);
  transition: var(--new-sidebar-transition-fast);
  flex-shrink: 0;
}

.nav-link:hover .nav-icon {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* ألوان الأيقونات */
.nav-icon-purple { color: var(--icon-purple); }
.nav-icon-green { color: var(--icon-green); }
.nav-icon-blue { color: var(--icon-blue); }
.nav-icon-yellow { color: var(--icon-yellow); }
.nav-icon-red { color: var(--icon-red); }
.nav-icon-orange { color: var(--icon-orange); }
.nav-icon-gold { color: var(--icon-gold); }

/* نصوص التنقل */
.nav-text {
  font-size: 0.9rem;
  font-weight: 500;
  flex: 1;
  line-height: 1.3;
}

.nav-text-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.nav-subtext {
  font-size: 0.75rem;
  color: var(--new-sidebar-text-secondary);
  line-height: 1.2;
}

/* ========================================
   🏷️ الشارات والمؤشرات (Badges & Indicators)
======================================== */
.nav-badge {
  background: #ef4444;
  color: #ffffff;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
  line-height: 1;
  flex-shrink: 0;
}

.nav-badge-pulse {
  animation: pulse 2s infinite;
}

.nav-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.nav-indicator-live {
  background: #ef4444;
  animation: pulse 1.5s infinite;
}

/* ========================================
   🎥 الحصص المباشرة والمجدولة
======================================== */
/* الحصص المباشرة */
.nav-item-live .nav-link {
  background: rgba(239, 68, 68, 0.1);
  border-right-color: #ef4444;
}

.nav-link-live .nav-icon,
.nav-link-live-student .nav-icon {
  background: #ef4444;
  color: #ffffff;
  animation: pulse 2s infinite;
}

.nav-badge-live,
.nav-badge-live-student {
  background: #ef4444;
  animation: pulse 2s infinite;
}

/* الحصص المجدولة */
.nav-item-scheduled .nav-link {
  background: rgba(59, 130, 246, 0.1);
  border-right-color: #3b82f6;
}

.nav-link-scheduled .nav-icon {
  background: #3b82f6;
  color: #ffffff;
}

.nav-badge-scheduled {
  background: #3b82f6;
}

/* حصص الاشتراكات */
.nav-item-subscription .nav-link,
.nav-item-subscription-student .nav-link {
  background: rgba(168, 85, 247, 0.1);
  border-right-color: #a855f7;
  animation: pulse 3s infinite;
}

.nav-link-subscription .nav-icon,
.nav-link-subscription-student .nav-icon {
  background: #a855f7;
  color: #ffffff;
  animation: pulse 2s infinite;
}

.nav-badge-subscription,
.nav-badge-subscription-student {
  background: #a855f7;
  animation: pulse 2s infinite;
}

/* حصص الطلاب */
.nav-item-live .nav-link-live-student {
  background: rgba(16, 185, 129, 0.1);
  border-right-color: #10b981;
}

.nav-icon-live-student {
  background: #10b981 !important;
  color: #ffffff !important;
}

.nav-badge-live-student {
  background: #10b981;
}

.nav-icon-subscription-student {
  background: #14b8a6 !important;
  color: #ffffff !important;
}

.nav-badge-subscription-student {
  background: #14b8a6;
}

/* ========================================
   🦶 تذييل القائمة (Footer)
======================================== */
.new-sidebar-footer {
  flex-shrink: 0;
  padding: 1rem;
  border-top: 1px solid var(--new-sidebar-border);
  background: rgba(0, 0, 0, 0.15);
  min-height: 120px;
  max-height: 200px;
  overflow-y: auto;
  position: relative;
  z-index: 10;
}

.footer-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  color: var(--new-sidebar-text);
  text-decoration: none;
  border-radius: 8px;
  transition: var(--new-sidebar-transition-fast);
  gap: 0.75rem;
}

.footer-nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--new-sidebar-text);
}

.footer-nav-link-logout:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

.footer-nav-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  color: var(--new-sidebar-gold);
  flex-shrink: 0;
}

.footer-nav-link-logout .footer-nav-icon {
  color: #ef4444;
}

.footer-nav-text {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.3;
}

/* ========================================
   📱 التجاوب - Mobile (< 768px)
======================================== */
@media (max-width: 767px) {
  /* إظهار زر التحكم */
  .new-sidebar-toggle {
    display: flex;
  }

  /* إظهار الخلفية الشفافة */
  .new-sidebar-overlay {
    display: block;
  }

  /* القائمة الجانبية - ملء الشاشة */
  .new-sidebar {
    width: var(--new-sidebar-width-mobile);
    right: -100%;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .new-sidebar.active {
    right: 0;
    transform: translateX(0);
  }

  /* إظهار زر الإغلاق */
  .new-sidebar-close {
    display: flex;
  }

  /* تقليل padding في الرأس */
  .new-sidebar-header {
    padding: 1rem;
    padding-top: 4rem; /* مساحة لزر الإغلاق */
  }

  /* تحسين معلومات المستخدم */
  .user-info-section {
    padding: 0.75rem;
  }

  /* تحسين التنقل */
  .nav-link {
    padding: 1rem 1.25rem;
    font-size: 1rem;
  }

  .nav-icon {
    width: 36px;
    height: 36px;
    font-size: 1.1rem;
  }

  .nav-text {
    font-size: 1rem;
  }

  /* تحسين التذييل للموبايل */
  .new-sidebar-footer {
    padding: 0.75rem;
    flex-shrink: 0;
    min-height: 140px;
    max-height: 180px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.25);
    border-top: 2px solid var(--new-sidebar-gold);
  }

  .footer-nav-list {
    gap: 0.25rem;
    display: flex;
    flex-direction: column;
  }

  .footer-nav-link {
    padding: 0.75rem;
    font-size: 0.95rem;
    border-radius: 8px;
    min-height: 48px; /* ارتفاع كافي للمس */
    display: flex;
    align-items: center;
  }

  .footer-nav-icon {
    width: 24px;
    height: 24px;
    font-size: 0.95rem;
    flex-shrink: 0;
  }

  .footer-nav-text {
    font-size: 0.9rem;
    font-weight: 600;
    margin-right: 0.75rem;
  }

  /* تقليل المحتوى العلوي لإفساح مجال للـ footer */
  .new-sidebar-nav {
    padding-bottom: 0;
    margin-bottom: 0;
  }

  .new-sidebar-content {
    padding-bottom: 0;
  }

  /* تحسين الشارات */
  .nav-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
}

/* ========================================
   📱 التجاوب - Tablet (768px - 1023px)
======================================== */
@media (min-width: 768px) and (max-width: 1023px) {
  /* إظهار زر التحكم */
  .new-sidebar-toggle {
    display: flex;
  }

  /* إظهار الخلفية الشفافة */
  .new-sidebar-overlay {
    display: block;
    background: rgba(0, 0, 0, 0.3);
  }

  /* القائمة الجانبية - منزلقة */
  .new-sidebar {
    width: var(--new-sidebar-width-tablet);
    right: calc(-1 * var(--new-sidebar-width-tablet));
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .new-sidebar.active {
    right: 0;
  }

  /* إظهار زر الإغلاق */
  .new-sidebar-close {
    display: flex;
  }

  /* تحسين الرأس */
  .new-sidebar-header {
    padding: 1.25rem;
    padding-top: 3.5rem; /* مساحة لزر الإغلاق */
  }

  /* تحسين التنقل */
  .nav-link {
    padding: 0.875rem 1.5rem;
  }

  .nav-icon {
    width: 34px;
    height: 34px;
    font-size: 1.05rem;
  }

  .nav-text {
    font-size: 0.95rem;
  }
}

/* ========================================
   🖥️ التجاوب - Desktop (≥ 1024px)
======================================== */
@media (min-width: 1024px) {
  /* إخفاء عناصر الموبايل */
  .new-sidebar-toggle,
  .new-sidebar-overlay,
  .new-sidebar-close {
    display: none !important;
  }

  /* القائمة الجانبية ثابتة */
  .new-sidebar {
    position: fixed;
    right: 0;
    width: var(--new-sidebar-width-desktop);
    transform: none;
    transition: none;
  }

  /* تحسين الرأس */
  .new-sidebar-header {
    padding: 1.5rem;
  }

  /* تحسين التنقل */
  .nav-link {
    padding: 0.75rem 1.5rem;
  }

  .nav-icon {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  .nav-text {
    font-size: 0.9rem;
  }
}

/* ========================================
   🎯 تحسينات الأداء
======================================== */
.new-sidebar {
  will-change: transform, right;
  backface-visibility: hidden;
  perspective: 1000px;
}

.new-sidebar-overlay {
  will-change: opacity, visibility;
}

/* تحسين الرسم */
.new-sidebar *,
.new-sidebar *::before,
.new-sidebar *::after {
  box-sizing: border-box;
}

/* تحسين التمرير */
.new-sidebar-nav {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: var(--new-sidebar-gold) rgba(255, 255, 255, 0.1);
}

/* ========================================
   🌟 تأثيرات إضافية
======================================== */
/* تأثير الضوء عند التمرير */
.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: width 0.3s ease;
  z-index: -1;
}

.nav-link:hover::before {
  width: 100%;
}

/* تأثير النبض للعناصر المهمة */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.8);
  }
}

.nav-link-active {
  animation: glow 3s ease-in-out infinite;
}

/* تحسين الوصولية */
.new-sidebar:focus-within {
  outline: 2px solid var(--new-sidebar-gold);
  outline-offset: -2px;
}

.nav-link:focus {
  outline: 2px solid var(--new-sidebar-gold);
  outline-offset: -2px;
  background: rgba(255, 255, 255, 0.15);
}

/* تحسين الطباعة */
@media print {
  .new-sidebar,
  .new-sidebar-toggle,
  .new-sidebar-overlay {
    display: none !important;
  }
}

/* ========================================
   📄 تحسينات المحتوى الرئيسي
======================================== */

/* المحتوى الرئيسي */
.main-content {
  transition: margin-right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
}

/* تحسينات للكمبيوتر */
@media (min-width: 1024px) {
  .main-content {
    margin-right: var(--new-sidebar-width-desktop) !important;
    padding: 2rem;
  }
}

/* تحسينات للتابلت */
@media (min-width: 768px) and (max-width: 1023px) {
  .main-content {
    margin-right: 0 !important;
    padding: 1.5rem;
  }
}

/* تحسينات للموبايل */
@media (max-width: 767px) {
  .main-content {
    margin-right: 0 !important;
    padding: 1rem;
  }

  /* منع التمرير عند فتح القائمة */
  body.new-sidebar-open .main-content {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }
}

/* تحسين العناصر داخل المحتوى */
.main-content .container {
  max-width: 100%;
  margin: 0 auto;
}

/* تحسين الكروت والعناصر */
.main-content .card,
.main-content .bg-white {
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.main-content .card:hover,
.main-content .bg-white:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* تحسين الجداول */
.main-content table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* تحسين الأزرار */
.main-content .btn,
.main-content button {
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* تحسين النماذج */
.main-content input,
.main-content select,
.main-content textarea {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.main-content input:focus,
.main-content select:focus,
.main-content textarea:focus {
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
  border-color: var(--new-sidebar-gold);
}

/* ========================================
   🎯 إصلاحات التوافق مع القائمة القديمة
======================================== */

/* إخفاء عناصر القائمة القديمة إذا كانت موجودة */
.sidebar-toggle:not(.new-sidebar-toggle),
.sidebar-overlay:not(.new-sidebar-overlay),
.mobile-sidebar,
.desktop-sidebar {
  display: none !important;
}

/* إصلاح تضارب الفئات */
.lg\:mr-64 {
  margin-right: 0 !important;
}

@media (min-width: 1024px) {
  .lg\:mr-64 {
    margin-right: var(--new-sidebar-width-desktop) !important;
  }
}

/* ========================================
   🌟 تحسينات إضافية للتجربة
======================================== */

/* تحسين التمرير السلس */
html {
  scroll-behavior: smooth;
}

/* تحسين التركيز */
*:focus {
  outline: 2px solid var(--new-sidebar-gold);
  outline-offset: 2px;
}

/* تحسين الانتقالات العامة */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* تحسين الصور */
img {
  border-radius: 8px;
  transition: transform 0.3s ease;
}

img:hover {
  transform: scale(1.02);
}

/* تحسين الروابط */
a {
  transition: color 0.3s ease;
}

a:hover {
  color: var(--new-sidebar-gold);
}

/* تحسين الظلال */
.shadow,
.shadow-sm,
.shadow-md,
.shadow-lg {
  transition: box-shadow 0.3s ease;
}

/* تحسين الحدود */
.border {
  border-color: rgba(0, 0, 0, 0.1);
  transition: border-color 0.3s ease;
}

.border:hover {
  border-color: var(--new-sidebar-gold);
}

/* ========================================
   📱 تحسينات للشاشات الصغيرة جداً
======================================== */
@media (max-height: 600px) and (max-width: 768px) {
  .new-sidebar-footer {
    min-height: 120px !important;
    padding: 0.5rem !important;
  }

  .footer-nav-link {
    padding: 0.5rem !important;
    font-size: 0.85rem !important;
    min-height: 40px !important;
  }

  .footer-nav-icon {
    width: 20px !important;
    height: 20px !important;
    font-size: 0.85rem !important;
  }

  .footer-nav-text {
    font-size: 0.8rem !important;
  }
}

@media (max-height: 500px) and (max-width: 768px) {
  .new-sidebar-footer {
    min-height: 100px !important;
    padding: 0.25rem !important;
  }

  .footer-nav-link {
    padding: 0.375rem !important;
    font-size: 0.75rem !important;
    min-height: 32px !important;
  }

  .footer-nav-icon {
    width: 18px !important;
    height: 18px !important;
    font-size: 0.75rem !important;
  }

  .footer-nav-text {
    font-size: 0.7rem !important;
  }
}
