<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>فاتورة {{ invoice.invoice_number }}</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            font-size: 12px;
        }

        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            background: #2c5aa0;
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }

        .header-content {
            display: table;
            width: 100%;
        }

        .company-info, .invoice-info {
            display: table-cell;
            vertical-align: top;
        }

        .company-info {
            width: 60%;
        }

        .invoice-info {
            width: 40%;
            text-align: left;
        }

        .company-info h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .company-info p, .invoice-info p {
            margin-bottom: 3px;
            font-size: 11px;
        }

        .invoice-info h2 {
            font-size: 20px;
            margin-bottom: 8px;
        }

        .customer-section {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }

        .customer-info, .subscription-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 10px;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c5aa0;
            border-bottom: 1px solid #2c5aa0;
            padding-bottom: 3px;
        }

        .info-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }

        .info-box p {
            margin-bottom: 5px;
            font-size: 11px;
        }

        .info-box .label {
            font-weight: bold;
            color: #666;
        }

        .info-box .value {
            color: #333;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 1px solid #ddd;
        }

        .items-table th {
            background: #f8f9fa;
            padding: 10px 8px;
            text-align: right;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
            color: #2c5aa0;
            font-size: 11px;
        }

        .items-table td {
            padding: 8px;
            border-bottom: 1px solid #eee;
            font-size: 10px;
        }

        .items-table tr:nth-child(even) {
            background: #f9f9f9;
        }

        .totals-section {
            text-align: left;
            margin-bottom: 20px;
        }

        .totals-box {
            width: 250px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            margin-left: auto;
        }

        .total-row {
            display: table;
            width: 100%;
            margin-bottom: 8px;
            padding: 3px 0;
        }

        .total-row .label, .total-row .value {
            display: table-cell;
        }

        .total-row .label {
            text-align: right;
            padding-left: 20px;
        }

        .total-row .value {
            text-align: left;
            font-weight: bold;
        }

        .total-row.final {
            border-top: 2px solid #2c5aa0;
            padding-top: 10px;
            margin-top: 10px;
            font-size: 14px;
            font-weight: bold;
            color: #2c5aa0;
        }

        .notes-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            margin-bottom: 20px;
        }

        .notes-section p {
            font-size: 11px;
            line-height: 1.5;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 9px;
            font-weight: bold;
        }

        .status-paid {
            background: #d4edda;
            color: #155724;
        }

        .status-sent {
            background: #fff3cd;
            color: #856404;
        }

        .status-overdue {
            background: #f8d7da;
            color: #721c24;
        }

        .status-draft {
            background: #e2e3e5;
            color: #383d41;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 10px;
        }

        .footer p {
            margin-bottom: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="company-info">
                    {% if company_info.logo %}
                    <div style="display: table; width: 100%;">
                        <div style="display: table-cell; width: 60px; vertical-align: top; padding-left: 15px;">
                            <img src="{{ company_info.logo }}" alt="{{ company_info.name }}" style="width: 50px; height: 50px; border-radius: 5px; background: white; padding: 3px;">
                        </div>
                        <div style="display: table-cell; vertical-align: top;">
                            <h1>{{ company_info.name }}</h1>
                            {% if company_info.description %}
                            <p style="font-size: 10px; margin-bottom: 5px;">{{ company_info.description }}</p>
                            {% endif %}
                            <p>{{ company_info.address }}</p>
                            <p>{{ company_info.phone }}</p>
                            <p>{{ company_info.email }}</p>
                            {% if company_info.website %}
                            <p>{{ company_info.website }}</p>
                            {% endif %}
                        </div>
                    </div>
                    {% else %}
                    <h1>{{ company_info.name }}</h1>
                    {% if company_info.description %}
                    <p style="font-size: 10px; margin-bottom: 5px;">{{ company_info.description }}</p>
                    {% endif %}
                    <p>{{ company_info.address }}</p>
                    <p>{{ company_info.phone }}</p>
                    <p>{{ company_info.email }}</p>
                    {% if company_info.website %}
                    <p>{{ company_info.website }}</p>
                    {% endif %}
                    {% endif %}
                </div>
                <div class="invoice-info">
                    <h2>فاتورة</h2>
                    <p>رقم: {{ invoice.invoice_number }}</p>
                    <p>التاريخ: {{ invoice.issue_date|date:"Y-m-d" }}</p>
                    <p>الاستحقاق: {{ invoice.due_date|date:"Y-m-d" }}</p>
                    <p>
                        الحالة:
                        <span class="status-badge status-{{ invoice.status }}">
                            {{ invoice.get_status_display }}
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Customer and Subscription Info -->
        <div class="customer-section">
            <div class="customer-info">
                <h3 class="section-title">معلومات العميل</h3>
                <div class="info-box">
                    <p><span class="label">الاسم:</span> <span class="value">{{ invoice.student_name }}</span></p>
                    <p><span class="label">البريد الإلكتروني:</span> <span class="value">{{ invoice.student_email }}</span></p>
                    {% if invoice.student_phone %}
                    <p><span class="label">الهاتف:</span> <span class="value">{{ invoice.student_phone }}</span></p>
                    {% endif %}
                </div>
            </div>

            <div class="subscription-info">
                <h3 class="section-title">تفاصيل الاشتراك</h3>
                <div class="info-box">
                    <p><span class="label">الباقة:</span> <span class="value">{{ invoice.plan_name }}</span></p>
                    <p><span class="label">تاريخ البداية:</span> <span class="value">{{ invoice.subscription.start_date|date:"Y-m-d" }}</span></p>
                    <p><span class="label">تاريخ النهاية:</span> <span class="value">{{ invoice.subscription.end_date|date:"Y-m-d" }}</span></p>
                </div>
            </div>
        </div>

        <!-- Invoice Items -->
        <h3 class="section-title">تفاصيل الفاتورة</h3>
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 50%;">الوصف</th>
                    <th style="width: 15%;">الكمية</th>
                    <th style="width: 20%;">سعر الوحدة</th>
                    <th style="width: 15%;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ item.description }}</td>
                    <td style="text-align: center;">{{ item.quantity }}</td>
                    <td style="text-align: center;">
                        {% if item.unit_price > 0 %}
                            {{ item.unit_price }} {{ invoice.get_currency_symbol }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td style="text-align: center;">
                        {% if item.total_price > 0 %}
                            {{ item.total_price }} {{ invoice.get_currency_symbol }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="totals-box">
                <div class="total-row">
                    <span class="label">المبلغ الفرعي:</span>
                    <span class="value">{{ invoice.subtotal }} {{ invoice.get_currency_symbol }}</span>
                </div>
                {% if invoice.discount_amount > 0 %}
                <div class="total-row" style="color: #28a745;">
                    <span class="label">الخصم:</span>
                    <span class="value">-{{ invoice.discount_amount }} {{ invoice.get_currency_symbol }}</span>
                </div>
                {% endif %}
                {% if invoice.tax_amount > 0 %}
                <div class="total-row">
                    <span class="label">الضريبة:</span>
                    <span class="value">{{ invoice.tax_amount }} {{ invoice.get_currency_symbol }}</span>
                </div>
                {% endif %}
                <div class="total-row final">
                    <span class="label">المجموع الكلي:</span>
                    <span class="value">{{ invoice.total_amount }} {{ invoice.get_currency_symbol }}</span>
                </div>
            </div>
        </div>

        <!-- Notes -->
        {% if invoice.notes %}
        <h3 class="section-title">ملاحظات</h3>
        <div class="notes-section">
            <p>{{ invoice.notes }}</p>
        </div>
        {% endif %}



        <!-- Footer -->
        <div class="footer">
            <p>شكراً لك على اختيار {{ company_info.name }}</p>
            <p>تم إنشاء هذه الفاتورة إلكترونياً في {{ invoice.created_at|date:"Y-m-d H:i" }}</p>
        </div>
    </div>
</body>
</html>
