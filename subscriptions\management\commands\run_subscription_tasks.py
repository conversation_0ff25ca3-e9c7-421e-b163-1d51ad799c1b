"""
أمر إدارة لتشغيل مهام الاشتراكات الدورية
يمكن استخدامه مع cron jobs أو task schedulers
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from subscriptions.tasks import (
    check_expiring_subscriptions,
    check_exhausted_lessons,
    check_expired_subscriptions,
    run_subscription_maintenance_task
)
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'تشغيل مهام الاشتراكات الدورية'

    def add_arguments(self, parser):
        parser.add_argument(
            '--task',
            type=str,
            choices=['all', 'expiring', 'exhausted', 'expired', 'maintenance'],
            default='all',
            help='نوع المهمة المراد تشغيلها'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='تشغيل تجريبي بدون إرسال إشعارات فعلية'
        )

    def handle(self, *args, **options):
        task_type = options['task']
        dry_run = options['dry_run']
        
        start_time = timezone.now()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'[{start_time}] بدء تشغيل مهام الاشتراكات - النوع: {task_type}'
            )
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('تشغيل تجريبي - لن يتم إرسال إشعارات فعلية')
            )
        
        results = {}
        
        try:
            if task_type == 'all' or task_type == 'maintenance':
                # تشغيل جميع المهام
                maintenance_results = run_subscription_maintenance_task()
                results.update(maintenance_results)
                
            elif task_type == 'expiring':
                # فحص الاشتراكات المنتهية قريباً
                expiring_count = check_expiring_subscriptions()
                results['expiring_count'] = expiring_count
                
            elif task_type == 'exhausted':
                # فحص الاشتراكات المنتهية الحصص
                exhausted_count = check_exhausted_lessons()
                results['exhausted_count'] = exhausted_count
                
            elif task_type == 'expired':
                # فحص الاشتراكات المنتهية
                expired_count = check_expired_subscriptions()
                results['expired_count'] = expired_count
            
            end_time = timezone.now()
            duration = (end_time - start_time).total_seconds()
            
            # عرض النتائج
            self.stdout.write(
                self.style.SUCCESS(
                    f'[{end_time}] انتهاء المهام بنجاح - المدة: {duration:.2f} ثانية'
                )
            )
            
            self.stdout.write('النتائج:')
            for key, value in results.items():
                if isinstance(value, dict):
                    self.stdout.write(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        self.stdout.write(f"    {sub_key}: {sub_value}")
                else:
                    self.stdout.write(f"  {key}: {value}")
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f'[{timezone.now()}] خطأ في تشغيل المهام: {str(e)}'
                )
            )
            logger.error(f"خطأ في مهام الاشتراكات: {str(e)}", exc_info=True)
            raise e
