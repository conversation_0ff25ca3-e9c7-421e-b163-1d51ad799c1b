# Generated manually for email models

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0010_remove_whatsapp_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider', models.CharField(choices=[('gmail', 'Gmail'), ('outlook', 'Outlook/Hotmail'), ('yahoo', 'Yahoo Mail'), ('namecheap', 'Namecheap'), ('hostinger', 'Hostinger'), ('godaddy', 'GoDaddy'), ('cpanel', 'cPanel/WHM'), ('custom', 'إعدادات مخصصة')], default='custom', max_length=50, verbose_name='مزود خدمة SMTP')),
                ('smtp_host', models.Char<PERSON>ield(max_length=255, verbose_name='خادم SMTP')),
                ('smtp_port', models.IntegerField(default=587, verbose_name='منفذ SMTP')),
                ('smtp_username', models.CharField(max_length=255, verbose_name='اسم المستخدم')),
                ('smtp_password', models.TextField(verbose_name='كلمة المرور (مشفرة)')),
                ('use_tls', models.BooleanField(default=True, verbose_name='استخدام TLS')),
                ('use_ssl', models.BooleanField(default=False, verbose_name='استخدام SSL')),
                ('from_email', models.EmailField(max_length=254, verbose_name='البريد المرسل')),
                ('from_name', models.CharField(max_length=255, verbose_name='اسم المرسل')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('max_emails_per_hour', models.IntegerField(default=100, verbose_name='الحد الأقصى للرسائل في الساعة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعدادات البريد الإلكتروني',
                'verbose_name_plural': 'إعدادات البريد الإلكتروني',
            },
        ),
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='اسم القالب')),
                ('template_type', models.CharField(choices=[('welcome', 'رسالة ترحيب'), ('lesson_reminder', 'تذكير بالحصة'), ('lesson_report', 'تقرير الحصة'), ('subscription_expiry', 'انتهاء الاشتراك'), ('admin_report', 'تقرير إداري'), ('general', 'إشعار عام'), ('payment', 'إشعار دفع'), ('system', 'إشعار نظام')], max_length=50, verbose_name='نوع القالب')),
                ('subject', models.CharField(max_length=255, verbose_name='موضوع الرسالة')),
                ('html_content', models.TextField(verbose_name='المحتوى HTML')),
                ('text_content', models.TextField(blank=True, verbose_name='المحتوى النصي')),
                ('variables', models.JSONField(default=dict, verbose_name='المتغيرات المستخدمة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_default', models.BooleanField(default=False, verbose_name='قالب افتراضي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قالب البريد الإلكتروني',
                'verbose_name_plural': 'قوالب البريد الإلكتروني',
            },
        ),
        migrations.CreateModel(
            name='EmailEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('student_registration', 'تسجيل طالب جديد'), ('subscription_created', 'اشتراك جديد'), ('subscription_renewed', 'تجديد اشتراك'), ('lesson_scheduled', 'جدولة حصة جديدة'), ('lesson_reminder_24h', 'تذكير بالحصة (24 ساعة)'), ('lesson_reminder_1h', 'تذكير بالحصة (ساعة واحدة)'), ('lesson_completed', 'انتهاء الحصة'), ('lesson_report_received', 'استلام تقرير الحصة'), ('subscription_expiry_7d', 'انتهاء الاشتراك خلال 7 أيام'), ('subscription_expiry_3d', 'انتهاء الاشتراك خلال 3 أيام'), ('subscription_expiry_1d', 'انتهاء الاشتراك خلال يوم واحد'), ('subscription_expired', 'انتهى الاشتراك'), ('payment_successful', 'نجح الدفع'), ('payment_failed', 'فشل الدفع'), ('teacher_assigned', 'تعيين معلم لطالب'), ('lesson_assigned', 'تعيين حصة للمعلم'), ('lesson_reminder_teacher', 'تذكير المعلم بالحصة'), ('student_enrolled', 'تسجيل طالب جديد للمعلم'), ('lesson_report_required', 'مطلوب كتابة تقرير الحصة'), ('student_evaluation_received', 'استلام تقييم من الطالب'), ('monthly_performance_report', 'تقرير الأداء الشهري'), ('new_user_registration', 'تسجيل مستخدم جديد'), ('new_subscription', 'اشتراك جديد في النظام'), ('subscription_renewal', 'تجديد اشتراك'), ('payment_received', 'استلام دفعة جديدة'), ('daily_summary', 'ملخص يومي'), ('weekly_report', 'تقرير أسبوعي'), ('monthly_report', 'تقرير شهري'), ('system_alert', 'تنبيه النظام')], max_length=100, unique=True, verbose_name='نوع الحدث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('send_delay', models.IntegerField(default=0, verbose_name='تأخير الإرسال (بالدقائق)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.emailtemplate', verbose_name='القالب المستخدم')),
            ],
            options={
                'verbose_name': 'حدث البريد الإلكتروني',
                'verbose_name_plural': 'أحداث البريد الإلكتروني',
            },
        ),
        migrations.CreateModel(
            name='EmailQueue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(blank=True, max_length=100, verbose_name='نوع الحدث')),
                ('subject', models.CharField(max_length=255, verbose_name='موضوع الرسالة')),
                ('context_data', models.JSONField(default=dict, verbose_name='بيانات السياق')),
                ('scheduled_at', models.DateTimeField(verbose_name='موعد الإرسال')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('sending', 'جاري الإرسال'), ('sent', 'تم الإرسال'), ('failed', 'فشل الإرسال'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('attempts', models.IntegerField(default=0, verbose_name='عدد المحاولات')),
                ('max_attempts', models.IntegerField(default=3, verbose_name='الحد الأقصى للمحاولات')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستلم')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.emailtemplate', verbose_name='القالب')),
            ],
            options={
                'verbose_name': 'طابور البريد الإلكتروني',
                'verbose_name_plural': 'طابور البريد الإلكتروني',
                'ordering': ['scheduled_at'],
            },
        ),
        migrations.CreateModel(
            name='EmailLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=255, verbose_name='موضوع الرسالة')),
                ('template_name', models.CharField(blank=True, max_length=255, verbose_name='اسم القالب')),
                ('event_type', models.CharField(blank=True, max_length=100, verbose_name='نوع الحدث')),
                ('status', models.CharField(choices=[('sent', 'تم الإرسال'), ('failed', 'فشل الإرسال'), ('bounced', 'مرتد'), ('opened', 'تم فتحه'), ('clicked', 'تم النقر')], max_length=20, verbose_name='الحالة')),
                ('sent_at', models.DateTimeField(verbose_name='تاريخ الإرسال')),
                ('opened_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الفتح')),
                ('clicked_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ النقر')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('smtp_response', models.TextField(blank=True, verbose_name='استجابة SMTP')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستلم')),
            ],
            options={
                'verbose_name': 'سجل البريد الإلكتروني',
                'verbose_name_plural': 'سجلات البريد الإلكتروني',
                'ordering': ['-sent_at'],
            },
        ),
        migrations.CreateModel(
            name='EmailSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_notifications_enabled', models.BooleanField(default=True, verbose_name='تفعيل إشعارات البريد الإلكتروني')),
                ('lesson_reminders', models.BooleanField(default=True, verbose_name='تذكيرات الحصص')),
                ('subscription_notifications', models.BooleanField(default=True, verbose_name='إشعارات الاشتراك')),
                ('lesson_reports', models.BooleanField(default=True, verbose_name='تقارير الحصص')),
                ('payment_notifications', models.BooleanField(default=True, verbose_name='إشعارات الدفع')),
                ('teacher_notifications', models.BooleanField(default=True, verbose_name='إشعارات المعلم')),
                ('student_updates', models.BooleanField(default=True, verbose_name='تحديثات الطلاب')),
                ('performance_reports', models.BooleanField(default=True, verbose_name='تقارير الأداء')),
                ('admin_reports', models.BooleanField(default=True, verbose_name='التقارير الإدارية')),
                ('system_alerts', models.BooleanField(default=True, verbose_name='تنبيهات النظام')),
                ('daily_summaries', models.BooleanField(default=True, verbose_name='الملخصات اليومية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'اشتراك البريد الإلكتروني',
                'verbose_name_plural': 'اشتراكات البريد الإلكتروني',
            },
        ),
        migrations.AlterUniqueTogether(
            name='emailtemplate',
            unique_together={('template_type', 'is_default')},
        ),
    ]
