from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
# تم حذف Lesson - استخدم LiveLesson بدلاً منه
import json

User = get_user_model()

class ReportCategory(models.TextChoices):
    TEACHERS = 'teachers', 'تقارير المعلمين'
    STUDENTS = 'students', 'تقارير الطلاب'
    COURSES = 'courses', 'تقارير الدورات'
    FINANCIAL = 'financial', 'تقارير المالية'

class ReportFormat(models.TextChoices):
    PDF = 'pdf', 'PDF'
    EXCEL = 'excel', 'Excel'
    CSV = 'csv', 'CSV'

class CustomReport(models.Model):
    """نموذج للتقارير المخصصة"""
    title = models.CharField(max_length=200, verbose_name="عنوان التقرير")
    description = models.TextField(blank=True, verbose_name="وصف التقرير")
    category = models.CharField(
        max_length=20,
        choices=ReportCategory.choices,
        verbose_name="فئة التقرير"
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_reports',
        verbose_name="منشئ التقرير"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    # فلاتر التقرير (JSON)
    filters = models.JSONField(default=dict, verbose_name="فلاتر التقرير")

    # إعدادات التقرير
    date_from = models.DateField(null=True, blank=True, verbose_name="من تاريخ")
    date_to = models.DateField(null=True, blank=True, verbose_name="إلى تاريخ")

    # حالة التقرير
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_scheduled = models.BooleanField(default=False, verbose_name="مجدول")
    schedule_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري'),
            ('quarterly', 'ربع سنوي'),
        ],
        blank=True,
        verbose_name="تكرار الجدولة"
    )

    class Meta:
        verbose_name = "تقرير مخصص"
        verbose_name_plural = "التقارير المخصصة"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.get_category_display()}"

    def get_filters_display(self):
        """عرض الفلاتر بشكل مقروء"""
        if not self.filters:
            return "بدون فلاتر"

        display_filters = []
        for key, value in self.filters.items():
            if value:
                display_filters.append(f"{key}: {value}")

        return ", ".join(display_filters) if display_filters else "بدون فلاتر"

class GeneratedReport(models.Model):
    """نموذج للتقارير المولدة"""
    custom_report = models.ForeignKey(
        CustomReport,
        on_delete=models.CASCADE,
        related_name='generated_reports',
        verbose_name="التقرير المخصص"
    )
    generated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='generated_reports',
        verbose_name="مولد التقرير"
    )
    generated_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التوليد")

    # ملف التقرير
    file_format = models.CharField(
        max_length=10,
        choices=ReportFormat.choices,
        verbose_name="صيغة الملف"
    )
    file_path = models.FileField(
        upload_to='reports/%Y/%m/',
        verbose_name="مسار الملف"
    )
    file_size = models.PositiveIntegerField(default=0, verbose_name="حجم الملف (بايت)")

    # إحصائيات التقرير
    total_records = models.PositiveIntegerField(default=0, verbose_name="إجمالي السجلات")
    processing_time = models.FloatField(default=0.0, verbose_name="وقت المعالجة (ثانية)")

    # حالة التقرير
    is_ready = models.BooleanField(default=False, verbose_name="جاهز للتحميل")
    download_count = models.PositiveIntegerField(default=0, verbose_name="عدد مرات التحميل")
    last_downloaded = models.DateTimeField(null=True, blank=True, verbose_name="آخر تحميل")

    class Meta:
        verbose_name = "تقرير مولد"
        verbose_name_plural = "التقارير المولدة"
        ordering = ['-generated_at']

    def __str__(self):
        return f"{self.custom_report.title} - {self.get_file_format_display()} - {self.generated_at.strftime('%Y-%m-%d %H:%M')}"

    def get_file_size_display(self):
        """عرض حجم الملف بشكل مقروء"""
        if self.file_size < 1024:
            return f"{self.file_size} بايت"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} كيلوبايت"
        else:
            return f"{self.file_size / (1024 * 1024):.1f} ميجابايت"

    def increment_download_count(self):
        """زيادة عداد التحميل"""
        self.download_count += 1
        self.last_downloaded = timezone.now()
        self.save(update_fields=['download_count', 'last_downloaded'])

class ReportTemplate(models.Model):
    """قوالب التقارير الجاهزة"""
    name = models.CharField(max_length=200, verbose_name="اسم القالب")
    description = models.TextField(verbose_name="وصف القالب")
    category = models.CharField(
        max_length=20,
        choices=ReportCategory.choices,
        verbose_name="فئة القالب"
    )

    # إعدادات القالب
    template_config = models.JSONField(default=dict, verbose_name="إعدادات القالب")
    default_filters = models.JSONField(default=dict, verbose_name="الفلاتر الافتراضية")

    # حالة القالب
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_system_template = models.BooleanField(default=False, verbose_name="قالب النظام")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "قالب تقرير"
        verbose_name_plural = "قوالب التقارير"
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} - {self.get_category_display()}"

class ReportSchedule(models.Model):
    """جدولة التقارير التلقائية"""
    custom_report = models.OneToOneField(
        CustomReport,
        on_delete=models.CASCADE,
        related_name='schedule',
        verbose_name="التقرير المخصص"
    )

    # إعدادات الجدولة
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري'),
            ('quarterly', 'ربع سنوي'),
        ],
        verbose_name="تكرار التوليد"
    )

    # توقيت التنفيذ
    execution_time = models.TimeField(verbose_name="وقت التنفيذ")
    next_execution = models.DateTimeField(verbose_name="التنفيذ التالي")
    last_execution = models.DateTimeField(null=True, blank=True, verbose_name="آخر تنفيذ")

    # إعدادات الإشعارات
    notify_on_completion = models.BooleanField(default=True, verbose_name="إشعار عند الانتهاء")
    notification_emails = models.JSONField(default=list, verbose_name="بريد الإشعارات")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "جدولة تقرير"
        verbose_name_plural = "جدولة التقارير"
        ordering = ['next_execution']

    def __str__(self):
        return f"جدولة {self.custom_report.title} - {self.get_frequency_display()}"
