# Django
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
db.sqlite3-journal
db.sqlite3-shm
db.sqlite3-wal

# Media files
media/

# Static files (if using collectstatic)
staticfiles/

# Virtual environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Cache
cache/
.cache/

# Logs
logs/
*.log

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Environment variables
.env
.env.local
.env.production

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover

# Pytest
.pytest_cache/

# Celery
celerybeat-schedule
celerybeat.pid

# Redis
dump.rdb

# Node.js (if using)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production specific
passenger_wsgi.pyc
*.pid



# Development files
create_test_data.py
passenger_wsgi.py

# Documentation drafts
*.draft.md
