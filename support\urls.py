from django.urls import path
from . import views

urlpatterns = [
    # Admin Support Dashboard
    path('admin/', views.support_dashboard, name='admin_support'),

    # Ticket Management
    path('tickets/', views.ticket_list, name='ticket_list'),
    path('tickets/create/', views.create_ticket, name='create_ticket'),
    path('tickets/<int:ticket_id>/', views.ticket_detail, name='ticket_detail'),
    path('tickets/<int:ticket_id>/update-status/', views.update_ticket_status, name='update_ticket_status'),
    path('tickets/<int:ticket_id>/close/', views.close_ticket, name='close_ticket'),

    # Support Messages (للعرض فقط)
    path('messages/', views.support_messages, name='support_messages'),

    # System Messages (رسائل النظام)
    path('send-system-message/', views.send_system_message, name='send_system_message'),
    path('system-messages/', views.system_messages, name='system_messages'),
    path('system-messages/<int:message_id>/', views.system_message_detail, name='system_message_detail'),
    path('system-messages/<int:message_id>/mark-read/', views.mark_system_message_read, name='mark_system_message_read'),
]
