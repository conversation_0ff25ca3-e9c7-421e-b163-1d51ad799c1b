{% extends 'base.html' %}
{% load static %}

{% block title %}موافقة على الاشتراك - {{ subscription.student.get_full_name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-islamic-primary">موافقة على الاشتراك</h1>
                    <p class="text-gray-600 mt-1">مراجعة وموافقة على طلب الاشتراك</p>
                </div>
                <div class="flex space-x-4 space-x-reverse">
                    <a href="{% url 'admin_subscriptions' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>

        <!-- Subscription Details -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">تفاصيل الاشتراك</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Student Information -->
                <div class="bg-blue-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-blue-900 mb-4">معلومات الطالب</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600">الاسم:</span>
                            <p class="font-semibold">{{ subscription.student.get_full_name }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">البريد الإلكتروني:</span>
                            <p class="font-semibold">{{ subscription.student.email }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">رقم الهاتف:</span>
                            <p class="font-semibold">{{ subscription.student.phone|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">تاريخ التسجيل:</span>
                            <p class="font-semibold">{{ subscription.student.date_joined|date:"Y-m-d" }}</p>
                        </div>
                    </div>
                </div>

                <!-- Plan Information -->
                <div class="bg-green-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-green-900 mb-4">معلومات الباقة</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600">اسم الباقة:</span>
                            <p class="font-semibold">{{ subscription.plan.name }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">نوع الباقة:</span>
                            <p class="font-semibold">{{ subscription.plan.get_plan_type_display }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">المدة:</span>
                            <p class="font-semibold">{{ subscription.plan.duration_days }} يوم</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">عدد الحصص:</span>
                            <p class="font-semibold">{{ subscription.plan.lessons_count }} حصة</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">السعر:</span>
                            <p class="font-semibold">{{ subscription.plan.get_discounted_price }} {{ subscription.plan.currency }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Details -->
            <div class="mt-6 bg-yellow-50 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-yellow-900 mb-4">تفاصيل الاشتراك</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-600">تاريخ البداية:</span>
                        <p class="font-semibold">{{ subscription.start_date|date:"Y-m-d" }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-600">تاريخ النهاية:</span>
                        <p class="font-semibold">{{ subscription.end_date|date:"Y-m-d" }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-600">المبلغ المدفوع:</span>
                        <p class="font-semibold">{{ subscription.amount_paid }} {{ subscription.plan.currency }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-600">الحالة الحالية:</span>
                        <span class="px-2 py-1 rounded-full text-xs font-medium
                            {% if subscription.status == 'pending_approval' %}bg-yellow-100 text-yellow-800
                            {% elif subscription.status == 'active' %}bg-green-100 text-green-800
                            {% elif subscription.status == 'cancelled' %}bg-red-100 text-red-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ subscription.get_status_display }}
                        </span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-600">تاريخ الطلب:</span>
                        <p class="font-semibold">{{ subscription.created_at|date:"Y-m-d H:i" }}</p>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            {% if subscription.payments.exists %}
            <div class="mt-6 bg-purple-50 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-purple-900 mb-4">معلومات الدفع</h3>
                {% for payment in subscription.payments.all %}
                <div class="border-b border-purple-200 pb-3 mb-3 last:border-b-0 last:pb-0 last:mb-0">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <span class="text-sm font-medium text-gray-600">طريقة الدفع:</span>
                            <p class="font-semibold">{{ payment.get_payment_method_display }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">المبلغ:</span>
                            <p class="font-semibold">{{ payment.amount }} {{ subscription.plan.currency }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">حالة الدفع:</span>
                            <span class="px-2 py-1 rounded-full text-xs font-medium
                                {% if payment.status == 'completed' %}bg-green-100 text-green-800
                                {% elif payment.status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif payment.status == 'processing' %}bg-blue-100 text-blue-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ payment.get_status_display }}
                            </span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">تاريخ الدفع:</span>
                            <p class="font-semibold">{{ payment.created_at|date:"Y-m-d H:i" }}</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <!-- Action Buttons -->
        {% if subscription.status == 'pending_approval' %}
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-6">إجراءات الموافقة</h2>

            <div class="flex space-x-4 space-x-reverse justify-center">
                <!-- Approve Button -->
                <form method="post" class="inline">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="approve">
                    <button type="submit"
                            onclick="return confirm('هل أنت متأكد من موافقة على هذا الاشتراك؟ سيتم تفعيله فوراً وإنشاء فاتورة للطالب.')"
                            class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-lg shadow-md transition-all duration-300 flex items-center">
                        <i class="fas fa-check ml-2"></i>
                        موافقة وتفعيل الاشتراك
                    </button>
                </form>

                <!-- Reject Button -->
                <form method="post" class="inline">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="reject">
                    <button type="submit"
                            onclick="return confirm('هل أنت متأكد من رفض هذا الاشتراك؟ لن يتمكن الطالب من استخدام الباقة.')"
                            class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-8 rounded-lg shadow-md transition-all duration-300 flex items-center">
                        <i class="fas fa-times ml-2"></i>
                        رفض الاشتراك
                    </button>
                </form>
            </div>

            <div class="mt-6 bg-gray-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900 mb-2">ملاحظات مهمة:</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• عند الموافقة، سيتم تفعيل الاشتراك فوراً وإنشاء فاتورة للطالب</li>
                    <li>• عند الرفض، سيتم إلغاء الاشتراك ولن يتمكن الطالب من استخدام الباقة</li>
                    <li>• يمكن للطالب تحميل وطباعة الفاتورة من لوحة التحكم الخاصة به</li>
                    <li>• سيتم إرسال إشعار للطالب بحالة الاشتراك</li>
                </ul>
            </div>
        </div>
        {% else %}
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full
                    {% if subscription.status == 'active' %}bg-green-100
                    {% elif subscription.status == 'cancelled' %}bg-red-100
                    {% else %}bg-gray-100{% endif %} mb-4">
                    {% if subscription.status == 'active' %}
                        <i class="fas fa-check text-green-600"></i>
                    {% elif subscription.status == 'cancelled' %}
                        <i class="fas fa-times text-red-600"></i>
                    {% else %}
                        <i class="fas fa-clock text-gray-600"></i>
                    {% endif %}
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                    {% if subscription.status == 'active' %}
                        تم تفعيل الاشتراك
                    {% elif subscription.status == 'cancelled' %}
                        تم رفض الاشتراك
                    {% else %}
                        {{ subscription.get_status_display }}
                    {% endif %}
                </h3>
                <p class="text-sm text-gray-500">
                    {% if subscription.status == 'active' %}
                        تم تفعيل هذا الاشتراك وإنشاء فاتورة للطالب
                    {% elif subscription.status == 'cancelled' %}
                        تم رفض هذا الاشتراك
                    {% else %}
                        لا يمكن تعديل حالة هذا الاشتراك
                    {% endif %}
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
