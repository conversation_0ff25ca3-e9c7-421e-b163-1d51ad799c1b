{% extends 'base.html' %}
{% load math_filters %}

{% block title %}لوحة تحكم المعلم - {{ ACADEMY_SLOGAN }}{% endblock %}

{% block extra_css %}
<style>
    /* تحسينات التجاوب للوحة تحكم المعلم */
    @media (max-width: 768px) {
        .stats-card {
            padding: 1rem !important;
        }

        .stats-icon {
            width: 2.5rem !important;
            height: 2.5rem !important;
            font-size: 1rem !important;
        }

        .enhanced-card-body {
            padding: 1rem !important;
        }

        .islamic-content-card {
            margin: 0 1rem 2rem 1rem !important;
        }

        .islamic-content-card .bg-gradient-to-r {
            padding: 1.5rem !important;
        }

        .islamic-content-card h3 {
            font-size: 1.25rem !important;
        }

        .islamic-content-card .w-14 {
            width: 2.5rem !important;
            height: 2.5rem !important;
        }

        .islamic-content-card .text-2xl {
            font-size: 1rem !important;
        }

        .islamic-content-card .grid-cols-1 {
            grid-template-columns: 1fr !important;
        }

        .islamic-content-card .lg\:grid-cols-2 {
            grid-template-columns: 1fr !important;
        }

        .enhanced-card-header h1 {
            font-size: 1.5rem !important;
        }

        .enhanced-card-header .text-3xl {
            font-size: 1.5rem !important;
        }

        .enhanced-card-header .ml-3 {
            margin-left: 0.5rem !important;
        }

        .enhanced-card-header .space-x-4 > * + * {
            margin-right: 0.5rem !important;
        }

        .enhanced-card-header .px-4 {
            padding-left: 0.75rem !important;
            padding-right: 0.75rem !important;
        }

        .enhanced-card-header .py-2 {
            padding-top: 0.5rem !important;
            padding-bottom: 0.5rem !important;
        }

        .grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-4 {
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .grid.grid-cols-1.lg\:grid-cols-2 {
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .grid.grid-cols-1.lg\:grid-cols-3 {
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .text-3xl {
            font-size: 1.5rem !important;
        }

        .text-4xl {
            font-size: 2rem !important;
        }

        .text-6xl {
            font-size: 3rem !important;
        }

        .p-6 {
            padding: 1rem !important;
        }

        .p-8 {
            padding: 1.5rem !important;
        }

        .mb-8 {
            margin-bottom: 1.5rem !important;
        }

        .space-y-6 > * + * {
            margin-top: 1rem !important;
        }

        .bg-white.rounded-lg.shadow-sm {
            margin-bottom: 1rem !important;
        }

        /* تحسين الأزرار للموبايل */
        .bg-islamic-primary,
        .bg-white.text-green-600,
        .bg-white.text-blue-600,
        .bg-white.text-red-600,
        .bg-green-600,
        .bg-blue-600 {
            padding: 0.75rem 1rem !important;
            font-size: 0.875rem !important;
            min-height: 44px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        /* تحسين النصوص للموبايل */
        .text-lg {
            font-size: 1rem !important;
        }

        .text-xl {
            font-size: 1.125rem !important;
        }

        .text-2xl {
            font-size: 1.25rem !important;
        }

        /* تحسين المحتوى الإسلامي للموبايل */
        .islamic-content-card .flex.items-center.justify-between {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 1rem !important;
        }

        .islamic-content-card .flex.items-center.space-x-2 {
            flex-direction: column !important;
            gap: 0.5rem !important;
            width: 100% !important;
        }

        .islamic-content-card button,
        .islamic-content-card .bg-white.bg-opacity-20 {
            width: 100% !important;
            justify-content: center !important;
        }

        /* تحسين قسم الحصص المباشرة للموبايل */
        .bg-gradient-to-r.from-red-500 {
            padding: 1.5rem !important;
        }

        .bg-gradient-to-r.from-red-500 .grid.gap-3 {
            gap: 1rem !important;
        }

        .bg-gradient-to-r.from-red-500 .flex.items-center.justify-between {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 1rem !important;
        }

        .bg-gradient-to-r.from-red-500 .flex.flex-col.gap-2 {
            width: 100% !important;
        }

        /* تحسين قسم التقييمات الموحدة للموبايل */
        .grid.grid-cols-2.gap-3 {
            grid-template-columns: 1fr !important;
            gap: 0.5rem !important;
        }
    }

    @media (max-width: 640px) {
        /* تحسينات الإجراءات السريعة للموبايل */
        .space-y-3 > * + * {
            margin-top: 0.5rem !important;
        }

        .space-y-3 a {
            padding: 0.75rem !important;
            font-size: 0.75rem !important;
        }

        .space-y-3 .fas {
            font-size: 0.875rem !important;
        }

        /* تحسين قسم التقييمات الموحدة للشاشات الصغيرة */
        .bg-gradient-to-r.from-yellow-50 {
            padding: 0.75rem !important;
        }

        .bg-gradient-to-r.from-yellow-50 span {
            font-size: 0.75rem !important;
        }

        .bg-gradient-to-r.from-yellow-50 .text-lg {
            font-size: 1rem !important;
        }

        /* تحسين عرض النجوم */
        .flex .text-sm {
            font-size: 0.75rem !important;
        }
    }

    @media (max-width: 1024px) {
        /* تحسينات للأجهزة اللوحية */
        .grid.grid-cols-1.lg\\:grid-cols-2 {
            grid-template-columns: 1fr !important;
        }

        .grid.grid-cols-1.lg\\:grid-cols-3 {
            grid-template-columns: repeat(2, 1fr) !important;
        }
    }

    @media (max-width: 480px) {
        .enhanced-card-header {
            padding: 1rem !important;
        }

        .enhanced-card-header .flex.items-center.justify-between {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 1rem !important;
        }

        .enhanced-card-header .flex.items-center.space-x-4 {
            flex-direction: column !important;
            gap: 0.5rem !important;
            width: 100% !important;
        }

        .enhanced-card-header .bg-white.bg-opacity-20 {
            width: 100% !important;
            text-align: center !important;
        }

        .stats-card .text-3xl {
            font-size: 1.25rem !important;
        }

        .islamic-content-card .grid.gap-6 {
            gap: 1rem !important;
        }

        .islamic-content-card .p-6 {
            padding: 1rem !important;
        }

        /* تحسين عناوين الحصص المباشرة */
        .bg-gradient-to-r.from-red-500 h3 {
            font-size: 1.125rem !important;
        }

        .bg-gradient-to-r.from-red-500 h4 {
            font-size: 1rem !important;
        }

        .bg-gradient-to-r.from-red-500 h5 {
            font-size: 0.875rem !important;
        }
    }
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <!-- Enhanced Header -->
    <div class="enhanced-card mb-8 animate-fade-in-up">
        <div class="enhanced-card-header">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-white flex items-center">
                        <i class="fas fa-chalkboard-teacher ml-2 md:ml-3 text-islamic-gold text-xl md:text-2xl"></i>
                        لوحة تحكم المعلم
                    </h1>
                    <p class="text-islamic-light-gold mt-2">مرحباً بك {{ user.get_full_name }}، إليك نظرة عامة على حصصك وطلابك</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                        <i class="fas fa-star ml-2 text-islamic-gold"></i>
                        <span class="text-white font-medium">{{ avg_rating }}/5</span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                        <i class="fas fa-graduation-cap ml-2 text-islamic-gold"></i>
                        <span class="text-white font-medium">معلم قرآن</span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                        <i class="fas fa-clock ml-2 text-islamic-gold"></i>
                        <span class="text-white font-medium" id="current-time-date">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8 px-4 md:px-0">
        <!-- Total Students -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.1s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                    <i class="fas fa-user-graduate text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">طلابي</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900"><span data-stat="total-students">{{ total_students }}</span></p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="badge-info-enhanced">طالب نشط</span>
            </div>
        </div>

        <!-- Today's Lessons -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                    <i class="fas fa-calendar-day text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">حصص اليوم</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900"><span data-stat="today-lessons">{{ today_lessons_count }}</span></p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="badge-success-enhanced"><span data-stat="upcoming-lessons">{{ upcoming_lessons_count }}</span> حصة قادمة</span>
            </div>
        </div>

        <!-- Completed Lessons -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-yellow-500 to-orange-500 text-white">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">الحصص المكتملة</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ completed_lessons_count }}</p>
                    <p class="text-xs text-gray-500">هذا الشهر</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="badge-warning-enhanced">مكتملة</span>
            </div>
        </div>

        <!-- Total Ratings -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-purple-500 to-violet-600 text-white">
                    <i class="fas fa-star text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">التقييمات</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ total_ratings }}</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <div class="flex items-center">
                    <span class="text-yellow-400 text-lg ml-2">★★★★★</span>
                    <span class="text-sm font-medium text-gray-600">{{ avg_rating }}/5</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Lessons Section -->
    {% if live_lessons or scheduled_live_lessons or subscription_scheduled_lessons %}
    <div class="mb-8">
        <div class="bg-gradient-to-r from-islamic-primary to-emerald-600 rounded-lg shadow-lg p-6 text-white">
            <h3 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-video text-white ml-2"></i>
                الحصص المباشرة والمجدولة
                <span class="bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full mr-2">
                    اليوم
                </span>
            </h3>

            <!-- Live Lessons -->
            {% if live_lessons %}
                <div class="mb-4">
                    <h4 class="text-lg font-semibold mb-3 flex items-center">
                        <div class="w-3 h-3 bg-white rounded-full animate-pulse ml-2"></div>
                        مباشرة الآن
                    </h4>
                    <div class="grid gap-3">
                        {% for lesson in live_lessons %}
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="font-bold">{{ lesson.title }}</h5>
                                    <p class="text-sm opacity-90">مع {{ lesson.student.get_full_name }}</p>
                                    <p class="text-xs opacity-75">بدأت منذ {{ lesson.started_at|timesince }}</p>
                                </div>
                                <a href="{% url 'teacher_live_lesson' lesson.id %}"
                                   class="bg-white text-red-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                                    <i class="fas fa-video ml-1"></i>
                                    دخول الحصة
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Scheduled Live Lessons -->
            {% if scheduled_live_lessons %}
                <div class="mb-4">
                    <h4 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-clock text-white ml-2"></i>
                        حصص مباشرة مجدولة
                    </h4>
                    <div class="grid gap-3">
                        {% for lesson in scheduled_live_lessons %}
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="font-bold">{{ lesson.title }}</h5>
                                    <p class="text-sm opacity-90">مع {{ lesson.student.get_full_name }}</p>
                                    <p class="text-xs opacity-75">{{ lesson.scheduled_date|date:"Y-m-d H:i" }}</p>
                                </div>
                                <a href="{% url 'teacher_live_lesson' lesson.id %}"
                                   class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                                    <i class="fas fa-calendar ml-1"></i>
                                    الاستعداد
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Subscription Scheduled Lessons (Main Card - Today only) -->
            {% if subscription_scheduled_lessons %}
                <div>
                    <h4 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-calendar-day text-white ml-2"></i>
                        حصص اليوم القادمة
                        <span class="bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full mr-2">
                            {{ subscription_scheduled_lessons.count }} حصة
                        </span>
                    </h4>
                    <div class="grid gap-3">
                        {% for lesson in subscription_scheduled_lessons %}
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm border-l-4 border-yellow-300">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="font-bold">حصة رقم {{ lesson.lesson_number }}</h5>
                                    <p class="text-sm opacity-90">مع {{ lesson.subscription.student.get_full_name }}</p>
                                    <p class="text-xs opacity-75">
                                        الساعة {{ lesson.scheduled_date|date:"H:i" }} - {{ lesson.duration_minutes }} دقيقة
                                    </p>
                                    <p class="text-xs opacity-75">باقة: {{ lesson.subscription.plan.name }}</p>
                                </div>
                                <div class="flex flex-col gap-2">
                                    {% load math_filters %}
                                    {% if lesson.scheduled_date|time_until_minutes <= 5 %}
                                        <span class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium animate-pulse">
                                            حان الوقت!
                                        </span>
                                        <button onclick="startScheduledLesson({{ lesson.id }})"
                                                class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors text-center">
                                            <i class="fas fa-play ml-1"></i>
                                            بدء الحصة
                                        </button>
                                    {% elif lesson.scheduled_date|time_until_minutes <= 30 %}
                                        <span class="bg-orange-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                                            خلال {{ lesson.scheduled_date|time_until_minutes|floatformat:0 }} دقيقة
                                        </span>
                                        <button onclick="prepareLesson({{ lesson.id }})"
                                                class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center">
                                            <i class="fas fa-clock ml-1"></i>
                                            استعداد
                                        </button>
                                    {% else %}
                                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                                            حصة اليوم
                                        </span>
                                        <button onclick="prepareLesson({{ lesson.id }})"
                                                class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center">
                                            <i class="fas fa-clock ml-1"></i>
                                            استعداد
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Islamic Content Card - Azkar & Quran -->
    <div class="mb-8 animate-fade-in-up islamic-content-card" style="animation-delay: 0.9s">
        <div class="bg-gradient-to-r from-islamic-primary to-emerald-600 rounded-xl p-8 text-white relative overflow-hidden shadow-lg">
            <!-- Islamic Pattern Background -->
            <div class="absolute inset-0 opacity-10">
                <div class="islamic-pattern w-full h-full"></div>
            </div>

            <!-- Content Container -->
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <div class="w-14 h-14 bg-islamic-gold rounded-full flex items-center justify-center ml-4 shadow-xl border-2 border-white border-opacity-30">
                            <i class="fas fa-quran text-islamic-primary text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-white">الأذكار والقرآن الكريم</h3>
                            <p class="text-islamic-light-gold text-sm">تذكير روحاني يومي</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="refreshIslamicContent()" class="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg px-4 py-2 transition-all duration-300 flex items-center">
                            <i class="fas fa-sync-alt text-islamic-gold ml-2" id="refresh-icon-teacher"></i>
                            <span class="text-white text-sm">تجديد</span>
                        </button>
                        <div class="bg-white bg-opacity-20 rounded-lg px-3 py-2">
                            <i class="fas fa-clock text-islamic-gold ml-1"></i>
                            <span class="text-white text-xs" id="islamic-timer-teacher">يتجدد كل 30 ثانية</span>
                        </div>
                    </div>
                </div>

                <!-- Content Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Quran Verse Section -->
                    <div class="bg-white bg-opacity-10 rounded-xl p-6 backdrop-blur-sm border border-white border-opacity-20">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-islamic-gold rounded-full flex items-center justify-center ml-3 shadow-lg">
                                <i class="fas fa-book-open text-islamic-primary text-lg font-bold"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-white">آية كريمة</h4>
                        </div>
                        <div id="quran-content-teacher" class="text-center">
                            <p class="text-xl arabic-text font-semibold text-islamic-light-gold mb-3 leading-relaxed" id="quran-verse-teacher">
                                "وَقُلْ رَبِّ زِدْنِي عِلْمًا"
                            </p>
                            <p class="text-sm text-white opacity-80" id="quran-reference-teacher">
                                سورة طه - آية 114
                            </p>
                        </div>
                    </div>

                    <!-- Azkar Section -->
                    <div class="bg-white bg-opacity-10 rounded-xl p-6 backdrop-blur-sm border border-white border-opacity-20">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-islamic-gold rounded-full flex items-center justify-center ml-3 shadow-lg">
                                <i class="fas fa-hands text-islamic-primary text-lg font-bold"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-white">ذكر شريف</h4>
                        </div>
                        <div id="azkar-content-teacher" class="text-center">
                            <p class="text-lg arabic-text font-semibold text-islamic-light-gold mb-3 leading-relaxed" id="azkar-text-teacher">
                                "سبحان الله وبحمده، سبحان الله العظيم"
                            </p>
                            <p class="text-sm text-white opacity-80" id="azkar-benefit-teacher">
                                كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Islamic Quote at Bottom -->
                <div class="mt-6 text-center border-t border-white border-opacity-20 pt-6">
                    <p class="text-islamic-light-gold text-sm italic" id="islamic-quote-teacher">
                        "اللهم بارك لنا فيما علمتنا وعلمنا ما ينفعنا وزدنا علماً"
                    </p>
                </div>
            </div>

            <!-- Decorative Elements -->
            <div class="absolute top-6 right-6 w-16 h-16 border-2 border-islamic-gold border-opacity-40 rounded-full animate-pulse"></div>
            <div class="absolute bottom-6 left-6 w-12 h-12 border-2 border-islamic-gold border-opacity-40 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/2 right-12 w-3 h-3 bg-islamic-gold rounded-full opacity-70 animate-bounce" style="animation-delay: 0.5s;"></div>
            <div class="absolute top-1/4 left-12 w-4 h-4 bg-islamic-gold rounded-full opacity-60 animate-bounce" style="animation-delay: 1.5s;"></div>
            <div class="absolute bottom-1/3 right-20 w-2 h-2 bg-islamic-gold rounded-full opacity-50"></div>
            <div class="absolute top-3/4 left-20 w-2 h-2 bg-islamic-gold rounded-full opacity-50"></div>
        </div>
    </div>

    <!-- Today's Schedule -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Today's Lessons -->
        <div class="enhanced-card animate-slide-in-right" style="animation-delay: 0.5s">
            <div class="enhanced-card-body">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-islamic-primary rounded-xl flex items-center justify-center ml-4 shadow-lg">
                            <i class="fas fa-calendar-day text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">حصص اليوم</h3>
                            <p class="text-gray-600 text-sm">جدولك اليومي</p>
                        </div>
                    </div>
                </div>
                <div class="text-center py-8">
                    <i class="fas fa-calendar-check text-islamic-primary text-4xl mb-4"></i>
                    <p class="text-gray-600 font-medium">{{ today_lessons_count }} حصة مجدولة لليوم</p>
                    <p class="text-gray-500 text-sm mt-2">يمكنك عرض التفاصيل من الجدول الأسبوعي</p>
                    <a href="{% url 'teacher_schedule' %}"
                       class="inline-block mt-4 bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-calendar ml-1"></i>
                        عرض الجدول
                    </a>
                </div>
            </div>
        </div>

        <!-- Upcoming Lessons -->
        <div class="enhanced-card animate-slide-in-right" style="animation-delay: 0.6s">
            <div class="enhanced-card-body">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-islamic-primary rounded-xl flex items-center justify-center ml-4 shadow-lg">
                            <i class="fas fa-clock text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">الحصص القادمة</h3>
                            <p class="text-gray-600 text-sm">الأسبوع القادم</p>
                        </div>
                    </div>
                </div>
                <div class="text-center py-8">
                    <i class="fas fa-clock text-islamic-primary text-4xl mb-4"></i>
                    <p class="text-gray-600 font-medium">{{ upcoming_lessons_count }} حصة قادمة</p>
                    <p class="text-gray-500 text-sm mt-2">خلال الأسبوع القادم</p>
                    <a href="{% url 'teacher_schedule' %}"
                       class="inline-block mt-4 text-islamic-primary hover:text-islamic-light font-medium">
                        عرض جميع الحصص
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-bolt text-islamic-primary ml-2"></i>
                إجراءات سريعة
            </h3>
            <div class="space-y-3">
                <a href="{% url 'teacher_schedule' %}"
                   class="block p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div class="flex items-center">
                        <i class="fas fa-calendar text-islamic-primary ml-3"></i>
                        <span class="text-sm font-medium text-gray-700">عرض الجدول الأسبوعي</span>
                    </div>
                </a>
                <a href="{% url 'teacher_students' %}"
                   class="block p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div class="flex items-center">
                        <i class="fas fa-users text-islamic-primary ml-3"></i>
                        <span class="text-sm font-medium text-gray-700">إدارة الطلاب</span>
                    </div>
                </a>
                <a href="{% url 'teacher_ratings' %}"
                   class="block p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div class="flex items-center">
                        <i class="fas fa-star text-islamic-primary ml-3"></i>
                        <span class="text-sm font-medium text-gray-700">التقييمات</span>
                    </div>
                </a>

            </div>
        </div>

        <!-- My Students -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-user-graduate text-islamic-primary ml-2"></i>
                طلابي
            </h3>
            <div class="text-center py-8">
                <i class="fas fa-users text-islamic-primary text-4xl mb-4"></i>
                <p class="text-gray-600 font-medium">{{ total_students }} طالب نشط</p>
                <p class="text-gray-500 text-sm mt-2">يمكنك إدارة طلابك من هنا</p>
                <a href="{% url 'teacher_students' %}"
                   class="inline-block mt-4 text-islamic-primary hover:text-islamic-light font-medium">
                    عرض جميع الطلاب
                </a>
            </div>
        </div>

        <!-- Unified Rating Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-star text-islamic-primary ml-2"></i>
                التقييمات الموحدة
                <span class="bg-islamic-primary text-white text-xs px-2 py-1 rounded-full mr-2">جديد</span>
            </h3>

            {% if unified_stats %}
            <div class="space-y-4">
                <!-- متوسط عام -->
                <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-3 border border-yellow-200">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-yellow-800">المتوسط العام</span>
                        <div class="flex items-center">
                            <span class="text-lg font-bold text-yellow-600 ml-2">{{ unified_stats.overall_average|floatformat:1 }}/5</span>
                            <div class="flex">
                                {% with stars=unified_stats.overall_average|floatformat:0|default:0 %}
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= stars %}
                                            <span class="text-yellow-500 text-sm">★</span>
                                        {% else %}
                                            <span class="text-gray-300 text-sm">★</span>
                                        {% endif %}
                                    {% endfor %}
                                {% endwith %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التفاصيل -->
                <div class="grid grid-cols-2 gap-3">
                    <div class="text-center">
                        <div class="text-sm text-gray-600 mb-1">جودة الحصة</div>
                        <div class="text-lg font-bold text-blue-600">{{ unified_stats.avg_quality|floatformat:1 }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-sm text-gray-600 mb-1">تفاعل المعلم</div>
                        <div class="text-lg font-bold text-green-600">{{ unified_stats.avg_interaction|floatformat:1 }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-sm text-gray-600 mb-1">الجودة التقنية</div>
                        <div class="text-lg font-bold text-purple-600">{{ unified_stats.avg_technical|floatformat:1 }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-sm text-gray-600 mb-1">إجمالي التقييمات</div>
                        <div class="text-lg font-bold text-islamic-primary">{{ unified_stats.total_ratings }}</div>
                    </div>
                </div>

                <!-- رابط التفاصيل -->
                <div class="pt-3 border-t border-gray-200">
                    <a href="{% url 'teacher_ratings' %}"
                       class="block text-center text-islamic-primary hover:text-islamic-light font-medium text-sm">
                        <i class="fas fa-chart-line ml-1"></i>
                        عرض التفاصيل الكاملة
                    </a>
                </div>
            </div>
            {% else %}
            <div class="text-center py-6">
                <i class="fas fa-star text-gray-300 text-3xl mb-3"></i>
                <p class="text-gray-500 text-sm">لا توجد تقييمات بعد</p>
                <p class="text-gray-400 text-xs mt-1">ستظهر تقييمات الطلاب هنا</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
<script>
// Islamic Content Data
const quranVerses = [
    {
        verse: "وَقُلْ رَبِّ زِدْنِي عِلْمًا",
        reference: "سورة طه - آية 114"
    },
    {
        verse: "وَمَا أُوتِيتُم مِّنَ الْعِلْمِ إِلَّا قَلِيلًا",
        reference: "سورة الإسراء - آية 85"
    },
    {
        verse: "يَرْفَعِ اللَّهُ الَّذِينَ آمَنُوا مِنكُمْ وَالَّذِينَ أُوتُوا الْعِلْمَ دَرَجَاتٍ",
        reference: "سورة المجادلة - آية 11"
    },
    {
        verse: "وَعَلَّمَكَ مَا لَمْ تَكُن تَعْلَمُ ۚ وَكَانَ فَضْلُ اللَّهِ عَلَيْكَ عَظِيمًا",
        reference: "سورة النساء - آية 113"
    },
    {
        verse: "اقْرَأْ بِاسْمِ رَبِّكَ الَّذِي خَلَقَ",
        reference: "سورة العلق - آية 1"
    },
    {
        verse: "وَقُل رَّبِّ أَدْخِلْنِي مُدْخَلَ صِدْقٍ وَأَخْرِجْنِي مُخْرَجَ صِدْقٍ",
        reference: "سورة الإسراء - آية 80"
    },
    {
        verse: "رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً",
        reference: "سورة البقرة - آية 201"
    },
    {
        verse: "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
        reference: "سورة الطلاق - آية 2"
    }
];

const azkarList = [
    {
        text: "سبحان الله وبحمده، سبحان الله العظيم",
        benefit: "كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن"
    },
    {
        text: "لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير",
        benefit: "من قالها في يوم مائة مرة كانت له عدل عشر رقاب"
    },
    {
        text: "اللهم أعني على ذكرك وشكرك وحسن عبادتك",
        benefit: "دعاء جامع للتوفيق في العبادة والذكر والشكر"
    },
    {
        text: "استغفر الله العظيم الذي لا إله إلا هو الحي القيوم وأتوب إليه",
        benefit: "الاستغفار يمحو الذنوب ويجلب الرزق والفرج"
    },
    {
        text: "اللهم صل وسلم على نبينا محمد",
        benefit: "من صلى على النبي صلاة واحدة صلى الله عليه بها عشراً"
    },
    {
        text: "حسبنا الله ونعم الوكيل",
        benefit: "كلمة التوكل والاعتماد على الله في جميع الأمور"
    },
    {
        text: "لا حول ولا قوة إلا بالله",
        benefit: "كنز من كنوز الجنة، تعين على تحمل المشاق"
    },
    {
        text: "اللهم بارك لنا فيما رزقتنا وقنا عذاب النار",
        benefit: "دعاء شامل للبركة في الرزق والوقاية من النار"
    }
];

const islamicQuotes = [
    "اللهم بارك لنا فيما علمتنا وعلمنا ما ينفعنا وزدنا علماً",
    "اللهم انفعني بما علمتني وعلمني ما ينفعني واكرمني بالعلم النافع",
    "اللهم أعني على ذكرك وشكرك وحسن عبادتك",
    "ربنا آتنا في الدنيا حسنة وفي الآخرة حسنة وقنا عذاب النار",
    "اللهم اهدني فيمن هديت وعافني فيمن عافيت",
    "اللهم أصلح لي ديني الذي هو عصمة أمري",
    "اللهم بارك لنا في أوقاتنا وأعمالنا وأرزاقنا"
];

let islamicContentInterval;
let timerInterval;
let timeLeft = 30;

// Function to get random content
function getRandomQuranVerse() {
    return quranVerses[Math.floor(Math.random() * quranVerses.length)];
}

function getRandomAzkar() {
    return azkarList[Math.floor(Math.random() * azkarList.length)];
}

function getRandomIslamicQuote() {
    return islamicQuotes[Math.floor(Math.random() * islamicQuotes.length)];
}

// Function to update Islamic content
function updateIslamicContent() {
    const quranVerse = getRandomQuranVerse();
    const azkar = getRandomAzkar();
    const quote = getRandomIslamicQuote();

    // Update Quran verse with animation
    const quranVerseElement = document.getElementById('quran-verse-teacher');
    const quranReferenceElement = document.getElementById('quran-reference-teacher');

    if (quranVerseElement && quranReferenceElement) {
        quranVerseElement.style.opacity = '0';
        quranReferenceElement.style.opacity = '0';

        setTimeout(() => {
            quranVerseElement.textContent = `"${quranVerse.verse}"`;
            quranReferenceElement.textContent = quranVerse.reference;
            quranVerseElement.style.opacity = '1';
            quranReferenceElement.style.opacity = '1';
        }, 300);
    }

    // Update Azkar with animation
    const azkarTextElement = document.getElementById('azkar-text-teacher');
    const azkarBenefitElement = document.getElementById('azkar-benefit-teacher');

    if (azkarTextElement && azkarBenefitElement) {
        azkarTextElement.style.opacity = '0';
        azkarBenefitElement.style.opacity = '0';

        setTimeout(() => {
            azkarTextElement.textContent = `"${azkar.text}"`;
            azkarBenefitElement.textContent = azkar.benefit;
            azkarTextElement.style.opacity = '1';
            azkarBenefitElement.style.opacity = '1';
        }, 300);
    }

    // Update Islamic quote with animation
    const quoteElement = document.getElementById('islamic-quote-teacher');
    if (quoteElement) {
        quoteElement.style.opacity = '0';

        setTimeout(() => {
            quoteElement.textContent = `"${quote}"`;
            quoteElement.style.opacity = '1';
        }, 300);
    }

    // Reset timer
    timeLeft = 30;
}

// Function to refresh Islamic content manually
function refreshIslamicContent() {
    const refreshIcon = document.getElementById('refresh-icon-teacher');
    if (refreshIcon) {
        refreshIcon.classList.add('fa-spin');

        updateIslamicContent();

        setTimeout(() => {
            refreshIcon.classList.remove('fa-spin');
        }, 1000);
    }
}

// Function to update timer display
function updateTimer() {
    const timerElement = document.getElementById('islamic-timer-teacher');
    if (timerElement) {
        if (timeLeft > 0) {
            timerElement.textContent = `يتجدد خلال ${timeLeft} ثانية`;
            timeLeft--;
        } else {
            updateIslamicContent();
        }
    }
}

// Initialize Islamic content functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth transitions to content elements
    const contentElements = [
        'quran-verse-teacher', 'quran-reference-teacher',
        'azkar-text-teacher', 'azkar-benefit-teacher',
        'islamic-quote-teacher'
    ];

    contentElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.style.transition = 'opacity 0.3s ease-in-out';
        }
    });

    // Start the automatic content update
    islamicContentInterval = setInterval(updateIslamicContent, 30000); // Every 30 seconds

    // Start the timer update
    timerInterval = setInterval(updateTimer, 1000); // Every second

    // Initial content load
    updateIslamicContent();
});

// Clean up intervals when page is unloaded
window.addEventListener('beforeunload', function() {
    if (islamicContentInterval) {
        clearInterval(islamicContentInterval);
    }
    if (timerInterval) {
        clearInterval(timerInterval);
    }
});

// Function to handle lesson preparation
function prepareLesson(lessonId) {
    // Show preparation message
    const button = event.target;
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري التحضير...';
    button.disabled = true;

    // Simulate preparation process
    setTimeout(() => {
        alert('تم تحضير الحصة بنجاح! ستتمكن من دخول الحصة عند حلول موعدها.');
        button.innerHTML = originalText;
        button.disabled = false;
    }, 2000);
}

// تحديث لوحة التحكم كل 15 ثانية للحصول على أحدث البيانات
document.addEventListener('DOMContentLoaded', function() {
    setInterval(function() {
        refreshTeacherDashboardData();
    }, 15000);

    // إضافة مستمع للتحديث الفوري عند التركيز على النافذة
    window.addEventListener('focus', function() {
        refreshTeacherDashboardData();
        console.log('تم تحديث بيانات المعلم عند التركيز على النافذة');
    });

    // تحديث فوري عند تغيير الصفحة
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            refreshTeacherDashboardData();
            console.log('تم تحديث بيانات المعلم عند عودة الرؤية للصفحة');
        }
    });
});

// دالة تحديث بيانات لوحة تحكم المعلم
function refreshTeacherDashboardData() {
    fetch('/dashboard/api/teacher/dashboard-stats/')
        .then(response => response.json())
        .then(data => {
            // تحديث عدد الحصص اليوم
            const todayLessonsElement = document.querySelector('[data-stat="today-lessons"]');
            if (todayLessonsElement && data.today_lessons_count !== undefined) {
                todayLessonsElement.textContent = data.today_lessons_count;
                // إضافة تأثير بصري للتحديث
                todayLessonsElement.style.color = '#10B981';
                setTimeout(() => {
                    todayLessonsElement.style.color = '';
                }, 1000);
            }

            // تحديث عدد الحصص القادمة
            const upcomingLessonsElement = document.querySelector('[data-stat="upcoming-lessons"]');
            if (upcomingLessonsElement && data.upcoming_lessons_count !== undefined) {
                upcomingLessonsElement.textContent = data.upcoming_lessons_count;
                // إضافة تأثير بصري للتحديث
                upcomingLessonsElement.style.color = '#10B981';
                setTimeout(() => {
                    upcomingLessonsElement.style.color = '';
                }, 1000);
            }

            // تحديث عدد الطلاب
            const totalStudentsElement = document.querySelector('[data-stat="total-students"]');
            if (totalStudentsElement && data.total_students !== undefined) {
                totalStudentsElement.textContent = data.total_students;
                // إضافة تأثير بصري للتحديث
                totalStudentsElement.style.color = '#10B981';
                setTimeout(() => {
                    totalStudentsElement.style.color = '';
                }, 1000);
            }

            // تحديث عدد الحصص المكتملة
            const completedLessonsElement = document.querySelector('[data-stat="completed-lessons"]');
            if (completedLessonsElement && data.completed_lessons_count !== undefined) {
                completedLessonsElement.textContent = data.completed_lessons_count;
            }

            // تحديث عدد التقييمات
            const totalRatingsElement = document.querySelector('[data-stat="total-ratings"]');
            if (totalRatingsElement && data.total_ratings !== undefined) {
                totalRatingsElement.textContent = data.total_ratings;
            }

            // إظهار رسالة تحديث
            showTeacherUpdateNotification('تم تحديث البيانات');
        })
        .catch(error => {
            console.log('تحديث بيانات لوحة تحكم المعلم:', error);
        });
}

// دالة إظهار إشعار التحديث للمعلم
function showTeacherUpdateNotification(message) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
    notification.textContent = message;
    notification.style.transform = 'translateX(100%)';

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>

<style>
/* Islamic Content Card Custom Styles */
.islamic-content-card {
    background: transparent !important;
}

.islamic-content-card .bg-gradient-to-r {
    background: linear-gradient(to right, #2D5016, #10B981) !important;
}

/* Ensure no white background overrides */
.islamic-content-card * {
    background-color: transparent;
}

.islamic-content-card .bg-white {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Enhanced Islamic Gold Icons */
.islamic-content-card .bg-islamic-gold {
    background: linear-gradient(135deg, #D4AF37 0%, #F4E4BC 50%, #D4AF37 100%) !important;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.islamic-content-card .bg-islamic-gold:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Decorative Elements Animation */
.islamic-content-card .animate-pulse {
    animation: islamicPulse 2s infinite;
}

.islamic-content-card .animate-bounce {
    animation: islamicBounce 2s infinite;
}

@keyframes islamicPulse {
    0%, 100% {
        opacity: 0.4;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes islamicBounce {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-5px);
        opacity: 0.9;
    }
}
</style>

<script>
// Function to start scheduled lesson
function startScheduledLesson(lessonId) {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري البدء...';
    button.disabled = true;

    fetch(`/api/teacher/start-scheduled-lesson/${lessonId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            button.innerHTML = '<i class="fas fa-check ml-1"></i>تم البدء!';
            button.className = 'bg-green-600 text-white px-4 py-2 rounded-lg font-medium';

            // Redirect to live lesson room after short delay
            setTimeout(() => {
                window.location.href = data.live_lesson_url;
            }, 1000);
        } else {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;

            // Show error message
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);

        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;

        alert('حدث خطأ أثناء بدء الحصة');
    });
}

// Function to prepare for lesson
function prepareLesson(lessonId) {
    // Show preparation message
    alert('أنت مستعد! سيكون زر بدء الحصة متاح قبل الحصة بـ 5 دقائق فقط.');

    // Optional: You can add more preparation logic here
    // For example, opening lesson materials, checking equipment, etc.
}

// Function to start subscription lesson (for sidebar)
function startSubscriptionLesson(lessonId, studentName, lessonNumber) {
    if (confirm(`هل تريد بدء حصة رقم ${lessonNumber} مع ${studentName}؟`)) {
        // Find the button that was clicked
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري البدء...';
        button.disabled = true;

        fetch(`/api/teacher/start-scheduled-lesson/${lessonId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                button.innerHTML = '<i class="fas fa-check ml-1"></i>تم البدء!';
                button.className = 'bg-green-600 text-white px-3 py-1 rounded-lg text-xs font-medium';

                // Redirect to live lesson room after short delay
                setTimeout(() => {
                    window.location.href = data.live_lesson_url;
                }, 1000);
            } else {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;

                // Show error message
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;

            alert('حدث خطأ أثناء بدء الحصة');
        });
    }
}
</script>

{% endblock %}