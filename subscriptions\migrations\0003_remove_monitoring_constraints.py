# Generated by Django 4.2.7 on 2025-06-08 01:51

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0002_initial'),
    ]

    operations = [
        # حذف أي foreign key constraints متبقية لجداول المراقبة
        migrations.RunSQL(
            """
            PRAGMA foreign_keys = OFF;

            -- حذف أي جداول مراقبة متبقية
            DROP TABLE IF EXISTS lessons_universallessonmonitoring;
            DROP TABLE IF EXISTS lessons_lessonmonitoring;
            DROP TABLE IF EXISTS lessons_monitoringsession;
            DROP TABLE IF EXISTS lessons_attendancemonitoring;
            DROP TABLE IF EXISTS lessons_qualitymonitoring;

            -- إعادة إنشاء جدول ScheduledLesson بدون أي مراجع للمراقبة
            CREATE TABLE IF NOT EXISTS subscriptions_scheduledlesson_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lesson_number INTEGER NOT NULL,
                scheduled_date DATETIME NOT NULL,
                duration_minutes INTEGER NOT NULL DEFAULT 60,
                status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
                notes TEXT,
                live_lesson_id INTEGER,
                completed_at DATETIME,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                subscription_id INTEGER NOT NULL REFERENCES subscriptions_studentsubscription(id) ON DELETE CASCADE,
                teacher_id INTEGER REFERENCES users_user(id) ON DELETE SET NULL
            );

            -- نسخ البيانات إذا كان الجدول موجود
            INSERT OR IGNORE INTO subscriptions_scheduledlesson_new
            SELECT id, lesson_number, scheduled_date, duration_minutes, status, notes,
                   live_lesson_id, completed_at, created_at, updated_at, subscription_id, teacher_id
            FROM subscriptions_scheduledlesson;

            -- حذف الجدول القديم وإعادة تسمية الجديد
            DROP TABLE IF EXISTS subscriptions_scheduledlesson;
            ALTER TABLE subscriptions_scheduledlesson_new RENAME TO subscriptions_scheduledlesson;

            PRAGMA foreign_keys = ON;
            """,
            reverse_sql="-- لا يمكن التراجع عن هذا التغيير"
        ),
    ]
