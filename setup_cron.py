"""
إعد<PERSON> Cron Jobs لنظام البريد الإلكتروني
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania_lms.settings')

import django
django.setup()

from django_crontab.crontab import CronTab


def setup_email_cron_jobs():
    """إعداد مهام Cron للبريد الإلكتروني"""
    
    print("🔧 إعداد مهام Cron للبريد الإلكتروني...")
    
    # الحصول على مسار Python ومسار المشروع
    python_path = sys.executable
    project_path = str(BASE_DIR)
    manage_py = os.path.join(project_path, 'manage.py')
    
    # إنشاء CronTab
    cron = CronTab(user=True)
    
    # حذف المهام الموجودة (لتجنب التكرار)
    cron.remove_all(comment='qurania_email_system')
    
    # 1. معالجة شاملة كل دقيقة
    job1 = cron.new(
        command=f'cd {project_path} && {python_path} {manage_py} process_scheduled_emails',
        comment='qurania_email_system_main'
    )
    job1.setall('* * * * *')  # كل دقيقة
    
    # 2. معالجة طابور البريد كل 5 دقائق (احتياطي)
    job2 = cron.new(
        command=f'cd {project_path} && {python_path} {manage_py} process_email_queue',
        comment='qurania_email_system_queue'
    )
    job2.setall('*/5 * * * *')  # كل 5 دقائق
    
    # 3. تنظيف السجلات القديمة (أسبوعياً)
    job3 = cron.new(
        command=f'cd {project_path} && {python_path} {manage_py} cleanup_email_logs --days=30',
        comment='qurania_email_system_cleanup'
    )
    job3.setall('0 2 * * 0')  # كل أحد في الساعة 2 صباحاً
    
    # حفظ المهام
    cron.write()
    
    print("✅ تم إعداد مهام Cron بنجاح!")
    print("\n📋 المهام المضافة:")
    print("  1. معالجة شاملة: كل دقيقة")
    print("  2. معالجة الطابور: كل 5 دقائق")
    print("  3. تنظيف السجلات: أسبوعياً")
    
    # عرض المهام الحالية
    print("\n🔍 المهام الحالية:")
    for job in cron:
        if 'qurania_email_system' in job.comment:
            print(f"  - {job.schedule} | {job.command}")


def remove_email_cron_jobs():
    """حذف مهام Cron للبريد الإلكتروني"""
    
    print("🗑️ حذف مهام Cron للبريد الإلكتروني...")
    
    cron = CronTab(user=True)
    
    # حذف جميع المهام المتعلقة بالنظام
    removed_count = 0
    for job in cron:
        if 'qurania_email_system' in job.comment:
            cron.remove(job)
            removed_count += 1
    
    cron.write()
    
    print(f"✅ تم حذف {removed_count} مهمة")


def show_cron_status():
    """عرض حالة مهام Cron"""
    
    print("📊 حالة مهام Cron للبريد الإلكتروني:")
    
    cron = CronTab(user=True)
    
    email_jobs = []
    for job in cron:
        if 'qurania_email_system' in job.comment:
            email_jobs.append(job)
    
    if email_jobs:
        print(f"✅ تم العثور على {len(email_jobs)} مهمة نشطة:")
        for i, job in enumerate(email_jobs, 1):
            status = "🟢 نشط" if job.is_enabled() else "🔴 معطل"
            print(f"  {i}. {status} | {job.schedule} | {job.comment}")
            print(f"     الأمر: {job.command}")
    else:
        print("❌ لا توجد مهام نشطة")
    
    print(f"\n📝 إجمالي مهام Cron: {len(list(cron))}")


def test_commands():
    """اختبار أوامر البريد الإلكتروني"""
    
    print("🧪 اختبار أوامر البريد الإلكتروني...")
    
    commands_to_test = [
        'process_email_queue --help',
        'send_lesson_reminders --help',
        'send_subscription_alerts --help', 
        'send_admin_reports --help',
        'process_scheduled_emails --help'
    ]
    
    python_path = sys.executable
    project_path = str(BASE_DIR)
    manage_py = os.path.join(project_path, 'manage.py')
    
    for cmd in commands_to_test:
        print(f"\n🔍 اختبار: {cmd}")
        try:
            full_cmd = f'cd {project_path} && {python_path} {manage_py} {cmd}'
            result = os.system(full_cmd + ' > /dev/null 2>&1')
            if result == 0:
                print("  ✅ الأمر يعمل بشكل صحيح")
            else:
                print("  ❌ خطأ في الأمر")
        except Exception as e:
            print(f"  ❌ خطأ: {str(e)}")


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='إدارة مهام Cron للبريد الإلكتروني')
    parser.add_argument(
        'action',
        choices=['setup', 'remove', 'status', 'test'],
        help='الإجراء المطلوب'
    )
    
    args = parser.parse_args()
    
    if args.action == 'setup':
        setup_email_cron_jobs()
    elif args.action == 'remove':
        remove_email_cron_jobs()
    elif args.action == 'status':
        show_cron_status()
    elif args.action == 'test':
        test_commands()
    
    print("\n🎉 تم إنجاز العملية بنجاح!")
