"""
إعدادات Celery لنظام إدارة التعلم
"""

import os
from celery import Celery
from celery.schedules import crontab

# تعيين إعدادات Django الافتراضية لـ Celery
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania_lms.settings')

app = Celery('qurania_lms')

# استخدام إعدادات Django لـ Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# البحث التلقائي عن المهام في جميع التطبيقات المثبتة
app.autodiscover_tasks()

# إعدادات المهام المجدولة - نظام WhatsApp
app.conf.beat_schedule = {
    # تذكيرات WhatsApp للحصص قبل يوم واحد - كل يوم الساعة 9 صباحاً
    'send-whatsapp-lesson-reminders-1day': {
        'task': 'users.tasks.send_lesson_reminder_1day',
        'schedule': crontab(hour=9, minute=0),
    },

    # تذكيرات WhatsApp للحصص قبل 30 دقيقة - كل 30 دقيقة
    'send-whatsapp-lesson-reminders-30min': {
        'task': 'users.tasks.send_lesson_reminder_30min',
        'schedule': crontab(minute='*/30'),
    },

    # معالجة رسائل WhatsApp المجدولة - كل 5 دقائق
    'process-scheduled-whatsapp-messages': {
        'task': 'users.tasks.send_scheduled_messages',
        'schedule': crontab(minute='*/5'),
    },

    # إعادة محاولة رسائل WhatsApp الفاشلة - كل 15 دقيقة
    'retry-failed-whatsapp-messages': {
        'task': 'users.whatsapp_tasks.retry_failed_whatsapp_messages',
        'schedule': crontab(minute='*/15'),
    },

    # تنظيف رسائل WhatsApp القديمة - كل يوم الساعة 2 صباحاً
    'cleanup-old-whatsapp-messages': {
        'task': 'users.whatsapp_tasks.cleanup_old_whatsapp_messages',
        'schedule': crontab(hour=2, minute=0),
    },

    # فحص انتهاء الاشتراكات - كل يوم الساعة 8 صباحاً
    'check-subscription-expiry': {
        'task': 'subscriptions.tasks.check_subscription_expiry',
        'schedule': crontab(hour=8, minute=0),
    },
}

# إعدادات المنطقة الزمنية
app.conf.timezone = 'UTC'

# إعدادات إضافية
app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    result_expires=3600,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 دقيقة
    task_soft_time_limit=25 * 60,  # 25 دقيقة
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

@app.task(bind=True)
def debug_task(self):
    """مهمة تجريبية للتأكد من عمل Celery"""
    print(f'Request: {self.request!r}')
