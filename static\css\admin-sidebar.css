/* Admin Sidebar Custom Colors - ألوان القائمة الجانبية للمدير */

/* ========================================
   🎨 ألوان خاصة بالمدير - أولوية عالية
   تطبق على جميع صفحات المدير تلقائياً
======================================== */

/* تطبيق تلقائي على جميع صفحات المدير */
body[data-user-type="admin"] .new-sidebar,
body.admin-page .new-sidebar,
.admin-dashboard .new-sidebar,
body.admin-dashboard .new-sidebar {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    --new-sidebar-bg: #2D5016 !important;
    --new-sidebar-bg-gradient: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body[data-user-type="admin"] .new-sidebar-toggle,
body.admin-page .new-sidebar-toggle,
.admin-dashboard .new-sidebar-toggle,
body.admin-dashboard .new-sidebar-toggle {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body[data-user-type="admin"] .enhanced-card-header,
body.admin-page .enhanced-card-header,
.admin-dashboard .enhanced-card-header,
body.admin-dashboard .enhanced-card-header {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body[data-user-type="admin"] .enhanced-card,
body.admin-page .enhanced-card,
.admin-dashboard .enhanced-card,
body.admin-dashboard .enhanced-card {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* تخصيص ألوان القائمة الجانبية للمدير */
.admin-dashboard .new-sidebar,
body.admin-dashboard .new-sidebar {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    --new-sidebar-bg: #2D5016 !important;
    --new-sidebar-bg-gradient: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

.admin-dashboard .new-sidebar-toggle,
body.admin-dashboard .new-sidebar-toggle {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* تخصيص ألوان header للمدير */
.admin-dashboard .enhanced-card-header,
body.admin-dashboard .enhanced-card-header {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* تخصيص ألوان البطاقة الرئيسية */
.admin-dashboard .enhanced-card,
body.admin-dashboard .enhanced-card {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* تخصيص ألوان الأيقونات في القائمة الجانبية */
body[data-user-type="admin"] .nav-icon,
body.admin-page .nav-icon,
body.admin-dashboard .nav-icon {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #d4af37 !important;
}

body[data-user-type="admin"] .nav-link:hover .nav-icon,
body.admin-page .nav-link:hover .nav-icon,
body.admin-dashboard .nav-link:hover .nav-icon {
    background: rgba(255, 255, 255, 0.2) !important;
}

/* تخصيص ألوان النصوص */
body[data-user-type="admin"] .nav-text,
body.admin-page .nav-text,
body.admin-dashboard .nav-text {
    color: #ffffff !important;
}

body[data-user-type="admin"] .nav-link,
body.admin-page .nav-link,
body.admin-dashboard .nav-link {
    color: #ffffff !important;
}

body[data-user-type="admin"] .nav-link:hover,
body.admin-page .nav-link:hover,
body.admin-dashboard .nav-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    border-right-color: #d4af37 !important;
}

body[data-user-type="admin"] .nav-link-active,
body.admin-page .nav-link-active,
body.admin-dashboard .nav-link-active {
    background: rgba(255, 255, 255, 0.15) !important;
    border-right-color: #d4af37 !important;
}

/* تخصيص ألوان العناوين */
body.admin-dashboard .nav-section-title {
    color: #d4af37 !important;
}

body.admin-dashboard .nav-section-header::before {
    background: linear-gradient(90deg, transparent, #d4af37, transparent) !important;
}

/* تخصيص ألوان معلومات المستخدم */
body.admin-dashboard .user-name {
    color: #ffffff !important;
}

body.admin-dashboard .academy-name {
    color: #ffffff !important;
}

body.admin-dashboard .academy-slogan {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* تخصيص ألوان الشارات */
body.admin-dashboard .role-admin {
    background: linear-gradient(135deg, #d4af37, #f4e4a6) !important;
    color: #2D5016 !important;
}

/* تخصيص ألوان الحدود */
body.admin-dashboard .new-sidebar-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* تخصيص ألوان شريط التمرير */
body.admin-dashboard .new-sidebar-nav::-webkit-scrollbar-thumb {
    background: #d4af37 !important;
}

body.admin-dashboard .new-sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: #e6c547 !important;
}

/* تخصيص ألوان footer القائمة الجانبية */
body.admin-dashboard .new-sidebar-footer {
    background: rgba(0, 0, 0, 0.25) !important;
    border-top: 2px solid #d4af37 !important;
}

body.admin-dashboard .footer-nav-link {
    color: #ffffff !important;
}

body.admin-dashboard .footer-nav-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

body.admin-dashboard .footer-nav-icon {
    color: #d4af37 !important;
}

body.admin-dashboard .footer-nav-text {
    color: #ffffff !important;
}

/* تخصيص ألوان الأزرار */
body.admin-dashboard .academy-logo-placeholder {
    background: #d4af37 !important;
    color: #2D5016 !important;
    border: 2px solid #d4af37 !important;
}

body.admin-dashboard .user-avatar-placeholder {
    background: #d4af37 !important;
    color: #2D5016 !important;
    border: 2px solid #d4af37 !important;
}

/* تخصيص ألوان الخلفية الشفافة */
body.admin-dashboard .user-info-section {
    background: rgba(255, 255, 255, 0.05) !important;
}

/* تخصيص ألوان زر الإغلاق */
body.admin-dashboard .new-sidebar-close {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

body.admin-dashboard .new-sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

/* تخصيص ألوان التأثيرات */
body.admin-dashboard .nav-link::before {
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent) !important;
}

body.admin-dashboard .nav-link-active {
    box-shadow: inset 0 0 20px rgba(212, 175, 55, 0.1) !important;
}

/* تخصيص ألوان التركيز */
body.admin-dashboard .nav-link:focus {
    outline: 2px solid #d4af37 !important;
    background: rgba(255, 255, 255, 0.15) !important;
}

/* تخصيص ألوان الحصص المباشرة */
body.admin-dashboard .nav-section-live .nav-section-title {
    color: #ef4444 !important;
}

/* تخصيص ألوان الشارات */
body.admin-dashboard .nav-badge {
    background: #ef4444 !important;
    color: #ffffff !important;
}

/* تأكيد الألوان للموبايل */
@media (max-width: 768px) {
    body[data-user-type="admin"] .new-sidebar,
    body.admin-page .new-sidebar,
    body.admin-dashboard .new-sidebar {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    body[data-user-type="admin"] .new-sidebar-toggle,
    body.admin-page .new-sidebar-toggle,
    body.admin-dashboard .new-sidebar-toggle {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    body[data-user-type="admin"] .enhanced-card-header,
    body.admin-page .enhanced-card-header,
    body.admin-dashboard .enhanced-card-header {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }
}

/* تأكيد الألوان للتابلت */
@media (min-width: 768px) and (max-width: 1023px) {
    body[data-user-type="admin"] .new-sidebar,
    body.admin-page .new-sidebar,
    body.admin-dashboard .new-sidebar {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    body[data-user-type="admin"] .enhanced-card-header,
    body.admin-page .enhanced-card-header,
    body.admin-dashboard .enhanced-card-header {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }
}

/* تأكيد الألوان للكمبيوتر */
@media (min-width: 1024px) {
    body[data-user-type="admin"] .new-sidebar,
    body.admin-page .new-sidebar,
    body.admin-dashboard .new-sidebar {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    body[data-user-type="admin"] .enhanced-card-header,
    body.admin-page .enhanced-card-header,
    body.admin-dashboard .enhanced-card-header {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }
}

/* إصلاح التدرجات الزرقاء في لوحة المدير */
body[data-user-type="admin"] .bg-gradient-to-br.from-blue-500.to-indigo-600,
body.admin-page .bg-gradient-to-br.from-blue-500.to-indigo-600,
body.admin-dashboard .bg-gradient-to-br.from-blue-500.to-indigo-600 {
    background: linear-gradient(135deg, #22c55e 0%, #059669 100%) !important;
}

body[data-user-type="admin"] .bg-blue-500,
body.admin-page .bg-blue-500,
body.admin-dashboard .bg-blue-500 {
    background-color: #22c55e !important;
}

body[data-user-type="admin"] .bg-blue-50,
body.admin-page .bg-blue-50,
body.admin-dashboard .bg-blue-50 {
    background-color: #f0fdf4 !important;
}

body[data-user-type="admin"] .border-blue-200,
body.admin-page .border-blue-200,
body.admin-dashboard .border-blue-200 {
    border-color: #bbf7d0 !important;
}

body[data-user-type="admin"] .bg-blue-100,
body.admin-page .bg-blue-100,
body.admin-dashboard .bg-blue-100 {
    background-color: #dcfce7 !important;
}

body[data-user-type="admin"] .text-blue-700,
body.admin-page .text-blue-700,
body.admin-dashboard .text-blue-700 {
    color: #15803d !important;
}
