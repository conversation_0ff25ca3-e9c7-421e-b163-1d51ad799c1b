# Generated by Django 4.2.7 on 2025-06-01 18:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('support', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='systemmessagerecipient',
            name='recipient',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='system_message_receipts', to=settings.AUTH_USER_MODEL, verbose_name='المستقبل'),
        ),
        migrations.AddField(
            model_name='systemmessagerecipient',
            name='system_message',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_recipients', to='support.systemmessage', verbose_name='رسالة النظام'),
        ),
        migrations.AddField(
            model_name='systemmessage',
            name='recipients',
            field=models.ManyToManyField(related_name='received_system_messages', through='support.SystemMessageRecipient', to=settings.AUTH_USER_MODEL, verbose_name='المستقبلون'),
        ),
        migrations.AddField(
            model_name='systemmessage',
            name='sent_by',
            field=models.ForeignKey(limit_choices_to={'user_type': 'admin'}, on_delete=django.db.models.deletion.CASCADE, related_name='sent_system_messages', to=settings.AUTH_USER_MODEL, verbose_name='مُرسل بواسطة'),
        ),
        migrations.AddField(
            model_name='supportticketresponse',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='support_responses', to=settings.AUTH_USER_MODEL, verbose_name='كاتب الرد'),
        ),
        migrations.AddField(
            model_name='supportticketresponse',
            name='ticket',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='support.supportticket', verbose_name='التذكرة'),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='assigned_to',
            field=models.ForeignKey(blank=True, limit_choices_to={'user_type': 'admin'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_tickets', to=settings.AUTH_USER_MODEL, verbose_name='مُعيّن إلى'),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_tickets', to=settings.AUTH_USER_MODEL, verbose_name='منشئ التذكرة'),
        ),
        migrations.AlterUniqueTogether(
            name='systemmessagerecipient',
            unique_together={('system_message', 'recipient')},
        ),
    ]
