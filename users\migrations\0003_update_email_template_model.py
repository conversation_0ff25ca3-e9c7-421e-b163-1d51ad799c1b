# Generated by Django 4.2.7 on 2025-06-04 23:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_remove_academysettings_smtp_enabled_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='emailtemplate',
            options={'ordering': ['-updated_at'], 'verbose_name': 'قالب بريد إلكتروني', 'verbose_name_plural': 'قوالب البريد الإلكتروني'},
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='is_system',
            field=models.BooleanField(default=False, help_text='قوالب النظام محمية من الحذف', verbose_name='قالب النظام'),
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_email_templates', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة'),
        ),
        migrations.AddField(
            model_name='emailtemplate',
            name='variables',
            field=models.JSONField(blank=True, default=dict, help_text='قائمة بالمتغيرات التي يمكن استخدامها في القالب', verbose_name='المتغيرات المتاحة'),
        ),
        migrations.AlterField(
            model_name='emailtemplate',
            name='body',
            field=models.TextField(verbose_name='محتوى الرسالة (نص عادي)'),
        ),
        migrations.AlterField(
            model_name='emailtemplate',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_email_templates', to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة'),
        ),
        migrations.AlterField(
            model_name='emailtemplate',
            name='html_body',
            field=models.TextField(blank=True, null=True, verbose_name='محتوى الرسالة (HTML)'),
        ),
        migrations.AlterField(
            model_name='emailtemplate',
            name='subject',
            field=models.CharField(max_length=255, verbose_name='موضوع الرسالة'),
        ),
        migrations.AlterField(
            model_name='emailtemplate',
            name='template_type',
            field=models.CharField(choices=[('lesson_reminder_1day', 'تذكير بالحصة - يوم واحد'), ('lesson_reminder_30min', 'تذكير بالحصة - 30 دقيقة'), ('lesson_cancelled', 'إلغاء الحصة'), ('lesson_rescheduled', 'إعادة جدولة الحصة'), ('welcome_message', 'رسالة ترحيب'), ('welcome', 'رسالة ترحيب للمستخدم الجديد'), ('manual_notification', 'إشعار يدوي'), ('account_approved', 'تم الموافقة على الحساب'), ('account_rejected', 'تم رفض الحساب'), ('account_suspended', 'إشعار حظر الحساب'), ('account_unsuspended', 'إشعار إلغاء حظر الحساب'), ('account_deleted', 'إشعار حذف الحساب'), ('under_review', 'إشعار وضع الحساب قيد المراجعة'), ('password_reset', 'إعادة تعيين كلمة المرور'), ('email_verification', 'تأكيد البريد الإلكتروني'), ('subscription_payment_success', 'نجح الدفع - إرسال فاتورة'), ('subscription_activated', 'تفعيل الاشتراك'), ('subscription_cancelled', 'إلغاء الاشتراك'), ('subscription_expiring_7days', 'تذكير انتهاء الاشتراك - 7 أيام'), ('subscription_expiring_3days', 'تذكير انتهاء الاشتراك - 3 أيام'), ('subscription_expired', 'انتهاء الاشتراك'), ('payment_received_admin', 'إشعار المدير - دفعة جديدة'), ('invoice', 'الفاتورة'), ('custom', 'قالب مخصص')], max_length=50, verbose_name='نوع القالب'),
        ),
        migrations.AlterUniqueTogether(
            name='emailtemplate',
            unique_together={('template_type',)},
        ),
    ]
