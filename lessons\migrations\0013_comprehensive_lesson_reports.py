# Generated by Django 4.2.7 on 2025-06-24 16:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('lessons', '0012_safe_fresh_database'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComprehensiveLessonReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lesson_type', models.CharField(choices=[('trial', 'حصة تجريبية'), ('subscription', 'حصة اشتراك'), ('scheduled', 'حصة مجدولة')], max_length=20, verbose_name='نوع الحصة')),
                ('teacher_report_submitted', models.BooleanField(default=False, verbose_name='تم إرسال تقرير المعلم')),
                ('teacher_report_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ تقرير المعلم')),
                ('student_performance', models.PositiveIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='أداء الطالب')),
                ('student_participation', models.PositiveIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='مشاركة الطالب')),
                ('student_understanding', models.PositiveIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='فهم الطالب')),
                ('overall_lesson_rating', models.PositiveIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='تقييم الحصة العام')),
                ('lesson_summary', models.TextField(blank=True, verbose_name='ملخص الحصة')),
                ('strengths', models.TextField(blank=True, verbose_name='نقاط القوة')),
                ('areas_for_improvement', models.TextField(blank=True, verbose_name='نقاط التحسين')),
                ('additional_notes', models.TextField(blank=True, verbose_name='ملاحظات إضافية')),
                ('student_evaluation_submitted', models.BooleanField(default=False, verbose_name='تم إرسال تقييم الطالب')),
                ('student_evaluation_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ تقييم الطالب')),
                ('overall_rating', models.PositiveIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='التقييم العام')),
                ('teaching_quality', models.PositiveIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='جودة التدريس')),
                ('lesson_content', models.PositiveIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='محتوى الحصة')),
                ('interaction_quality', models.PositiveIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='جودة التفاعل')),
                ('punctuality', models.PositiveIntegerField(blank=True, choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], null=True, verbose_name='الالتزام بالوقت')),
                ('student_comment', models.TextField(blank=True, verbose_name='تعليق الطالب')),
                ('would_recommend', models.BooleanField(blank=True, null=True, verbose_name='هل ينصح بالمعلم؟')),
                ('interested_in_subscription', models.BooleanField(blank=True, null=True, verbose_name='مهتم بالاشتراك؟')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='comprehensive_reports', to='lessons.lesson', verbose_name='الحصة المجدولة')),
                ('live_lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='comprehensive_reports', to='lessons.livelesson', verbose_name='الحصة المباشرة')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comprehensive_student_reports', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comprehensive_teacher_reports', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'تقرير حصة شامل',
                'verbose_name_plural': 'تقارير الحصص الشاملة',
                'ordering': ['-created_at'],
            },
        ),

    ]
