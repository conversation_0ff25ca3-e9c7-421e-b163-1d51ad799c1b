{% extends 'base.html' %}
{% load static %}

{% block title %}الحصص المباشرة - {{ SITE_NAME }}{% endblock %}

{% block extra_css %}
<style>
    /* تحسينات التجاوب للحصص المباشرة */
    @media (max-width: 768px) {
        .live-lesson-card {
            padding: 1rem !important;
        }

        .lesson-info-grid {
            grid-template-columns: 1fr !important;
            gap: 0.5rem !important;
        }

        .lesson-actions {
            flex-direction: column !important;
            gap: 0.5rem !important;
        }

        .lesson-actions a,
        .lesson-actions button {
            font-size: 0.75rem !important;
            padding: 0.5rem 0.75rem !important;
        }

        .stats-card {
            padding: 1rem !important;
        }

        .stats-icon {
            font-size: 1.5rem !important;
        }

        .stats-number {
            font-size: 1.5rem !important;
        }

        .header-title {
            font-size: 1.5rem !important;
        }

        .section-title {
            font-size: 1.125rem !important;
        }
    }

    @media (max-width: 640px) {
        .lesson-info-item {
            font-size: 0.75rem !important;
        }

        .lesson-title {
            font-size: 1rem !important;
        }

        .badge {
            font-size: 0.625rem !important;
            padding: 0.25rem 0.5rem !important;
        }

        .container {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }

        .grid {
            gap: 0.75rem !important;
        }
    }

    @media (max-width: 480px) {
        .lesson-actions {
            flex-direction: column !important;
        }

        .lesson-actions a,
        .lesson-actions button {
            width: 100% !important;
            text-align: center !important;
        }

        .lesson-info-grid {
            grid-template-columns: 1fr !important;
        }

        .header-title {
            font-size: 1.25rem !important;
        }

        .stats-number {
            font-size: 1.25rem !important;
        }

        .stats-icon {
            font-size: 1.25rem !important;
        }
    }

    .animate-pulse-slow {
        animation: pulse-slow 3s infinite;
    }

    @keyframes pulse-slow {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }

    /* تحسينات إضافية للبطاقات */
    .stats-card {
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s;
    }

    .stats-card:hover::before {
        transform: translateX(100%);
    }

    /* تأثيرات الأيقونات */
    .stats-card .fas {
        transition: all 0.3s ease;
    }

    .stats-card:hover .fas {
        transform: scale(1.1) rotate(5deg);
    }

    /* تحسين الألوان والظلال */
    .bg-gradient-to-br {
        background-attachment: fixed;
    }

    .hover\:scale-105:hover {
        transform: scale(1.05) translateY(-2px);
    }

    /* تأثيرات إضافية للبطاقات */
    .stats-card {
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* تحسين الأيقونات */
    .icon-container {
        background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.3);
    }

    /* تأثير النبض المحسن */
    .pulse-enhanced {
        animation: pulse-enhanced 2s infinite;
    }

    @keyframes pulse-enhanced {
        0% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
        }
        70% {
            transform: scale(1.05);
            box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
        }
    }

    /* تحسين الخطوط */
    .stats-number {
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* تأثيرات التمرير */
    .hover-lift {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-lift:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* تحسين الأرقام */
    .stats-number {
        font-family: 'Cairo', sans-serif;
        font-weight: 900;
        letter-spacing: -0.025em;
    }

    /* تأثير الضوء */
    .light-effect {
        position: relative;
        overflow: hidden;
    }

    .light-effect::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg) translate(-100%, -100%);
        transition: transform 0.6s;
    }

    .light-effect:hover::after {
        transform: rotate(45deg) translate(100%, 100%);
    }

    /* تحسين الأيقونات الصغيرة */
    .mini-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        font-size: 0.75rem;
    }

    /* تحسين الألوان الإسلامية */
    .bg-islamic-primary {
        background: linear-gradient(135deg, #2D5A27, #3E7B3E);
    }

    .bg-islamic-light {
        background: linear-gradient(135deg, #3E7B3E, #4A9B4A);
    }

    /* تأثيرات متقدمة للبطاقات */
    .card-glow {
        position: relative;
    }

    .card-glow::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);
        border-radius: inherit;
        opacity: 0;
        z-index: -1;
        transition: opacity 0.3s;
        animation: rotate 2s linear infinite;
    }

    .card-glow:hover::before {
        opacity: 0.1;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* تحسين الخطوط العربية */
    .arabic-text {
        font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
        font-weight: 600;
        letter-spacing: 0.025em;
    }

    /* تأثيرات الحركة المتقدمة */
    .bounce-in {
        animation: bounceIn 0.6s ease-out;
    }

    @keyframes bounceIn {
        0% {
            transform: scale(0.3);
            opacity: 0;
        }
        50% {
            transform: scale(1.05);
        }
        70% {
            transform: scale(0.9);
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* تحسينات بطاقات الفلاتر */
    .filter-stat-card {
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .filter-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s;
    }

    .filter-stat-card:hover::before {
        transform: translateX(100%);
    }

    .filter-stat-card:hover {
        transform: translateY(-4px) scale(1.05);
        box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }

    /* تحسين الأيقونات في البطاقات */
    .filter-stat-card .fas {
        transition: all 0.3s ease;
    }

    .filter-stat-card:hover .fas {
        transform: scale(1.1) rotate(5deg);
    }

    /* تأثيرات خاصة للأرقام */
    .filter-stat-card .font-black {
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .filter-stat-card:hover .font-black {
        transform: scale(1.1);
    }

    /* تحسينات أزرار الإجراءات */
    .action-card {
        position: relative;
        overflow: hidden;
    }

    .action-card::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .action-card:hover::after {
        width: 300px;
        height: 300px;
    }

    .action-card .fas {
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
    }

    .action-card:hover .fas {
        transform: scale(1.2) rotate(10deg);
    }

    .action-card h3,
    .action-card p {
        position: relative;
        z-index: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 py-6 md:py-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
            <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div>
                    <h1 class="header-title text-2xl md:text-3xl font-bold text-islamic-primary mb-2">
                        <i class="fas fa-broadcast-tower text-islamic-gold ml-2 md:ml-3"></i>
                        الحصص المباشرة
                    </h1>
                    <p class="text-sm md:text-base text-gray-600">مراقبة الحصص الجارية والقادمة مع فلترة متقدمة</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-2 sm:gap-4">
                    <button onclick="refreshPage()"
                            class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 md:py-3 px-4 md:px-6 rounded-lg shadow-md transition-all duration-300 flex items-center justify-center text-sm md:text-base">
                        <i class="fas fa-sync-alt ml-1 md:ml-2"></i>
                        تحديث
                    </button>
                    <a href="{% url 'admin_create_live_lesson' %}"
                       class="bg-islamic-primary hover:bg-islamic-light text-white font-bold py-2 md:py-3 px-4 md:px-6 rounded-lg shadow-md transition-all duration-300 flex items-center justify-center text-sm md:text-base">
                        <i class="fas fa-plus ml-1 md:ml-2"></i>
                        إنشاء حصة تجريبية
                    </a>
                </div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-4">
                <h2 class="text-lg md:text-xl font-bold text-islamic-primary">
                    <i class="fas fa-filter text-islamic-gold ml-2"></i>
                    فلترة متقدمة
                </h2>
                <button onclick="clearAllFilters()"
                        class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                    <i class="fas fa-times ml-1"></i>
                    مسح جميع الفلاتر
                </button>
            </div>

            <form id="filter-form" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                <!-- فلتر المعلم -->
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-chalkboard-teacher text-green-600 ml-1"></i>
                        المعلم
                    </label>
                    <select name="teacher_id" id="teacher-filter" class="form-control w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع المعلمين</option>
                        {% for teacher in available_teachers %}
                            <option value="{{ teacher.id }}" {% if teacher_filter == teacher.id|stringformat:"s" %}selected{% endif %}>
                                {{ teacher.get_full_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- فلتر الطالب -->
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user-graduate text-blue-600 ml-1"></i>
                        الطالب
                    </label>
                    <select name="student_id" id="student-filter" class="form-control w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الطلاب</option>
                        {% for student in available_students %}
                            <option value="{{ student.id }}" {% if student_filter == student.id|stringformat:"s" %}selected{% endif %}>
                                {{ student.get_full_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- فلتر التاريخ -->
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-purple-600 ml-1"></i>
                        التاريخ
                    </label>
                    <select name="date_filter" id="date-filter" class="form-control w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        {% for value, label in date_filter_options %}
                            <option value="{{ value }}" {% if date_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- فلتر نوع الحصة -->
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-video text-red-600 ml-1"></i>
                        نوع الحصة
                    </label>
                    <select name="lesson_type" id="lesson-type-filter" class="form-control w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        {% for value, label in lesson_type_options %}
                            <option value="{{ value }}" {% if lesson_type_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- البحث النصي -->
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-search text-orange-600 ml-1"></i>
                        البحث
                    </label>
                    <input type="text" name="search" id="search-input" value="{{ search_query }}"
                           placeholder="ابحث في الأسماء والعناوين..."
                           class="form-control w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>

                <!-- أزرار التحكم -->
                <div class="form-group flex flex-col justify-end">
                    <div class="flex gap-2">
                        <button type="submit" class="bg-islamic-primary hover:bg-islamic-light text-white px-4 py-2 rounded-lg text-sm transition-colors flex-1">
                            <i class="fas fa-filter ml-1"></i>
                            تطبيق
                        </button>
                        <button type="button" onclick="exportPDF()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center gap-2" title="تصدير PDF">
                            <i class="fas fa-file-pdf"></i>
                            <span>تصدير PDF</span>
                        </button>
                    </div>
                </div>
            </form>

            <!-- إحصائيات الفلترة -->
            <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4" id="filter-stats">
                <!-- حصص جارية -->
                <div class="filter-stat-card bg-gradient-to-br from-red-50 via-red-100 to-red-50 border-2 border-red-200 rounded-xl p-4 text-center hover:shadow-lg hover:scale-105 transition-all duration-300 transform">
                    <div class="flex items-center justify-center mb-3">
                        <div class="bg-red-500 p-2.5 rounded-xl shadow-md">
                            <i class="fas fa-broadcast-tower text-red-100 text-lg"></i>
                        </div>
                    </div>
                    <div class="text-red-600 font-black text-2xl mb-1" id="live-count">{{ total_live_lessons }}</div>
                    <div class="text-red-700 text-sm font-bold tracking-wide">حصص جارية</div>
                    <div class="mt-2 flex items-center justify-center text-red-600 text-xs">
                        <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse ml-1"></div>
                        <span class="font-medium">مباشر الآن</span>
                    </div>
                </div>

                <!-- حصص مجدولة -->
                <div class="filter-stat-card bg-gradient-to-br from-blue-50 via-blue-100 to-blue-50 border-2 border-blue-200 rounded-xl p-4 text-center hover:shadow-lg hover:scale-105 transition-all duration-300 transform">
                    <div class="flex items-center justify-center mb-3">
                        <div class="bg-blue-500 p-2.5 rounded-xl shadow-md">
                            <i class="fas fa-calendar-check text-blue-100 text-lg"></i>
                        </div>
                    </div>
                    <div class="text-blue-600 font-black text-2xl mb-1" id="scheduled-count">{{ total_scheduled_lessons }}</div>
                    <div class="text-blue-700 text-sm font-bold tracking-wide">حصص مجدولة</div>
                    <div class="mt-2 flex items-center justify-center text-blue-600 text-xs">
                        <i class="fas fa-clock ml-1"></i>
                        <span class="font-medium">في انتظار البدء</span>
                    </div>
                </div>

                <!-- حصص اشتراكات -->
                <div class="filter-stat-card bg-gradient-to-br from-purple-50 via-purple-100 to-purple-50 border-2 border-purple-200 rounded-xl p-4 text-center hover:shadow-lg hover:scale-105 transition-all duration-300 transform">
                    <div class="flex items-center justify-center mb-3">
                        <div class="bg-purple-500 p-2.5 rounded-xl shadow-md">
                            <i class="fas fa-users text-purple-100 text-lg"></i>
                        </div>
                    </div>
                    <div class="text-purple-600 font-black text-2xl mb-1" id="subscription-count">{{ total_subscription_lessons }}</div>
                    <div class="text-purple-700 text-sm font-bold tracking-wide">حصص اشتراكات</div>
                    <div class="mt-2 flex items-center justify-center text-purple-600 text-xs">
                        <i class="fas fa-package ml-1"></i>
                        <span class="font-medium">من الباقات</span>
                    </div>
                </div>

                <!-- إجمالي النتائج -->
                <div class="filter-stat-card bg-gradient-to-br from-green-50 via-green-100 to-green-50 border-2 border-green-200 rounded-xl p-4 text-center hover:shadow-lg hover:scale-105 transition-all duration-300 transform">
                    <div class="flex items-center justify-center mb-3">
                        <div class="bg-green-500 p-2.5 rounded-xl shadow-md">
                            <i class="fas fa-chart-bar text-green-100 text-lg"></i>
                        </div>
                    </div>
                    <div class="text-green-600 font-black text-2xl mb-1" id="total-count">{{ total_live_lessons|add:total_scheduled_lessons }}</div>
                    <div class="text-green-700 text-sm font-bold tracking-wide">إجمالي النتائج</div>
                    <div class="mt-2 flex items-center justify-center text-green-600 text-xs">
                        <i class="fas fa-check-circle ml-1"></i>
                        <span class="font-medium">العدد الكلي</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Lessons -->
        <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
            <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
                <h2 class="section-title text-lg md:text-xl font-bold text-islamic-primary">
                    <i class="fas fa-circle text-red-500 animate-pulse ml-2"></i>
                    الحصص الجارية الآن
                </h2>
                <div class="flex flex-col sm:flex-row items-center gap-2 sm:gap-4">
                    <span class="bg-red-600 text-white px-3 md:px-4 py-2 rounded-full text-xs md:text-sm font-bold border-2 border-red-500 shadow-md">
                        {{ live_lessons.count }} حصة جارية
                    </span>
                </div>
            </div>

            {% if live_lessons %}
                <div class="grid gap-3 md:gap-4">
                    {% for lesson in live_lessons %}
                        <div class="live-lesson-card border-2 border-red-300 rounded-xl p-3 md:p-4 bg-red-50 animate-pulse-slow hover:shadow-lg transition-all duration-300">
                            <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                                <div class="flex-1">
                                    <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-3">
                                        <h3 class="lesson-title font-bold text-base md:text-lg text-gray-800">{{ lesson.title }}</h3>
                                        <span class="badge bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse w-fit">
                                            مباشر
                                        </span>
                                    </div>

                                    <div class="lesson-info-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 md:gap-3 text-xs md:text-sm">
                                        <div class="lesson-info-item flex items-center">
                                            <i class="fas fa-user-graduate text-blue-600 ml-1 md:ml-2 text-sm"></i>
                                            <span class="text-gray-800 font-semibold truncate">{{ lesson.student.get_full_name }}</span>
                                        </div>
                                        <div class="lesson-info-item flex items-center">
                                            <i class="fas fa-chalkboard-teacher text-green-600 ml-1 md:ml-2 text-sm"></i>
                                            <span class="text-gray-800 font-semibold truncate">{{ lesson.teacher.get_full_name }}</span>
                                        </div>
                                        <div class="lesson-info-item flex items-center">
                                            <i class="fas fa-clock text-orange-600 ml-1 md:ml-2 text-sm"></i>
                                            <span class="text-gray-800 font-semibold">{{ lesson.duration_minutes }} دقيقة</span>
                                        </div>
                                        <div class="lesson-info-item flex items-center">
                                            <i class="fas fa-video text-purple-600 ml-1 md:ml-2 text-sm"></i>
                                            <span class="text-gray-800 font-semibold">Jitsi Meet</span>
                                        </div>
                                    </div>

                                    <div class="mt-2 md:mt-3 text-xs md:text-sm text-gray-700 font-medium">
                                        <i class="fas fa-calendar ml-1 text-sm"></i>
                                        بدأت في: {{ lesson.started_at|default:lesson.scheduled_date|date:"H:i" }}
                                    </div>
                                </div>

                                <div class="lesson-actions flex flex-row lg:flex-col gap-2 justify-center lg:justify-start">
                                    <a href="{% url 'live_lesson_room' lesson.id %}" target="_blank"
                                       class="bg-green-600 hover:bg-green-700 text-white px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm transition-colors text-center flex-1 lg:flex-none">
                                        <i class="fas fa-video ml-1"></i>
                                        <span class="hidden sm:inline">مراقبة الحصة</span>
                                        <span class="sm:hidden">مراقبة</span>
                                    </a>
                                    <a href="{{ lesson.get_jitsi_url }}" target="_blank"
                                       class="bg-blue-600 hover:bg-blue-700 text-white px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm transition-colors text-center flex-1 lg:flex-none">
                                        <i class="fas fa-external-link-alt ml-1"></i>
                                        <span class="hidden sm:inline">Jitsi مباشر</span>
                                        <span class="sm:hidden">Jitsi</span>
                                    </a>
                                    {% if lesson.status == 'live' %}
                                    <button onclick="stopLiveLesson({{ lesson.id }})"
                                            class="bg-red-600 hover:bg-red-700 text-white px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm transition-colors text-center flex-1 lg:flex-none">
                                        <i class="fas fa-stop ml-1"></i>
                                        <span class="hidden sm:inline">إيقاف الحصة</span>
                                        <span class="sm:hidden">إيقاف</span>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8 md:py-12 text-gray-600">
                    <i class="fas fa-video-slash text-4xl md:text-6xl mb-4 text-gray-400"></i>
                    <h3 class="text-lg md:text-xl font-bold mb-2 text-gray-700">لا توجد حصص جارية حالياً</h3>
                    <p class="text-sm md:text-base text-gray-600 font-medium">جميع الحصص منتهية أو لم تبدأ بعد</p>
                </div>
            {% endif %}
        </div>

        <!-- الحصص القادمة المجدولة -->
        <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
            <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
                <h2 class="section-title text-lg md:text-xl font-bold text-islamic-primary">
                    <i class="fas fa-calendar-alt text-blue-500 ml-2"></i>
                    الحصص القادمة المجدولة
                </h2>
                <span class="bg-blue-600 text-white px-3 md:px-4 py-2 rounded-full text-xs md:text-sm font-bold border-2 border-blue-500 shadow-md w-fit">
                    {{ all_scheduled_lessons|length }} حصة مجدولة
                </span>
            </div>

            {% if all_scheduled_lessons %}
                <div class="grid gap-3 md:gap-4">
                    {% for lesson in all_scheduled_lessons %}
                        <div class="live-lesson-card border border-blue-200 rounded-xl p-3 md:p-4 bg-blue-50 hover:shadow-lg transition-all duration-300">
                            <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                                <div class="flex-1">
                                    <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-3">
                                        <h3 class="lesson-title font-bold text-base md:text-lg text-gray-800">{{ lesson.title }}</h3>
                                        <div class="flex flex-wrap gap-2">
                                            {% if lesson.type == 'subscription' %}
                                                <span class="badge bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                    من الاشتراكات
                                                </span>
                                            {% else %}
                                                <span class="badge bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                    حصة مباشرة
                                                </span>
                                            {% endif %}
                                            {% if lesson.scheduled_date|timeuntil|slice:":1" == "-" %}
                                            <span class="badge bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse">
                                                حان الوقت!
                                            </span>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="lesson-info-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 md:gap-3 text-xs md:text-sm">
                                        <div class="lesson-info-item flex items-center">
                                            <i class="fas fa-user-graduate text-blue-600 ml-1 md:ml-2 text-sm"></i>
                                            <span class="text-gray-800 font-semibold truncate">{{ lesson.student.get_full_name }}</span>
                                        </div>
                                        <div class="lesson-info-item flex items-center">
                                            <i class="fas fa-chalkboard-teacher text-green-600 ml-1 md:ml-2 text-sm"></i>
                                            <span class="text-gray-800 font-semibold truncate">{{ lesson.teacher.get_full_name }}</span>
                                        </div>
                                        <div class="lesson-info-item flex items-center">
                                            <i class="fas fa-clock text-orange-600 ml-1 md:ml-2 text-sm"></i>
                                            <span class="text-gray-800 font-semibold">{{ lesson.duration_minutes }} دقيقة</span>
                                        </div>
                                        <div class="lesson-info-item flex items-center">
                                            <i class="fas fa-video text-purple-600 ml-1 md:ml-2 text-sm"></i>
                                            <span class="text-gray-800 font-semibold">
                                                {% if lesson.type == 'subscription' %}
                                                    حصة مجدولة
                                                {% else %}
                                                    حصة مباشرة
                                                {% endif %}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="mt-2 md:mt-3 space-y-1 text-xs md:text-sm text-gray-700 font-medium">
                                        <div class="flex flex-wrap items-center gap-2 md:gap-4">
                                            <span>
                                                <i class="fas fa-calendar ml-1 text-sm"></i>
                                                موعد البداية: {{ lesson.scheduled_date|date:"Y-m-d H:i" }}
                                            </span>
                                            <span class="{% if lesson.scheduled_date|timeuntil|slice:':1' == '-' %}text-orange-600 font-bold{% endif %}">
                                                <i class="fas fa-hourglass-half ml-1 text-sm"></i>
                                                {% if lesson.scheduled_date|timeuntil|slice:":1" == "-" %}
                                                    متأخرة {{ lesson.scheduled_date|timesince }}
                                                {% else %}
                                                    تبدأ خلال {{ lesson.scheduled_date|timeuntil }}
                                                {% endif %}
                                            </span>
                                        </div>
                                        {% if lesson.type == 'subscription' %}
                                            <div>
                                                <i class="fas fa-package ml-1 text-sm"></i>
                                                باقة: {{ lesson.lesson_obj.subscription.plan.name }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="lesson-actions flex flex-row lg:flex-col gap-2 justify-center lg:justify-start">
                                    {% if lesson.type == 'subscription' %}
                                        <!-- أزرار للحصص المجدولة من الاشتراكات -->
                                        {% if lesson.scheduled_date|timeuntil|slice:":1" == "-" %}
                                        <button onclick="startSubscriptionLesson({{ lesson.id }})"
                                                class="bg-green-600 hover:bg-green-700 text-white px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm transition-colors text-center flex-1 lg:flex-none">
                                            <i class="fas fa-play ml-1"></i>
                                            <span class="hidden sm:inline">بدء الحصة الآن</span>
                                            <span class="sm:hidden">بدء</span>
                                        </button>
                                        {% endif %}
                                        <a href="{% url 'admin_subscriptions_list' %}"
                                           class="bg-purple-600 hover:bg-purple-700 text-white px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm transition-colors text-center flex-1 lg:flex-none">
                                            <i class="fas fa-list ml-1"></i>
                                            <span class="hidden sm:inline">إدارة الاشتراكات</span>
                                            <span class="sm:hidden">اشتراكات</span>
                                        </a>
                                    {% else %}
                                        <!-- أزرار للحصص المباشرة المجدولة -->
                                        {% if lesson.scheduled_date|timeuntil|slice:":1" == "-" %}
                                        <button onclick="startLiveLessonNow({{ lesson.id }})"
                                                class="bg-green-600 hover:bg-green-700 text-white px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm transition-colors text-center flex-1 lg:flex-none">
                                            <i class="fas fa-play ml-1"></i>
                                            <span class="hidden sm:inline">بدء الحصة الآن</span>
                                            <span class="sm:hidden">بدء</span>
                                        </button>
                                        {% endif %}
                                        <a href="{% url 'live_lesson_room' lesson.id %}" target="_blank"
                                           class="bg-green-600 hover:bg-green-700 text-white px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm transition-colors text-center flex-1 lg:flex-none">
                                            <i class="fas fa-video ml-1"></i>
                                            <span class="hidden sm:inline">دخول الغرفة</span>
                                            <span class="sm:hidden">دخول</span>
                                        </a>
                                        <a href="{{ lesson.lesson_obj.get_jitsi_url }}" target="_blank"
                                           class="bg-blue-600 hover:bg-blue-700 text-white px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm transition-colors text-center flex-1 lg:flex-none">
                                            <i class="fas fa-external-link-alt ml-1"></i>
                                            <span class="hidden sm:inline">Jitsi مباشر</span>
                                            <span class="sm:hidden">Jitsi</span>
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8 md:py-12 text-gray-600">
                    <i class="fas fa-calendar-times text-4xl md:text-6xl mb-4 text-gray-400"></i>
                    <h3 class="text-lg md:text-xl font-bold mb-2 text-gray-700">لا توجد حصص مجدولة</h3>
                    <p class="text-sm md:text-base text-gray-600 font-medium">يمكنك جدولة حصص من نظام الاشتراكات أو إنشاء حصة تجريبية</p>
                </div>
            {% endif %}
        </div>



        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6 md:mb-8">
            <!-- الحصص الجارية -->
            <div class="stats-card light-effect card-glow bg-gradient-to-br from-red-50 via-red-100 to-red-50 border border-red-200 p-4 md:p-6 rounded-xl shadow-lg hover-lift bounce-in">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-3">
                            <div class="icon-container bg-red-500 p-3 rounded-xl shadow-lg ml-3 pulse-enhanced">
                                <i class="fas fa-broadcast-tower text-red-100 text-xl"></i>
                            </div>
                            <div>
                                <p class="arabic-text text-red-700 text-xs md:text-sm font-bold mb-1 tracking-wide">الحصص الجارية</p>
                                <p class="stats-number text-3xl md:text-4xl font-black text-red-600">{{ live_lessons.count }}</p>
                            </div>
                        </div>
                        <div class="flex items-center text-red-600 text-xs font-medium bg-red-100 px-2 py-1 rounded-full w-fit">
                            <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse ml-1"></div>
                            <span class="arabic-text">مباشر الآن</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الحصص المجدولة -->
            <div class="stats-card light-effect card-glow bg-gradient-to-br from-blue-50 via-blue-100 to-blue-50 border border-blue-200 p-4 md:p-6 rounded-xl shadow-lg hover-lift bounce-in">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-3">
                            <div class="icon-container bg-blue-500 p-3 rounded-xl shadow-lg ml-3">
                                <i class="fas fa-calendar-check text-blue-100 text-xl"></i>
                            </div>
                            <div>
                                <p class="arabic-text text-blue-700 text-xs md:text-sm font-bold mb-1 tracking-wide">الحصص المجدولة</p>
                                <p class="stats-number text-3xl md:text-4xl font-black text-blue-600">{{ all_scheduled_lessons|length }}</p>
                            </div>
                        </div>
                        <div class="flex items-center text-blue-600 text-xs font-medium bg-blue-100 px-2 py-1 rounded-full w-fit">
                            <i class="fas fa-clock ml-1"></i>
                            <span class="arabic-text">في انتظار البدء</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجمالي الحصص -->
            <div class="stats-card light-effect card-glow bg-gradient-to-br from-green-50 via-green-100 to-green-50 border border-green-200 p-4 md:p-6 rounded-xl shadow-lg hover-lift bounce-in">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-3">
                            <div class="icon-container bg-green-500 p-3 rounded-xl shadow-lg ml-3">
                                <i class="fas fa-chart-line text-green-100 text-xl"></i>
                            </div>
                            <div>
                                <p class="arabic-text text-green-700 text-xs md:text-sm font-bold mb-1 tracking-wide">إجمالي الحصص</p>
                                <p class="stats-number text-3xl md:text-4xl font-black text-green-600">{{ live_lessons.count|add:all_scheduled_lessons|length }}</p>
                            </div>
                        </div>
                        <div class="flex items-center text-green-600 text-xs font-medium bg-green-100 px-2 py-1 rounded-full w-fit">
                            <i class="fas fa-check-circle ml-1"></i>
                            <span class="arabic-text">العدد الكلي</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            <a href="{% url 'admin_lessons' %}"
               class="action-card light-effect bg-gradient-to-br from-islamic-primary to-islamic-light text-white p-4 md:p-6 rounded-xl text-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-2 hover:scale-105">
                <div class="bg-white bg-opacity-30 p-3 rounded-full w-fit mx-auto mb-3 shadow-md">
                    <i class="fas fa-list text-islamic-primary text-xl md:text-2xl"></i>
                </div>
                <h3 class="font-bold text-sm md:text-base mb-1">جميع الحصص</h3>
                <p class="text-xs md:text-sm opacity-90">عرض وإدارة جميع الحصص</p>
            </a>

            <a href="{% url 'admin_create_live_lesson' %}"
               class="action-card light-effect bg-gradient-to-br from-blue-500 to-blue-600 text-white p-4 md:p-6 rounded-xl text-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-2 hover:scale-105">
                <div class="bg-white bg-opacity-30 p-3 rounded-full w-fit mx-auto mb-3 shadow-md">
                    <i class="fas fa-plus text-blue-600 text-xl md:text-2xl"></i>
                </div>
                <h3 class="font-bold text-sm md:text-base mb-1">إنشاء حصة جديدة</h3>
                <p class="text-xs md:text-sm opacity-90">إضافة حصة تجريبية مباشرة</p>
            </a>

            <a href="{% url 'admin_subscriptions_list' %}"
               class="action-card light-effect bg-gradient-to-br from-purple-500 to-purple-600 text-white p-4 md:p-6 rounded-xl text-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-2 hover:scale-105">
                <div class="bg-white bg-opacity-30 p-3 rounded-full w-fit mx-auto mb-3 shadow-md">
                    <i class="fas fa-list text-purple-600 text-xl md:text-2xl"></i>
                </div>
                <h3 class="font-bold text-sm md:text-base mb-1">إدارة الاشتراكات</h3>
                <p class="text-xs md:text-sm opacity-90">عرض وإدارة جميع الاشتراكات</p>
            </a>
        </div>
    </div>
</div>

<script>
// تحديث الحصص الجارية في الوقت الفعلي مع إعادة تصنيف تلقائي
function updateLiveLessonsStatus() {
    fetch('/api/admin/live-lessons-status/')
        .then(response => response.json())
        .then(data => {
            if (data && data.success) {
                // التأكد من وجود البيانات المطلوبة
                if (!data.live_lessons) data.live_lessons = [];
                if (!data.upcoming_soon) data.upcoming_soon = [];
                if (!data.scheduled_lessons) data.scheduled_lessons = [];

                // تحديث الأعداد
                updateCounts(data);

                // إعادة تصنيف الحصص
                reclassifyLessons(data);

                // تحديث العدادات التنازلية
                updateCountdowns();
            } else {
                console.warn('Invalid response from live lessons status API:', data);
            }
        })
        .catch(error => {
            console.error('Error updating live lessons status:', error);
        });
}

function updateCounts(data) {
    // تحديث عدد الحصص الجارية
    const liveCountElement = document.querySelector('#live-count');
    if (liveCountElement && data.live_count !== undefined) {
        liveCountElement.textContent = data.live_count || 0;
    }

    // تحديث عدد الحصص المجدولة
    const scheduledCountElement = document.querySelector('.bg-blue-100 .text-3xl');
    if (scheduledCountElement && data.scheduled_count !== undefined) {
        scheduledCountElement.textContent = data.scheduled_count || 0;
    }

    // تحديث عدد الحصص القادمة قريباً
    const upcomingSoonCountElement = document.querySelector('#upcoming-soon-count');
    if (upcomingSoonCountElement && data.upcoming_count !== undefined) {
        upcomingSoonCountElement.textContent = `${data.upcoming_count || 0} حصة قادمة قريباً`;
    }

    // تحديث العدد الإجمالي
    const totalCountElement = document.querySelector('#total-count');
    if (totalCountElement && data.total_count !== undefined) {
        totalCountElement.textContent = data.total_count || 0;
    }
}

function reclassifyLessons(data) {
    // إعادة تصنيف الحصص حسب حالتها الحالية
    const allLessonCards = document.querySelectorAll('[data-lesson-id]');

    allLessonCards.forEach(card => {
        const lessonId = parseInt(card.dataset.lessonId);
        const currentSection = card.closest('.mb-8');

        // البحث عن الحصة في البيانات الجديدة
        let newCategory = null;
        let lessonData = null;

        // البحث في الحصص الجارية
        if (data.live_lessons && Array.isArray(data.live_lessons)) {
            lessonData = data.live_lessons.find(l => l.id === lessonId);
            if (lessonData) {
                newCategory = 'live';
            }
        }

        if (!lessonData) {
            // البحث في الحصص القادمة قريباً
            if (data.upcoming_soon && Array.isArray(data.upcoming_soon)) {
                lessonData = data.upcoming_soon.find(l => l.id === lessonId);
                if (lessonData) {
                    newCategory = 'upcoming_soon';
                }
            }
        }

        if (!lessonData) {
            // البحث في الحصص المجدولة
            if (data.scheduled_lessons && Array.isArray(data.scheduled_lessons)) {
                lessonData = data.scheduled_lessons.find(l => l.id === lessonId);
                if (lessonData) {
                    newCategory = 'scheduled';
                }
            }
        }

        // نقل الحصة إلى القسم المناسب إذا تغيرت حالتها
        if (newCategory && lessonData) {
            moveCardToSection(card, newCategory, lessonData);
        } else if (!lessonData) {
            // إزالة الحصة إذا لم تعد موجودة (انتهت أو ألغيت)
            card.remove();
        }
    });

    // إضافة حصص جديدة إذا ظهرت
    addNewLessons(data);
}

function moveCardToSection(card, newCategory, lessonData) {
    const currentCategory = getCurrentCategory(card);

    if (currentCategory !== newCategory) {
        // إزالة الحصة من موقعها الحالي
        card.remove();

        // إنشاء بطاقة جديدة في القسم المناسب
        createLessonCard(lessonData, newCategory);

        // إظهار إشعار
        showUpdateNotification(`تم نقل الحصة "${lessonData.title}" إلى قسم ${getCategoryName(newCategory)}`);
    } else {
        // تحديث محتوى البطاقة فقط
        updateCardContent(card, lessonData, newCategory);
    }
}

function getCurrentCategory(card) {
    if (card.classList.contains('border-red-300')) return 'live';
    if (card.classList.contains('border-orange-200')) return 'upcoming_soon';
    if (card.classList.contains('border-blue-200')) return 'scheduled';
    return null;
}

function getCategoryName(category) {
    switch(category) {
        case 'live': return 'الحصص الجارية';
        case 'upcoming_soon': return 'الحصص القادمة قريباً';
        case 'scheduled': return 'الحصص المجدولة';
        default: return 'غير محدد';
    }
}

function createLessonCard(lessonData, category) {
    const container = getContainerForCategory(category);
    if (!container) return;

    const cardHTML = generateCardHTML(lessonData, category);
    container.insertAdjacentHTML('beforeend', cardHTML);

    // إضافة تأثير الظهور
    const newCard = container.lastElementChild;
    newCard.style.opacity = '0';
    newCard.style.transform = 'translateY(20px)';

    setTimeout(() => {
        newCard.style.transition = 'all 0.5s ease';
        newCard.style.opacity = '1';
        newCard.style.transform = 'translateY(0)';
    }, 100);
}

function getContainerForCategory(category) {
    switch(category) {
        case 'live':
            return document.querySelector('.grid.gap-4'); // أول container
        case 'upcoming_soon':
            return document.querySelector('#upcoming-soon-container');
        case 'scheduled':
            return document.querySelectorAll('.grid.gap-4')[1]; // ثاني container
        default:
            return null;
    }
}

function generateCardHTML(lessonData, category) {
    const borderColor = {
        'live': 'border-red-300 bg-red-50',
        'upcoming_soon': 'border-orange-200 bg-orange-50',
        'scheduled': 'border-blue-200 bg-blue-50'
    }[category];

    const statusBadge = {
        'live': '<span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse">مباشر</span>',
        'upcoming_soon': '<span class="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium">قريباً</span>',
        'scheduled': '<span class="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">مجدولة</span>'
    }[category];

    return `
        <div class="border ${borderColor} rounded-lg p-4 ${category === 'live' ? 'animate-pulse-slow' : ''}"
             data-lesson-id="${lessonData.id}" data-scheduled-time="${lessonData.scheduled_date}">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center gap-3 mb-3">
                        <h3 class="font-bold text-lg text-gray-800">${lessonData.title}</h3>
                        ${statusBadge}
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-3 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-user-graduate text-blue-600 ml-2"></i>
                            <span class="text-gray-700">${lessonData.student_name}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-chalkboard-teacher text-green-600 ml-2"></i>
                            <span class="text-gray-700">${lessonData.teacher_name}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock text-orange-600 ml-2"></i>
                            <span class="text-gray-700">${lessonData.duration_minutes} دقيقة</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-video text-purple-600 ml-2"></i>
                            <span class="text-gray-700">حصة مباشرة</span>
                        </div>
                    </div>
                    <div class="mt-3 text-sm text-gray-600">
                        <i class="fas fa-calendar ml-1"></i>
                        ${category === 'live' ? 'بدأت في:' : 'موعد البداية:'} ${new Date(lessonData.scheduled_date).toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}
                    </div>
                </div>
                <div class="flex flex-col gap-2">
                    ${generateActionButtons(lessonData, category)}
                </div>
            </div>
        </div>
    `;
}

function generateActionButtons(lessonData, category) {
    let buttons = `
        <a href="/live-lesson-room/${lessonData.id}/" target="_blank"
           class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors text-center">
            <i class="fas fa-video ml-1"></i>
            ${category === 'live' ? 'مراقبة الحصة' : 'دخول الغرفة'}
        </a>
        <a href="${lessonData.jitsi_url}" target="_blank"
           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors text-center">
            <i class="fas fa-external-link-alt ml-1"></i>
            Jitsi مباشر
        </a>
    `;

    if (category === 'live') {
        buttons += `
            <button onclick="stopLiveLesson(${lessonData.id})"
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors text-center">
                <i class="fas fa-stop ml-1"></i>
                إيقاف الحصة
            </button>
        `;
    } else if (category === 'upcoming_soon') {
        const now = new Date();
        const scheduledTime = new Date(lessonData.scheduled_date);
        if (scheduledTime <= now) {
            buttons = `
                <button onclick="startLiveLessonNow(${lessonData.id})"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors text-center">
                    <i class="fas fa-play ml-1"></i>
                    بدء الحصة الآن
                </button>
            ` + buttons;
        }
    }

    return buttons;
}

function addNewLessons(data) {
    // إضافة حصص جديدة لم تكن موجودة من قبل
    const existingLessonIds = Array.from(document.querySelectorAll('[data-lesson-id]'))
        .map(card => parseInt(card.dataset.lessonId));

    // إضافة حصص جارية جديدة
    if (data.live_lessons && Array.isArray(data.live_lessons)) {
        data.live_lessons.forEach(lesson => {
            if (!existingLessonIds.includes(lesson.id)) {
                createLessonCard(lesson, 'live');
            }
        });
    }

    // إضافة حصص قادمة قريباً جديدة
    if (data.upcoming_soon && Array.isArray(data.upcoming_soon)) {
        data.upcoming_soon.forEach(lesson => {
            if (!existingLessonIds.includes(lesson.id)) {
                createLessonCard(lesson, 'upcoming_soon');
            }
        });
    }

    // إضافة حصص مجدولة جديدة
    if (data.scheduled_lessons && Array.isArray(data.scheduled_lessons)) {
        data.scheduled_lessons.forEach(lesson => {
            if (!existingLessonIds.includes(lesson.id)) {
                createLessonCard(lesson, 'scheduled');
            }
        });
    }
}

function updateCardContent(card, lessonData, category) {
    // تحديث محتوى البطاقة بدون إعادة إنشائها
    const countdownElement = card.querySelector('.countdown');
    if (countdownElement && category !== 'live') {
        const now = new Date();
        const scheduledTime = new Date(lessonData.scheduled_date);
        const diff = scheduledTime - now;

        if (diff > 0) {
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;

            let timeText = '';
            if (hours > 0) {
                timeText = `${hours} ساعة و ${remainingMinutes} دقيقة`;
            } else {
                timeText = `${remainingMinutes} دقيقة`;
            }

            countdownElement.innerHTML = `<i class="fas fa-hourglass-half ml-1"></i>تبدأ خلال ${timeText}`;
        } else {
            countdownElement.innerHTML = `<i class="fas fa-exclamation-triangle ml-1"></i>متأخرة ${Math.abs(Math.floor(diff / (1000 * 60)))} دقيقة`;
        }
    }
}

function startLiveLessonNow(lessonId) {
    if (confirm('هل تريد بدء هذه الحصة الآن؟')) {
        fetch(`/api/live-lessons/${lessonId}/start/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUpdateNotification('تم بدء الحصة بنجاح');
                // سيتم نقل الحصة تلقائياً في التحديث التالي
            } else {
                alert('حدث خطأ أثناء بدء الحصة: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء بدء الحصة');
        });
    }
}

// دالة تحديث يدوي
function refreshPage() {
    showUpdateNotification('جاري تحديث الصفحة...');
    location.reload();
}

// عرض إشعار التحديث
function showUpdateNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 animate-bounce';
    notification.innerHTML = `<i class="fas fa-sync-alt ml-2"></i>${message}`;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// إضافة تأثيرات بصرية
document.addEventListener('DOMContentLoaded', function() {
    // إضافة classes للعناصر لسهولة التحديث باستخدام data attributes
    const liveLessonsCountElement = document.getElementById('live-count');
    if (liveLessonsCountElement) {
        liveLessonsCountElement.classList.add('live-lessons-count');
    }

    const scheduledLessonsCountElement = document.getElementById('scheduled-count');
    if (scheduledLessonsCountElement) {
        scheduledLessonsCountElement.classList.add('scheduled-lessons-count');
    }

    // إضافة class للحصص الجارية
    document.querySelectorAll('.border-red-200').forEach(card => {
        card.classList.add('live-lesson-card');
    });

    // إضافة classes للعناصر في الشريط العلوي
    const headerElements = document.querySelectorAll('span');
    headerElements.forEach(element => {
        if (element.textContent.includes('حصة جارية')) {
            element.classList.add('header-live-count');
        }
        if (element.textContent.includes('حصة مجدولة')) {
            element.classList.add('header-scheduled-count');
        }
    });

    // تأثير النبض للحصص الجارية
    const liveCards = document.querySelectorAll('.live-lesson-card');
    liveCards.forEach(card => {
        card.style.animation = 'pulse 3s infinite';
    });

    // بدء التحديث فوراً
    setTimeout(updateLiveLessonsStatus, 3000);

    // تحديث دوري كل 30 ثانية
    setInterval(updateLiveLessonsStatus, 30000);

    // عداد تنازلي للحصص القادمة
    updateCountdowns();
    setInterval(updateCountdowns, 60000); // تحديث كل دقيقة

    // التحقق من الحصص المنتهية تلقائياً كل دقيقة
    setInterval(checkAutoEndedLessons, 60000);

    // إضافة مؤشر التحديث التلقائي
    addAutoUpdateIndicator();
});

function updateCountdowns() {
    // تحديث العدادات التنازلية للحصص القادمة
    const scheduledLessons = document.querySelectorAll('[data-scheduled-time]');
    scheduledLessons.forEach(lesson => {
        const scheduledTime = new Date(lesson.dataset.scheduledTime);
        const now = new Date();
        const diff = scheduledTime - now;

        if (diff > 0) {
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;

            let timeText = '';
            if (hours > 0) {
                timeText = `${hours} ساعة و ${remainingMinutes} دقيقة`;
            } else {
                timeText = `${remainingMinutes} دقيقة`;
            }

            const countdownElement = lesson.querySelector('.countdown');
            if (countdownElement) {
                countdownElement.textContent = `تبدأ خلال ${timeText}`;
            }
        }
    });
}

function checkAutoEndedLessons() {
    // التحقق من الحصص المنتهية تلقائياً
    fetch('/api/admin/live-lessons-status/')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.auto_ended_count > 0) {
                // إظهار إشعار للمدير
                showUpdateNotification(`تم إنهاء ${data.auto_ended_count} حصة تلقائياً بعد انقضاء المدة المحددة`);

                // إعادة تحميل الصفحة لتحديث القائمة
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        })
        .catch(error => console.error('Error checking auto-ended lessons:', error));
}

// بدء حصة مباشرة الآن
function startLiveLessonNow(lessonId) {
    if (confirm('هل تريد بدء هذه الحصة الآن؟')) {
        fetch(`/api/admin/start-live-lesson/${lessonId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUpdateNotification('تم بدء الحصة بنجاح!');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                alert(data.message || 'حدث خطأ أثناء بدء الحصة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء بدء الحصة');
        });
    }
}

// بدء حصة مجدولة من الاشتراكات
function startSubscriptionLesson(lessonId) {
    if (confirm('هل تريد تحويل هذه الحصة المجدولة إلى حصة مباشرة وبدئها الآن؟')) {
        fetch(`/api/admin/start-subscription-lesson/${lessonId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUpdateNotification('تم تحويل الحصة وبدئها بنجاح!');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                alert(data.message || 'حدث خطأ أثناء تحويل الحصة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحويل الحصة');
        });
    }
}

function stopLiveLesson(lessonId) {
    if (confirm('هل أنت متأكد من إيقاف هذه الحصة؟ سيتم إخراج جميع المشاركين فوراً.')) {
        fetch(`/api/live-lessons/${lessonId}/end/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUpdateNotification('تم إيقاف الحصة بنجاح');
                // إعادة تحميل الصفحة لتحديث القائمة
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                alert('حدث خطأ أثناء إيقاف الحصة: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إيقاف الحصة');
        });
    }
}

// Helper function للحصول على CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function addAutoUpdateIndicator() {
    // إضافة مؤشر التحديث التلقائي
    const indicator = document.createElement('div');
    indicator.id = 'auto-update-indicator';
    indicator.className = 'fixed bottom-4 left-4 bg-blue-500 text-white px-3 py-2 rounded-lg shadow-lg text-sm z-50';
    indicator.innerHTML = `
        <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>تحديث تلقائي كل 30 ثانية</span>
        </div>
    `;
    document.body.appendChild(indicator);

    // إخفاء المؤشر بعد 5 ثوان
    setTimeout(() => {
        indicator.style.opacity = '0.7';
    }, 5000);
}

// تحديث كل 20 ثانية للحصص الجارية
setInterval(updateLiveLessonsStatus, 20000);

// تحديث تلقائي كامل كل 3 دقائق
setInterval(function() {
    showUpdateNotification('تحديث تلقائي شامل...');
    setTimeout(() => location.reload(), 1000);
}, 180000);

// ===== نظام الفلترة المتقدم =====

// تطبيق الفلاتر مباشرة عند التغيير
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.getElementById('filter-form');
    const teacherFilter = document.getElementById('teacher-filter');
    const studentFilter = document.getElementById('student-filter');
    const dateFilter = document.getElementById('date-filter');
    const lessonTypeFilter = document.getElementById('lesson-type-filter');
    const searchInput = document.getElementById('search-input');

    // تطبيق الفلترة عند تغيير أي فلتر
    [teacherFilter, studentFilter, dateFilter, lessonTypeFilter].forEach(filter => {
        filter.addEventListener('change', applyFiltersInstantly);
    });

    // تطبيق البحث مع تأخير
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(applyFiltersInstantly, 500);
    });
});

// تطبيق الفلاتر فوراً بدون إعادة تحميل الصفحة
function applyFiltersInstantly() {
    const formData = new FormData(document.getElementById('filter-form'));
    const params = new URLSearchParams(formData);

    // تحديث URL بدون إعادة تحميل
    const newUrl = window.location.pathname + '?' + params.toString();
    window.history.pushState({}, '', newUrl);

    // تحديث الإحصائيات عبر AJAX
    updateFilterStats(params);

    // إعادة تحميل الصفحة لتطبيق الفلاتر على المحتوى
    setTimeout(() => {
        window.location.reload();
    }, 300);
}

// تحديث إحصائيات الفلترة
function updateFilterStats(params) {
    fetch('/dashboard/admin/lessons/live/api/filter/?' + params.toString())
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('live-count').textContent = data.stats.total_live_lessons;
                document.getElementById('scheduled-count').textContent = data.stats.total_scheduled;
                document.getElementById('subscription-count').textContent = data.stats.total_scheduled_subscription;
                document.getElementById('total-count').textContent =
                    data.stats.total_live_lessons + data.stats.total_scheduled;

                // إضافة تأثير تحديث
                ['live-count', 'scheduled-count', 'subscription-count', 'total-count'].forEach(id => {
                    const element = document.getElementById(id);
                    element.classList.add('animate-pulse');
                    setTimeout(() => element.classList.remove('animate-pulse'), 1000);
                });
            }
        })
        .catch(error => console.error('Error updating filter stats:', error));
}

// مسح جميع الفلاتر
function clearAllFilters() {
    document.getElementById('teacher-filter').value = '';
    document.getElementById('student-filter').value = '';
    document.getElementById('date-filter').value = '';
    document.getElementById('lesson-type-filter').value = '';
    document.getElementById('search-input').value = '';

    // إعادة توجيه إلى الصفحة بدون معاملات
    window.location.href = window.location.pathname;
}

// تصدير النتائج كـ PDF
function exportPDF() {
    const formData = new FormData(document.getElementById('filter-form'));
    const params = new URLSearchParams(formData);

    // إنشاء رابط تحميل PDF
    const downloadUrl = '/dashboard/admin/lessons/live/export-pdf/?' + params.toString();

    // إنشاء عنصر رابط مخفي للتحميل
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = 'live_lessons_report.pdf';
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showUpdateNotification('جاري تحضير ملف PDF...');
}

// تحسين تجربة المستخدم - إضافة مؤشرات التحميل
function showFilterLoading() {
    const filterStats = document.getElementById('filter-stats');
    filterStats.style.opacity = '0.6';
    filterStats.style.pointerEvents = 'none';
}

function hideFilterLoading() {
    const filterStats = document.getElementById('filter-stats');
    filterStats.style.opacity = '1';
    filterStats.style.pointerEvents = 'auto';
}

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse-slow {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }
    .animate-pulse-slow {
        animation: pulse-slow 3s infinite;
    }

    /* تحسينات الفلاتر */
    .form-control:focus {
        box-shadow: 0 0 0 3px rgba(45, 80, 22, 0.1);
        border-color: #2D5016;
    }

    .form-group label {
        font-weight: 600;
        color: #374151;
    }

    #filter-stats > div {
        transition: all 0.3s ease;
    }

    #filter-stats > div:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* تحسين مظهر الأزرار */
    button[type="submit"], button[onclick*="export"], button[onclick*="PDF"] {
        transition: all 0.2s ease;
    }

    button[type="submit"]:hover, button[onclick*="export"]:hover, button[onclick*="PDF"]:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* تحسين أزرار التصدير */
    button[onclick*="export"], button[onclick*="PDF"] {
        position: relative;
        overflow: hidden;
    }

    button[onclick*="export"]:before, button[onclick*="PDF"]:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
    }

    button[onclick*="export"]:hover:before, button[onclick*="PDF"]:hover:before {
        width: 100%;
        height: 100%;
    }

    /* تحسين مظهر البحث */
    #search-input {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z'/%3e%3c/svg%3e");
        background-position: left 0.75rem center;
        background-repeat: no-repeat;
        background-size: 1.25rem 1.25rem;
        padding-left: 2.5rem;
    }

    /* تحسين الاستجابة */
    @media (max-width: 768px) {
        #filter-form {
            grid-template-columns: 1fr;
        }

        .form-group .flex {
            flex-direction: column;
        }

        .form-group .flex > * {
            width: 100%;
        }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
