"""
URLs للنظام الموحد للحصص
"""

from django.urls import path
from . import views_unified

# URLs للنظام الموحد
unified_patterns = [
    # ===== لوحات التحكم =====
    
    # لوحة تحكم المدير
    path('admin/dashboard/', views_unified.admin_lessons_dashboard, name='admin_lessons_dashboard'),
    
    # لوحة تحكم المعلم
    path('teacher/dashboard/', views_unified.teacher_lessons_dashboard, name='teacher_lessons_dashboard'),
    
    # لوحة تحكم الطالب
    path('student/dashboard/', views_unified.student_lessons_dashboard, name='student_lessons_dashboard'),
    
    # ===== API Endpoints =====
    
    # API للحصول على بيانات الحصص
    path('api/lessons/', views_unified.lessons_api, name='lessons_api'),
    
    # API لتفاصيل حصة محددة
    path('api/lessons/<int:lesson_id>/', views_unified.lesson_detail_api, name='lesson_detail_api'),
    
    # API لإجراءات الحصص
    path('api/lessons/<int:lesson_id>/cancel/', views_unified.cancel_lesson_api, name='cancel_lesson_api'),
    path('api/lessons/<int:lesson_id>/reschedule/', views_unified.reschedule_lesson_api, name='reschedule_lesson_api'),
    path('api/lessons/<int:lesson_id>/start/', views_unified.start_lesson_api, name='start_lesson_api'),
    path('api/lessons/<int:lesson_id>/end/', views_unified.end_lesson_api, name='end_lesson_api'),
    
    # API للتقارير والتقييمات
    path('api/lessons/<int:lesson_id>/teacher-report/', views_unified.submit_teacher_report_api, name='submit_teacher_report_api'),
    path('api/lessons/<int:lesson_id>/student-evaluation/', views_unified.submit_student_evaluation_api, name='submit_student_evaluation_api'),
    
    # ===== صفحات إنشاء الحصص =====
    
    # إنشاء حصة تجريبية (للمدير)
    path('admin/create-trial/', views_unified.admin_create_trial_lesson, name='admin_create_trial_lesson'),
    
    # إنشاء حصة من الاشتراك (للمدير)
    path('admin/create-subscription/', views_unified.admin_create_subscription_lesson, name='admin_create_subscription_lesson'),
    
    # حجز حصة تجريبية (للطالب)
    path('student/book-trial/', views_unified.student_book_trial_lesson, name='student_trial_lesson'),
    
    # حجز حصة من الاشتراك (للطالب)
    path('student/book-lesson/', views_unified.student_book_lesson, name='student_book_lesson'),
    
    # ===== غرفة الحصة =====
    
    # دخول غرفة الحصة
    path('room/<int:lesson_id>/', views_unified.lesson_room, name='lesson_room'),
    
    # ===== إدارة الحصص =====
    
    # تعديل حصة (للمدير)
    path('admin/edit/<int:lesson_id>/', views_unified.admin_edit_lesson, name='admin_edit_lesson'),
    
    # حذف حصة (للمدير)
    path('admin/delete/<int:lesson_id>/', views_unified.admin_delete_lesson, name='admin_delete_lesson'),
    
    # ===== طلبات الحصص التعويضية =====
    
    # عرض طلبات الحصص التعويضية (للمدير)
    path('admin/makeup-requests/', views_unified.admin_makeup_requests, name='admin_makeup_requests'),
    
    # الموافقة على طلب حصة تعويضية (للمدير)
    path('admin/makeup-requests/<int:request_id>/approve/', views_unified.approve_makeup_request, name='approve_makeup_request'),
    
    # رفض طلب حصة تعويضية (للمدير)
    path('admin/makeup-requests/<int:request_id>/reject/', views_unified.reject_makeup_request, name='reject_makeup_request'),
    
    # طلب حصة تعويضية (للطالب)
    path('student/request-makeup/<int:lesson_id>/', views_unified.request_makeup_lesson, name='request_makeup_lesson'),
    
    # ===== التقارير والإحصائيات =====
    
    # تقارير المدير
    path('admin/reports/', views_unified.admin_lesson_reports, name='admin_lesson_reports'),
    
    # تصدير البيانات
    path('admin/export/', views_unified.admin_export_lessons, name='admin_export_lessons'),
    
    # تقارير المعلم
    path('teacher/reports/', views_unified.teacher_reports, name='teacher_reports'),
    
    # إحصائيات المعلم
    path('teacher/performance/', views_unified.teacher_performance, name='teacher_performance'),
    
    # تقدم الطالب
    path('student/progress/', views_unified.student_progress, name='student_progress'),
    
    # شهادات الطالب
    path('student/certificates/', views_unified.student_certificates, name='student_certificates'),
    
    # ===== إدارة الأوقات المتاحة =====
    
    # إدارة أوقات المعلم المتاحة
    path('teacher/availability/', views_unified.teacher_availability, name='teacher_availability'),
    
    # تحديث الأوقات المتاحة
    path('teacher/availability/update/', views_unified.update_teacher_availability, name='update_teacher_availability'),
    
    # ===== إعادة الجدولة المتعددة =====
    
    # إعادة جدولة متعددة (للمدير)
    path('admin/bulk-reschedule/', views_unified.admin_bulk_reschedule, name='admin_bulk_reschedule'),
    
    # تطبيق إعادة الجدولة المتعددة
    path('admin/bulk-reschedule/apply/', views_unified.apply_bulk_reschedule, name='apply_bulk_reschedule'),
    
    # ===== التقييمات =====
    
    # عرض تقييمات المعلم
    path('teacher/evaluations/', views_unified.teacher_evaluations, name='teacher_evaluations'),
    
    # عرض تقييمات الطالب
    path('student/my-evaluations/', views_unified.student_evaluations, name='student_evaluations'),
    
    # تقييم حصة
    path('evaluate/<int:lesson_id>/', views_unified.evaluate_lesson, name='evaluate_lesson'),
    
    # ===== إدارة الطلاب (للمعلم) =====
    
    # عرض طلاب المعلم
    path('teacher/students/', views_unified.teacher_students, name='teacher_students'),
    
    # تفاصيل طالب
    path('teacher/students/<int:student_id>/', views_unified.teacher_student_detail, name='teacher_student_detail'),
    
    # ===== الملف الشخصي =====
    
    # الملف الشخصي للطالب
    path('student/profile/', views_unified.student_profile, name='student_profile'),
    
    # تحديث الملف الشخصي
    path('student/profile/update/', views_unified.update_student_profile, name='update_student_profile'),
    
    # ===== إعدادات الإشعارات =====
    
    # إعدادات إشعارات الحصص
    path('notifications/settings/', views_unified.lesson_notification_settings, name='lesson_notification_settings'),
    
    # تحديث إعدادات الإشعارات
    path('notifications/settings/update/', views_unified.update_notification_settings, name='update_notification_settings'),
]

# إضافة URLs للنظام الموحد إلى urlpatterns الرئيسي
urlpatterns = unified_patterns
