"""
أمر Django لتنظيف سجلات البريد الإلكتروني القديمة
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from users.email_models import EmailLog, EmailQueue


class Command(BaseCommand):
    help = 'تنظيف سجلات البريد الإلكتروني القديمة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='عدد الأيام للاحتفاظ بالسجلات (افتراضي: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='تشغيل تجريبي بدون حذف فعلي'
        )

    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        
        self.stdout.write(f"🧹 بدء تنظيف سجلات البريد الإلكتروني (أقدم من {days} يوم)")
        
        # تحديد التاريخ الحد
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # تنظيف سجلات البريد
        old_logs = EmailLog.objects.filter(sent_at__lt=cutoff_date)
        logs_count = old_logs.count()
        
        # تنظيف طابور البريد المكتمل
        old_queue = EmailQueue.objects.filter(
            created_at__lt=cutoff_date,
            status__in=['sent', 'failed']
        )
        queue_count = old_queue.count()
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'🧪 تشغيل تجريبي: كان سيتم حذف:'
                )
            )
            self.stdout.write(f'  📋 {logs_count} سجل بريد إلكتروني')
            self.stdout.write(f'  📬 {queue_count} عنصر من الطابور')
        else:
            # حذف السجلات
            if logs_count > 0:
                old_logs.delete()
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✅ تم حذف {logs_count} سجل بريد إلكتروني'
                    )
                )
            
            # حذف عناصر الطابور
            if queue_count > 0:
                old_queue.delete()
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✅ تم حذف {queue_count} عنصر من الطابور'
                    )
                )
            
            if logs_count == 0 and queue_count == 0:
                self.stdout.write(
                    self.style.SUCCESS('✅ لا توجد سجلات قديمة للحذف')
                )
        
        # عرض الإحصائيات الحالية
        remaining_logs = EmailLog.objects.count()
        remaining_queue = EmailQueue.objects.count()
        
        self.stdout.write(f"\n📊 الإحصائيات الحالية:")
        self.stdout.write(f"  📋 سجلات البريد المتبقية: {remaining_logs}")
        self.stdout.write(f"  📬 عناصر الطابور المتبقية: {remaining_queue}")
        
        self.stdout.write(
            self.style.SUCCESS('🎉 تم إنجاز عملية التنظيف')
        )
