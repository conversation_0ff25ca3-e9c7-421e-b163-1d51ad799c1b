"""
أمر Django لإرسال التقارير الإدارية اليومية
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from django.db.models import Count, Sum, Q
from users.email_service import EmailService
from users.email_models import EmailTemplate
from subscriptions.models import StudentSubscription, ScheduledLesson, SubscriptionPayment
from lessons.models import Lesson
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'إرسال التقارير الإدارية اليومية للمديرين'

    def add_arguments(self, parser):
        parser.add_argument(
            '--report-type',
            type=str,
            choices=['daily', 'weekly', 'monthly'],
            default='daily',
            help='نوع التقرير (daily, weekly, monthly)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='تشغيل تجريبي بدون إرسال فعلي'
        )

    def handle(self, *args, **options):
        report_type = options['report_type']
        dry_run = options['dry_run']
        
        self.stdout.write(f"📊 بدء إنشاء التقرير الإداري ({report_type})")
        
        # الحصول على قالب التقرير
        report_template = EmailTemplate.objects.filter(
            template_type='admin_report',
            is_active=True
        ).first()
        
        if not report_template:
            self.stdout.write(
                self.style.ERROR('❌ لا يوجد قالب تقرير إداري نشط')
            )
            return
        
        # تحديد الفترة الزمنية
        now = timezone.now()
        today = now.date()
        
        if report_type == 'daily':
            start_date = today
            end_date = today
            period_name = f"اليوم {today.strftime('%Y-%m-%d')}"
        elif report_type == 'weekly':
            start_date = today - timedelta(days=7)
            end_date = today
            period_name = f"الأسبوع من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}"
        elif report_type == 'monthly':
            start_date = today.replace(day=1)
            end_date = today
            period_name = f"الشهر {today.strftime('%Y-%m')}"
        
        # جمع البيانات الإحصائية
        stats = self._collect_statistics(start_date, end_date)
        
        # إعداد السياق
        context = {
            'report_type': report_type,
            'period_name': period_name,
            'current_date': today.strftime('%Y-%m-%d'),
            'current_time': now.strftime('%H:%M'),
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            **stats
        }
        
        # الحصول على المديرين
        admin_users = User.objects.filter(
            user_type='admin',
            is_active=True
        )
        
        if not admin_users.exists():
            self.stdout.write(
                self.style.WARNING('⚠️ لا يوجد مديرين نشطين')
            )
            return
        
        email_service = EmailService()
        sent_count = 0
        failed_count = 0
        
        # إرسال التقرير لكل مدير
        for admin in admin_users:
            try:
                admin_context = context.copy()
                admin_context.update({
                    'admin_name': admin.get_full_name(),
                    'admin_email': admin.email,
                })
                
                if not dry_run:
                    success = email_service.send_email(
                        recipient=admin,
                        template=report_template,
                        context=admin_context,
                        immediate=False
                    )
                    if success:
                        sent_count += 1
                    else:
                        failed_count += 1
                else:
                    sent_count += 1
                    self.stdout.write(
                        f"  📧 سيتم إرسال تقرير للمدير: {admin.email}"
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'❌ خطأ في إرسال تقرير للمدير {admin.email}: {str(e)}'
                    )
                )
                failed_count += 1
        
        # عرض النتائج
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'🧪 تشغيل تجريبي: كان سيتم إرسال {sent_count} تقرير'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'📧 تم إرسال {sent_count} تقرير بنجاح'
                )
            )
            if failed_count > 0:
                self.stdout.write(
                    self.style.ERROR(
                        f'❌ فشل في إرسال {failed_count} تقرير'
                    )
                )
        
        # عرض ملخص الإحصائيات
        self._display_statistics(stats)

    def _collect_statistics(self, start_date, end_date):
        """جمع الإحصائيات للفترة المحددة"""
        
        # تحويل التواريخ إلى datetime للمقارنة
        start_datetime = timezone.make_aware(
            timezone.datetime.combine(start_date, timezone.datetime.min.time())
        )
        end_datetime = timezone.make_aware(
            timezone.datetime.combine(end_date, timezone.datetime.max.time())
        )
        
        # إحصائيات المستخدمين
        new_registrations = User.objects.filter(
            date_joined__gte=start_datetime,
            date_joined__lte=end_datetime
        ).count()
        
        # إحصائيات الاشتراكات
        new_subscriptions = StudentSubscription.objects.filter(
            created_at__gte=start_datetime,
            created_at__lte=end_datetime
        ).count()
        
        active_subscriptions = StudentSubscription.objects.filter(
            status='active',
            end_date__gte=start_date
        ).count()
        
        expiring_subscriptions = StudentSubscription.objects.filter(
            status='active',
            end_date__lte=start_date + timedelta(days=7)
        ).count()
        
        # إحصائيات الحصص
        completed_lessons_scheduled = ScheduledLesson.objects.filter(
            completed_at__gte=start_datetime,
            completed_at__lte=end_datetime,
            status='completed'
        ).count()
        
        completed_lessons_unified = Lesson.objects.filter(
            ended_at__gte=start_datetime,
            ended_at__lte=end_datetime,
            status='completed'
        ).count()
        
        completed_lessons = completed_lessons_scheduled + completed_lessons_unified
        
        # إحصائيات المدفوعات
        try:
            payments = SubscriptionPayment.objects.filter(
                payment_date__gte=start_datetime,
                payment_date__lte=end_datetime,
                status='completed'
            )

            total_revenue = payments.aggregate(
                total=Sum('amount')
            )['total'] or 0
        except Exception:
            # في حالة عدم وجود نموذج المدفوعات أو مشكلة في الحقول
            total_revenue = 0
        
        # إحصائيات المعلمين والطلاب
        active_students = User.objects.filter(
            user_type='student',
            is_active=True
        ).count()
        
        active_teachers = User.objects.filter(
            user_type='teacher',
            is_active=True
        ).count()
        
        # إحصائيات التقييمات المعلقة
        try:
            pending_evaluations = Lesson.objects.filter(
                status='completed',
                student_evaluation_submitted=False
            ).count()
        except Exception:
            # في حالة عدم وجود حقل التقييم
            pending_evaluations = 0
        
        return {
            'new_registrations': new_registrations,
            'new_subscriptions': new_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expiring_subscriptions': expiring_subscriptions,
            'completed_lessons': completed_lessons,
            'active_students': active_students,
            'active_teachers': active_teachers,
            'total_revenue': float(total_revenue),
            'pending_evaluations': pending_evaluations,
            'admin_notes': f'تقرير تلقائي تم إنشاؤه في {timezone.now().strftime("%Y-%m-%d %H:%M")}',
        }

    def _display_statistics(self, stats):
        """عرض الإحصائيات في وحدة التحكم"""
        self.stdout.write("\n📊 ملخص الإحصائيات:")
        self.stdout.write(f"  👥 تسجيلات جديدة: {stats['new_registrations']}")
        self.stdout.write(f"  📝 اشتراكات جديدة: {stats['new_subscriptions']}")
        self.stdout.write(f"  ✅ اشتراكات نشطة: {stats['active_subscriptions']}")
        self.stdout.write(f"  ⚠️ اشتراكات تنتهي قريباً: {stats['expiring_subscriptions']}")
        self.stdout.write(f"  📚 حصص مكتملة: {stats['completed_lessons']}")
        self.stdout.write(f"  🎓 طلاب نشطين: {stats['active_students']}")
        self.stdout.write(f"  👨‍🏫 معلمين نشطين: {stats['active_teachers']}")
        self.stdout.write(f"  💰 إجمالي الإيرادات: {stats['total_revenue']}")
        self.stdout.write(f"  📋 تقييمات معلقة: {stats['pending_evaluations']}")
