"""
أمر Django لمعالجة جميع المهام المجدولة للبريد الإلكتروني
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.management import call_command
import io
import sys


class Command(BaseCommand):
    help = 'معالجة جميع المهام المجدولة للبريد الإلكتروني'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='تشغيل تجريبي بدون إرسال فعلي'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='عرض تفاصيل أكثر'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        current_time = timezone.now()
        self.stdout.write(
            self.style.SUCCESS(
                f"🚀 بدء معالجة المهام المجدولة - {current_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING("🧪 وضع التشغيل التجريبي مفعل")
            )
        
        total_processed = 0
        
        # 1. معالجة طابور البريد الإلكتروني
        self.stdout.write("\n📬 معالجة طابور البريد الإلكتروني...")
        try:
            if not dry_run:
                call_command('process_email_queue', verbosity=1 if verbose else 0)
            else:
                self.stdout.write("  🧪 تم تخطي معالجة الطابور (وضع تجريبي)")
            total_processed += 1
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ خطأ في معالجة الطابور: {str(e)}")
            )
        
        # 2. تذكيرات الحصص (24 ساعة)
        self.stdout.write("\n⏰ إرسال تذكيرات الحصص (24 ساعة)...")
        try:
            self._run_command_with_output(
                'send_lesson_reminders',
                ['--reminder-type=24h'] + (['--dry-run'] if dry_run else []),
                verbose
            )
            total_processed += 1
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ خطأ في تذكيرات 24 ساعة: {str(e)}")
            )
        
        # 3. تذكيرات الحصص (30 دقيقة)
        self.stdout.write("\n⏰ إرسال تذكيرات الحصص (30 دقيقة)...")
        try:
            self._run_command_with_output(
                'send_lesson_reminders',
                ['--reminder-type=30min'] + (['--dry-run'] if dry_run else []),
                verbose
            )
            total_processed += 1
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ خطأ في تذكيرات 30 دقيقة: {str(e)}")
            )
        
        # 4. تذكيرات الحصص (5 دقائق)
        self.stdout.write("\n⏰ إرسال تذكيرات الحصص (5 دقائق)...")
        try:
            self._run_command_with_output(
                'send_lesson_reminders',
                ['--reminder-type=5min'] + (['--dry-run'] if dry_run else []),
                verbose
            )
            total_processed += 1
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ خطأ في تذكيرات 5 دقائق: {str(e)}")
            )
        
        # 5. تنبيهات انتهاء الاشتراك (7 أيام)
        self.stdout.write("\n⚠️ إرسال تنبيهات انتهاء الاشتراك (7 أيام)...")
        try:
            self._run_command_with_output(
                'send_subscription_alerts',
                ['--alert-type=7d'] + (['--dry-run'] if dry_run else []),
                verbose
            )
            total_processed += 1
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ خطأ في تنبيهات 7 أيام: {str(e)}")
            )
        
        # 6. تنبيهات انتهاء الاشتراك (3 أيام)
        self.stdout.write("\n⚠️ إرسال تنبيهات انتهاء الاشتراك (3 أيام)...")
        try:
            self._run_command_with_output(
                'send_subscription_alerts',
                ['--alert-type=3d'] + (['--dry-run'] if dry_run else []),
                verbose
            )
            total_processed += 1
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ خطأ في تنبيهات 3 أيام: {str(e)}")
            )
        
        # 7. تنبيهات انتهاء الاشتراك (يوم واحد)
        self.stdout.write("\n⚠️ إرسال تنبيهات انتهاء الاشتراك (يوم واحد)...")
        try:
            self._run_command_with_output(
                'send_subscription_alerts',
                ['--alert-type=1d'] + (['--dry-run'] if dry_run else []),
                verbose
            )
            total_processed += 1
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ خطأ في تنبيهات يوم واحد: {str(e)}")
            )
        
        # 8. التقارير الإدارية اليومية (فقط في الساعة 6 صباحاً)
        if current_time.hour == 6:
            self.stdout.write("\n📊 إرسال التقارير الإدارية اليومية...")
            try:
                self._run_command_with_output(
                    'send_admin_reports',
                    ['--report-type=daily'] + (['--dry-run'] if dry_run else []),
                    verbose
                )
                total_processed += 1
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ خطأ في التقارير اليومية: {str(e)}")
                )
        else:
            self.stdout.write(
                f"\n📊 تخطي التقارير اليومية (الوقت الحالي: {current_time.hour}:00، المطلوب: 06:00)"
            )
        
        # 9. التقارير الأسبوعية (فقط يوم الأحد في الساعة 8 صباحاً)
        if current_time.weekday() == 6 and current_time.hour == 8:  # الأحد = 6
            self.stdout.write("\n📊 إرسال التقارير الأسبوعية...")
            try:
                self._run_command_with_output(
                    'send_admin_reports',
                    ['--report-type=weekly'] + (['--dry-run'] if dry_run else []),
                    verbose
                )
                total_processed += 1
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ خطأ في التقارير الأسبوعية: {str(e)}")
                )
        
        # 10. معالجة الطابور مرة أخيرة
        if not dry_run:
            self.stdout.write("\n📬 معالجة نهائية للطابور...")
            try:
                call_command('process_email_queue', verbosity=0)
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ خطأ في المعالجة النهائية: {str(e)}")
                )
        
        # عرض النتائج النهائية
        end_time = timezone.now()
        duration = (end_time - current_time).total_seconds()
        
        self.stdout.write("\n" + "="*60)
        self.stdout.write(
            self.style.SUCCESS(
                f"✅ تم إنجاز معالجة المهام المجدولة"
            )
        )
        self.stdout.write(f"⏱️ المدة: {duration:.2f} ثانية")
        self.stdout.write(f"📊 المهام المعالجة: {total_processed}")
        self.stdout.write(f"🕐 وقت الانتهاء: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.stdout.write("="*60)

    def _run_command_with_output(self, command_name, args, verbose):
        """تشغيل أمر مع التحكم في المخرجات"""
        if verbose:
            call_command(command_name, *args, verbosity=2)
        else:
            # التقاط المخرجات وعرض ملخص فقط
            old_stdout = sys.stdout
            sys.stdout = buffer = io.StringIO()
            
            try:
                call_command(command_name, *args, verbosity=1)
                output = buffer.getvalue()
                
                # عرض السطر الأخير فقط (النتيجة)
                lines = output.strip().split('\n')
                if lines:
                    last_line = lines[-1]
                    if '✅' in last_line or '📧' in last_line:
                        self.stdout.write(f"  {last_line}")
                    
            finally:
                sys.stdout = old_stdout
