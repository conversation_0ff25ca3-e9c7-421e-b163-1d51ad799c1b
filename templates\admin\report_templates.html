{% extends 'base.html' %}

{% block title %}قوالب التقارير - نظام قرآنيا التعليمي{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-gradient-to-r from-islamic-primary to-islamic-light shadow-lg border-b border-islamic-gold mb-6">
        <div class="px-6 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <a href="{% url 'admin_reports' %}" class="text-white hover:text-islamic-gold transition-colors ml-3">
                            <i class="fas fa-arrow-right text-xl"></i>
                        </a>
                        <h1 class="text-3xl font-bold text-white">
                            قوالب التقارير الجاهزة
                        </h1>
                    </div>
                    <p class="text-islamic-light-blue">قوالب محددة مسبقاً لإنشاء التقارير بسرعة</p>
                </div>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <button class="bg-islamic-gold text-islamic-dark px-4 py-2 rounded-lg hover:bg-yellow-400 transition-colors font-semibold">
                        <i class="fas fa-plus ml-1"></i>
                        إنشاء قالب جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="px-6">
        <!-- Templates Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for template in templates %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">{{ template.name }}</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-islamic-primary text-white">
                            {{ template.get_category_display }}
                        </span>
                    </div>
                    
                    <p class="text-gray-600 text-sm mb-4">{{ template.description|truncatechars:100 }}</p>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            {% if template.is_system_template %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-cog ml-1"></i>
                                قالب النظام
                            </span>
                            {% endif %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium {% if template.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if template.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </div>
                        
                        <button class="use-template-btn bg-islamic-primary text-white px-3 py-1 rounded text-sm hover:bg-islamic-light transition-colors" 
                                data-template-id="{{ template.id }}">
                            <i class="fas fa-play ml-1"></i>
                            استخدام
                        </button>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-span-full text-center py-12">
                <i class="fas fa-file-alt text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد قوالب متاحة</h3>
                <p class="text-gray-600 mb-4">ابدأ بإنشاء قالب جديد لتسهيل إنشاء التقارير</p>
                <button class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors">
                    <i class="fas fa-plus ml-1"></i>
                    إنشاء قالب جديد
                </button>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Use template buttons
    document.querySelectorAll('.use-template-btn').forEach(button => {
        button.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template-id');
            
            // Redirect to reports page with template parameter
            window.location.href = `{% url 'admin_reports' %}?template=${templateId}`;
        });
    });
});
</script>

{% csrf_token %}
{% endblock %}
