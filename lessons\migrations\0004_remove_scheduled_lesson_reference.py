# Generated by Django 4.2.7 on 2025-06-08 01:59

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0003_force_remove_monitoring_tables'),
    ]

    operations = [
        # حذف حقل scheduled_lesson من UnifiedLessonRating
        migrations.RunSQL(
            """
            -- حذف الحقل scheduled_lesson من جدول lessons_unifiedlessonrating
            PRAGMA foreign_keys = OFF;

            CREATE TABLE lessons_unifiedlessonrating_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                overall_rating INTEGER NOT NULL,
                lesson_quality INTEGER NOT NULL,
                teacher_interaction INTEGER NOT NULL,
                technical_quality INTEGER NOT NULL,
                punctuality INTEGER,
                lesson_preparation INTEGER,
                comment TEXT NOT NULL,
                attendance_quality_score REAL,
                lesson_duration_minutes INTEGER,
                status VARCHAR(20) NOT NULL DEFAULT 'submitted',
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                live_lesson_id INTEGER REFERENCES lessons_livelesson(id) ON DELETE CASCADE,
                student_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
                teacher_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE
            );

            -- نسخ البيانات (بدون scheduled_lesson)
            INSERT INTO lessons_unifiedlessonrating_new
            SELECT id, overall_rating, lesson_quality, teacher_interaction, technical_quality,
                   punctuality, lesson_preparation, comment, attendance_quality_score,
                   lesson_duration_minutes, status, created_at, updated_at,
                   live_lesson_id, student_id, teacher_id
            FROM lessons_unifiedlessonrating;

            -- حذف الجدول القديم وإعادة تسمية الجديد
            DROP TABLE lessons_unifiedlessonrating;
            ALTER TABLE lessons_unifiedlessonrating_new RENAME TO lessons_unifiedlessonrating;

            PRAGMA foreign_keys = ON;
            """,
            reverse_sql="-- لا يمكن التراجع عن هذا التغيير"
        ),
    ]
