{% extends 'base.html' %}
{% load static %}
{% load lesson_filters %}
{% load math_filters %}

{% block extra_css %}
<style>
    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .btn-secondary:hover {
        background: linear-gradient(135deg, #4b5563, #374151);
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
    }

    .form-control {
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        outline: none;
    }

    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }

    /* تقليل المساحات بين الأقسام */
    .stats-section {
        margin-bottom: 0.5rem !important;
    }

    .content-section {
        margin-top: 0 !important;
    }

    /* إزالة المساحات الإضافية من بطاقات الإحصائيات */
    .stats-section .grid {
        margin-bottom: 0 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-4">
        <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
                <h1 class="header-title text-2xl md:text-3xl font-bold text-blue-600 mb-2">
                    <i class="fas fa-calendar-alt text-orange-500 ml-2 md:ml-3"></i>
                    جدول حصصي
                </h1>
                <p class="text-sm md:text-base text-gray-600">عرض وإدارة جميع حصصك مع التقويم التفاعلي</p>
            </div>
                <button onclick="exportSchedule()" class="btn-primary flex items-center justify-center">
                    <i class="fas fa-download ml-2"></i>
                    تصدير الجدول
                </button>

                <button onclick="toggleFilters()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center">
                    <i class="fas fa-filter ml-2"></i>
                    الفلاتر
                </button>

                <button id="refreshCalendar" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center">
                    <i class="fas fa-sync-alt ml-2"></i>
                    تحديث
                </button>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div id="filtersSection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 md:p-6 mb-2 hidden">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-filter text-blue-600 ml-2"></i>
                فلاتر البحث
            </h3>
        <button onclick="clearAllFilters()" class="text-sm text-gray-500 hover:text-gray-700">
            <i class="fas fa-times ml-1"></i>
            مسح الكل
        </button>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <div>
            <label class="form-label">الطالب</label>
            <select id="studentFilter" class="form-control w-full">
                <option value="">جميع الطلاب</option>
                {% for student in active_students %}
                    <option value="{{ student.id }}">{{ student.get_full_name }}</option>
                {% endfor %}
            </select>
        </div>

        <div>
            <label class="form-label">الباقة</label>
            <select id="planFilter" class="form-control w-full">
                <option value="">جميع الباقات</option>
                {% for plan in available_plans %}
                    <option value="{{ plan.id }}">{{ plan.name }}</option>
                {% endfor %}
            </select>
        </div>

        <div>
            <label class="form-label">الحالة</label>
            <select id="statusFilter" class="form-control w-full">
                <option value="">جميع الحالات</option>
                <option value="scheduled">مجدولة</option>
                <option value="completed">مكتملة</option>
                <option value="cancelled">ملغية</option>
                <option value="rescheduled">معاد جدولتها</option>
                <option value="no_show">غياب</option>
                <option value="live">مباشرة</option>
            </select>
        </div>

        <div>
            <label class="form-label">الفترة الزمنية</label>
            <select id="dateRangeFilter" class="form-control w-full">
                <option value="">جميع الأوقات</option>
                <option value="today">اليوم</option>
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
                <option value="custom">فترة مخصصة</option>
            </select>
        </div>
    </div>
</div>

    <!-- Stats Section -->
    <div class="stats-section">
        {% include 'components/lesson_stats.html' with stats=stats user_type='teacher' %}
    </div>

    <div class="space-y-4 content-section">
        <!-- الحصة القادمة -->
    {% if next_lesson %}
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-blue-900">
                <i class="fas fa-star text-yellow-500 ml-2"></i>
                حصتك القادمة
            </h3>
            <span class="text-sm text-blue-700" data-time-until="{{ next_lesson.scheduled_date|date:'c' }}">
                <!-- سيتم تحديثها بـ JavaScript -->
            </span>
        </div>

        <div class="bg-white rounded-lg p-4 border border-blue-100">
            {% include 'components/lesson_card.html' with lesson=next_lesson user_type='teacher' %}
        </div>

        {% if next_lesson.scheduled_date|time_until_minutes <= 15 and next_lesson.scheduled_date|time_until_minutes >= 0 %}
        <div class="mt-4 flex items-center justify-center">
            <a href="{% url 'teacher_scheduled_lesson' next_lesson.id %}"
               class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center">
                <i class="fas fa-video ml-2"></i>
                بدء الحصة الآن
            </a>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- حصص اليوم -->
    {% if today_lessons %}
    <div class="bg-white rounded-xl shadow-sm border border-green-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-green-900">
                <i class="fas fa-calendar-day text-green-600 ml-2"></i>
                حصص اليوم ({{ today_lessons.count }})
            </h3>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                <span class="w-2 h-2 bg-green-600 rounded-full ml-2"></span>
                {{ today_lessons.count }} حصة
            </span>
        </div>
        <div class="space-y-3">
            {% for lesson in today_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- الحصص الجارية -->
    {% if live_lessons %}
    <div class="bg-white rounded-xl shadow-sm border border-red-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-red-900">
                <i class="fas fa-video text-red-600 ml-2"></i>
                الحصص الجارية
            </h3>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-red-100 text-red-800 animate-pulse">
                <span class="w-2 h-2 bg-red-600 rounded-full ml-2"></span>
                جارية الآن
            </span>
        </div>
        <div class="space-y-3">
            {% for lesson in live_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- التقويم التفاعلي -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-semibold text-gray-900 flex items-center">
                <i class="fas fa-calendar-alt text-blue-600 ml-2"></i>
                التقويم التفاعلي
                <span id="eventsCounter" class="bg-blue-100 text-blue-600 text-sm px-3 py-1 rounded-full mr-3">
                    0 حصة
                </span>
            </h3>
            <div class="flex items-center space-x-2 space-x-reverse">
                <button id="refreshCalendar" class="p-2 text-gray-500 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors" title="تحديث التقويم">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button id="prevMonth" class="p-2 text-gray-500 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <span id="currentMonth" class="text-lg font-medium text-gray-900 px-4"></span>
                <button id="nextMonth" class="p-2 text-gray-500 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span id="lastUpdate" class="text-xs text-gray-500 mr-3"></span>
            </div>
        </div>

        <!-- Calendar Container -->
        <div id="calendar" class="min-h-96"></div>

        <!-- Calendar Legend -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-3">دليل الألوان:</h4>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-3 text-xs">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full ml-2"></div>
                    <span>مجدولة</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full ml-2"></div>
                    <span>مكتملة</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-red-500 rounded-full ml-2"></div>
                    <span>ملغية</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-orange-500 rounded-full ml-2"></div>
                    <span>معاد جدولتها</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-purple-500 rounded-full ml-2"></div>
                    <span>حصة مباشرة</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-gray-500 rounded-full ml-2"></div>
                    <span>غياب</span>
                </div>
            </div>
        </div>
    </div>


    <!-- حصص الاشتراك -->
    {% if subscription_lessons %}
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-calendar text-blue-600 ml-2"></i>
                حصص الاشتراك ({{ subscription_lessons|length }})
            </h3>
            <button onclick="toggleSection('subscription-lessons')" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div id="subscription-lessons" class="space-y-3">
            {% for lesson in subscription_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- الحصص التجريبية -->
    {% if trial_lessons %}
    <div class="bg-white rounded-xl shadow-sm border border-orange-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-orange-900">
                <i class="fas fa-flask text-orange-600 ml-2"></i>
                الحصص التجريبية ({{ trial_lessons|length }})
            </h3>
            <button onclick="toggleSection('trial-lessons')" class="text-orange-600 hover:text-orange-800">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div id="trial-lessons" class="space-y-3">
            {% for lesson in trial_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- الحصص التعويضية -->
    {% if makeup_lessons %}
    <div class="bg-white rounded-xl shadow-sm border border-pink-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-pink-900">
                <i class="fas fa-redo text-pink-600 ml-2"></i>
                الحصص التعويضية ({{ makeup_lessons|length }})
            </h3>
            <button onclick="toggleSection('makeup-lessons')" class="text-pink-600 hover:text-pink-800">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div id="makeup-lessons" class="space-y-3">
            {% for lesson in makeup_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- الحصص المكتملة التي تحتاج تقارير -->
    {% if pending_reports %}
    <div class="bg-yellow-50 rounded-xl border border-yellow-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-yellow-900">
                <i class="fas fa-file-alt text-yellow-600 ml-2"></i>
                حصص تحتاج تقارير ({{ pending_reports|length }})
            </h3>
            <button onclick="writeAllReports()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm">
                <i class="fas fa-edit ml-1"></i>
                كتابة جميع التقارير
            </button>
        </div>
        <div class="space-y-3">
            {% for lesson in pending_reports %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='teacher' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- إجراءات سريعة للمعلم -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-tools text-blue-600 ml-2"></i>
            أدوات المعلم
        </h3>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <a href="{% url 'teacher_students' %}" class="bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center">
                <i class="fas fa-users ml-2"></i>
                طلابي
            </a>

            <button onclick="refreshCalendar()" class="bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors text-sm font-medium flex items-center justify-center">
                <i class="fas fa-sync-alt ml-2"></i>
                تحديث التقويم
            </button>

            <button onclick="exportSchedule()" class="bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium flex items-center justify-center">
                <i class="fas fa-download ml-2"></i>
                تصدير الجدول
            </button>
        </div>
    </div>

    <!-- رسالة عدم وجود حصص -->
    {% if not subscription_lessons and not trial_lessons and not makeup_lessons and not today_lessons %}
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="text-center py-12">
            <i class="fas fa-calendar-times text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد حصص مجدولة</h3>
            <p class="text-gray-500 mb-6">سيتم إشعارك عند جدولة حصص جديدة من قبل الإدارة</p>

            <div class="space-y-3">
                <a href="{% url 'teacher_students' %}" class="btn-primary inline-flex items-center">
                    <i class="fas fa-users ml-2"></i>
                    عرض طلابي
                </a>
                <p class="text-sm text-gray-600">أو</p>
                <button onclick="refreshCalendar()" class="btn-secondary inline-flex items-center">
                    <i class="fas fa-sync-alt ml-2"></i>
                    تحديث الصفحة
                </button>
            </div>
        </div>
    </div>
    {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
<script>
document.body.dataset.userType = 'teacher';

document.addEventListener('DOMContentLoaded', function() {
    // Initialize calendar
    const calendarEl = document.getElementById('calendar');
    const calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        locale: 'ar',
        direction: 'rtl',
        headerToolbar: false,
        height: 'auto',
        events: [
            // Load events from server
            {% for lesson in all_lessons %}
            {
                id: '{{ lesson.id }}',
                title: '{{ lesson.subscription.student.get_full_name }}',
                start: '{{ lesson.scheduled_date|date:"c" }}',
                backgroundColor: {% if lesson.status == 'scheduled' %}'#3b82f6'{% elif lesson.status == 'completed' %}'#10b981'{% elif lesson.status == 'cancelled' %}'#ef4444'{% elif lesson.status == 'live' %}'#8b5cf6'{% else %}'#6b7280'{% endif %},
                borderColor: {% if lesson.status == 'scheduled' %}'#1d4ed8'{% elif lesson.status == 'completed' %}'#059669'{% elif lesson.status == 'cancelled' %}'#dc2626'{% elif lesson.status == 'live' %}'#7c3aed'{% else %}'#4b5563'{% endif %},
                extendedProps: {
                    student: '{{ lesson.subscription.student.get_full_name }}',
                    plan: '{{ lesson.subscription.plan.name }}',
                    status: '{{ lesson.status }}',
                    duration: '{{ lesson.duration_minutes }}',
                    lessonNumber: '{{ lesson.lesson_number }}'
                }
            },
            {% endfor %}
        ],
        eventClick: function(info) {
            showLessonDetails(info.event.id);
        },
        eventDidMount: function(info) {
            // Add tooltip
            info.el.title = info.event.extendedProps.student + ' - ' + info.event.extendedProps.plan;
        }
    });

    calendar.render();

    // Update events counter
    updateEventsCounter();

    // Calendar navigation
    document.getElementById('prevMonth').addEventListener('click', function() {
        calendar.prev();
        updateCurrentMonth();
    });

    document.getElementById('nextMonth').addEventListener('click', function() {
        calendar.next();
        updateCurrentMonth();
    });

    document.getElementById('refreshCalendar').addEventListener('click', function() {
        calendar.refetchEvents();
        updateLastUpdate();
    });

    function updateCurrentMonth() {
        const currentDate = calendar.getDate();
        const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                           'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
        document.getElementById('currentMonth').textContent =
            monthNames[currentDate.getMonth()] + ' ' + currentDate.getFullYear();
    }

    function updateEventsCounter() {
        const events = calendar.getEvents();
        document.getElementById('eventsCounter').textContent = events.length + ' حصة';
    }

    function updateLastUpdate() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA');
        document.getElementById('lastUpdate').textContent = 'آخر تحديث: ' + timeString;
    }

    // Initialize
    updateCurrentMonth();
    updateLastUpdate();

    // Filters functionality
    const filters = {
        student: document.getElementById('studentFilter'),
        plan: document.getElementById('planFilter'),
        status: document.getElementById('statusFilter'),
        dateRange: document.getElementById('dateRangeFilter')
    };

    Object.values(filters).forEach(filter => {
        if (filter) {
            filter.addEventListener('change', applyFilters);
        }
    });

    function applyFilters() {
        // Apply filters to calendar
        // Will be implemented later
    }
});

// Global functions
function toggleFilters() {
    const filtersSection = document.getElementById('filtersSection');
    filtersSection.classList.toggle('hidden');
}

function clearAllFilters() {
    const filters = ['studentFilter', 'planFilter', 'statusFilter', 'dateRangeFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) filter.value = '';
    });
}

function showLessonDetails(lessonId) {
    // Will be implemented later
    console.log('Show lesson details for:', lessonId);
}

function exportSchedule() {
    // تصدير الجدول للطباعة
    window.print();
}

function viewAllStudents() {
    // الانتقال لصفحة الطلاب
    window.location.href = "{% url 'teacher_students' %}";
}

function refreshCalendar() {
    location.reload();
}

function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    const button = event.target;

    if (section.style.display === 'none') {
        section.style.display = 'block';
        button.innerHTML = '<i class="fas fa-chevron-down"></i>';
    } else {
        section.style.display = 'none';
        button.innerHTML = '<i class="fas fa-chevron-up"></i>';
    }
}

function writeAllReports() {
    // الانتقال لصفحة التقارير
    alert('يرجى كتابة التقارير من خلال صفحة كل حصة على حدة');
}

function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    dropdown.classList.toggle('hidden');

    // Close other dropdowns
    document.querySelectorAll('[id$="-actions"]').forEach(el => {
        if (el.id !== dropdownId) {
            el.classList.add('hidden');
        }
    });
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick*="toggleDropdown"]')) {
        document.querySelectorAll('[id$="-actions"]').forEach(el => {
            el.classList.add('hidden');
        });
    }
});

// Update next lesson countdown
function updateNextLessonCountdown() {
    const countdownElement = document.querySelector('[data-time-until]');
    if (countdownElement) {
        const targetTime = new Date(countdownElement.dataset.timeUntil);
        const now = new Date();
        const diff = targetTime - now;

        if (diff > 0) {
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

            if (hours > 0) {
                countdownElement.textContent = `خلال ${hours} ساعة و ${minutes} دقيقة`;
            } else if (minutes > 0) {
                countdownElement.textContent = `خلال ${minutes} دقيقة`;
            } else {
                countdownElement.textContent = 'حان الوقت!';
                countdownElement.classList.add('text-red-600', 'font-bold', 'animate-pulse');
            }
        } else {
            countdownElement.textContent = 'انتهى الوقت';
        }
    }
}

// Update countdown every minute
setInterval(updateNextLessonCountdown, 60000);
updateNextLessonCountdown(); // Run immediately

// Auto refresh for live lessons
setInterval(() => {
    if (document.querySelector('.animate-pulse')) {
        window.lessonsManager?.refreshLessonsData();
    }
}, 30000);
</script>
{% endblock %}
