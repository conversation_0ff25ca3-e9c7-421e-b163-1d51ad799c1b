from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth import get_user_model
from django.utils.html import format_html
from django.utils import timezone

from .models import (
    UserProfile, UserStatus, AcademySettings
)
from .admin_actions import UserAdminActions

User = get_user_model()


@admin.register(User)
class UserAdmin(BaseUserAdmin, UserAdminActions):
    """إدارة المستخدمين مع الإشعارات التلقائية"""

    list_display = [
        'username', 'email', 'get_full_name', 'user_type',
        'verification_status_display', 'ban_status_display',
        'is_active', 'date_joined', 'last_login'
    ]

    list_filter = [
        'user_type', 'verification_status', 'is_active',
        'is_banned', 'ban_type', 'date_joined'
    ]

    search_fields = ['username', 'email', 'first_name', 'last_name']

    ordering = ['-date_joined']

    actions = [
        'approve_users',
        'reject_users',
        'set_under_review',
        'ban_temporary_7days',
        'ban_temporary_30days',
        'ban_permanent',
        'unban_users',
        'delete_users_with_notification',
        'send_welcome_notification'
    ]

    fieldsets = BaseUserAdmin.fieldsets + (
        ('معلومات إضافية', {
            'fields': ('user_type', 'phone', 'date_of_birth', 'profile_picture', 'bio')
        }),
        ('معلومات الطلاب', {
            'fields': ('student_level',),
            'classes': ('collapse',)
        }),
        ('معلومات المعلمين', {
            'fields': (
                'hourly_rate_30', 'hourly_rate_45', 'hourly_rate_60',
                'commission_rate_30', 'commission_rate_45', 'commission_rate_60',
                'is_active_teacher'
            ),
            'classes': ('collapse',)
        }),
        ('حالة التحقق', {
            'fields': (
                'verification_status', 'verification_notes',
                'verified_by', 'verified_at', 'rejection_reason'
            )
        }),
        ('معلومات الحظر', {
            'fields': (
                'is_banned', 'ban_type', 'ban_reason',
                'banned_at', 'banned_until', 'banned_by'
            ),
            'classes': ('collapse',)
        }),
        ('حقول النظام', {
            'fields': ('was_active',),
            'classes': ('collapse',)
        }),
    )

    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('معلومات إضافية', {
            'fields': ('user_type', 'email', 'first_name', 'last_name')
        }),
    )

    readonly_fields = ['date_joined', 'last_login', 'verified_at', 'banned_at']

    def verification_status_display(self, obj):
        """عرض حالة التحقق مع ألوان"""
        colors = {
            'pending': '#ffc107',      # أصفر
            'approved': '#28a745',     # أخضر
            'rejected': '#dc3545',     # أحمر
            'under_review': '#17a2b8'  # أزرق
        }
        color = colors.get(obj.verification_status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_verification_status_display_ar()
        )
    verification_status_display.short_description = 'حالة التحقق'

    def ban_status_display(self, obj):
        """عرض حالة الحظر مع ألوان"""
        if not obj.is_banned:
            return format_html('<span style="color: #28a745;">غير محظور</span>')

        if obj.ban_type == 'permanent':
            return format_html('<span style="color: #dc3545; font-weight: bold;">محظور دائماً</span>')

        if obj.ban_type == 'temporary' and obj.banned_until:
            if timezone.now() >= obj.banned_until:
                return format_html('<span style="color: #ffc107;">انتهى الحظر</span>')
            else:
                return format_html(
                    '<span style="color: #fd7e14;">محظور حتى {}</span>',
                    obj.banned_until.strftime('%Y-%m-%d')
                )

        return format_html('<span style="color: #dc3545;">محظور</span>')
    ban_status_display.short_description = 'حالة الحظر'

    def get_queryset(self, request):
        """تحسين الاستعلامات"""
        return super().get_queryset(request).select_related(
            'verified_by', 'banned_by'
        )


@admin.register(AcademySettings)
class AcademySettingsAdmin(admin.ModelAdmin):
    """إدارة إعدادات الأكاديمية"""

    fieldsets = (
        ('معلومات الأكاديمية', {
            'fields': ('academy_name', 'academy_description', 'academy_logo')
        }),
        ('معلومات الاتصال', {
            'fields': ('contact_email', 'contact_phone', 'contact_address')
        }),
        ('إعدادات البريد الإلكتروني', {
            'fields': (
                'smtp_enabled', 'smtp_host', 'smtp_port', 'smtp_use_tls',
                'smtp_use_ssl', 'smtp_username', 'smtp_from_email', 'smtp_from_name'
            )
        }),
        ('إعدادات النظام', {
            'fields': ('timezone', 'language', 'currency')
        }),
    )

    def has_add_permission(self, request):
        """منع إضافة إعدادات متعددة"""
        return not AcademySettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        """منع حذف الإعدادات"""
        return False


# تسجيل النماذج الأخرى
admin.site.register(UserProfile)
admin.site.register(UserStatus)


# ==================== إدارة نماذج البريد الإلكتروني ====================

from .email_models import EmailSettings, EmailTemplate, EmailEvent, EmailQueue, EmailLog, EmailSubscription


@admin.register(EmailSettings)
class EmailSettingsAdmin(admin.ModelAdmin):
    """إدارة إعدادات البريد الإلكتروني"""

    list_display = ['provider', 'smtp_host', 'from_email', 'is_active', 'created_at']
    list_filter = ['provider', 'is_active', 'use_tls', 'use_ssl']
    search_fields = ['smtp_host', 'from_email', 'from_name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('معلومات المزود', {
            'fields': ('provider', 'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password')
        }),
        ('إعدادات التشفير', {
            'fields': ('use_tls', 'use_ssl')
        }),
        ('إعدادات الإرسال', {
            'fields': ('from_email', 'from_name', 'max_emails_per_hour')
        }),
        ('إعدادات النظام', {
            'fields': ('is_active',)
        }),
        ('معلومات إضافية', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(EmailTemplate)
class EmailTemplateAdmin(admin.ModelAdmin):
    """إدارة قوالب البريد الإلكتروني"""

    list_display = ['name', 'template_type', 'is_active', 'is_default', 'created_at']
    list_filter = ['template_type', 'is_active', 'is_default']
    search_fields = ['name', 'subject']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('معلومات القالب', {
            'fields': ('name', 'template_type', 'subject')
        }),
        ('محتوى القالب', {
            'fields': ('html_content', 'text_content')
        }),
        ('إعدادات القالب', {
            'fields': ('is_active', 'is_default', 'variables')
        }),
        ('معلومات إضافية', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(EmailEvent)
class EmailEventAdmin(admin.ModelAdmin):
    """إدارة أحداث البريد الإلكتروني"""

    list_display = ['get_event_type_display', 'template', 'is_active', 'send_delay']
    list_filter = ['is_active', 'event_type']
    search_fields = ['event_type']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(EmailQueue)
class EmailQueueAdmin(admin.ModelAdmin):
    """إدارة طابور البريد الإلكتروني"""

    list_display = ['recipient', 'subject', 'status', 'scheduled_at', 'attempts']
    list_filter = ['status', 'scheduled_at', 'attempts']
    search_fields = ['recipient__email', 'subject']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(EmailLog)
class EmailLogAdmin(admin.ModelAdmin):
    """إدارة سجلات البريد الإلكتروني"""

    list_display = ['recipient', 'subject', 'status', 'sent_at']
    list_filter = ['status', 'sent_at']
    search_fields = ['recipient__email', 'subject']
    readonly_fields = ['created_at']

    def has_add_permission(self, request):
        """منع إضافة سجلات يدوياً"""
        return False

    def has_change_permission(self, request, obj=None):
        """منع تعديل السجلات"""
        return False


@admin.register(EmailSubscription)
class EmailSubscriptionAdmin(admin.ModelAdmin):
    """إدارة اشتراكات البريد الإلكتروني"""

    list_display = ['user', 'email_notifications_enabled', 'lesson_reminders', 'subscription_notifications']
    list_filter = ['email_notifications_enabled', 'lesson_reminders', 'subscription_notifications']
    search_fields = ['user__email', 'user__first_name', 'user__last_name']
    readonly_fields = ['created_at', 'updated_at']
