{% extends 'base.html' %}
{% load static %}

{% block title %}إرسال رسالة نظام{% endblock %}

{% block extra_css %}
<style>
    .message-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .form-input, .form-select, .form-textarea {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e5e7eb;
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        padding: 0.75rem 2rem;
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
        padding: 0.75rem 2rem;
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.9);
        padding: 1.5rem;
        border-radius: 0.75rem;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #3b82f6;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .priority-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }

    .priority-low { background: #f3f4f6; color: #6b7280; }
    .priority-medium { background: #dbeafe; color: #1d4ed8; }
    .priority-high { background: #fed7aa; color: #ea580c; }
    .priority-urgent { background: #fecaca; color: #dc2626; }
</style>
{% endblock %}

{% block content %}
<div class="message-form">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">
                <i class="fas fa-broadcast-tower ml-3"></i>
                إرسال رسالة نظام
            </h1>
            <p class="text-white text-opacity-90 text-lg">إرسال رسائل النظام للمعلمين والطلاب</p>
        </div>

        <!-- Statistics -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ students_count }}</div>
                    <div class="stat-label">
                        <i class="fas fa-user-graduate text-blue-500 ml-1"></i>
                        طالب نشط
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ teachers_count }}</div>
                    <div class="stat-label">
                        <i class="fas fa-chalkboard-teacher text-green-500 ml-1"></i>
                        معلم نشط
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ students_count|add:teachers_count }}</div>
                    <div class="stat-label">
                        <i class="fas fa-users text-purple-500 ml-1"></i>
                        إجمالي المستخدمين
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="max-w-4xl mx-auto">
            <div class="form-card rounded-2xl shadow-2xl p-8">
                <form method="POST" class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Recipients Selection -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-users text-blue-600 ml-2"></i>
                            اختيار المستقبلين
                        </label>
                        <select name="recipient_type" class="form-select" required>
                            <option value="all">جميع المستخدمين ({{ students_count|add:teachers_count }})</option>
                            <option value="students">الطلاب فقط ({{ students_count }})</option>
                            <option value="teachers">المعلمين فقط ({{ teachers_count }})</option>
                        </select>
                    </div>

                    <!-- Message Type -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tag text-green-600 ml-2"></i>
                            نوع الرسالة
                        </label>
                        <select name="message_type" class="form-select" required>
                            {% for value, label in message_types %}
                            <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Priority -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-exclamation-triangle text-orange-600 ml-2"></i>
                            الأولوية
                        </label>
                        <select name="priority" class="form-select" required>
                            {% for value, label in priority_levels %}
                            <option value="{{ value }}" {% if value == 'medium' %}selected{% endif %}>
                                {{ label }}
                                {% if value == 'urgent' %}<span class="priority-badge priority-urgent">عاجل</span>{% endif %}
                                {% if value == 'high' %}<span class="priority-badge priority-high">عالي</span>{% endif %}
                                {% if value == 'medium' %}<span class="priority-badge priority-medium">متوسط</span>{% endif %}
                                {% if value == 'low' %}<span class="priority-badge priority-low">منخفض</span>{% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Title -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-heading text-purple-600 ml-2"></i>
                            عنوان الرسالة
                        </label>
                        <input type="text" name="title" class="form-input" 
                               placeholder="أدخل عنوان الرسالة..." required maxlength="200">
                    </div>

                    <!-- Content -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-edit text-indigo-600 ml-2"></i>
                            محتوى الرسالة
                        </label>
                        <textarea name="content" class="form-textarea" rows="8" 
                                  placeholder="اكتب محتوى الرسالة هنا..." required></textarea>
                    </div>

                    <!-- Expiry Date -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-calendar-times text-red-600 ml-2"></i>
                            تاريخ انتهاء الصلاحية (اختياري)
                        </label>
                        <input type="datetime-local" name="expires_at" class="form-input">
                        <small class="text-gray-600 text-sm mt-1 block">
                            اتركه فارغاً إذا كانت الرسالة دائمة
                        </small>
                    </div>

                    <!-- Buttons -->
                    <div class="flex items-center justify-between pt-6">
                        <a href="{% url 'admin_support' %}" class="btn-secondary">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة
                        </a>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-paper-plane ml-2"></i>
                            إرسال رسالة النظام
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const messageTypeSelect = document.querySelector('select[name="message_type"]');
    const prioritySelect = document.querySelector('select[name="priority"]');
    
    // Auto-adjust priority based on message type
    messageTypeSelect.addEventListener('change', function() {
        const messageType = this.value;
        
        if (messageType === 'warning' || messageType === 'urgent') {
            prioritySelect.value = 'urgent';
        } else if (messageType === 'maintenance' || messageType === 'policy') {
            prioritySelect.value = 'high';
        } else if (messageType === 'reminder') {
            prioritySelect.value = 'medium';
        }
    });
    
    // Character counter for title
    const titleInput = document.querySelector('input[name="title"]');
    const titleCounter = document.createElement('small');
    titleCounter.className = 'text-gray-500 text-sm mt-1 block';
    titleInput.parentNode.appendChild(titleCounter);
    
    titleInput.addEventListener('input', function() {
        const remaining = 200 - this.value.length;
        titleCounter.textContent = `${remaining} حرف متبقي`;
        titleCounter.style.color = remaining < 20 ? '#ef4444' : '#6b7280';
    });
    
    // Initial count
    titleInput.dispatchEvent(new Event('input'));
});
</script>
{% endblock %}
