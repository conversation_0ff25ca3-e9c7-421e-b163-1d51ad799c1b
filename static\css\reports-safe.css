/* تصميم صفحة التقارير - متوافق مع نظام الألوان الإسلامي للموقع */

/* تحسين التبويبات */
.reports-page .report-tab {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.reports-page .report-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.reports-page .report-tab:hover::before {
    transform: translateX(100%);
}

.reports-page .report-tab.active {
    background: linear-gradient(135deg, #2D5016 0%, #4A7C59 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(45, 80, 22, 0.3);
    transform: translateY(-2px);
}

.reports-page .report-tab:not(.active) {
    background: #F0F8F0;
    color: #2D5016;
}

.reports-page .report-tab:not(.active):hover {
    background: #E8F4FD;
    color: #1A3009;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(45, 80, 22, 0.1);
}

/* بطاقات الإحصائيات المحسنة */
.reports-page .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.reports-page .stat-card-enhanced {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.reports-page .stat-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #2D5016;
    transition: width 0.3s ease;
}

.reports-page .stat-card-enhanced:hover::before {
    width: 8px;
}

.reports-page .stat-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 28px rgba(45, 80, 22, 0.12);
    border-color: #4A7C59;
}

.reports-page .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 20px;
    color: white;
}

.reports-page .stat-icon.teachers { background: linear-gradient(135deg, #2D5016 0%, #4A7C59 100%); }
.reports-page .stat-icon.students { background: linear-gradient(135deg, #50C878 0%, #9CAF88 100%); }
.reports-page .stat-icon.courses { background: linear-gradient(135deg, #D4AF37 0%, #F4E4BC 100%); color: #1A3009 !important; }
.reports-page .stat-icon.financial { background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%); }

.reports-page .stat-value {
    font-size: 28px;
    font-weight: 700;
    color: #1A3009;
    margin-bottom: 4px;
    background: linear-gradient(135deg, #2D5016 0%, #4A7C59 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.reports-page .stat-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

.reports-page .stat-change {
    display: flex;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    font-weight: 600;
}

.reports-page .stat-change.positive { color: #10b981; }
.reports-page .stat-change.negative { color: #ef4444; }
.reports-page .stat-change.neutral { color: #64748b; }

/* تحسين لوحة المعلومات */
.reports-page .dashboard-overview {
    background: linear-gradient(135deg, #2D5016 0%, #4A7C59 100%);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    color: white;
    position: relative;
    overflow: hidden;
}

.reports-page .dashboard-overview::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.reports-page .overview-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
}

.reports-page .overview-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 20px;
}

.reports-page .overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.reports-page .overview-stat {
    text-align: center;
    padding: 16px;
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.reports-page .overview-stat-value {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 4px;
}

.reports-page .overview-stat-label {
    font-size: 12px;
    opacity: 0.8;
}

/* أنماط الرسوم البيانية */
.reports-page .chart-container {
    position: relative;
    height: 300px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e5e7eb;
}

.reports-page .chart-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
}

.reports-page .chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #1A3009;
    margin-bottom: 16px;
    text-align: center;
}

/* تحسين الجداول */
.reports-page .reports-table {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    border: 1px solid #e5e7eb;
}

.reports-page .table-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.reports-page .table-title {
    font-size: 18px;
    font-weight: 600;
    color: #1A3009;
    margin-bottom: 4px;
}

.reports-page .table-subtitle {
    font-size: 14px;
    color: #64748b;
}

.reports-page .table-content {
    max-height: 400px;
    overflow-y: auto;
}

.reports-page .table-row {
    padding: 16px 20px;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.reports-page .table-row:hover {
    background: linear-gradient(90deg, rgba(45, 80, 22, 0.05) 0%, transparent 100%);
}

.reports-page .table-row:last-child {
    border-bottom: none;
}

/* تحسين التبويبات */
.reports-page .tab-content {
    display: none;
}

.reports-page .tab-content.active {
    display: block !important;
}

.reports-page .tab-content:not(.hidden) {
    display: block !important;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .reports-page .report-tab {
        padding: 12px 8px;
        font-size: 0.875rem;
    }

    .reports-page .report-tab i {
        font-size: 1rem;
        margin-bottom: 4px;
    }

    .reports-page .stat-card-enhanced {
        padding: 16px;
    }

    .reports-page .table-row {
        padding: 12px;
    }
}
