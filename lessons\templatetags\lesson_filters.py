"""
Template filters للنظام الموحد للحصص
"""

from django import template
from django.utils import timezone
from datetime import timedelta

register = template.Library()


@register.filter
def time_until_minutes(value):
    """حساب الوقت المتبقي بالدقائق"""
    if not value:
        return 0
    
    now = timezone.now()
    if isinstance(value, str):
        # تحويل النص إلى datetime إذا لزم الأمر
        try:
            from django.utils.dateparse import parse_datetime
            value = parse_datetime(value)
        except:
            return 0
    
    if value <= now:
        return 0
    
    diff = value - now
    return int(diff.total_seconds() / 60)


@register.filter
def time_since_minutes(value):
    """حساب الوقت المنقضي بالدقائق"""
    if not value:
        return 0
    
    now = timezone.now()
    if isinstance(value, str):
        try:
            from django.utils.dateparse import parse_datetime
            value = parse_datetime(value)
        except:
            return 0
    
    if value >= now:
        return 0
    
    diff = now - value
    return int(diff.total_seconds() / 60)


@register.filter
def lesson_status_color(status):
    """إرجاع لون CSS حسب حالة الحصة"""
    colors = {
        'scheduled': 'blue',
        'live': 'green',
        'completed': 'emerald',
        'rated': 'purple',
        'cancelled_by_student': 'red',
        'cancelled_by_teacher': 'red',
        'cancelled_by_admin': 'red',
        'rescheduled': 'orange',
        'no_show_student': 'gray',
        'no_show_teacher': 'gray',
    }
    return colors.get(status, 'gray')


@register.filter
def lesson_type_color(lesson_type):
    """إرجاع لون CSS حسب نوع الحصة"""
    colors = {
        'subscription': 'blue',
        'trial': 'orange',
        'makeup': 'pink',
    }
    return colors.get(lesson_type, 'gray')


@register.filter
def lesson_type_icon(lesson_type):
    """إرجاع أيقونة حسب نوع الحصة"""
    icons = {
        'subscription': 'fas fa-calendar',
        'trial': 'fas fa-flask',
        'makeup': 'fas fa-redo',
    }
    return icons.get(lesson_type, 'fas fa-question')


@register.filter
def lesson_status_icon(status):
    """إرجاع أيقونة حسب حالة الحصة"""
    icons = {
        'scheduled': 'fas fa-clock',
        'live': 'fas fa-video',
        'completed': 'fas fa-check-circle',
        'rated': 'fas fa-star',
        'cancelled_by_student': 'fas fa-user-times',
        'cancelled_by_teacher': 'fas fa-chalkboard-teacher',
        'cancelled_by_admin': 'fas fa-user-shield',
        'rescheduled': 'fas fa-calendar-alt',
        'no_show_student': 'fas fa-user-slash',
        'no_show_teacher': 'fas fa-chalkboard-teacher',
    }
    return icons.get(status, 'fas fa-question')


@register.filter
def can_join_lesson(lesson):
    """تحديد إمكانية دخول الحصة"""
    if lesson.status != 'scheduled':
        return lesson.status == 'live'
    
    now = timezone.now()
    time_diff = lesson.scheduled_date - now
    
    # يمكن الدخول قبل 15 دقيقة من الموعد
    return time_diff.total_seconds() <= 900  # 15 دقيقة


@register.filter
def can_cancel_lesson(lesson, user):
    """تحديد إمكانية إلغاء الحصة"""
    if lesson.status not in ['scheduled']:
        return False
    
    # المدير يمكنه الإلغاء دائماً
    if hasattr(user, 'is_admin') and user.is_admin():
        return True
    
    # التحقق من الوقت المتبقي
    now = timezone.now()
    time_until = lesson.scheduled_date - now
    
    # لا يمكن الإلغاء قبل الحصة بأقل من ساعة
    return time_until.total_seconds() > 3600


@register.filter
def format_duration(minutes):
    """تنسيق المدة بالساعات والدقائق"""
    if not minutes:
        return "0 دقيقة"
    
    hours = minutes // 60
    remaining_minutes = minutes % 60
    
    if hours > 0:
        if remaining_minutes > 0:
            return f"{hours} ساعة و {remaining_minutes} دقيقة"
        else:
            return f"{hours} ساعة"
    else:
        return f"{remaining_minutes} دقيقة"


@register.filter
def lesson_progress_percentage(lesson):
    """حساب نسبة تقدم الحصة"""
    if lesson.status in ['scheduled']:
        return 0
    elif lesson.status == 'live':
        if lesson.started_at:
            now = timezone.now()
            elapsed = now - lesson.started_at
            total_duration = timedelta(minutes=lesson.duration_minutes)
            progress = (elapsed.total_seconds() / total_duration.total_seconds()) * 100
            return min(100, max(0, progress))
        return 0
    elif lesson.status in ['completed', 'rated']:
        return 100
    else:
        return 0


@register.filter
def next_lesson_countdown(lesson):
    """عد تنازلي للحصة القادمة"""
    if not lesson or lesson.status != 'scheduled':
        return ""
    
    now = timezone.now()
    time_diff = lesson.scheduled_date - now
    
    if time_diff.total_seconds() <= 0:
        return "حان الوقت!"
    
    days = time_diff.days
    hours = time_diff.seconds // 3600
    minutes = (time_diff.seconds % 3600) // 60
    
    if days > 0:
        return f"{days} يوم و {hours} ساعة"
    elif hours > 0:
        return f"{hours} ساعة و {minutes} دقيقة"
    else:
        return f"{minutes} دقيقة"


@register.filter
def is_lesson_overdue(lesson):
    """تحديد إذا كانت الحصة متأخرة"""
    if lesson.status != 'scheduled':
        return False
    
    now = timezone.now()
    return lesson.scheduled_date < now


@register.filter
def lesson_urgency_class(lesson):
    """إرجاع CSS class حسب إلحاح الحصة"""
    if lesson.status != 'scheduled':
        return ''
    
    now = timezone.now()
    time_diff = lesson.scheduled_date - now
    minutes_until = time_diff.total_seconds() / 60
    
    if minutes_until <= 0:
        return 'lesson-overdue'
    elif minutes_until <= 15:
        return 'lesson-urgent'
    elif minutes_until <= 60:
        return 'lesson-soon'
    else:
        return ''


@register.simple_tag
def lesson_can_be_rated(lesson, user):
    """تحديد إمكانية تقييم الحصة"""
    if lesson.status != 'completed':
        return False
    
    if user.user_type == 'student':
        return not lesson.student_evaluation_submitted
    elif user.user_type == 'teacher':
        return not lesson.teacher_report_submitted
    
    return False


@register.simple_tag
def get_lesson_participants(lesson, exclude_user=None):
    """الحصول على مشاركي الحصة"""
    participants = [lesson.teacher, lesson.student]
    
    if exclude_user:
        participants = [p for p in participants if p != exclude_user]
    
    return participants


@register.filter
def multiply(value, arg):
    """ضرب قيمتين"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0
