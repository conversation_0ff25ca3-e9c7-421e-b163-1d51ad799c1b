/* Sidebar Fix CSS - إصلاحات القائمة الجانبية */

/* ========================================
   🔧 إصلاحات عامة للقائمة الجانبية
======================================== */

/* إخفاء القوائم الجانبية القديمة */
.sidebar-toggle:not(.new-sidebar-toggle),
.sidebar-overlay:not(.new-sidebar-overlay),
.mobile-sidebar:not(.new-sidebar),
.desktop-sidebar:not(.new-sidebar) {
    display: none !important;
}

/* إصلاح تضارب الهوامش */
.lg\:mr-64 {
    margin-right: 0 !important;
}

@media (min-width: 1024px) {
    .lg\:mr-64 {
        margin-right: 280px !important; /* عرض القائمة الجانبية */
    }
}

/* ========================================
   📱 إصلاحات الموبايل
======================================== */

/* إصلاح z-index للقائمة الجانبية */
.new-sidebar {
    z-index: 9999 !important;
}

.new-sidebar-overlay {
    z-index: 9998 !important;
}

.new-sidebar-toggle {
    z-index: 10000 !important;
}

/* إصلاح التمرير على الموبايل */
@media (max-width: 768px) {
    body.sidebar-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }
    
    .new-sidebar-open {
        transform: translateX(0) !important;
    }
    
    .new-sidebar-overlay.active {
        opacity: 1 !important;
        visibility: visible !important;
    }
}

/* ========================================
   🎯 إصلاحات التفاعل
======================================== */

/* إصلاح النقر على الروابط */
.nav-link {
    pointer-events: auto !important;
    cursor: pointer !important;
}

.nav-link:hover {
    text-decoration: none !important;
}

/* إصلاح الأيقونات */
.nav-icon i,
.footer-nav-icon i {
    pointer-events: none;
}

/* ========================================
   🔄 إصلاحات الانتقالات
======================================== */

/* تحسين انتقالات القائمة الجانبية */
.new-sidebar {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.new-sidebar-overlay {
    transition: opacity 0.3s ease, visibility 0.3s ease !important;
}

/* إصلاح انتقالات الروابط */
.nav-link,
.footer-nav-link {
    transition: all 0.2s ease !important;
}

/* ========================================
   🎨 إصلاحات التصميم
======================================== */

/* إصلاح الألوان المتضاربة */
.text-islamic-primary {
    color: #2D5016 !important;
}

.text-islamic-gold {
    color: #d4af37 !important;
}

.bg-islamic-primary {
    background-color: #2D5016 !important;
}

.bg-islamic-gold {
    background-color: #d4af37 !important;
}

/* إصلاح الحدود */
.border-islamic-primary {
    border-color: #1a365d !important;
}

.border-islamic-gold {
    border-color: #d4af37 !important;
}

/* ========================================
   📐 إصلاحات التخطيط
======================================== */

/* إصلاح المحتوى الرئيسي */
.main-content {
    transition: margin-right 0.3s ease !important;
}

@media (min-width: 1024px) {
    .main-content {
        margin-right: 280px !important;
    }
}

/* إصلاح الحاويات */
.container-fluid {
    padding-right: 15px !important;
    padding-left: 15px !important;
}

/* ========================================
   🔍 إصلاحات الرؤية
======================================== */

/* إصلاح النصوص المخفية */
.nav-text,
.footer-nav-text {
    opacity: 1 !important;
    visibility: visible !important;
}

/* إصلاح الأيقونات المخفية */
.nav-icon,
.footer-nav-icon {
    opacity: 1 !important;
    visibility: visible !important;
}

/* ========================================
   ⚡ إصلاحات الأداء
======================================== */

/* تحسين الرسم */
.new-sidebar,
.new-sidebar-content,
.new-sidebar-nav {
    will-change: transform !important;
    backface-visibility: hidden !important;
}

/* تحسين التمرير */
.new-sidebar-nav {
    -webkit-overflow-scrolling: touch !important;
    scrollbar-width: thin !important;
}

/* ========================================
   🛠️ إصلاحات متنوعة
======================================== */

/* إصلاح التركيز */
.nav-link:focus,
.footer-nav-link:focus {
    outline: 2px solid #d4af37 !important;
    outline-offset: 2px !important;
}

/* إصلاح النقر المزدوج */
.nav-link,
.footer-nav-link {
    user-select: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* إصلاح الخطوط */
.nav-text,
.footer-nav-text {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500 !important;
}

/* إصلاح الاتجاه */
.new-sidebar {
    direction: rtl !important;
}

.new-sidebar * {
    direction: rtl !important;
}

/* ========================================
   🎯 إصلاحات خاصة بالمتصفحات
======================================== */

/* إصلاحات Safari */
@supports (-webkit-appearance: none) {
    .new-sidebar {
        -webkit-transform: translateZ(0) !important;
    }
}

/* إصلاحات Firefox */
@-moz-document url-prefix() {
    .new-sidebar-nav {
        scrollbar-width: thin !important;
        scrollbar-color: #d4af37 transparent !important;
    }
}

/* إصلاحات Edge */
@supports (-ms-ime-align: auto) {
    .new-sidebar {
        -ms-overflow-style: -ms-autohiding-scrollbar !important;
    }
}

/* ========================================
   🔧 إصلاحات الطوارئ
======================================== */

/* إصلاح عدم ظهور القائمة */
.new-sidebar.force-show {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* إصلاح عدم عمل الروابط */
.nav-link.force-active,
.footer-nav-link.force-active {
    pointer-events: auto !important;
    cursor: pointer !important;
    display: flex !important;
}

/* إصلاح التداخل */
.new-sidebar.fix-overlap {
    z-index: 99999 !important;
}

/* إصلاح عدم التمرير */
.new-sidebar-nav.fix-scroll {
    overflow-y: auto !important;
    max-height: calc(100vh - 200px) !important;
}
