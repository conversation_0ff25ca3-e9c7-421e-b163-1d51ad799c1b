# Generated by Django 4.2.7 on 2025-06-30 02:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0008_remove_email_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='WhatsAppSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enabled', models.BooleanField(default=False, help_text='تفعيل أو إلغاء تفعيل نظام إشعارات WhatsApp', verbose_name='تفعيل WhatsApp')),
                ('gupshup_api_key', models.CharField(blank=True, help_text='مفتاح API من منصة Gupshup', max_length=200, verbose_name='Gupshup API Key')),
                ('gupshup_app_name', models.Char<PERSON><PERSON>(blank=True, help_text='اسم التطبيق المسجل في Gupshup', max_length=100, verbose_name='اسم التطبيق')),
                ('source_number', models.CharField(blank=True, help_text='رقم WhatsApp المرسل (اختياري)', max_length=20, verbose_name='رقم المرسل')),
                ('daily_limit', models.PositiveIntegerField(default=1000, help_text='عدد الرسائل المسموح إرسالها يومياً', verbose_name='الحد اليومي للرسائل')),
                ('retry_attempts', models.PositiveIntegerField(default=3, help_text='عدد المحاولات عند فشل الإرسال', verbose_name='عدد المحاولات')),
                ('auto_lesson_reminders', models.BooleanField(default=True, help_text='إرسال تذكيرات تلقائية للحصص', verbose_name='تذكيرات الحصص التلقائية')),
                ('auto_subscription_notifications', models.BooleanField(default=True, help_text='إرسال إشعارات تلقائية للاشتراكات', verbose_name='إشعارات الاشتراكات التلقائية')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'إعدادات WhatsApp',
                'verbose_name_plural': 'إعدادات WhatsApp',
            },
        ),
        migrations.CreateModel(
            name='WhatsAppTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='اسم القالب كما هو مسجل في منصة Gupshup', max_length=100, unique=True, verbose_name='اسم القالب في Gupshup')),
                ('display_name', models.CharField(help_text='الاسم المعروض في النظام', max_length=100, verbose_name='الاسم المعروض')),
                ('description', models.TextField(blank=True, help_text='وصف مختصر للقالب', verbose_name='الوصف')),
                ('template_type', models.CharField(choices=[('lesson_reminder', 'تذكيرات الحصص'), ('subscription_notification', 'إشعارات الاشتراكات'), ('account_notification', 'إشعارات الحساب'), ('general_notification', 'إشعارات عامة'), ('manual_message', 'رسائل يدوية')], max_length=50, verbose_name='نوع القالب')),
                ('variables', models.JSONField(default=dict, help_text='قائمة المتغيرات المطلوبة للقالب مع أوصافها', verbose_name='المتغيرات المطلوبة')),
                ('is_active', models.BooleanField(default=True, help_text='هل القالب نشط ومتاح للاستخدام', verbose_name='نشط')),
                ('auto_trigger_events', models.JSONField(default=list, help_text='قائمة الأحداث التي تؤدي لإرسال هذا القالب تلقائياً', verbose_name='الأحداث التلقائية')),
                ('usage_count', models.PositiveIntegerField(default=0, verbose_name='عدد مرات الاستخدام')),
                ('last_used_at', models.DateTimeField(blank=True, null=True, verbose_name='آخر استخدام')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
            ],
            options={
                'verbose_name': 'قالب WhatsApp',
                'verbose_name_plural': 'قوالب WhatsApp',
                'ordering': ['template_type', 'display_name'],
            },
        ),
        migrations.CreateModel(
            name='WhatsAppMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recipient_phone', models.CharField(help_text='رقم WhatsApp المرسل إليه', max_length=20, verbose_name='رقم الهاتف')),
                ('template_name', models.CharField(help_text='اسم القالب المستخدم في Gupshup', max_length=100, verbose_name='اسم القالب')),
                ('variables_sent', models.JSONField(default=dict, help_text='المتغيرات التي تم إرسالها مع القالب', verbose_name='المتغيرات المرسلة')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('sent', 'تم الإرسال'), ('delivered', 'تم التسليم'), ('read', 'تم القراءة'), ('failed', 'فشل الإرسال'), ('cancelled', 'تم الإلغاء')], default='pending', max_length=20, verbose_name='حالة الرسالة')),
                ('gupshup_message_id', models.CharField(blank=True, max_length=100, verbose_name='معرف الرسالة في Gupshup')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('delivered_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التسليم')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('retry_count', models.PositiveIntegerField(default=0, verbose_name='عدد المحاولات')),
                ('message_type', models.CharField(choices=[('lesson_reminder_30min', 'تذكير حصة - 30 دقيقة'), ('lesson_reminder_1day', 'تذكير حصة - يوم واحد'), ('lesson_cancelled', 'إلغاء حصة'), ('lesson_rescheduled', 'إعادة جدولة حصة'), ('subscription_activated', 'تفعيل اشتراك'), ('subscription_expiry_warning', 'تحذير انتهاء اشتراك'), ('subscription_expired', 'انتهاء اشتراك'), ('account_welcome', 'رسالة ترحيب'), ('account_suspended', 'تعليق حساب'), ('manual_message', 'رسالة يدوية')], max_length=50, verbose_name='نوع الرسالة')),
                ('related_lesson_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الحصة المرتبطة')),
                ('related_subscription_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الاشتراك المرتبط')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='whatsapp_messages', to=settings.AUTH_USER_MODEL, verbose_name='المستقبل')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.whatsapptemplate', verbose_name='القالب')),
            ],
            options={
                'verbose_name': 'رسالة WhatsApp',
                'verbose_name_plural': 'رسائل WhatsApp',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ScheduledWhatsAppMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('template_name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('variables', models.JSONField(default=dict, verbose_name='متغيرات القالب')),
                ('scheduled_time', models.DateTimeField(verbose_name='وقت الإرسال المجدول')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('sent', 'تم الإرسال'), ('failed', 'فشل الإرسال'), ('cancelled', 'تم الإلغاء'), ('expired', 'منتهي الصلاحية')], default='pending', max_length=20, verbose_name='حالة الجدولة')),
                ('event_type', models.CharField(help_text='نوع الحدث الذي أدى لجدولة هذه الرسالة', max_length=50, verbose_name='نوع الحدث')),
                ('related_object_type', models.CharField(blank=True, max_length=50, verbose_name='نوع الكائن المرتبط')),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الكائن المرتبط')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال الفعلي')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('retry_count', models.PositiveIntegerField(default=0, verbose_name='عدد المحاولات')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('auto_cancel_after', models.DateTimeField(blank=True, help_text='إلغاء الرسالة تلقائياً إذا لم ترسل قبل هذا التاريخ', null=True, verbose_name='إلغاء تلقائي بعد')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scheduled_whatsapp_messages', to=settings.AUTH_USER_MODEL, verbose_name='المستقبل')),
                ('sent_message', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.whatsappmessage', verbose_name='الرسالة المرسلة')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.whatsapptemplate', verbose_name='القالب')),
            ],
            options={
                'verbose_name': 'رسالة WhatsApp مجدولة',
                'verbose_name_plural': 'رسائل WhatsApp مجدولة',
                'ordering': ['scheduled_time'],
            },
        ),
        migrations.AddIndex(
            model_name='whatsappmessage',
            index=models.Index(fields=['status', 'created_at'], name='users_whats_status_e34b0a_idx'),
        ),
        migrations.AddIndex(
            model_name='whatsappmessage',
            index=models.Index(fields=['recipient', 'message_type'], name='users_whats_recipie_23dc7d_idx'),
        ),
        migrations.AddIndex(
            model_name='whatsappmessage',
            index=models.Index(fields=['template_name', 'sent_at'], name='users_whats_templat_39864d_idx'),
        ),
        migrations.AddIndex(
            model_name='scheduledwhatsappmessage',
            index=models.Index(fields=['status', 'scheduled_time'], name='users_sched_status_c1e8cf_idx'),
        ),
        migrations.AddIndex(
            model_name='scheduledwhatsappmessage',
            index=models.Index(fields=['event_type', 'created_at'], name='users_sched_event_t_7a9d0d_idx'),
        ),
        migrations.AddIndex(
            model_name='scheduledwhatsappmessage',
            index=models.Index(fields=['recipient', 'status'], name='users_sched_recipie_57f511_idx'),
        ),
    ]
