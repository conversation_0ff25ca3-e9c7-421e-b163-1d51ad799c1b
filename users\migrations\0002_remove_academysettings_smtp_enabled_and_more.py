# Generated by Django 4.2.7 on 2025-06-04 23:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_enabled',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_from_email',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_from_name',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_host',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_password',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_port',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_provider',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_reply_to',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_use_ssl',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_use_tls',
        ),
        migrations.RemoveField(
            model_name='academysettings',
            name='smtp_username',
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_api_key',
            field=models.TextField(blank=True, null=True, verbose_name='مفتاح API (مشفر)'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_api_region',
            field=models.CharField(blank=True, choices=[('us-east-1', 'US East (N. Virginia)'), ('us-west-2', 'US West (Oregon)'), ('eu-west-1', 'Europe (Ireland)'), ('eu-central-1', 'Europe (Frankfurt)'), ('ap-southeast-1', 'Asia Pacific (Singapore)'), ('ap-northeast-1', 'Asia Pacific (Tokyo)')], max_length=50, null=True, verbose_name='المنطقة (للخدمات التي تحتاجها)'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_api_secret',
            field=models.TextField(blank=True, null=True, verbose_name='المفتاح السري (مشفر)'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_from_email',
            field=models.EmailField(blank=True, max_length=255, null=True, verbose_name='البريد المرسل'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_from_name',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم المرسل'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_reply_to',
            field=models.EmailField(blank=True, max_length=255, null=True, verbose_name='الرد على'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_service_enabled',
            field=models.BooleanField(default=False, verbose_name='تفعيل خدمة البريد الإلكتروني'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_service_provider',
            field=models.CharField(choices=[('sendinblue', 'SendinBlue (Brevo)'), ('amazon_ses', 'Amazon SES'), ('sendgrid', 'SendGrid'), ('mailgun', 'Mailgun'), ('postmark', 'Postmark'), ('mailjet', 'Mailjet'), ('sparkpost', 'SparkPost'), ('mandrill', 'Mandrill (Mailchimp)'), ('elastic_email', 'Elastic Email'), ('pepipost', 'Pepipost (Netcore)'), ('socketlabs', 'SocketLabs'), ('mailersend', 'MailerSend'), ('resend', 'Resend'), ('loops', 'Loops'), ('plunk', 'Plunk'), ('emailjs', 'EmailJS'), ('smtp2go', 'SMTP2GO'), ('turbosmtp', 'turboSMTP'), ('mailpace', 'Mailpace'), ('courier', 'Courier')], default='sendinblue', max_length=50, verbose_name='مزود خدمة البريد الإلكتروني'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_webhook_secret',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='مفتاح Webhook السري'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='email_webhook_url',
            field=models.URLField(blank=True, null=True, verbose_name='رابط Webhook للإحصائيات'),
        ),
    ]
