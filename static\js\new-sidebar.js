/**
 * 🚀 القائمة الجانبية الجديدة الموحدة والمتجاوبة
 * تعمل على جميع الأجهزة: Mobile, Tablet, Desktop
 * 
 * المميزات:
 * - تجاوب مثالي على جميع الأجهزة
 * - انتقالات سلسة وجميلة
 * - دعم اللمس والكيبورد
 * - حفظ حالة القائمة
 * - أداء محسن
 */

class NewSidebar {
    constructor() {
        // العناصر الأساسية
        this.sidebar = document.getElementById('newSidebar');
        this.toggle = document.getElementById('newSidebarToggle');
        this.close = document.getElementById('newSidebarClose');
        this.overlay = document.getElementById('newSidebarOverlay');
        
        // الحالة
        this.isOpen = false;
        this.currentBreakpoint = this.getCurrentBreakpoint();
        
        // الإعدادات
        this.settings = {
            storageKey: 'newSidebarState',
            animationDuration: 300,
            touchThreshold: 50,
            keyboardSupport: true
        };
        
        // التهيئة
        this.init();
    }
    
    /**
     * تهيئة القائمة الجانبية
     */
    init() {
        if (!this.sidebar) {
            console.warn('New Sidebar: العناصر المطلوبة غير موجودة');
            return;
        }
        
        // تحميل الحالة المحفوظة
        this.loadState();
        
        // ربط الأحداث
        this.bindEvents();
        
        // تطبيق الحالة الأولية
        this.applyInitialState();
        
        // إضافة دعم الوصولية
        this.setupAccessibility();
        
        console.log('New Sidebar: تم التهيئة بنجاح');
    }
    
    /**
     * تحديد نقطة الكسر الحالية
     */
    getCurrentBreakpoint() {
        const width = window.innerWidth;
        if (width < 768) return 'mobile';
        if (width < 1024) return 'tablet';
        return 'desktop';
    }
    
    /**
     * ربط جميع الأحداث
     */
    bindEvents() {
        // أحداث النقر
        if (this.toggle) {
            this.toggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSidebar();
            });
        }
        
        if (this.close) {
            this.close.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeSidebar();
            });
        }
        
        if (this.overlay) {
            this.overlay.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeSidebar();
            });
        }
        
        // أحداث تغيير حجم الشاشة
        const debouncedResize = this.debounce(() => {
            this.handleResize();
        }, 250);
        
        window.addEventListener('resize', debouncedResize);
        
        // أحداث الكيبورد
        if (this.settings.keyboardSupport) {
            document.addEventListener('keydown', (e) => {
                this.handleKeyboard(e);
            });
        }
        
        // أحداث اللمس
        this.setupTouchEvents();
        
        // أحداث التركيز
        this.setupFocusEvents();
    }
    
    /**
     * فتح القائمة الجانبية
     */
    openSidebar() {
        if (this.isOpen || this.currentBreakpoint === 'desktop') return;
        
        this.isOpen = true;
        
        // تطبيق الفئات
        this.sidebar?.classList.add('active');
        this.overlay?.classList.add('active');
        this.toggle?.classList.add('active');
        
        // منع التمرير على الموبايل
        if (this.currentBreakpoint === 'mobile') {
            document.body.classList.add('new-sidebar-open');
        }
        
        // تحديث ARIA
        this.updateAriaStates(true);
        
        // حفظ الحالة
        this.saveState();
        
        // تركيز على أول رابط
        this.focusFirstLink();
        
        // إطلاق حدث مخصص
        this.dispatchEvent('sidebarOpened');
    }
    
    /**
     * إغلاق القائمة الجانبية
     */
    closeSidebar() {
        if (!this.isOpen || this.currentBreakpoint === 'desktop') return;
        
        this.isOpen = false;
        
        // إزالة الفئات
        this.sidebar?.classList.remove('active');
        this.overlay?.classList.remove('active');
        this.toggle?.classList.remove('active');
        
        // السماح بالتمرير
        document.body.classList.remove('new-sidebar-open');
        
        // تحديث ARIA
        this.updateAriaStates(false);
        
        // حفظ الحالة
        this.saveState();
        
        // إعادة التركيز لزر التحكم
        this.toggle?.focus();
        
        // إطلاق حدث مخصص
        this.dispatchEvent('sidebarClosed');
    }
    
    /**
     * تبديل حالة القائمة الجانبية
     */
    toggleSidebar() {
        if (this.isOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }
    
    /**
     * التعامل مع تغيير حجم الشاشة
     */
    handleResize() {
        const newBreakpoint = this.getCurrentBreakpoint();
        const oldBreakpoint = this.currentBreakpoint;
        
        this.currentBreakpoint = newBreakpoint;
        
        // إذا انتقلنا للكمبيوتر، أغلق القائمة
        if (newBreakpoint === 'desktop' && oldBreakpoint !== 'desktop') {
            this.closeSidebar();
            document.body.classList.remove('new-sidebar-open');
        }
        
        // إذا انتقلنا من الكمبيوتر، طبق الحالة المحفوظة
        if (oldBreakpoint === 'desktop' && newBreakpoint !== 'desktop') {
            this.loadState();
        }
        
        // إطلاق حدث مخصص
        this.dispatchEvent('sidebarResized', { 
            oldBreakpoint, 
            newBreakpoint 
        });
    }
    
    /**
     * التعامل مع أحداث الكيبورد
     */
    handleKeyboard(e) {
        // ESC لإغلاق القائمة
        if (e.key === 'Escape' && this.isOpen) {
            e.preventDefault();
            this.closeSidebar();
        }

        // Ctrl+M لتبديل القائمة
        if (e.ctrlKey && e.key === 'm') {
            e.preventDefault();
            this.toggleSidebar();
        }
        
        // Tab trapping عند فتح القائمة
        if (e.key === 'Tab' && this.isOpen && this.currentBreakpoint !== 'desktop') {
            this.handleTabTrapping(e);
        }
    }
    
    /**
     * إعداد أحداث اللمس
     */
    setupTouchEvents() {
        let startX = 0;
        let startY = 0;
        let isSwipeGesture = false;
        
        // بداية اللمس
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isSwipeGesture = false;
        }, { passive: true });
        
        // حركة اللمس
        document.addEventListener('touchmove', (e) => {
            if (!startX || !startY) return;
            
            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            
            const diffX = startX - currentX;
            const diffY = startY - currentY;
            
            // تحديد إذا كانت حركة أفقية
            if (Math.abs(diffX) > Math.abs(diffY)) {
                isSwipeGesture = true;
            }
        }, { passive: true });
        
        // نهاية اللمس
        document.addEventListener('touchend', (e) => {
            if (!startX || !startY || !isSwipeGesture) return;
            
            const endX = e.changedTouches[0].clientX;
            const diffX = startX - endX;
            
            // سحب من اليمين لفتح القائمة
            if (diffX < -this.settings.touchThreshold && startX < 50 && !this.isOpen) {
                this.openSidebar();
            }

            // سحب لليسار لإغلاق القائمة
            if (diffX > this.settings.touchThreshold && this.isOpen) {
                this.closeSidebar();
            }
            
            // إعادة تعيين القيم
            startX = 0;
            startY = 0;
            isSwipeGesture = false;
        }, { passive: true });
    }
    
    /**
     * إعداد أحداث التركيز
     */
    setupFocusEvents() {
        // التركيز على القائمة عند فتحها
        this.sidebar?.addEventListener('focusin', () => {
            if (this.currentBreakpoint !== 'desktop') {
                this.sidebar.classList.add('focused');
            }
        });
        
        this.sidebar?.addEventListener('focusout', () => {
            this.sidebar.classList.remove('focused');
        });
    }
    
    /**
     * إعداد الوصولية
     */
    setupAccessibility() {
        // تحديث ARIA labels
        if (this.toggle) {
            this.toggle.setAttribute('aria-expanded', 'false');
            this.toggle.setAttribute('aria-controls', 'newSidebar');
        }
        
        if (this.sidebar) {
            this.sidebar.setAttribute('aria-hidden', 'true');
        }
        
        if (this.overlay) {
            this.overlay.setAttribute('aria-hidden', 'true');
        }
    }
    
    /**
     * تحديث حالات ARIA
     */
    updateAriaStates(isOpen) {
        if (this.toggle) {
            this.toggle.setAttribute('aria-expanded', isOpen.toString());
        }
        
        if (this.sidebar) {
            this.sidebar.setAttribute('aria-hidden', (!isOpen).toString());
        }
        
        if (this.overlay) {
            this.overlay.setAttribute('aria-hidden', (!isOpen).toString());
        }
    }
    
    /**
     * التركيز على أول رابط
     */
    focusFirstLink() {
        if (this.currentBreakpoint === 'desktop') return;
        
        const firstLink = this.sidebar?.querySelector('.nav-link');
        if (firstLink) {
            setTimeout(() => {
                firstLink.focus();
            }, this.settings.animationDuration);
        }
    }
    
    /**
     * التعامل مع Tab trapping
     */
    handleTabTrapping(e) {
        const focusableElements = this.sidebar?.querySelectorAll(
            'a, button, [tabindex]:not([tabindex="-1"])'
        );
        
        if (!focusableElements || focusableElements.length === 0) return;
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }
    
    /**
     * تطبيق الحالة الأولية
     */
    applyInitialState() {
        if (this.currentBreakpoint === 'desktop') {
            this.isOpen = false;
            return;
        }
        
        if (this.isOpen) {
            this.openSidebar();
        } else {
            this.closeSidebar();
        }
    }
    
    /**
     * حفظ الحالة
     */
    saveState() {
        if (this.currentBreakpoint === 'desktop') return;
        
        try {
            localStorage.setItem(this.settings.storageKey, JSON.stringify({
                isOpen: this.isOpen,
                timestamp: Date.now()
            }));
        } catch (e) {
            console.warn('New Sidebar: فشل في حفظ الحالة', e);
        }
    }
    
    /**
     * تحميل الحالة
     */
    loadState() {
        if (this.currentBreakpoint === 'desktop') {
            this.isOpen = false;
            return;
        }
        
        try {
            const saved = localStorage.getItem(this.settings.storageKey);
            if (saved) {
                const state = JSON.parse(saved);
                // تحميل الحالة فقط إذا كانت حديثة (أقل من يوم)
                if (Date.now() - state.timestamp < 24 * 60 * 60 * 1000) {
                    this.isOpen = state.isOpen || false;
                } else {
                    this.isOpen = false;
                }
            } else {
                this.isOpen = false;
            }
        } catch (e) {
            console.warn('New Sidebar: فشل في تحميل الحالة', e);
            this.isOpen = false;
        }
    }
    
    /**
     * إطلاق حدث مخصص
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, {
            detail: {
                sidebar: this,
                isOpen: this.isOpen,
                breakpoint: this.currentBreakpoint,
                ...detail
            }
        });
        
        document.dispatchEvent(event);
    }
    
    /**
     * Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * تدمير القائمة الجانبية
     */
    destroy() {
        // إزالة جميع الأحداث
        // (يمكن إضافة المزيد حسب الحاجة)
        
        // إزالة الفئات
        document.body.classList.remove('new-sidebar-open');
        
        // إطلاق حدث التدمير
        this.dispatchEvent('sidebarDestroyed');
        
        console.log('New Sidebar: تم التدمير');
    }
    
    /**
     * الحصول على معلومات القائمة
     */
    getInfo() {
        return {
            isOpen: this.isOpen,
            breakpoint: this.currentBreakpoint,
            version: '1.0.0'
        };
    }
}

// تهيئة القائمة الجانبية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // التأكد من وجود العناصر المطلوبة
    if (document.getElementById('newSidebar')) {
        window.newSidebar = new NewSidebar();
        
        // إضافة للكونسول للتطوير
        if (typeof window !== 'undefined') {
            window.newSidebar.debug = true;
        }
    }
});

// تصدير الكلاس للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NewSidebar;
}
