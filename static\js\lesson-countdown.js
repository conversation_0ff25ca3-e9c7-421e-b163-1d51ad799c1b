/**
 * نظام العداد التنازلي للحصص المباشرة
 * يدير العداد التنازلي لمدة الحصة وإشعارات انتهاء الوقت
 */

class LessonCountdown {
    constructor(lessonId, durationMinutes, startTime = null) {
        this.lessonId = lessonId;
        this.durationMinutes = durationMinutes;
        this.startTime = startTime ? new Date(startTime) : new Date();
        this.endTime = new Date(this.startTime.getTime() + (durationMinutes * 60 * 1000));
        this.interval = null;
        this.warningShown = false;
        this.finalWarningShown = false;
        
        console.log('🕐 تم إنشاء عداد تنازلي للحصة:', {
            lessonId: this.lessonId,
            duration: this.durationMinutes,
            startTime: this.startTime,
            endTime: this.endTime
        });
    }

    /**
     * بدء العداد التنازلي
     */
    start() {
        if (this.interval) {
            clearInterval(this.interval);
        }

        this.createTimerDisplay();
        this.updateDisplay();
        
        this.interval = setInterval(() => {
            this.updateDisplay();
        }, 1000);

        console.log('▶️ تم بدء العداد التنازلي');
    }

    /**
     * إيقاف العداد التنازلي
     */
    stop() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        console.log('⏹️ تم إيقاف العداد التنازلي');
    }

    /**
     * إنشاء عرض العداد في الواجهة
     */
    createTimerDisplay() {
        // البحث عن عنصر العداد الموجود أو إنشاء واحد جديد
        let timerElement = document.getElementById('lesson-countdown-timer');
        
        if (!timerElement) {
            timerElement = document.createElement('div');
            timerElement.id = 'lesson-countdown-timer';
            timerElement.className = 'fixed top-4 left-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 font-mono text-lg';
            document.body.appendChild(timerElement);
        }

        this.timerElement = timerElement;
    }

    /**
     * تحديث عرض العداد
     */
    updateDisplay() {
        const now = new Date();
        const timeLeft = this.endTime - now;

        if (timeLeft <= 0) {
            this.onTimeUp();
            return;
        }

        const minutes = Math.floor(timeLeft / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

        // تحديث النص
        this.timerElement.innerHTML = `
            <i class="fas fa-clock ml-1"></i>
            ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}
        `;

        // تغيير اللون حسب الوقت المتبقي
        if (minutes <= 2) {
            this.timerElement.className = this.timerElement.className.replace('bg-blue-600', 'bg-red-600 animate-pulse');
            
            if (!this.finalWarningShown) {
                this.showFinalWarning();
                this.finalWarningShown = true;
            }
        } else if (minutes <= 5) {
            this.timerElement.className = this.timerElement.className.replace('bg-blue-600', 'bg-yellow-600');
            
            if (!this.warningShown) {
                this.showWarning();
                this.warningShown = true;
            }
        }
    }

    /**
     * إظهار تحذير 5 دقائق
     */
    showWarning() {
        this.showNotification('⚠️ تبقى 5 دقائق على انتهاء الحصة', 'warning');
        console.log('⚠️ تحذير: 5 دقائق متبقية');
    }

    /**
     * إظهار تحذير أخير 2 دقيقة
     */
    showFinalWarning() {
        this.showNotification('🚨 تبقى دقيقتان فقط على انتهاء الحصة!', 'error');
        console.log('🚨 تحذير أخير: دقيقتان متبقيتان');
    }

    /**
     * عند انتهاء الوقت
     */
    onTimeUp() {
        this.stop();
        
        // تحديث العداد لإظهار انتهاء الوقت
        this.timerElement.innerHTML = `
            <i class="fas fa-exclamation-triangle ml-1"></i>
            انتهت الحصة
        `;
        this.timerElement.className = 'fixed top-4 left-4 bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 font-mono text-lg animate-pulse';

        // إظهار إشعار انتهاء الحصة
        this.showNotification('⏰ انتهت الحصة! يرجى إنهاء المكالمة وتقييم الحصة', 'error', 10000);
        
        // إرسال إشعار للخادم
        this.notifyServerTimeUp();
        
        // توجيه لصفحة التقييم بعد 30 ثانية
        setTimeout(() => {
            this.redirectToRating();
        }, 30000);

        console.log('⏰ انتهت الحصة');
    }

    /**
     * إشعار الخادم بانتهاء الوقت
     */
    notifyServerTimeUp() {
        fetch(`/api/live-lessons/${this.lessonId}/time-up/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                lesson_id: this.lessonId,
                end_time: new Date().toISOString()
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('✅ تم إشعار الخادم بانتهاء الوقت:', data);
        })
        .catch(error => {
            console.error('❌ خطأ في إشعار الخادم:', error);
        });
    }

    /**
     * توجيه لصفحة التقييم أو التقرير
     */
    redirectToRating() {
        const userRole = window.userRole || 'student';
        let redirectUrl;
        let message;

        if (userRole === 'teacher') {
            redirectUrl = `/dashboard/teacher/live-lesson/${this.lessonId}/report/`;
            message = 'جاري التوجيه لصفحة كتابة التقرير...';
        } else {
            redirectUrl = `/dashboard/student/live-lesson/${this.lessonId}/rate/`;
            message = 'جاري التوجيه لصفحة التقييم...';
        }

        this.showNotification(message, 'info');

        setTimeout(() => {
            window.location.href = redirectUrl;
        }, 2000);
    }

    /**
     * إظهار إشعار
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `fixed top-20 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white font-medium transition-all duration-300 transform translate-x-full max-w-sm`;
        
        // تحديد لون الإشعار
        switch(type) {
            case 'success':
                notification.classList.add('bg-green-500');
                break;
            case 'error':
                notification.classList.add('bg-red-500');
                break;
            case 'warning':
                notification.classList.add('bg-yellow-500');
                break;
            default:
                notification.classList.add('bg-blue-500');
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // إخفاء الإشعار
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    /**
     * الحصول على الوقت المتبقي بالثواني
     */
    getTimeLeftSeconds() {
        const now = new Date();
        const timeLeft = this.endTime - now;
        return Math.max(0, Math.floor(timeLeft / 1000));
    }

    /**
     * التحقق من انتهاء الوقت
     */
    isTimeUp() {
        return this.getTimeLeftSeconds() === 0;
    }
}

// تصدير الكلاس للاستخدام العام
window.LessonCountdown = LessonCountdown;
