<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - {{ SITE_NAME }}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .arabic-text {
            font-family: '<PERSON><PERSON>', serif;
        }

        /* Islamic Design Colors */
        :root {
            --primary-green: #2D5016;
            --light-green: #4A7C59;
            --gold: #D4AF37;
            --light-gold: #F4E4BC;
            --dark-blue: #1B365D;
            --light-blue: #E8F4FD;
        }

        .bg-islamic-primary { background-color: var(--primary-green); }
        .bg-islamic-light { background-color: var(--light-green); }
        .bg-islamic-gold { background-color: var(--gold); }
        .bg-islamic-light-gold { background-color: var(--light-gold); }
        .bg-islamic-dark { background-color: var(--dark-blue); }
        .bg-islamic-light-blue { background-color: var(--light-blue); }

        .text-islamic-primary { color: var(--primary-green); }
        .text-islamic-gold { color: var(--gold); }
        .text-islamic-dark { color: var(--dark-blue); }

        .border-islamic-gold { border-color: var(--gold); }

        /* Islamic Pattern Background */
        .islamic-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="bg-islamic-light-blue islamic-pattern min-h-screen flex items-center justify-center py-12">
    <div class="max-w-lg w-full mx-4">
        <!-- Register Card -->
        <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
            <!-- Header -->
            <div class="bg-islamic-primary p-8 text-center">
                {% if ACADEMY_SETTINGS.academy_logo %}
                    <img src="{{ ACADEMY_SETTINGS.academy_logo.url }}" alt="{{ ACADEMY_SETTINGS.academy_name }}" class="w-20 h-20 rounded-full mx-auto mb-4 border-2 border-islamic-gold">
                {% else %}
                    <div class="w-20 h-20 bg-islamic-gold rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user-plus text-islamic-primary text-3xl"></i>
                    </div>
                {% endif %}
                <h1 class="text-white text-2xl font-bold arabic-text mb-2">إنشاء حساب جديد</h1>
                <p class="text-islamic-light-gold">{{ ACADEMY_SLOGAN }}</p>
            </div>

            <!-- Register Form -->
            <div class="p-8">
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="mb-4 p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" class="space-y-6">
                    {% csrf_token %}

                    <!-- User Type -->
                    <div>
                        <label class="block text-sm font-medium text-islamic-dark mb-2">
                            <i class="fas fa-user-tag ml-2"></i>
                            نوع الحساب
                        </label>
                        <select name="user_type" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                            <option value="">اختر نوع الحساب</option>
                            <option value="student">طالب</option>
                            <option value="teacher">معلم</option>
                        </select>
                    </div>

                    <!-- Name Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-islamic-dark mb-2">الاسم الأول</label>
                            <input type="text" name="first_name" required
                                   value="{% if form_data.first_name %}{{ form_data.first_name }}{% endif %}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="أدخل الاسم الأول">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-islamic-dark mb-2">اسم العائلة</label>
                            <input type="text" name="last_name" required
                                   value="{% if form_data.last_name %}{{ form_data.last_name }}{% endif %}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="أدخل اسم العائلة">
                        </div>
                    </div>

                    <!-- Username Field -->
                    <div>
                        <label class="block text-sm font-medium text-islamic-dark mb-2">
                            <i class="fas fa-user ml-2"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" name="username" required
                               value="{% if form_data.username %}{{ form_data.username }}{% endif %}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                               placeholder="أدخل اسم المستخدم">
                        <p class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-info-circle ml-1"></i>
                            يجب أن يكون اسم المستخدم فريد ولم يتم استخدامه من قبل
                        </p>
                    </div>

                    <!-- Email Field -->
                    <div>
                        <label class="block text-sm font-medium text-islamic-dark mb-2">
                            <i class="fas fa-envelope ml-2"></i>
                            البريد الإلكتروني
                        </label>
                        <input type="email" name="email" required
                               value="{% if form_data.email %}{{ form_data.email }}{% endif %}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                               placeholder="أدخل البريد الإلكتروني">
                        <p class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-info-circle ml-1"></i>
                            يجب أن يكون البريد الإلكتروني فريد ولم يتم استخدامه من قبل
                        </p>
                    </div>

                    <!-- Phone Field -->
                    <div>
                        <label class="block text-sm font-medium text-islamic-dark mb-2">
                            <i class="fas fa-phone ml-2"></i>
                            رقم الهاتف
                        </label>
                        <input type="tel" name="phone"
                               value="{% if form_data.phone %}{{ form_data.phone }}{% endif %}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                               placeholder="أدخل رقم الهاتف (اختياري)">
                    </div>

                    <!-- Password Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-islamic-dark mb-2">كلمة المرور</label>
                            <div class="relative">
                                <input type="password" name="password1" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent pl-12"
                                       placeholder="أدخل كلمة المرور">
                                <button type="button" onclick="togglePassword('password1', 'icon1')"
                                        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-islamic-primary">
                                    <i id="icon1" class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-islamic-dark mb-2">تأكيد كلمة المرور</label>
                            <div class="relative">
                                <input type="password" name="password2" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent pl-12"
                                       placeholder="أعد إدخال كلمة المرور">
                                <button type="button" onclick="togglePassword('password2', 'icon2')"
                                        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-islamic-primary">
                                    <i id="icon2" class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Student Level (shown only for students) -->
                    <div id="student-level" style="display: none;">
                        <label class="block text-sm font-medium text-islamic-dark mb-2">
                            <i class="fas fa-layer-group ml-2"></i>
                            مستوى الطالب
                        </label>
                        <select name="student_level"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                            <option value="">اختر مستواك</option>
                            <option value="beginner">مبتدئ</option>
                            <option value="intermediate">متوسط</option>
                            <option value="advanced">متقدم</option>
                        </select>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="flex items-center">
                        <input type="checkbox" name="terms" required
                               class="rounded border-gray-300 text-islamic-primary focus:ring-islamic-primary">
                        <span class="mr-2 text-sm text-gray-600">
                            أوافق على
                            <a href="{% url 'terms' %}" class="text-islamic-primary hover:text-islamic-light">شروط الاستخدام</a>
                            و
                            <a href="{% url 'privacy' %}" class="text-islamic-primary hover:text-islamic-light">سياسة الخصوصية</a>
                        </span>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit"
                            class="w-full bg-islamic-primary text-white py-3 px-4 rounded-lg hover:bg-islamic-light transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-user-plus ml-2"></i>
                        إنشاء الحساب
                    </button>
                </form>

                <!-- Login Link -->
                <div class="text-center mt-6">
                    <p class="text-gray-600">لديك حساب بالفعل؟</p>
                    <a href="{% url 'login' %}" class="text-islamic-primary hover:text-islamic-light font-semibold transition-colors duration-200">
                        تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>

        <!-- Islamic Quote -->
        <div class="text-center mt-8">
            <p class="text-islamic-primary arabic-text text-lg font-semibold">
                "وَقُلْ رَبِّ زِدْنِي عِلْمًا"
            </p>
            <p class="text-islamic-dark text-sm mt-1">
                سورة طه - آية 114
            </p>
        </div>
    </div>

    <script>
        function togglePassword(fieldId, iconId) {
            const field = document.getElementsByName(fieldId)[0];
            const icon = document.getElementById(iconId);

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Show/hide student level based on user type
        document.getElementsByName('user_type')[0].addEventListener('change', function() {
            const studentLevel = document.getElementById('student-level');
            if (this.value === 'student') {
                studentLevel.style.display = 'block';
            } else {
                studentLevel.style.display = 'none';
            }
        });

        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const messages = document.querySelectorAll('.mb-4.p-4.rounded-lg');
            messages.forEach(message => {
                message.style.transition = 'opacity 0.5s';
                message.style.opacity = '0';
                setTimeout(() => message.remove(), 500);
            });
        }, 5000);
    </script>
</body>
</html>
