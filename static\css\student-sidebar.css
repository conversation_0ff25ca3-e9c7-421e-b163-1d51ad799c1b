/* Student Sidebar Custom Colors - ألوان القائمة الجانبية للطالب */

/* ========================================
   🎨 ألوان خاصة بالطالب - أولوية عالية
   تطبق على جميع صفحات الطالب تلقائياً
======================================== */

/* تطبيق تلقائي على جميع صفحات الطالب */
body[data-user-type="student"] .new-sidebar,
body.student-page .new-sidebar,
.student-dashboard .new-sidebar,
body.student-dashboard .new-sidebar {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    --new-sidebar-bg: #2D5016 !important;
    --new-sidebar-bg-gradient: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body[data-user-type="student"] .new-sidebar-toggle,
body.student-page .new-sidebar-toggle,
.student-dashboard .new-sidebar-toggle,
body.student-dashboard .new-sidebar-toggle {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body[data-user-type="student"] .enhanced-card-header,
body.student-page .enhanced-card-header,
.student-dashboard .enhanced-card-header,
body.student-dashboard .enhanced-card-header {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

body[data-user-type="student"] .enhanced-card,
body.student-page .enhanced-card,
.student-dashboard .enhanced-card,
body.student-dashboard .enhanced-card {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* تخصيص ألوان القائمة الجانبية للطالب */
.student-dashboard .new-sidebar,
body.student-dashboard .new-sidebar {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    --new-sidebar-bg: #2D5016 !important;
    --new-sidebar-bg-gradient: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

.student-dashboard .new-sidebar-toggle,
body.student-dashboard .new-sidebar-toggle {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* تخصيص ألوان header للطالب */
.student-dashboard .enhanced-card-header,
body.student-dashboard .enhanced-card-header {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* تخصيص ألوان البطاقة الرئيسية */
.student-dashboard .enhanced-card,
body.student-dashboard .enhanced-card {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
}

/* تخصيص ألوان الأيقونات في القائمة الجانبية */
body[data-user-type="student"] .nav-icon,
body.student-page .nav-icon,
body.student-dashboard .nav-icon {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #d4af37 !important;
}

body[data-user-type="student"] .nav-link:hover .nav-icon,
body.student-page .nav-link:hover .nav-icon,
body.student-dashboard .nav-link:hover .nav-icon {
    background: rgba(255, 255, 255, 0.2) !important;
}

/* تخصيص ألوان النصوص */
body[data-user-type="student"] .nav-text,
body.student-page .nav-text,
body.student-dashboard .nav-text {
    color: #ffffff !important;
}

body[data-user-type="student"] .nav-link,
body.student-page .nav-link,
body.student-dashboard .nav-link {
    color: #ffffff !important;
}

body[data-user-type="student"] .nav-link:hover,
body.student-page .nav-link:hover,
body.student-dashboard .nav-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    border-right-color: #d4af37 !important;
}

body[data-user-type="student"] .nav-link-active,
body.student-page .nav-link-active,
body.student-dashboard .nav-link-active {
    background: rgba(255, 255, 255, 0.15) !important;
    border-right-color: #d4af37 !important;
}

/* تخصيص ألوان العناوين */
body[data-user-type="student"] .nav-section-title,
body.student-page .nav-section-title,
body.student-dashboard .nav-section-title {
    color: #d4af37 !important;
}

body[data-user-type="student"] .nav-section-header::before,
body.student-page .nav-section-header::before,
body.student-dashboard .nav-section-header::before {
    background: linear-gradient(90deg, transparent, #d4af37, transparent) !important;
}

/* تخصيص ألوان معلومات المستخدم */
body[data-user-type="student"] .user-name,
body.student-page .user-name,
body.student-dashboard .user-name {
    color: #ffffff !important;
}

body[data-user-type="student"] .academy-name,
body.student-page .academy-name,
body.student-dashboard .academy-name {
    color: #ffffff !important;
}

body[data-user-type="student"] .academy-slogan,
body.student-page .academy-slogan,
body.student-dashboard .academy-slogan {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* تخصيص ألوان الشارات */
body[data-user-type="student"] .role-student,
body.student-page .role-student,
body.student-dashboard .role-student {
    background: linear-gradient(135deg, #d4af37, #f4e4a6) !important;
    color: #2D5016 !important;
}

/* تخصيص ألوان الحدود */
body[data-user-type="student"] .new-sidebar-header,
body.student-page .new-sidebar-header,
body.student-dashboard .new-sidebar-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* تخصيص ألوان شريط التمرير */
body[data-user-type="student"] .new-sidebar-nav::-webkit-scrollbar-thumb,
body.student-page .new-sidebar-nav::-webkit-scrollbar-thumb,
body.student-dashboard .new-sidebar-nav::-webkit-scrollbar-thumb {
    background: #d4af37 !important;
}

body[data-user-type="student"] .new-sidebar-nav::-webkit-scrollbar-thumb:hover,
body.student-page .new-sidebar-nav::-webkit-scrollbar-thumb:hover,
body.student-dashboard .new-sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: #e6c547 !important;
}

/* تخصيص ألوان footer القائمة الجانبية */
body[data-user-type="student"] .new-sidebar-footer,
body.student-page .new-sidebar-footer,
body.student-dashboard .new-sidebar-footer {
    background: rgba(0, 0, 0, 0.25) !important;
    border-top: 2px solid #d4af37 !important;
}

body[data-user-type="student"] .footer-nav-link,
body.student-page .footer-nav-link,
body.student-dashboard .footer-nav-link {
    color: #ffffff !important;
}

body[data-user-type="student"] .footer-nav-link:hover,
body.student-page .footer-nav-link:hover,
body.student-dashboard .footer-nav-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

body[data-user-type="student"] .footer-nav-icon,
body.student-page .footer-nav-icon,
body.student-dashboard .footer-nav-icon {
    color: #d4af37 !important;
}

body[data-user-type="student"] .footer-nav-text,
body.student-page .footer-nav-text,
body.student-dashboard .footer-nav-text {
    color: #ffffff !important;
}

/* تخصيص ألوان الأزرار */
body[data-user-type="student"] .academy-logo-placeholder,
body.student-page .academy-logo-placeholder,
body.student-dashboard .academy-logo-placeholder {
    background: #d4af37 !important;
    color: #2D5016 !important;
    border: 2px solid #d4af37 !important;
}

body[data-user-type="student"] .user-avatar-placeholder,
body.student-page .user-avatar-placeholder,
body.student-dashboard .user-avatar-placeholder {
    background: #d4af37 !important;
    color: #2D5016 !important;
    border: 2px solid #d4af37 !important;
}

/* تخصيص ألوان الخلفية الشفافة */
body[data-user-type="student"] .user-info-section,
body.student-page .user-info-section,
body.student-dashboard .user-info-section {
    background: rgba(255, 255, 255, 0.05) !important;
}

/* تخصيص ألوان زر الإغلاق */
body[data-user-type="student"] .new-sidebar-close,
body.student-page .new-sidebar-close,
body.student-dashboard .new-sidebar-close {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

body[data-user-type="student"] .new-sidebar-close:hover,
body.student-page .new-sidebar-close:hover,
body.student-dashboard .new-sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

/* تخصيص ألوان التأثيرات */
body[data-user-type="student"] .nav-link::before,
body.student-page .nav-link::before,
body.student-dashboard .nav-link::before {
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent) !important;
}

body[data-user-type="student"] .nav-link-active,
body.student-page .nav-link-active,
body.student-dashboard .nav-link-active {
    box-shadow: inset 0 0 20px rgba(212, 175, 55, 0.1) !important;
}

/* تخصيص ألوان التركيز */
body[data-user-type="student"] .nav-link:focus,
body.student-page .nav-link:focus,
body.student-dashboard .nav-link:focus {
    outline: 2px solid #d4af37 !important;
    background: rgba(255, 255, 255, 0.15) !important;
}

/* تخصيص ألوان الحصص المباشرة */
body[data-user-type="student"] .nav-section-live .nav-section-title,
body.student-page .nav-section-live .nav-section-title,
body.student-dashboard .nav-section-live .nav-section-title {
    color: #ef4444 !important;
}

/* تخصيص ألوان الشارات */
body[data-user-type="student"] .nav-badge,
body.student-page .nav-badge,
body.student-dashboard .nav-badge {
    background: #ef4444 !important;
    color: #ffffff !important;
}

/* تأكيد الألوان للموبايل */
@media (max-width: 768px) {
    body[data-user-type="student"] .new-sidebar,
    body.student-page .new-sidebar,
    body.student-dashboard .new-sidebar {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    body[data-user-type="student"] .new-sidebar-toggle,
    body.student-page .new-sidebar-toggle,
    body.student-dashboard .new-sidebar-toggle {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    body[data-user-type="student"] .enhanced-card-header,
    body.student-page .enhanced-card-header,
    body.student-dashboard .enhanced-card-header {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }
}

/* تأكيد الألوان للتابلت */
@media (min-width: 768px) and (max-width: 1023px) {
    body[data-user-type="student"] .new-sidebar,
    body.student-page .new-sidebar,
    body.student-dashboard .new-sidebar {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    body[data-user-type="student"] .enhanced-card-header,
    body.student-page .enhanced-card-header,
    body.student-dashboard .enhanced-card-header {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }
}

/* تأكيد الألوان للكمبيوتر */
@media (min-width: 1024px) {
    body[data-user-type="student"] .new-sidebar,
    body.student-page .new-sidebar,
    body.student-dashboard .new-sidebar {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    body[data-user-type="student"] .enhanced-card-header,
    body.student-page .enhanced-card-header,
    body.student-dashboard .enhanced-card-header {
        background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }
}

/* إصلاح التدرجات الزرقاء في لوحة الطالب */
body[data-user-type="student"] .bg-gradient-to-br.from-blue-500.to-indigo-600,
body.student-page .bg-gradient-to-br.from-blue-500.to-indigo-600,
body.student-dashboard .bg-gradient-to-br.from-blue-500.to-indigo-600 {
    background: linear-gradient(135deg, #22c55e 0%, #059669 100%) !important;
}

body[data-user-type="student"] .bg-blue-500,
body.student-page .bg-blue-500,
body.student-dashboard .bg-blue-500 {
    background-color: #22c55e !important;
}

body[data-user-type="student"] .bg-blue-50,
body.student-page .bg-blue-50,
body.student-dashboard .bg-blue-50 {
    background-color: #f0fdf4 !important;
}

body[data-user-type="student"] .border-blue-200,
body.student-page .border-blue-200,
body.student-dashboard .border-blue-200 {
    border-color: #bbf7d0 !important;
}

body[data-user-type="student"] .bg-blue-100,
body.student-page .bg-blue-100,
body.student-dashboard .bg-blue-100 {
    background-color: #dcfce7 !important;
}

body[data-user-type="student"] .text-blue-700,
body.student-page .text-blue-700,
body.student-dashboard .text-blue-700 {
    color: #15803d !important;
}
