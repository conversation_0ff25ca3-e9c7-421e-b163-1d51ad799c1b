"""
مهام دورية لنظام الاشتراكات
"""

from django.utils import timezone
from .services import ScheduledLessonToLiveService
import logging
from celery import shared_task

logger = logging.getLogger(__name__)


def convert_scheduled_lessons_task():
    """
    مهمة دورية لتحويل الحصص المجدولة إلى حصص مباشرة
    يتم تشغيلها كل 5 دقائق
    """
    try:
        logger.info("بدء مهمة تحويل الحصص المجدولة")
        converted_count = ScheduledLessonToLiveService.check_and_convert_lessons()
        
        if converted_count > 0:
            logger.info(f"تم تحويل {converted_count} حصة مجدولة إلى حصص مباشرة")
        else:
            logger.debug("لا توجد حصص مجدولة جاهزة للتحويل")
            
        return converted_count
        
    except Exception as e:
        logger.error(f"خطأ في مهمة تحويل الحصص المجدولة: {str(e)}")
        return 0


def send_lesson_reminders_task():
    """
    مهمة دورية لإرسال تذكيرات الحصص
    يتم تشغيلها كل ساعة
    """
    from .models import ScheduledLesson
    from datetime import timedelta

    try:
        now = timezone.now()

        # البحث عن الحصص التي ستبدأ خلال ساعة
        upcoming_lessons = ScheduledLesson.objects.filter(
            status='scheduled',
            scheduled_date__gte=now,
            scheduled_date__lte=now + timedelta(hours=1)
        ).select_related('subscription__student', 'teacher')

        reminder_count = 0

        for lesson in upcoming_lessons:
            try:
                # يمكن إضافة نظام إشعارات آخر هنا مستقبلاً
                # مثل الإشعارات الداخلية أو البريد الإلكتروني

                # إنشاء إشعار داخلي للطالب
                from notifications.models import Notification

                # التحقق من عدم وجود إشعار مشابه
                existing_notification = Notification.objects.filter(
                    user=lesson.subscription.student,
                    title__contains=f"تذكير حصة رقم {lesson.lesson_number}",
                    created_at__gte=now - timedelta(hours=1)
                ).exists()

                if not existing_notification:
                    # إنشاء إشعار للطالب
                    Notification.objects.create(
                        user=lesson.subscription.student,
                        title=f"تذكير: حصة رقم {lesson.lesson_number}",
                        message=f"حصتك ستبدأ خلال ساعة في {lesson.scheduled_date.strftime('%H:%M')}",
                        notification_type='lesson_reminder',
                        is_read=False
                    )

                    # إنشاء إشعار للمعلم إذا كان معين
                    if lesson.teacher:
                        teacher_notification = Notification.objects.filter(
                            user=lesson.teacher,
                            title__contains=f"تذكير حصة رقم {lesson.lesson_number}",
                            created_at__gte=now - timedelta(hours=1)
                        ).exists()

                        if not teacher_notification:
                            Notification.objects.create(
                                user=lesson.teacher,
                                title=f"تذكير: حصة رقم {lesson.lesson_number}",
                                message=f"حصتك مع {lesson.subscription.student.get_full_name()} ستبدأ خلال ساعة",
                                notification_type='lesson_reminder',
                                is_read=False
                            )

                    reminder_count += 1

            except Exception as e:
                logger.error(f"خطأ في إرسال تذكير للحصة {lesson.id}: {str(e)}")
                continue

        if reminder_count > 0:
            logger.info(f"تم إرسال {reminder_count} تذكير للحصص القادمة")
        
        return reminder_count
        
    except Exception as e:
        logger.error(f"خطأ في مهمة إرسال تذكيرات الحصص: {str(e)}")
        return 0


@shared_task
def check_expiring_subscriptions():
    """فحص الاشتراكات التي على وشك الانتهاء وإرسال تذكيرات بريد إلكتروني"""
    from .models import StudentSubscription
    from notifications.utils import SubscriptionNotificationService
    from subscriptions.email_service import SubscriptionEmailService
    from datetime import timedelta

    try:
        email_service = SubscriptionEmailService()
        total_sent = 0

        # التحقق من الاشتراكات التي ستنتهي خلال 7 أيام
        seven_days_from_now = timezone.now().date() + timedelta(days=7)
        three_days_from_now = timezone.now().date() + timedelta(days=3)

        # الاشتراكات التي ستنتهي خلال 7 أيام (ولكن ليس خلال 3 أيام)
        seven_day_expiring = StudentSubscription.objects.filter(
            status='active',
            end_date__lte=seven_days_from_now,
            end_date__gt=three_days_from_now
        ).select_related('student', 'plan')

        # الاشتراكات التي ستنتهي خلال 3 أيام
        three_day_expiring = StudentSubscription.objects.filter(
            status='active',
            end_date__lte=three_days_from_now,
            end_date__gt=timezone.now().date()
        ).select_related('student', 'plan')

        # إرسال تذكيرات 7 أيام
        for subscription in seven_day_expiring:
            days_remaining = (subscription.end_date - timezone.now().date()).days

            # إرسال إشعار داخلي
            SubscriptionNotificationService.notify_subscription_expiring_soon(
                subscription,
                days_remaining
            )

            # إرسال بريد إلكتروني
            if email_service.send_subscription_expiring_reminder(subscription, days_remaining):
                total_sent += 1

        # إرسال تذكيرات 3 أيام
        for subscription in three_day_expiring:
            days_remaining = (subscription.end_date - timezone.now().date()).days

            # إرسال إشعار داخلي
            SubscriptionNotificationService.notify_subscription_expiring_soon(
                subscription,
                days_remaining
            )

            # إرسال بريد إلكتروني
            if email_service.send_subscription_expiring_reminder(subscription, days_remaining):
                total_sent += 1

        logger.info(f"تم فحص {len(seven_day_expiring) + len(three_day_expiring)} اشتراك على وشك الانتهاء وإرسال {total_sent} بريد إلكتروني")
        return total_sent

    except Exception as e:
        logger.error(f"خطأ في فحص الاشتراكات المنتهية قريباً: {str(e)}")
        return 0


@shared_task
def check_exhausted_lessons():
    """فحص الاشتراكات التي انتهت حصصها"""
    from .models import StudentSubscription
    from notifications.utils import SubscriptionNotificationService

    try:
        exhausted_subscriptions = StudentSubscription.objects.filter(
            status='active',
            remaining_lessons=0
        ).select_related('student', 'plan')

        for subscription in exhausted_subscriptions:
            # إرسال إشعار انتهاء الحصص
            SubscriptionNotificationService.notify_lessons_exhausted(subscription)

            # تحديث حالة الاشتراك إلى منتهي
            subscription.status = 'expired'
            subscription.save()

        logger.info(f"تم تحديث {len(exhausted_subscriptions)} اشتراك انتهت حصصه")
        return len(exhausted_subscriptions)

    except Exception as e:
        logger.error(f"خطأ في فحص الاشتراكات المنتهية الحصص: {str(e)}")
        return 0


@shared_task
def check_expired_subscriptions():
    """فحص الاشتراكات المنتهية بالتاريخ وإرسال إشعارات انتهاء"""
    from .models import StudentSubscription

    try:
        total_sent = 0

        expired_subscriptions = StudentSubscription.objects.filter(
            status='active',
            end_date__lt=timezone.now().date()
        ).select_related('student', 'plan')

        for subscription in expired_subscriptions:
            try:
                # إنشاء إشعار داخلي لانتهاء الاشتراك
                from notifications.models import Notification

                Notification.objects.create(
                    user=subscription.student,
                    title="انتهاء الاشتراك",
                    message=f"انتهى اشتراكك في خطة {subscription.plan.name}. يرجى تجديد الاشتراك للمتابعة.",
                    notification_type='subscription_expired',
                    is_read=False
                )
                total_sent += 1
            except Exception as e:
                logger.error(f"خطأ في إنشاء إشعار انتهاء الاشتراك للطالب {subscription.student.id}: {str(e)}")

            # تحديث حالة الاشتراك إلى منتهي
            subscription.status = 'expired'
            subscription.save()

        logger.info(f"تم تحديث {len(expired_subscriptions)} اشتراك منتهي بالتاريخ وإرسال {total_sent} إشعار")
        return total_sent

    except Exception as e:
        logger.error(f"خطأ في فحص الاشتراكات المنتهية: {str(e)}")
        return 0


@shared_task
def run_subscription_maintenance_task():
    """مهمة دورية شاملة لصيانة الاشتراكات"""

    try:
        logger.info("بدء مهمة صيانة الاشتراكات")

        # فحص الاشتراكات
        expiring_count = check_expiring_subscriptions()
        exhausted_count = check_exhausted_lessons()
        expired_count = check_expired_subscriptions()

        results = {
            'expiring_count': expiring_count,
            'exhausted_count': exhausted_count,
            'expired_count': expired_count,
            'timestamp': timezone.now()
        }

        logger.info(f"انتهاء مهمة صيانة الاشتراكات - النتائج: {results}")
        return results

    except Exception as e:
        logger.error(f"خطأ في مهمة صيانة الاشتراكات: {str(e)}")
        return {'error': str(e), 'timestamp': timezone.now()}
