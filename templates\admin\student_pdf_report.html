<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير تقييمات الطالب - {{ student.get_full_name }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #2D5016, #4a7c59);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 20px;
            opacity: 0.9;
        }
        .info-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #2D5016;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #2D5016;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
        }
        .info-value {
            color: #333;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #2D5016;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            margin: 8px 0;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .progress-overall { background: #2D5016; }
        .progress-quality { background: #007bff; }
        .progress-interaction { background: #28a745; }
        .progress-technical { background: #6f42c1; }
        .ratings-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .ratings-table th,
        .ratings-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .ratings-table th {
            background: #2D5016;
            color: white;
            font-weight: bold;
        }
        .ratings-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .star-rating {
            color: #ffc107;
            font-size: 16px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            color: #666;
            font-size: 12px;
        }
        .performance-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .excellent { background: #28a745; }
        .very-good { background: #17a2b8; }
        .good { background: #007bff; }
        .average { background: #ffc107; color: #333; }
        .needs-improvement { background: #dc3545; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>أكاديمية القرآنية</h1>
        <h2>تقرير تقييمات الطالب: {{ student.get_full_name }}</h2>
    </div>

    <!-- معلومات الطالب -->
    <div class="info-section">
        <div class="section-title">معلومات الطالب</div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">الاسم الكامل:</span>
                <span class="info-value">{{ student.get_full_name }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">البريد الإلكتروني:</span>
                <span class="info-value">{{ student.email }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ التسجيل:</span>
                <span class="info-value">{{ student.date_joined|date:"d/m/Y" }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">إجمالي التقييمات:</span>
                <span class="info-value">{{ stats.total_ratings }}</span>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ stats.overall_average|floatformat:1 }}</div>
            <div class="stat-label">المتوسط الإجمالي</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.scheduled_count }}</div>
            <div class="stat-label">حصص مجدولة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.live_count }}</div>
            <div class="stat-label">حصص مباشرة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_ratings }}</div>
            <div class="stat-label">إجمالي التقييمات</div>
        </div>
    </div>

    <!-- تفاصيل المعايير -->
    <div class="info-section">
        <div class="section-title">تفاصيل معايير التقييم</div>
        
        <div class="info-item">
            <span class="info-label">التقييم العام</span>
            <span class="info-value">{{ stats.avg_overall|floatformat:1 }}/5</span>
            <div class="progress-bar">
                <div class="progress-fill progress-overall" style="width: {% widthratio stats.avg_overall 5 100 %}%"></div>
            </div>
        </div>

        <div class="info-item">
            <span class="info-label">جودة الحصة</span>
            <span class="info-value">{{ stats.avg_quality|floatformat:1 }}/5</span>
            <div class="progress-bar">
                <div class="progress-fill progress-quality" style="width: {% widthratio stats.avg_quality 5 100 %}%"></div>
            </div>
        </div>

        <div class="info-item">
            <span class="info-label">تفاعل الطالب</span>
            <span class="info-value">{{ stats.avg_interaction|floatformat:1 }}/5</span>
            <div class="progress-bar">
                <div class="progress-fill progress-interaction" style="width: {% widthratio stats.avg_interaction 5 100 %}%"></div>
            </div>
        </div>

        <div class="info-item">
            <span class="info-label">الجودة التقنية</span>
            <span class="info-value">{{ stats.avg_technical|floatformat:1 }}/5</span>
            <div class="progress-bar">
                <div class="progress-fill progress-technical" style="width: {% widthratio stats.avg_technical 5 100 %}%"></div>
            </div>
        </div>
    </div>

    <!-- توزيع النجوم -->
    <div class="info-section">
        <div class="section-title">توزيع التقييمات</div>
        <table class="ratings-table">
            <thead>
                <tr>
                    <th>عدد النجوم</th>
                    <th>عدد التقييمات</th>
                    <th>النسبة المئوية</th>
                </tr>
            </thead>
            <tbody>
                {% for stars in "54321" %}
                <tr>
                    <td>
                        <span class="star-rating">
                            {% for i in "12345" %}
                                {% if forloop.counter <= stars|add:0 %}★{% else %}☆{% endif %}
                            {% endfor %}
                        </span>
                    </td>
                    <td>{{ star_distribution|default_if_none:0 }}</td>
                    <td>
                        {% if stats.total_ratings > 0 %}
                            {% widthratio star_distribution stats.total_ratings 100 %}%
                        {% else %}
                            0%
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- آخر التقييمات -->
    {% if recent_ratings %}
    <div class="info-section">
        <div class="section-title">آخر التقييمات</div>
        <table class="ratings-table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>المعلم</th>
                    <th>التقييم</th>
                    <th>نوع الحصة</th>
                    <th>التعليق</th>
                </tr>
            </thead>
            <tbody>
                {% for rating in recent_ratings|slice:":10" %}
                <tr>
                    <td>{{ rating.created_at|date:"d/m/Y" }}</td>
                    <td>{{ rating.teacher.get_full_name|default:"غير محدد" }}</td>
                    <td>
                        <span class="star-rating">
                            {% for i in "12345" %}
                                {% if forloop.counter <= rating.overall_rating %}★{% else %}☆{% endif %}
                            {% endfor %}
                        </span>
                        ({{ rating.overall_rating }}/5)
                    </td>
                    <td>
                        {% if rating.lesson_type == 'scheduled' %}
                            مجدولة
                        {% else %}
                            مباشرة
                        {% endif %}
                    </td>
                    <td>{{ rating.comment|default:"لا يوجد تعليق"|truncatechars:50 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <strong>أكاديمية القرآنية</strong><br>
        تقرير تم إنشاؤه تلقائياً في {{ generated_at|date:"d/m/Y H:i" }}<br>
        هذا التقرير يحتوي على معلومات سرية ومخصص للاستخدام الداخلي فقط
    </div>
</body>
</html>
