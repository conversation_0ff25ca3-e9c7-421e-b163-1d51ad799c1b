# Generated by Django 4.2.7 on 2025-06-01 18:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CustomReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التقرير')),
                ('description', models.TextField(blank=True, verbose_name='وصف التقرير')),
                ('category', models.CharField(choices=[('teachers', 'تقارير المعلمين'), ('students', 'تقارير الطلاب'), ('courses', 'تقارير الدورات'), ('financial', 'تقارير المالية')], max_length=20, verbose_name='فئة التقرير')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('filters', models.JSONField(default=dict, verbose_name='فلاتر التقرير')),
                ('date_from', models.DateField(blank=True, null=True, verbose_name='من تاريخ')),
                ('date_to', models.DateField(blank=True, null=True, verbose_name='إلى تاريخ')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_scheduled', models.BooleanField(default=False, verbose_name='مجدول')),
                ('schedule_frequency', models.CharField(blank=True, choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي')], max_length=20, verbose_name='تكرار الجدولة')),
            ],
            options={
                'verbose_name': 'تقرير مخصص',
                'verbose_name_plural': 'التقارير المخصصة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم القالب')),
                ('description', models.TextField(verbose_name='وصف القالب')),
                ('category', models.CharField(choices=[('teachers', 'تقارير المعلمين'), ('students', 'تقارير الطلاب'), ('courses', 'تقارير الدورات'), ('financial', 'تقارير المالية')], max_length=20, verbose_name='فئة القالب')),
                ('template_config', models.JSONField(default=dict, verbose_name='إعدادات القالب')),
                ('default_filters', models.JSONField(default=dict, verbose_name='الفلاتر الافتراضية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_system_template', models.BooleanField(default=False, verbose_name='قالب النظام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قالب تقرير',
                'verbose_name_plural': 'قوالب التقارير',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ReportSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي')], max_length=20, verbose_name='تكرار التوليد')),
                ('execution_time', models.TimeField(verbose_name='وقت التنفيذ')),
                ('next_execution', models.DateTimeField(verbose_name='التنفيذ التالي')),
                ('last_execution', models.DateTimeField(blank=True, null=True, verbose_name='آخر تنفيذ')),
                ('notify_on_completion', models.BooleanField(default=True, verbose_name='إشعار عند الانتهاء')),
                ('notification_emails', models.JSONField(default=list, verbose_name='بريد الإشعارات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('custom_report', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='schedule', to='reports.customreport', verbose_name='التقرير المخصص')),
            ],
            options={
                'verbose_name': 'جدولة تقرير',
                'verbose_name_plural': 'جدولة التقارير',
                'ordering': ['next_execution'],
            },
        ),
        migrations.CreateModel(
            name='GeneratedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('generated_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التوليد')),
                ('file_format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV')], max_length=10, verbose_name='صيغة الملف')),
                ('file_path', models.FileField(upload_to='reports/%Y/%m/', verbose_name='مسار الملف')),
                ('file_size', models.PositiveIntegerField(default=0, verbose_name='حجم الملف (بايت)')),
                ('total_records', models.PositiveIntegerField(default=0, verbose_name='إجمالي السجلات')),
                ('processing_time', models.FloatField(default=0.0, verbose_name='وقت المعالجة (ثانية)')),
                ('is_ready', models.BooleanField(default=False, verbose_name='جاهز للتحميل')),
                ('download_count', models.PositiveIntegerField(default=0, verbose_name='عدد مرات التحميل')),
                ('last_downloaded', models.DateTimeField(blank=True, null=True, verbose_name='آخر تحميل')),
                ('custom_report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='generated_reports', to='reports.customreport', verbose_name='التقرير المخصص')),
            ],
            options={
                'verbose_name': 'تقرير مولد',
                'verbose_name_plural': 'التقارير المولدة',
                'ordering': ['-generated_at'],
            },
        ),
    ]
