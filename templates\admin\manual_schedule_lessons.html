{% extends 'base.html' %}
{% load static %}

{% block title %}الجدولة اليدوية للحصص - {{ subscription.student.get_full_name }}{% endblock %}

{% block extra_css %}
<!-- FullCalendar CSS -->
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
<style>
    /* تحسينات التجاوب للجدولة اليدوية */
    @media (max-width: 768px) {
        .schedule-container {
            padding: 1rem !important;
        }

        .schedule-header {
            flex-direction: column !important;
            gap: 1rem !important;
        }

        .schedule-title {
            font-size: 1.5rem !important;
        }

        .schedule-controls {
            flex-direction: column !important;
            gap: 0.5rem !important;
        }

        .schedule-controls button,
        .schedule-controls select {
            font-size: 0.75rem !important;
            padding: 0.5rem 0.75rem !important;
        }

        .lessons-summary {
            padding: 1rem !important;
        }

        .fc-toolbar {
            flex-direction: column !important;
            gap: 0.5rem !important;
            padding: 1rem !important;
        }

        .fc-toolbar-chunk {
            display: flex !important;
            justify-content: center !important;
        }

        .fc-button {
            font-size: 0.75rem !important;
            padding: 0.25rem 0.5rem !important;
        }

        .fc-toolbar-title {
            font-size: 1rem !important;
        }
    }

    @media (max-width: 640px) {
        .schedule-grid {
            grid-template-columns: 1fr !important;
        }

        .lesson-item {
            font-size: 0.75rem !important;
            padding: 0.5rem !important;
        }

        .modal-content {
            margin: 1rem !important;
            padding: 1rem !important;
        }
    }

    .fc-event {
        cursor: pointer;
        border-radius: 5px;
        border: none;
        padding: 2px 5px;
    }
    .fc-event:hover {
        opacity: 0.8;
    }
    .lesson-slot {
        background-color: #3498db;
        border: 1px solid #2980b9;
        color: white;
        padding: 2px 5px;
        border-radius: 3px;
        margin: 1px 0;
        font-size: 11px;
    }
    .lesson-slot:hover {
        background-color: #2980b9;
    }

    .responsive-calendar {
        min-height: 400px;
    }

    @media (max-width: 768px) {
        .responsive-calendar {
            min-height: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="schedule-container container mx-auto px-4 py-6 md:py-8">
        <!-- Header -->
        <div class="schedule-header bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
            <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div>
                    <h1 class="schedule-title text-2xl md:text-3xl lg:text-4xl font-bold text-islamic-primary mb-2 flex items-center">
                        <i class="fas fa-calendar-plus text-islamic-gold ml-2 md:ml-3"></i>
                        الجدولة اليدوية للحصص
                    </h1>
                    <p class="text-sm md:text-base text-gray-600">جدولة حصص {{ subscription.student.get_full_name }} - {{ subscription.plan.name }}</p>
                </div>
                <a href="{% url 'admin_subscriptions_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg flex items-center justify-center transition-colors text-sm md:text-base">
                    <i class="fas fa-arrow-right ml-1 md:ml-2"></i>
                    <span class="hidden sm:inline">العودة لقائمة الاشتراكات</span>
                    <span class="sm:hidden">العودة</span>
                </a>
            </div>
        </div>

        <!-- Subscription Status Warnings -->
        {% if subscription.status != 'active' %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl ml-3"></i>
                <div>
                    <h4 class="text-lg font-bold text-red-800">⚠️ تحذير: الاشتراك غير نشط</h4>
                    <p class="text-red-700">
                        حالة الاشتراك: <strong>{{ subscription.get_status_display }}</strong>
                        - لا يمكن جدولة حصص لاشتراك غير نشط.
                    </p>
                </div>
            </div>
        </div>
        {% elif subscription.end_date < today %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-calendar-times text-red-600 text-xl ml-3"></i>
                <div>
                    <h4 class="text-lg font-bold text-red-800">⚠️ تحذير: الاشتراك منتهي الصلاحية</h4>
                    <p class="text-red-700">
                        انتهى الاشتراك في: <strong>{{ subscription.end_date }}</strong>
                        - لا يمكن جدولة حصص لاشتراك منتهي الصلاحية.
                    </p>
                </div>
            </div>
        </div>
        {% elif subscription.is_lessons_exhausted %}
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle text-yellow-600 text-xl ml-3"></i>
                <div>
                    <h4 class="text-lg font-bold text-yellow-800">⚠️ تحذير: انتهت حصص الباقة</h4>
                    <p class="text-yellow-700">
                        الطالب <strong>{{ subscription.student.get_full_name }}</strong> قد أكمل جميع حصص باقته
                        ({{ subscription.get_completed_lessons_count }}/{{ subscription.plan.lessons_count }}).
                        أي حصص جديدة ستحتاج موافقة خاصة.
                    </p>
                </div>
            </div>
        </div>
        {% elif subscription.remaining_lessons <= 3 %}
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-info-circle text-blue-600 text-xl ml-3"></i>
                <div>
                    <h4 class="text-lg font-bold text-blue-800">💡 تنبيه: حصص قليلة متبقية</h4>
                    <p class="text-blue-700">
                        الطالب <strong>{{ subscription.student.get_full_name }}</strong> لديه
                        <strong>{{ subscription.remaining_lessons }}</strong> حصة متبقية فقط من باقته.
                    </p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Subscription Info -->
        <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
            <h3 class="text-lg md:text-xl font-semibold text-gray-900 mb-4">
                <i class="fas fa-info-circle ml-2"></i>
                معلومات الاشتراك
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
                <div class="bg-blue-50 rounded-lg p-3 md:p-4">
                    <div class="flex items-center">
                        <i class="fas fa-user text-blue-600 text-lg md:text-xl ml-2 md:ml-3"></i>
                        <div>
                            <p class="text-xs md:text-sm font-medium text-blue-600">الطالب</p>
                            <p class="text-sm md:text-lg font-bold text-blue-900 truncate">{{ subscription.student.get_full_name }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-green-50 rounded-lg p-3 md:p-4">
                    <div class="flex items-center">
                        <i class="fas fa-box text-green-600 text-lg md:text-xl ml-2 md:ml-3"></i>
                        <div>
                            <p class="text-xs md:text-sm font-medium text-green-600">الباقة</p>
                            <p class="text-sm md:text-lg font-bold text-green-900 truncate">{{ subscription.plan.name }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-yellow-50 rounded-lg p-3 md:p-4">
                    <div class="flex items-center">
                        <i class="fas fa-list-ol text-yellow-600 text-lg md:text-xl ml-2 md:ml-3"></i>
                        <div>
                            <p class="text-xs md:text-sm font-medium text-yellow-600">عدد الحصص</p>
                            <p class="text-sm md:text-lg font-bold text-yellow-900">{{ subscription.plan.lessons_count }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-purple-50 rounded-lg p-3 md:p-4">
                    <div class="flex items-center">
                        <i class="fas fa-clock text-purple-600 text-lg md:text-xl ml-2 md:ml-3"></i>
                        <div>
                            <p class="text-xs md:text-sm font-medium text-purple-600">مدة الحصة</p>
                            <p class="text-sm md:text-lg font-bold text-purple-900">{{ subscription.plan.lesson_duration }} دقيقة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher Selection -->
        <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
            <h3 class="text-lg md:text-xl font-semibold text-gray-900 mb-4">
                <i class="fas fa-chalkboard-teacher ml-2"></i>
                اختيار المعلم
            </h3>
            <form id="scheduleForm" method="post">
                {% csrf_token %}
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                        <label for="teacher_id" class="block text-sm md:text-base font-medium text-gray-700 mb-2">المعلم المسؤول عن الحصص</label>
                        <select name="teacher_id" id="teacher_id" required class="w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent text-sm md:text-base">
                            <option value="">اختر المعلم</option>
                            {% for teacher in available_teachers %}
                            <option value="{{ teacher.id }}">{{ teacher.get_full_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="flex items-end">
                        <div class="text-xs md:text-sm text-gray-600">
                            <p><strong>ملاحظة:</strong> يجب اختيار المعلم قبل إنشاء الجدولة</p>
                            <p>سيتم تعيين نفس المعلم لجميع الحصص</p>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="lessons_data" id="lessons_data">
            </form>
        </div>

        <!-- Calendar Section -->
        <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
            <div class="flex flex-col md:flex-row md:items-center justify-between mb-4 gap-3">
                <h3 class="text-lg md:text-xl font-semibold text-gray-900">
                    <i class="fas fa-calendar-alt ml-2"></i>
                    تقويم الجدولة
                </h3>
                <div class="schedule-controls flex flex-col sm:flex-row gap-2">
                    <button id="clearAllLessons" class="bg-red-500 hover:bg-red-600 text-white px-3 md:px-4 py-2 rounded-lg transition-colors text-sm md:text-base">
                        <i class="fas fa-trash ml-1 md:ml-2"></i>
                        مسح جميع الحصص
                    </button>
                    <button id="autoSchedule" class="bg-blue-500 hover:bg-blue-600 text-white px-3 md:px-4 py-2 rounded-lg transition-colors text-sm md:text-base">
                        <i class="fas fa-magic ml-1 md:ml-2"></i>
                        جدولة تلقائية
                    </button>
                </div>
            </div>
            
            <div class="mb-4 p-3 md:p-4 bg-gray-50 rounded-lg">
                <h4 class="font-semibold text-gray-800 mb-2 text-sm md:text-base">تعليمات الاستخدام:</h4>
                <ul class="text-xs md:text-sm text-gray-600 space-y-1">
                    <li>• انقر على أي تاريخ في التقويم لإضافة حصة</li>
                    <li>• انقر على الحصة المجدولة لحذفها</li>
                    <li>• يجب جدولة {{ subscription.plan.lessons_count }} حصة</li>
                    <li>• مدة كل حصة {{ subscription.plan.lesson_duration }} دقيقة</li>
                    <li>• يمكن استخدام الجدولة التلقائية كنقطة بداية ثم التعديل عليها</li>
                    <li>• تجنب جدولة الحصص في أيام الجمعة والسبت</li>
                </ul>
            </div>

            {% if existing_lessons %}
            <div class="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 class="font-semibold text-blue-800 mb-2">
                    <i class="fas fa-info-circle ml-2"></i>
                    يوجد جدولة سابقة
                </h4>
                <p class="text-sm text-blue-700">
                    تم العثور على {{ existing_lessons.count }} حصة مجدولة مسبقاً. ستظهر باللون الأخضر في التقويم.
                    يمكنك حذفها وإعادة الجدولة أو التعديل عليها.
                </p>
            </div>
            {% endif %}

            <div id="calendar" class="responsive-calendar"></div>
        </div>

        <!-- Scheduled Lessons Summary -->
        <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
            <h3 class="text-lg md:text-xl font-semibold text-gray-900 mb-4">
                <i class="fas fa-list ml-2"></i>
                ملخص الحصص المجدولة
            </h3>
            <div id="lessonsCount" class="mb-4">
                <span class="text-lg md:text-xl font-bold text-blue-600">0</span> من <span class="text-lg md:text-xl font-bold text-gray-800">{{ subscription.plan.lessons_count }}</span> حصة
            </div>
            <div id="lessonsList" class="lessons-summary space-y-2">
                <!-- سيتم ملء هذا القسم بـ JavaScript -->
            </div>
        </div>

        <!-- Save Buttons -->
        <div class="text-center flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
            <button id="clearAndSave" class="bg-red-600 hover:bg-red-700 text-white px-4 md:px-6 py-3 md:py-4 rounded-lg text-sm md:text-lg font-semibold transition-colors">
                <i class="fas fa-trash ml-1 md:ml-2"></i>
                مسح جميع الحصص وحفظ
            </button>
            <button id="saveSchedule" class="bg-green-600 hover:bg-green-700 text-white px-6 md:px-8 py-3 md:py-4 rounded-lg text-sm md:text-lg font-semibold transition-colors">
                <i class="fas fa-save ml-1 md:ml-2"></i>
                حفظ الجدولة
            </button>
        </div>
    </div>
</div>

<!-- Time Selection Modal -->
<div id="timeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="modal-content bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
            <div class="px-4 md:px-6 py-3 md:py-4 border-b border-gray-200">
                <h3 class="text-base md:text-lg font-semibold text-gray-900">اختيار وقت الحصة</h3>
            </div>
            <div class="p-4 md:p-6">
                <div class="mb-4">
                    <label for="lessonTime" class="block text-xs md:text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-clock text-islamic-primary ml-1"></i>
                        وقت الحصة (ساعة:دقيقة)
                    </label>
                    <input type="time" id="lessonTime" value="10:00"
                           class="w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent text-sm md:text-lg">
                </div>

                <div class="mb-4">
                    <label class="block text-xs md:text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-blue-600 ml-1"></i>
                        التاريخ المحدد
                    </label>
                    <div class="bg-gray-50 rounded-lg p-2 md:p-3 border">
                        <span id="selectedDate" class="text-sm md:text-lg font-medium text-gray-800"></span>
                    </div>
                </div>

                <div class="mb-4 p-3 md:p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 class="text-xs md:text-sm font-semibold text-blue-800 mb-2">
                        <i class="fas fa-info-circle ml-1"></i>
                        نصائح لاختيار الوقت
                    </h4>
                    <ul class="text-xs text-blue-700 space-y-1">
                        <li>• يمكنك اختيار أي وقت بالساعات والدقائق</li>
                        <li>• الأوقات المفضلة: 10:00، 14:00، 16:00، 18:00</li>
                        <li>• تجنب الأوقات المتأخرة (بعد 21:00)</li>
                        <li>• تأكد من توافق الوقت مع المعلم والطالب</li>
                    </ul>
                </div>
            </div>
            <div class="px-4 md:px-6 py-3 md:py-4 border-t border-gray-200 flex flex-col sm:flex-row justify-end gap-2 sm:gap-0 sm:space-x-2 sm:space-x-reverse">
                <button id="cancelTimeSelection" class="bg-gray-500 hover:bg-gray-600 text-white px-3 md:px-4 py-2 rounded-lg transition-colors text-sm md:text-base">
                    إلغاء
                </button>
                <button id="confirmTimeSelection" class="bg-islamic-primary hover:bg-islamic-light text-white px-3 md:px-4 py-2 rounded-lg transition-colors text-sm md:text-base">
                    إضافة الحصة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- FullCalendar JS -->
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let calendar;
    let scheduledLessons = [];
    const maxLessons = {{ subscription.plan.lessons_count }};
    const lessonDuration = {{ subscription.plan.lesson_duration }};
    
    // Initialize calendar
    const calendarEl = document.getElementById('calendar');
    calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        locale: 'ar',
        direction: 'rtl',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        buttonText: {
            today: 'اليوم',
            month: 'شهر',
            week: 'أسبوع',
            day: 'يوم'
        },
        selectable: true,
        selectMirror: true,
        select: function(info) {
            if (scheduledLessons.length >= maxLessons) {
                alert(`لا يمكن إضافة أكثر من ${maxLessons} حصة`);
                return;
            }

            // Check if it's weekend (Friday or Saturday)
            const selectedDay = info.start.getDay();
            if (selectedDay === 5 || selectedDay === 6) {
                if (!confirm('لقد اخترت يوم جمعة أو سبت. هل تريد المتابعة؟')) {
                    calendar.unselect();
                    return;
                }
            }

            // Show time selection modal instead of prompt
            showTimeSelectionModal(info.start);
            calendar.unselect();
        },
        eventClick: function(info) {
            if (confirm('هل تريد حذف هذه الحصة؟')) {
                removeLesson(info.event.id);
            }
        },
        height: 'auto',
        aspectRatio: 1.8
    });
    
    calendar.render();
    
    // Load existing lessons if any
    {% if existing_lessons %}
    {% for lesson in existing_lessons %}
    addExistingLesson('{{ lesson.scheduled_date.isoformat }}', {{ lesson.lesson_number }});
    {% endfor %}
    {% endif %}

    // Initial update of lessons summary and button state
    updateLessonsSummary();
    
    function addLesson(date, time) {
        const [hours, minutes] = time.split(':');
        const lessonDate = new Date(date);
        lessonDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);
        
        const lessonNumber = scheduledLessons.length + 1;
        const lessonId = 'lesson_' + Date.now();
        
        const lesson = {
            id: lessonId,
            number: lessonNumber,
            datetime: lessonDate.toISOString(),
            title: `حصة رقم ${lessonNumber}`,
            start: lessonDate.toISOString(),
            end: new Date(lessonDate.getTime() + lessonDuration * 60000).toISOString(),
            color: '#3498db'
        };
        
        scheduledLessons.push(lesson);
        calendar.addEvent(lesson);
        updateLessonsSummary();
    }
    
    function addExistingLesson(datetime, number) {
        const lessonDate = new Date(datetime);
        const lessonId = 'existing_lesson_' + number;
        
        const lesson = {
            id: lessonId,
            number: number,
            datetime: lessonDate.toISOString(),
            title: `حصة رقم ${number}`,
            start: lessonDate.toISOString(),
            end: new Date(lessonDate.getTime() + lessonDuration * 60000).toISOString(),
            color: '#27ae60'
        };
        
        scheduledLessons.push(lesson);
        calendar.addEvent(lesson);
        updateLessonsSummary();
    }
    
    function removeLesson(lessonId) {
        scheduledLessons = scheduledLessons.filter(lesson => lesson.id !== lessonId);
        calendar.getEventById(lessonId).remove();
        
        // Renumber lessons
        scheduledLessons.forEach((lesson, index) => {
            lesson.number = index + 1;
            lesson.title = `حصة رقم ${lesson.number}`;
            const event = calendar.getEventById(lesson.id);
            if (event) {
                event.setProp('title', lesson.title);
            }
        });
        
        updateLessonsSummary();
    }
    
    function updateLessonsSummary() {
        document.getElementById('lessonsCount').innerHTML = 
            `<span class="text-lg font-bold text-blue-600">${scheduledLessons.length}</span> من <span class="text-lg font-bold text-gray-800">${maxLessons}</span> حصة`;
        
        const lessonsList = document.getElementById('lessonsList');
        lessonsList.innerHTML = '';
        
        scheduledLessons.sort((a, b) => new Date(a.datetime) - new Date(b.datetime));
        
        scheduledLessons.forEach(lesson => {
            const lessonDate = new Date(lesson.datetime);
            const div = document.createElement('div');
            div.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
            div.innerHTML = `
                <div>
                    <span class="font-semibold">حصة رقم ${lesson.number}</span>
                    <span class="text-gray-600 mr-2">${lessonDate.toLocaleDateString('ar-SA')} - ${lessonDate.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}</span>
                </div>
                <button onclick="removeLesson('${lesson.id}')" class="text-red-600 hover:text-red-800 px-2 py-1 rounded text-sm">
                    <i class="fas fa-trash ml-1"></i>
                    حذف
                </button>
            `;
            lessonsList.appendChild(div);
        });
        
        // Enable/disable save button
        const saveButton = document.getElementById('saveSchedule');
        const teacherSelect = document.getElementById('teacher_id');
        const hasLessons = scheduledLessons.length > 0;
        const hasTeacher = teacherSelect && teacherSelect.value && teacherSelect.value.trim() !== '';

        // زر الحفظ العادي متاح دائماً الآن
        saveButton.disabled = false;
        saveButton.className = 'bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors';

        // زر "مسح الكل وحفظ" متاح دائماً
        const clearAndSaveButton = document.getElementById('clearAndSave');
        clearAndSaveButton.disabled = false;
    }
    
    // Event listeners
    document.getElementById('clearAllLessons').addEventListener('click', function() {
        if (confirm('هل أنت متأكد من حذف جميع الحصص المجدولة؟')) {
            scheduledLessons.forEach(lesson => {
                calendar.getEventById(lesson.id).remove();
            });
            scheduledLessons = [];
            updateLessonsSummary();
        }
    });
    
    document.getElementById('autoSchedule').addEventListener('click', function() {
        if (scheduledLessons.length > 0) {
            if (!confirm('سيتم حذف الجدولة الحالية. هل تريد المتابعة؟')) {
                return;
            }
            // Clear existing lessons
            scheduledLessons.forEach(lesson => {
                calendar.getEventById(lesson.id).remove();
            });
            scheduledLessons = [];
        }
        
        // Auto schedule lessons (improved implementation)
        const startDate = new Date();
        startDate.setDate(startDate.getDate() + 1); // Start from tomorrow

        // Preferred times for auto scheduling
        const preferredTimes = ['10:00', '14:00', '16:00', '18:00'];
        let timeIndex = 0;

        for (let i = 0; i < maxLessons; i++) {
            const lessonDate = new Date(startDate);

            // Schedule lessons every 2-3 days
            const dayOffset = Math.floor(i / 2) * 3 + (i % 2) * 2;
            lessonDate.setDate(startDate.getDate() + dayOffset);

            // Skip weekends (Friday=5 and Saturday=6)
            while (lessonDate.getDay() === 5 || lessonDate.getDay() === 6) {
                lessonDate.setDate(lessonDate.getDate() + 1);
            }

            // Set time from preferred times
            const timeStr = preferredTimes[timeIndex % preferredTimes.length];
            const [hours, minutes] = timeStr.split(':').map(Number);
            lessonDate.setHours(hours, minutes, 0, 0);
            timeIndex++;

            const lessonId = 'auto_lesson_' + (i + 1);
            const lesson = {
                id: lessonId,
                number: i + 1,
                datetime: lessonDate.toISOString(),
                title: `حصة رقم ${i + 1}`,
                start: lessonDate.toISOString(),
                end: new Date(lessonDate.getTime() + lessonDuration * 60000).toISOString(),
                color: '#3498db'
            };

            scheduledLessons.push(lesson);
            calendar.addEvent(lesson);
        }
        
        updateLessonsSummary();
    });
    
    document.getElementById('teacher_id').addEventListener('change', function() {
        updateLessonsSummary();
    });

    // زر مسح الكل وحفظ
    document.getElementById('clearAndSave').addEventListener('click', function() {
        if (confirm('هل أنت متأكد من مسح جميع الحصص المجدولة؟ سيتم حذف جميع الحصص الموجودة نهائياً.')) {
            // إرسال طلب مسح مباشرة
            const scheduleData = {
                teacher_id: null,
                lessons: [],
                operation_type: 'clear'
            };

            console.log('إرسال طلب مسح الكل:', scheduleData);

            // إرسال البيانات باستخدام fetch API
            fetch(`{% url 'admin_save_scheduled_lessons' subscription.id %}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify(scheduleData)
            })
            .then(response => {
                console.log('استجابة الخادم:', response.status, response.statusText);
                return response.json();
            })
            .then(data => {
                console.log('بيانات الاستجابة:', data);
                if (data.success) {
                    alert(`تم مسح جميع الحصص المجدولة بنجاح!\nتم حذف ${data.deleted_count || 0} حصة للطالب ${data.student_name}`);

                    // إعادة تحميل الصفحة لإظهار التحديثات
                    window.location.reload();
                } else {
                    alert('خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('خطأ في الشبكة:', error);
                alert('حدث خطأ أثناء مسح الحصص. يرجى المحاولة مرة أخرى.');
            });
        }
    });

    document.getElementById('saveSchedule').addEventListener('click', function() {
        const teacherId = document.getElementById('teacher_id').value;

        // إزالة القيود - السماح بالحفظ حتى بدون معلم أو حصص
        if (scheduledLessons.length === 0) {
            if (!confirm('لا توجد حصص مجدولة. هل تريد حفظ جدولة فارغة (مسح جميع الحصص الموجودة)؟')) {
                return;
            }
        }

        // تحضير البيانات للإرسال
        const lessonsToSend = scheduledLessons.map(lesson => ({
            datetime: lesson.datetime,
            number: lesson.number,
            title: lesson.title
        }));

        console.log('Lessons to send:', lessonsToSend);

        // تحديد نوع العملية
        let operationType = '';
        let confirmMessage = '';

        if (scheduledLessons.length === 0) {
            operationType = 'clear';
            confirmMessage = 'هل أنت متأكد من مسح جميع الحصص المجدولة؟ سيتم حذف جميع الحصص الموجودة.';
        } else {
            operationType = 'schedule';
            confirmMessage = `هل أنت متأكد من حفظ جدولة ${scheduledLessons.length} حصة؟`;
        }

        if (confirm(confirmMessage)) {
            // استخدام API الجديد الموحد
            const scheduleData = {
                teacher_id: teacherId || null,  // السماح بقيمة فارغة
                lessons: lessonsToSend,
                operation_type: operationType
            };

            console.log('إرسال بيانات الجدولة:', scheduleData);

            // إرسال البيانات باستخدام fetch API
            fetch(`{% url 'admin_save_scheduled_lessons' subscription.id %}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify(scheduleData)
            })
            .then(response => {
                console.log('استجابة الخادم:', response.status, response.statusText);
                return response.json();
            })
            .then(data => {
                console.log('بيانات الاستجابة:', data);
                if (data.success) {
                    if (operationType === 'clear') {
                        alert(`تم مسح جميع الحصص المجدولة بنجاح!\nتم حذف ${data.deleted_count || 0} حصة للطالب ${data.student_name}`);
                    } else {
                        alert(`تم حفظ الجدولة بنجاح!\nتم إنشاء ${data.lessons_count} حصة للطالب ${data.student_name}${data.teacher_name ? ' مع المعلم ' + data.teacher_name : ''}`);
                    }

                    // إعادة تحميل الصفحة لإظهار التحديثات
                    window.location.reload();
                } else {
                    // التحقق من نوع الخطأ
                    if (data.error === 'subscription_not_active') {
                        // تحذير الاشتراك غير نشط
                        alert(`⚠️ تحذير ⚠️\n\n` +
                            `الطالب: ${data.student_name}\n` +
                            `الباقة: ${data.plan_name}\n` +
                            `حالة الاشتراك: ${data.subscription_status}\n` +
                            `تاريخ انتهاء الاشتراك: ${data.subscription_end_date}\n\n` +
                            `${data.message}\n\n` +
                            `لا يمكن جدولة حصص لاشتراك غير نشط.`);
                    } else if (data.error === 'subscription_expired') {
                        // تحذير انتهاء صلاحية الاشتراك
                        alert(`⚠️ تحذير ⚠️\n\n` +
                            `الطالب: ${data.student_name}\n` +
                            `الباقة: ${data.plan_name}\n` +
                            `تاريخ انتهاء الاشتراك: ${data.subscription_end_date}\n\n` +
                            `${data.message}\n\n` +
                            `لا يمكن جدولة حصص لاشتراك منتهي الصلاحية.`);
                    } else if (data.error === 'lessons_exhausted') {
                        // تحذير انتهاء الحصص
                        const warningMessage = `⚠️ تحذير هام ⚠️\n\n` +
                            `الطالب: ${data.student_name}\n` +
                            `الباقة: ${data.plan_name}\n` +
                            `إجمالي الحصص: ${data.total_lessons}\n` +
                            `الحصص المكتملة: ${data.completed_lessons}\n` +
                            `الحصص المتبقية: ${data.remaining_lessons}\n` +
                            `تاريخ انتهاء الاشتراك: ${data.subscription_end_date}\n\n` +
                            `${data.message}\n\n` +
                            `هل تريد المتابعة والجدولة رغم ذلك؟\n` +
                            `(سيتم إنشاء الحصص ولكن قد لا يتمكن الطالب من حضورها)`;

                        if (confirm(warningMessage)) {
                            // إعادة الإرسال مع التأكيد
                            scheduleData.force_schedule = true;
                            sendScheduleRequest(scheduleData);
                        }
                    } else if (data.error === 'insufficient_lessons') {
                        // تحذير عدم كفاية الحصص
                        const warningMessage = `⚠️ تحذير ⚠️\n\n` +
                            `الطالب: ${data.student_name}\n` +
                            `الباقة: ${data.plan_name}\n` +
                            `تحاول جدولة: ${data.lessons_to_schedule} حصة\n` +
                            `الحصص المتبقية: ${data.remaining_lessons} حصة فقط\n\n` +
                            `${data.message}\n\n` +
                            `هل تريد المتابعة والجدولة رغم ذلك؟`;

                        if (confirm(warningMessage)) {
                            // إعادة الإرسال مع التأكيد
                            scheduleData.force_schedule = true;
                            sendScheduleRequest(scheduleData);
                        }
                    } else {
                        // خطأ عادي
                        alert('خطأ: ' + (data.message || data.error));
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في الشبكة:', error);
                alert('حدث خطأ أثناء حفظ الجدولة. يرجى المحاولة مرة أخرى.');
            });
        }
    });

    // دالة لإرسال طلب الجدولة (للاستخدام في إعادة الإرسال مع التأكيد)
    function sendScheduleRequest(scheduleData) {
        fetch(`{% url 'admin_save_scheduled_lessons' subscription.id %}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify(scheduleData)
        })
        .then(response => {
            console.log('استجابة الخادم (إعادة الإرسال):', response.status, response.statusText);
            return response.json();
        })
        .then(data => {
            console.log('بيانات الاستجابة (إعادة الإرسال):', data);
            if (data.success) {
                alert(`تم حفظ الجدولة بنجاح!\nتم إنشاء ${data.lessons_count} حصة للطالب ${data.student_name}${data.teacher_name ? ' مع المعلم ' + data.teacher_name : ''}`);
                // إعادة تحميل الصفحة لإظهار التحديثات
                window.location.reload();
            } else {
                alert('خطأ: ' + (data.message || data.error));
            }
        })
        .catch(error => {
            console.error('خطأ في الشبكة (إعادة الإرسال):', error);
            alert('حدث خطأ أثناء حفظ الجدولة. يرجى المحاولة مرة أخرى.');
        });
    }

    // Time selection modal functions
    let selectedDateForModal = null;

    function showTimeSelectionModal(date) {
        selectedDateForModal = date;
        document.getElementById('selectedDate').textContent = date.toLocaleDateString('ar-SA');
        document.getElementById('timeModal').classList.remove('hidden');
    }

    function hideTimeSelectionModal() {
        document.getElementById('timeModal').classList.add('hidden');
        selectedDateForModal = null;
    }

    // Modal event listeners
    document.getElementById('cancelTimeSelection').addEventListener('click', hideTimeSelectionModal);

    document.getElementById('confirmTimeSelection').addEventListener('click', function() {
        const selectedTime = document.getElementById('lessonTime').value;
        if (selectedDateForModal && selectedTime) {
            addLesson(selectedDateForModal, selectedTime);
            hideTimeSelectionModal();
        }
    });

    // Close modal when clicking outside
    document.getElementById('timeModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideTimeSelectionModal();
        }
    });

    // Make functions global
    window.removeLesson = removeLesson;
    window.showTimeSelectionModal = showTimeSelectionModal;
});
</script>
{% endblock %}
