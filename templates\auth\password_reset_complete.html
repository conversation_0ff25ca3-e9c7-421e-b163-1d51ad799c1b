<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم تغيير كلمة المرور بنجاح - نظام قرآنيا التعليمي</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .arabic-text {
            font-family: '<PERSON>i', serif;
        }

        /* Islamic Design Colors */
        :root {
            --primary-green: #2D5016;
            --light-green: #4A7C59;
            --gold: #D4AF37;
            --light-gold: #F4E4BC;
            --dark-blue: #1B365D;
            --light-blue: #E8F4FD;
        }

        .bg-islamic-primary { background-color: var(--primary-green); }
        .bg-islamic-light { background-color: var(--light-green); }
        .bg-islamic-gold { background-color: var(--gold); }
        .bg-islamic-light-gold { background-color: var(--light-gold); }
        .bg-islamic-dark { background-color: var(--dark-blue); }
        .bg-islamic-light-blue { background-color: var(--light-blue); }

        .text-islamic-primary { color: var(--primary-green); }
        .text-islamic-gold { color: var(--gold); }
        .text-islamic-dark { color: var(--dark-blue); }

        .border-islamic-gold { border-color: var(--gold); }

        /* Islamic Pattern Background */
        .islamic-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        /* Success Animation */
        @keyframes checkmark {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .success-icon {
            animation: checkmark 0.6s ease-in-out;
        }
    </style>
</head>
<body class="bg-islamic-light-blue islamic-pattern min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- Success Card -->
        <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
            <!-- Header -->
            <div class="bg-green-600 p-8 text-center">
                <div class="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4 success-icon">
                    <i class="fas fa-check text-green-600 text-3xl"></i>
                </div>
                <h1 class="text-white text-2xl font-bold arabic-text mb-2">تم بنجاح!</h1>
                <p class="text-green-100">{{ ACADEMY_SLOGAN }}</p>
            </div>

            <!-- Content -->
            <div class="p-8 text-center">
                <div class="mb-6">
                    <i class="fas fa-shield-alt text-islamic-gold text-6xl mb-4"></i>
                    <h2 class="text-2xl font-bold text-islamic-dark mb-4">تم تغيير كلمة المرور</h2>
                    <p class="text-gray-600 leading-relaxed">
                        تم تغيير كلمة المرور الخاصة بك بنجاح. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.
                    </p>
                </div>

                <!-- Security Tips -->
                <div class="bg-islamic-light-blue rounded-lg p-6 mb-6">
                    <h3 class="font-semibold text-islamic-dark mb-3 flex items-center justify-center">
                        <i class="fas fa-lightbulb text-islamic-gold ml-2"></i>
                        نصائح أمنية
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-2 text-right">
                        <li class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 ml-2"></i>
                            <span>احتفظ بكلمة المرور في مكان آمن</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 ml-2"></i>
                            <span>لا تشارك كلمة المرور مع أحد</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 ml-2"></i>
                            <span>سجل الخروج عند الانتهاء</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 ml-2"></i>
                            <span>غير كلمة المرور دورياً</span>
                        </li>
                    </ul>
                </div>

                <!-- Login Button -->
                <a href="{% url 'login' %}"
                   class="block w-full bg-islamic-primary text-white py-4 px-4 rounded-lg hover:bg-islamic-light transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 mb-4">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول الآن
                </a>

                <!-- Additional Help -->
                <div class="pt-4 border-t border-gray-200">
                    <p class="text-sm text-gray-500 mb-2">تحتاج مساعدة؟</p>
                    <a href="#" class="text-islamic-primary hover:text-islamic-light font-medium text-sm">
                        <i class="fas fa-headset ml-1"></i>
                        تواصل مع الدعم الفني
                    </a>
                </div>
            </div>
        </div>

        <!-- Islamic Quote -->
        <div class="text-center mt-8">
            <p class="text-islamic-primary arabic-text text-lg font-semibold">
                "وَاللَّهُ غَالِبٌ عَلَىٰ أَمْرِهِ"
            </p>
            <p class="text-islamic-dark text-sm mt-1">
                سورة يوسف - آية 21
            </p>
        </div>
    </div>

    <script>
        // Auto redirect to login after 10 seconds
        setTimeout(function() {
            window.location.href = "{% url 'login' %}";
        }, 10000);

        // Show countdown
        let countdown = 10;
        const countdownElement = document.createElement('p');
        countdownElement.className = 'text-xs text-gray-400 mt-2';
        countdownElement.innerHTML = `سيتم توجيهك لصفحة تسجيل الدخول خلال <span id="countdown">${countdown}</span> ثانية`;
        document.querySelector('.pt-4.border-t.border-gray-200').appendChild(countdownElement);

        const countdownInterval = setInterval(function() {
            countdown--;
            document.getElementById('countdown').textContent = countdown;
            if (countdown <= 0) {
                clearInterval(countdownInterval);
            }
        }, 1000);
    </script>
</body>
</html>
