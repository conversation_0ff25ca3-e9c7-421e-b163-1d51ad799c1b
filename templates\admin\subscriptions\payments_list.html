{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة المدفوعات{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-4xl font-bold text-islamic-primary mb-2 flex items-center">
                    <i class="fas fa-credit-card text-islamic-gold ml-3"></i>
                    إدارة المدفوعات
                </h1>
                <p class="text-gray-600">إدارة ومراقبة جميع المدفوعات والتحويلات البنكية</p>
            </div>
            <a href="{% url 'admin_subscriptions' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للوحة الرئيسية
            </a>
        </div>

        <!-- Pending Bank Transfers Alert -->
        {% if pending_transfers %}
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-xl ml-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-yellow-800">تحويلات بنكية تحتاج مراجعة</h3>
                    <p class="text-yellow-700">يوجد {{ pending_transfers.count }} تحويل بنكي في انتظار التحقق والموافقة</p>
                </div>
            </div>
            <div class="mt-4">
                <a href="#pending-transfers" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                    مراجعة التحويلات
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-filter ml-2"></i>
                فلترة المدفوعات
            </h3>
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">حالة الدفع</label>
                    <select name="status" id="status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if value == status_filter %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="method" class="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع</label>
                    <select name="method" id="method" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الطرق</option>
                        {% for value, label in method_choices %}
                        <option value="{{ value }}" {% if value == method_filter %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <input type="text" name="search" id="search" value="{{ search_query }}" 
                           placeholder="اسم الطالب أو رقم المعاملة..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-2 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-search ml-2"></i>
                        بحث
                    </button>
                    <a href="{% url 'admin_payments_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg mr-2 transition-colors">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>

        <!-- Payments Table -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-list ml-2"></i>
                    قائمة المدفوعات
                    <span class="text-sm font-normal text-gray-500">({{ payments.count }} دفعة)</span>
                </h3>
            </div>
            
            {% if payments %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباقة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة الدفع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الدفع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for payment in payments %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-islamic-primary rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ payment.subscription.student.get_full_name }}</div>
                                        <div class="text-sm text-gray-500">{{ payment.subscription.student.email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ payment.subscription.plan.name }}</div>
                                <div class="text-sm text-gray-500">{{ payment.subscription.plan.get_plan_type_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ payment.amount }} {{ payment.subscription.plan.get_currency_symbol }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    {% if payment.payment_method == 'paypal' %}
                                        <i class="fab fa-paypal text-blue-600 ml-2"></i>
                                    {% elif payment.payment_method == 'stripe' %}
                                        <i class="fab fa-stripe text-purple-600 ml-2"></i>
                                    {% elif payment.payment_method == 'bank_transfer' %}
                                        <i class="fas fa-university text-green-600 ml-2"></i>
                                    {% else %}
                                        <i class="fas fa-credit-card text-gray-600 ml-2"></i>
                                    {% endif %}
                                    <span class="text-sm text-gray-900">{{ payment.get_payment_method_display }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ payment.payment_date|date:"Y-m-d H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if payment.status == 'completed' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        مكتمل
                                    </span>
                                {% elif payment.status == 'pending' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock ml-1"></i>
                                        في الانتظار
                                    </span>
                                {% elif payment.status == 'processing' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        <i class="fas fa-spinner ml-1"></i>
                                        قيد المعالجة
                                    </span>
                                {% elif payment.status == 'failed' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle ml-1"></i>
                                        فاشل
                                    </span>
                                {% else %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        {{ payment.get_status_display }}
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button onclick="viewPayment({{ payment.id }})" 
                                            class="text-blue-600 hover:text-blue-900 transition-colors">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if payment.transaction_id %}
                                    <button onclick="copyTransactionId('{{ payment.transaction_id }}')" 
                                            class="text-gray-600 hover:text-gray-900 transition-colors" 
                                            title="نسخ رقم المعاملة">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-credit-card text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد مدفوعات</h3>
                {% if status_filter or method_filter or search_query %}
                <p class="text-gray-500 mb-6">لم يتم العثور على مدفوعات تطابق معايير البحث</p>
                <a href="{% url 'admin_payments_list' %}" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors">
                    <i class="fas fa-times ml-2"></i>
                    إزالة الفلاتر
                </a>
                {% else %}
                <p class="text-gray-500 mb-6">لم يتم تسجيل أي مدفوعات بعد</p>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Pending Bank Transfers -->
        {% if pending_transfers %}
        <div id="pending-transfers" class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-yellow-50">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-university ml-2"></i>
                    التحويلات البنكية المعلقة
                    <span class="text-sm font-normal text-gray-500">({{ pending_transfers.count }} تحويل)</span>
                </h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباقة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التحويل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إثبات التحويل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for transfer in pending_transfers %}
                        <tr class="hover:bg-yellow-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ transfer.payment.subscription.student.get_full_name }}</div>
                                        <div class="text-sm text-gray-500">{{ transfer.payment.subscription.student.email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ transfer.payment.subscription.plan.name }}</div>
                                <div class="text-sm text-gray-500">{{ transfer.payment.subscription.plan.get_plan_type_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ transfer.transfer_amount }} {{ transfer.payment.subscription.plan.get_currency_symbol }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ transfer.uploaded_at|date:"Y-m-d H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if transfer.transfer_receipt %}
                                <a href="{{ transfer.transfer_receipt.url }}" target="_blank" 
                                   class="text-blue-600 hover:text-blue-900 flex items-center">
                                    <i class="fas fa-file-image ml-1"></i>
                                    عرض الإثبات
                                </a>
                                {% else %}
                                <span class="text-gray-500 text-sm">لا يوجد إثبات</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="{% url 'admin_verify_transfer' transfer.id %}" 
                                       class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs transition-colors">
                                        <i class="fas fa-check ml-1"></i>
                                        مراجعة
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
function viewPayment(paymentId) {
    alert(`عرض تفاصيل الدفعة رقم: ${paymentId}\nسيتم تطوير هذه الميزة قريباً`);
}

function copyTransactionId(transactionId) {
    navigator.clipboard.writeText(transactionId).then(function() {
        alert('تم نسخ رقم المعاملة: ' + transactionId);
    }, function(err) {
        alert('فشل في نسخ رقم المعاملة');
    });
}
</script>
{% endblock %}
