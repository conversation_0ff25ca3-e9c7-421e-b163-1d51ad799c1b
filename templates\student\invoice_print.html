<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ invoice.invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #2D5A87 0%, #1E3A5F 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin-bottom: 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .company-info h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .company-info p {
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .invoice-info {
            text-align: left;
        }

        .invoice-info h2 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .invoice-info p {
            margin-bottom: 5px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }

        .status-paid { background: #D1FAE5; color: #065F46; }
        .status-pending { background: #FEF3C7; color: #92400E; }
        .status-overdue { background: #FEE2E2; color: #991B1B; }

        .content {
            background: white;
            padding: 30px;
            border: 1px solid #e5e7eb;
            border-top: none;
            border-radius: 0 0 10px 10px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2D5A87;
            border-bottom: 2px solid #2D5A87;
            padding-bottom: 5px;
        }

        .customer-info {
            background: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }

        .customer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .label {
            font-weight: bold;
            color: #6b7280;
            font-size: 12px;
            display: block;
            margin-bottom: 2px;
        }

        .value {
            color: #111827;
            font-weight: 600;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
        }

        .invoice-table th,
        .invoice-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }

        .invoice-table th {
            background: #f9fafb;
            font-weight: bold;
            color: #374151;
            font-size: 12px;
            text-transform: uppercase;
        }

        .invoice-table tbody tr:hover {
            background: #f9fafb;
        }

        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 25px;
        }

        .totals-box {
            background: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            min-width: 300px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .total-row:last-child {
            border-bottom: 2px solid #2D5A87;
            font-weight: bold;
            font-size: 16px;
            color: #2D5A87;
        }

        .subscription-details {
            background: #eff6ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }

        .subscription-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 12px;
        }

        @media print {
            body {
                background: white;
            }
            
            .container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .header {
                border-radius: 0;
            }
            
            .content {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="company-info">
                    <div style="display: flex; align-items: flex-start; gap: 15px;">
                        {% if company_info.logo %}
                        <div style="flex-shrink: 0;">
                            <img src="{{ company_info.logo }}" alt="{{ company_info.name }}" style="width: 60px; height: 60px; border-radius: 8px; background: white; padding: 5px;">
                        </div>
                        {% endif %}
                        <div>
                            <h1>{{ company_info.name }}</h1>
                            {% if company_info.description %}
                            <p style="font-size: 12px; margin-bottom: 8px;">{{ company_info.description }}</p>
                            {% endif %}
                            <p>{{ company_info.address }}</p>
                            <p>{{ company_info.phone }}</p>
                            <p>{{ company_info.email }}</p>
                            {% if company_info.website %}
                            <p>{{ company_info.website }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="invoice-info">
                    <h2>فاتورة</h2>
                    <p>رقم: {{ invoice.invoice_number }}</p>
                    <p>التاريخ: {{ invoice.issue_date|date:"Y-m-d" }}</p>
                    <p>الاستحقاق: {{ invoice.due_date|date:"Y-m-d" }}</p>
                    <p>
                        الحالة: 
                        <span class="status-badge status-{{ invoice.status }}">
                            {{ invoice.get_status_display }}
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Customer Information -->
            <h3 class="section-title">معلومات العميل</h3>
            <div class="customer-info">
                <div class="customer-grid">
                    <div class="info-item">
                        <span class="label">الاسم:</span>
                        <span class="value">{{ invoice.customer_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">البريد الإلكتروني:</span>
                        <span class="value">{{ invoice.customer_email }}</span>
                    </div>
                    {% if invoice.customer_phone %}
                    <div class="info-item">
                        <span class="label">رقم الهاتف:</span>
                        <span class="value">{{ invoice.customer_phone }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Invoice Items -->
            <h3 class="section-title">تفاصيل الفاتورة</h3>
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td>{{ item.description }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.unit_price|floatformat:2 }}</td>
                        <td>{{ item.total_price|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Totals -->
            <div class="totals-section">
                <div class="totals-box">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span>{{ invoice.subtotal|floatformat:2 }}</span>
                    </div>
                    {% if invoice.tax_amount > 0 %}
                    <div class="total-row">
                        <span>الضريبة:</span>
                        <span>{{ invoice.tax_amount|floatformat:2 }}</span>
                    </div>
                    {% endif %}
                    {% if invoice.discount_amount > 0 %}
                    <div class="total-row">
                        <span>الخصم:</span>
                        <span style="color: #059669;">-{{ invoice.discount_amount|floatformat:2 }}</span>
                    </div>
                    {% endif %}
                    <div class="total-row">
                        <span>المجموع الإجمالي:</span>
                        <span>{{ invoice.total_amount|floatformat:2 }}</span>
                    </div>
                </div>
            </div>

            <!-- Subscription Details -->
            <h3 class="section-title">تفاصيل الاشتراك</h3>
            <div class="subscription-details">
                <div class="subscription-grid">
                    <div>
                        <span class="label">الباقة:</span>
                        <span class="value">{{ invoice.plan_name }}</span>
                    </div>
                    <div>
                        <span class="label">تاريخ البداية:</span>
                        <span class="value">{{ invoice.subscription.start_date|date:"Y-m-d" }}</span>
                    </div>
                    <div>
                        <span class="label">تاريخ النهاية:</span>
                        <span class="value">{{ invoice.subscription.end_date|date:"Y-m-d" }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>شكراً لك على اختيار {{ company_info.name }}</p>
            <p>تم إنشاء هذه الفاتورة إلكترونياً في {{ invoice.created_at|date:"Y-m-d H:i" }}</p>
        </div>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
