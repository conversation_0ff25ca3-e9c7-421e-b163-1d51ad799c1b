from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth import get_user_model
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.db.models import Avg, Sum
from django.utils import timezone
from datetime import datetime, timedelta

# تم إزالة التبعية على courses.models
from lessons.models import LiveLesson
from .models import CustomReport, ReportTemplate, ReportSchedule
from .utils import ReportGenerator

User = get_user_model()

def is_admin(user):
    """التحقق من أن المستخدم مدير"""
    return user.is_authenticated and user.user_type == 'admin'

@login_required
@user_passes_test(is_admin)
def admin_reports(request):
    """صفحة التقارير الرئيسية"""

    # إحصائيات سريعة للمعلمين
    teachers_count = User.objects.filter(user_type='teacher').count()
    active_teachers_count = User.objects.filter(user_type='teacher', is_active=True).count()
    # تبسيط حساب متوسط التقييم
    teachers_avg_rating = 4.5  # قيمة افتراضية مؤقتة
    teachers_total_lessons = LiveLesson.objects.filter(teacher__user_type='teacher').count()

    # إحصائيات سريعة للطلاب
    students_count = User.objects.filter(user_type='student').count()
    active_students_count = User.objects.filter(user_type='student', is_active=True).count()
    students_total_enrollments = 0  # تم إزالة التبعية على Enrollment
    completed_lessons = LiveLesson.objects.filter(status__in=['completed', 'rated']).count()
    total_lessons = LiveLesson.objects.count()
    students_completion_rate = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0

    # إحصائيات سريعة للدورات - تم تعطيلها بعد حذف courses app
    courses_count = 0  # تم إزالة التبعية على Course
    active_courses_count = 0  # تم إزالة التبعية على Course
    courses_avg_rating = 0  # تم إزالة التبعية على Course
    courses_total_enrollments = 0  # تم إزالة التبعية على Enrollment

    # إحصائيات مالية سريعة - تم تعطيلها بعد حذف courses app
    total_revenue = 0  # تم إزالة التبعية على Enrollment
    monthly_revenue = 0  # تم إزالة التبعية على Enrollment
    avg_course_price = 0  # تم إزالة التبعية على Course
    total_payments = 0  # تم إزالة التبعية على Enrollment

    # جلب التقارير المحفوظة لكل فئة
    teachers_reports = CustomReport.objects.filter(category='teachers').order_by('-created_at')[:5]
    students_reports = CustomReport.objects.filter(category='students').order_by('-created_at')[:5]
    courses_reports = CustomReport.objects.filter(category='courses').order_by('-created_at')[:5]
    financial_reports = CustomReport.objects.filter(category='financial').order_by('-created_at')[:5]

    # إحصائيات إضافية للنظرة العامة
    total_reports = CustomReport.objects.count()
    active_reports = CustomReport.objects.filter(is_active=True).count()
    scheduled_reports = CustomReport.objects.filter(is_scheduled=True).count()
    total_downloads = sum(report.generated_reports.aggregate(
        total=Sum('download_count'))['total'] or 0 for report in CustomReport.objects.all())

    # التقارير الحديثة
    recent_reports = CustomReport.objects.order_by('-created_at')[:10]

    context = {
        # إحصائيات المعلمين
        'teachers_count': teachers_count,
        'active_teachers_count': active_teachers_count,
        'teachers_avg_rating': round(teachers_avg_rating, 1),
        'teachers_total_lessons': teachers_total_lessons,

        # إحصائيات الطلاب
        'students_count': students_count,
        'active_students_count': active_students_count,
        'students_completion_rate': round(students_completion_rate, 1),
        'students_total_enrollments': students_total_enrollments,

        # إحصائيات الدورات
        'courses_count': courses_count,
        'active_courses_count': active_courses_count,
        'courses_avg_rating': round(courses_avg_rating, 1),
        'courses_total_enrollments': courses_total_enrollments,

        # إحصائيات مالية
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue,
        'avg_course_price': avg_course_price,
        'total_payments': total_payments,

        # التقارير المحفوظة
        'teachers_reports': teachers_reports,
        'students_reports': students_reports,
        'courses_reports': courses_reports,
        'financial_reports': financial_reports,

        # إحصائيات النظرة العامة
        'total_reports': total_reports,
        'active_reports': active_reports,
        'scheduled_reports': scheduled_reports,
        'total_downloads': total_downloads,
        'recent_reports': recent_reports,
    }

    return render(request, 'admin/reports.html', context)

@login_required
@user_passes_test(is_admin)
@require_http_methods(["POST"])
def admin_reports_create(request):
    """إنشاء تقرير مخصص جديد"""
    try:
        # استخراج البيانات من النموذج
        title = request.POST.get('title')
        description = request.POST.get('description', '')
        category = request.POST.get('category')
        date_from = request.POST.get('date_from')
        date_to = request.POST.get('date_to')
        is_scheduled = request.POST.get('is_scheduled') == 'on'
        schedule_frequency = request.POST.get('schedule_frequency', '')

        # التحقق من البيانات المطلوبة
        if not title or not category:
            return JsonResponse({
                'success': False,
                'error': 'العنوان وفئة التقرير مطلوبان'
            })

        # تحويل التواريخ
        date_from_obj = None
        date_to_obj = None
        if date_from:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
        if date_to:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()

        # استخراج الفلاتر
        filters = {}
        for key, value in request.POST.items():
            if key.startswith('filters[') and key.endswith(']'):
                filter_key = key[8:-1]  # إزالة 'filters[' و ']'
                filters[filter_key] = value

        # إنشاء التقرير
        custom_report = CustomReport.objects.create(
            title=title,
            description=description,
            category=category,
            created_by=request.user,
            date_from=date_from_obj,
            date_to=date_to_obj,
            filters=filters,
            is_scheduled=is_scheduled,
            schedule_frequency=schedule_frequency if is_scheduled else ''
        )

        # إنشاء جدولة إذا كان مطلوباً
        if is_scheduled and schedule_frequency:
            # حساب التنفيذ التالي
            next_execution = timezone.now()
            if schedule_frequency == 'daily':
                next_execution += timedelta(days=1)
            elif schedule_frequency == 'weekly':
                next_execution += timedelta(weeks=1)
            elif schedule_frequency == 'monthly':
                next_execution += timedelta(days=30)
            elif schedule_frequency == 'quarterly':
                next_execution += timedelta(days=90)

            ReportSchedule.objects.create(
                custom_report=custom_report,
                frequency=schedule_frequency,
                execution_time=timezone.now().time(),
                next_execution=next_execution
            )

        return JsonResponse({
            'success': True,
            'message': 'تم إنشاء التقرير بنجاح',
            'report_id': custom_report.id
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'حدث خطأ: {str(e)}'
        })

@login_required
@user_passes_test(is_admin)
def admin_reports_download(request, report_id):
    """تحميل تقرير"""
    try:
        custom_report = get_object_or_404(CustomReport, id=report_id)
        file_format = request.GET.get('format', 'pdf')

        if file_format not in ['pdf', 'excel', 'csv']:
            return JsonResponse({'error': 'صيغة غير مدعومة'}, status=400)

        # توليد التقرير
        generator = ReportGenerator(custom_report)
        generated_report = generator.generate_report(file_format, request.user)

        if not generated_report.is_ready:
            return JsonResponse({'error': 'فشل في توليد التقرير'}, status=500)

        # إرسال الملف
        response = HttpResponse(
            generated_report.file_path.read(),
            content_type='application/octet-stream'
        )

        filename = f"{custom_report.title}_{datetime.now().strftime('%Y%m%d')}"
        if file_format == 'pdf':
            response['Content-Type'] = 'application/pdf'
            filename += '.pdf'
        elif file_format == 'excel':
            response['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            filename += '.xlsx'
        elif file_format == 'csv':
            response['Content-Type'] = 'text/csv'
            filename += '.csv'

        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # تحديث عداد التحميل
        generated_report.increment_download_count()

        return response

    except Exception as e:
        return JsonResponse({'error': f'حدث خطأ: {str(e)}'}, status=500)

@login_required
@user_passes_test(is_admin)
@require_http_methods(["DELETE"])
def admin_reports_delete(request, report_id):
    """حذف تقرير"""
    try:
        custom_report = get_object_or_404(CustomReport, id=report_id)

        # حذف الملفات المولدة
        for generated_report in custom_report.generated_reports.all():
            if generated_report.file_path:
                try:
                    generated_report.file_path.delete()
                except:
                    pass

        # حذف التقرير
        custom_report.delete()

        return JsonResponse({
            'success': True,
            'message': 'تم حذف التقرير بنجاح'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'حدث خطأ: {str(e)}'
        })

@login_required
@user_passes_test(is_admin)
def admin_reports_view(request, report_id):
    """عرض تفاصيل التقرير"""
    custom_report = get_object_or_404(CustomReport, id=report_id)
    generated_reports = custom_report.generated_reports.order_by('-generated_at')

    context = {
        'custom_report': custom_report,
        'generated_reports': generated_reports,
    }

    return render(request, 'admin/report_detail.html', context)

@login_required
@user_passes_test(is_admin)
def admin_reports_templates(request):
    """صفحة قوالب التقارير"""
    templates = ReportTemplate.objects.filter(is_active=True).order_by('category', 'name')

    context = {
        'templates': templates,
    }

    return render(request, 'admin/report_templates.html', context)
