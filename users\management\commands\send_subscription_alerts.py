"""
أمر Django لإرسال تنبيهات انتهاء الاشتراكات
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from users.email_service import EmailService
from users.email_models import EmailTemplate
from subscriptions.models import StudentSubscription
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'إرسال تنبيهات انتهاء الاشتراكات للطلاب والمديرين'

    def add_arguments(self, parser):
        parser.add_argument(
            '--alert-type',
            type=str,
            choices=['7d', '3d', '1d', 'expired'],
            default='7d',
            help='نوع التنبيه (7d, 3d, 1d, expired)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='تشغيل تجريبي بدون إرسال فعلي'
        )

    def handle(self, *args, **options):
        alert_type = options['alert_type']
        dry_run = options['dry_run']
        
        self.stdout.write(f"⚠️ بدء إرسال تنبيهات الاشتراك ({alert_type})")
        
        # تحديد التاريخ المناسب للتنبيه
        today = timezone.now().date()
        
        if alert_type == '7d':
            target_date = today + timedelta(days=7)
            alert_title = "انتهاء الاشتراك خلال 7 أيام"
        elif alert_type == '3d':
            target_date = today + timedelta(days=3)
            alert_title = "انتهاء الاشتراك خلال 3 أيام"
        elif alert_type == '1d':
            target_date = today + timedelta(days=1)
            alert_title = "انتهاء الاشتراك غداً"
        elif alert_type == 'expired':
            target_date = today
            alert_title = "انتهى الاشتراك"
        
        # البحث عن الاشتراكات المناسبة
        if alert_type == 'expired':
            subscriptions = StudentSubscription.objects.filter(
                end_date=target_date,
                status='active'
            ).select_related('student', 'plan')
        else:
            subscriptions = StudentSubscription.objects.filter(
                end_date=target_date,
                status='active'
            ).select_related('student', 'plan')
        
        # الحصول على قالب التنبيه
        expiry_template = EmailTemplate.objects.filter(
            template_type='subscription_expiry',
            is_active=True
        ).first()
        
        if not expiry_template:
            self.stdout.write(
                self.style.ERROR('❌ لا يوجد قالب تنبيه انتهاء اشتراك نشط')
            )
            return
        
        email_service = EmailService()
        sent_count = 0
        failed_count = 0
        
        # معالجة كل اشتراك
        for subscription in subscriptions:
            try:
                # حساب الأيام المتبقية
                days_remaining = (subscription.end_date - today).days
                
                # إعداد السياق
                context = {
                    'student_name': subscription.student.get_full_name(),
                    'subscription_type': subscription.plan.name,
                    'expiry_date': subscription.end_date.strftime('%Y-%m-%d'),
                    'days_remaining': max(0, days_remaining),
                    'remaining_lessons': subscription.remaining_lessons,
                    'alert_type': alert_type,
                    'alert_title': alert_title,

                    'plan_price': subscription.plan.price,
                    'plan_currency': subscription.plan.currency,
                }
                
                # إرسال تنبيه للطالب
                if not dry_run:
                    success = email_service.send_email(
                        recipient=subscription.student,
                        template=expiry_template,
                        context=context,
                        immediate=False
                    )
                    if success:
                        sent_count += 1
                    else:
                        failed_count += 1
                else:
                    sent_count += 1
                    self.stdout.write(
                        f"  📧 سيتم إرسال تنبيه للطالب: {subscription.student.email}"
                    )
                
                # إرسال تنبيه للمديرين (فقط للحالات الحرجة)
                if alert_type in ['1d', 'expired']:
                    admin_users = User.objects.filter(user_type='admin', is_active=True)
                    
                    admin_context = context.copy()
                    admin_context.update({
                        'recipient_type': 'admin',
                        'student_email': subscription.student.email,
                        'subscription_id': subscription.id,
                    })
                    
                    for admin in admin_users:
                        if not dry_run:
                            success = email_service.send_email(
                                recipient=admin,
                                template=expiry_template,
                                context=admin_context,
                                immediate=False
                            )
                            if success:
                                sent_count += 1
                            else:
                                failed_count += 1
                        else:
                            sent_count += 1
                            self.stdout.write(
                                f"  📧 سيتم إرسال تنبيه للمدير: {admin.email}"
                            )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'❌ خطأ في معالجة اشتراك {subscription.id}: {str(e)}'
                    )
                )
                failed_count += 1
        
        # عرض النتائج
        total_subscriptions = len(subscriptions)
        self.stdout.write(
            self.style.SUCCESS(
                f'✅ تم العثور على {total_subscriptions} اشتراك'
            )
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'🧪 تشغيل تجريبي: كان سيتم إرسال {sent_count} تنبيه'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'📧 تم إرسال {sent_count} تنبيه بنجاح'
                )
            )
            if failed_count > 0:
                self.stdout.write(
                    self.style.ERROR(
                        f'❌ فشل في إرسال {failed_count} تنبيه'
                    )
                )


