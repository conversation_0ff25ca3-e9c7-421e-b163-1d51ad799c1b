{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/enhanced-breadcrumb.css' %}">
<style>
    .logs-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .logs-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 1.5rem;
    }

    .search-box {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .log-item {
        padding: 1rem;
        border-bottom: 1px solid #eee;
        transition: all 0.3s ease;
    }

    .log-item:hover {
        background-color: #f8f9fa;
    }

    .log-item:last-child {
        border-bottom: none;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .status-sent {
        background: #d4edda;
        color: #155724;
    }

    .status-failed {
        background: #f8d7da;
        color: #721c24;
    }

    .status-bounced {
        background: #fff3cd;
        color: #856404;
    }

    .status-opened {
        background: #cce5ff;
        color: #004085;
    }

    .status-clicked {
        background: #e2e3ff;
        color: #383d41;
    }

    /* تحسين مسارات التوجيه */
    .breadcrumb {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    .breadcrumb-item {
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #11998e;
        text-decoration: none;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: inline-block;
    }

    .breadcrumb-item a:hover {
        background: rgba(17, 153, 142, 0.1);
        color: #0d7377;
        transform: translateY(-1px);
    }

    .breadcrumb-item.active {
        color: #2D5016;
        font-weight: 600;
        background: rgba(45, 80, 22, 0.1);
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
        color: #11998e;
        font-weight: bold;
        margin: 0 0.5rem;
    }

    .page-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    .btn-export {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }

    @media (max-width: 768px) {
        .log-item {
            padding: 0.75rem;
        }
        
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'admin_dashboard' %}">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'technical_settings' %}">
                    <i class="fas fa-cogs me-2"></i>الإعدادات التقنية
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'email_settings_dashboard' %}">
                    <i class="fas fa-envelope-open-text me-2"></i>إعدادات البريد
                </a>
            </li>
            <li class="breadcrumb-item active">
                <i class="fas fa-history me-2"></i>سجلات الإرسال
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="page-header text-center">
        <div class="container">
            <h1 class="mb-3">
                <i class="fas fa-history me-3"></i>
                سجلات البريد الإلكتروني
            </h1>
            <p class="lead mb-0">عرض وتتبع جميع رسائل البريد الإلكتروني المرسلة</p>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="search-box">
        <form method="get" class="row align-items-end">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث في السجلات</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="البريد الإلكتروني أو الموضوع">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">حالة الإرسال</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
                <a href="{% url 'email_logs_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>مسح
                </a>
            </div>
            <div class="col-md-2 text-end">
                <button type="button" class="btn btn-export" onclick="exportLogs()">
                    <i class="fas fa-download me-2"></i>تصدير
                </button>
            </div>
        </form>
    </div>

    <!-- Logs List -->
    <div class="logs-card">
        <div class="logs-header">
            <h4 class="mb-0">
                <i class="fas fa-list me-2"></i>
                سجل الرسائل
                {% if page_obj %}
                    <span class="badge bg-light text-dark ms-2">{{ page_obj.paginator.count }} رسالة</span>
                {% endif %}
            </h4>
        </div>
        
        {% if page_obj %}
            {% for log in page_obj %}
                <div class="log-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <strong class="me-3">{{ log.recipient.get_full_name|default:log.recipient.email }}</strong>
                                <span class="status-badge status-{{ log.status }}">
                                    {% if log.status == 'sent' %}
                                        <i class="fas fa-check me-1"></i>تم الإرسال
                                    {% elif log.status == 'failed' %}
                                        <i class="fas fa-times me-1"></i>فشل
                                    {% elif log.status == 'bounced' %}
                                        <i class="fas fa-exclamation-triangle me-1"></i>مرتد
                                    {% elif log.status == 'opened' %}
                                        <i class="fas fa-envelope-open me-1"></i>تم فتحه
                                    {% elif log.status == 'clicked' %}
                                        <i class="fas fa-mouse-pointer me-1"></i>تم النقر
                                    {% endif %}
                                </span>
                            </div>
                            
                            <h6 class="mb-1">{{ log.subject }}</h6>
                            
                            <div class="d-flex align-items-center gap-3 text-muted">
                                <small>
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ log.sent_at|date:"d/m/Y H:i" }}
                                </small>
                                {% if log.template_name %}
                                    <small>
                                        <i class="fas fa-file-alt me-1"></i>
                                        {{ log.template_name }}
                                    </small>
                                {% endif %}
                                {% if log.event_type %}
                                    <small>
                                        <i class="fas fa-bell me-1"></i>
                                        {{ log.event_type }}
                                    </small>
                                {% endif %}
                            </div>
                            
                            {% if log.error_message %}
                                <div class="alert alert-danger mt-2 mb-0 py-2">
                                    <small>
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ log.error_message|truncatechars:100 }}
                                    </small>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="text-end">
                            {% if log.opened_at %}
                                <small class="text-success d-block">
                                    <i class="fas fa-eye me-1"></i>
                                    فُتح في {{ log.opened_at|date:"d/m H:i" }}
                                </small>
                            {% endif %}
                            {% if log.clicked_at %}
                                <small class="text-info d-block">
                                    <i class="fas fa-mouse-pointer me-1"></i>
                                    نُقر في {{ log.clicked_at|date:"d/m H:i" }}
                                </small>
                            {% endif %}
                            <button class="btn btn-outline-info btn-sm mt-1" onclick="viewLogDetails({{ log.id }})">
                                <i class="fas fa-info-circle me-1"></i>تفاصيل
                            </button>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد سجلات</h4>
                <p class="text-muted">لم يتم العثور على سجلات تطابق معايير البحث</p>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                                {{ num }}
                            </a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}
</div>

<!-- Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل السجل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewLogDetails(logId) {
    const modal = new bootstrap.Modal(document.getElementById('logDetailsModal'));
    const content = document.getElementById('logDetailsContent');
    
    // إظهار تحميل
    content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">جاري التحميل...</p></div>';
    modal.show();
    
    // تحميل التفاصيل (سيتم إضافة API لاحقاً)
    setTimeout(() => {
        content.innerHTML = `
            <div class="alert alert-info">
                <h6>تفاصيل السجل #${logId}</h6>
                <p>سيتم إضافة تفاصيل مفصلة قريباً تتضمن:</p>
                <ul>
                    <li>محتوى الرسالة الكامل</li>
                    <li>معلومات SMTP المفصلة</li>
                    <li>سجل المحاولات</li>
                    <li>إحصائيات التفاعل</li>
                </ul>
            </div>
        `;
    }, 1000);
}

function exportLogs() {
    // تصدير السجلات (سيتم إضافة API لاحقاً)
    showToast('سيتم إضافة وظيفة التصدير قريباً', 'info');
}

function showToast(message, type) {
    // إنشاء toast بسيط
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
        ${message}
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// تحديث الصفحة كل دقيقة لعرض السجلات الجديدة
setInterval(function() {
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 60000);
</script>
{% endblock %}
