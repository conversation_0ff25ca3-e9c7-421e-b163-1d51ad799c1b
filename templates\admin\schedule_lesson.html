{% extends 'base.html' %}
{% load static %}

{% block title %}جدولة حصة جديدة - {{ SITE_NAME }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-islamic-primary mb-2">
                    <i class="fas fa-calendar-plus text-islamic-gold ml-3"></i>
                    جدولة حصة جديدة
                </h1>
                <p class="text-gray-600">إنشاء حصة جديدة وربطها بالتسجيلات النشطة</p>
            </div>
            <div class="flex gap-3">
                <a href="{% url 'admin_lessons' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-arrow-right ml-1"></i>
                    العودة للحصص
                </a>
            </div>
        </div>
    </div>

    <!-- Schedule Lesson Form -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Enrollment Selection -->
            <div>
                <label for="enrollment_id" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-user-graduate text-blue-600 ml-1"></i>
                    اختيار التسجيل (الطالب - المعلم - الدورة)
                </label>
                <select name="enrollment_id" id="enrollment_id" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                    <option value="">اختر التسجيل...</option>
                    {% for enrollment in active_enrollments %}
                        <option value="{{ enrollment.id }}">
                            {{ enrollment.student.get_full_name }} - {{ enrollment.teacher.get_full_name }} - {{ enrollment.course.title }}
                        </option>
                    {% endfor %}
                </select>
            </div>

            <!-- Lesson Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-heading text-purple-600 ml-1"></i>
                    عنوان الحصة
                </label>
                <input type="text" name="title" id="title" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                       placeholder="مثال: حفظ سورة البقرة - الآيات 1-10">
            </div>

            <!-- Lesson Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-align-left text-green-600 ml-1"></i>
                    وصف الحصة (اختياري)
                </label>
                <textarea name="description" id="description" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                          placeholder="وصف مختصر لمحتوى الحصة وأهدافها..."></textarea>
            </div>

            <!-- Date and Time -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="scheduled_date" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-blue-600 ml-1"></i>
                        تاريخ الحصة
                    </label>
                    <input type="date" name="scheduled_date" id="scheduled_date" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>
                
                <div>
                    <label for="scheduled_time" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-clock text-orange-600 ml-1"></i>
                        وقت الحصة
                    </label>
                    <input type="time" name="scheduled_time" id="scheduled_time" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>
            </div>

            <!-- Duration -->
            <div>
                <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-hourglass-half text-red-600 ml-1"></i>
                    مدة الحصة
                </label>
                <select name="duration_minutes" id="duration_minutes" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                    {% for value, label in duration_choices %}
                        <option value="{{ value }}" {% if value == 45 %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end gap-4">
                <a href="{% url 'admin_lessons' %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-times ml-1"></i>
                    إلغاء
                </a>
                <button type="submit" 
                        class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-save ml-1"></i>
                    جدولة الحصة
                </button>
            </div>
        </form>
    </div>

    <!-- Active Enrollments Info -->
    {% if active_enrollments %}
    <div class="mt-8 bg-blue-50 rounded-lg p-6">
        <h3 class="text-lg font-bold text-blue-900 mb-4">
            <i class="fas fa-info-circle text-blue-600 ml-2"></i>
            التسجيلات النشطة المتاحة
        </h3>
        <div class="grid gap-4">
            {% for enrollment in active_enrollments %}
                <div class="bg-white rounded-lg p-4 border border-blue-200">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">{{ enrollment.course.title }}</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2 text-sm text-gray-600">
                                <div>
                                    <i class="fas fa-user-graduate text-blue-600 ml-1"></i>
                                    الطالب: {{ enrollment.student.get_full_name }}
                                </div>
                                <div>
                                    <i class="fas fa-chalkboard-teacher text-green-600 ml-1"></i>
                                    المعلم: {{ enrollment.teacher.get_full_name }}
                                </div>
                            </div>
                            <div class="mt-2 text-sm text-gray-500">
                                <i class="fas fa-calendar ml-1"></i>
                                تاريخ التسجيل: {{ enrollment.enrollment_date|date:"Y-m-d" }}
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                                {{ enrollment.get_status_display }}
                            </span>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <div class="mt-8 bg-yellow-50 rounded-lg p-6 text-center">
        <i class="fas fa-exclamation-triangle text-yellow-600 text-3xl mb-3"></i>
        <h3 class="text-lg font-bold text-yellow-900 mb-2">لا توجد تسجيلات نشطة</h3>
        <p class="text-yellow-700 mb-4">يجب إنشاء تسجيلات نشطة أولاً قبل جدولة الحصص</p>
        <a href="{% url 'admin_create_enrollment' %}" 
           class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-plus ml-1"></i>
            إنشاء تسجيل جديد
        </a>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const dateInput = document.getElementById('scheduled_date');
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;
    
    // Set default time to current time + 1 hour
    const timeInput = document.getElementById('scheduled_time');
    const now = new Date();
    now.setHours(now.getHours() + 1);
    const timeString = now.toTimeString().slice(0, 5);
    timeInput.value = timeString;
    
    // Auto-fill lesson title based on enrollment selection
    const enrollmentSelect = document.getElementById('enrollment_id');
    const titleInput = document.getElementById('title');
    
    enrollmentSelect.addEventListener('change', function() {
        if (this.value) {
            const selectedOption = this.options[this.selectedIndex];
            const text = selectedOption.text;
            const parts = text.split(' - ');
            if (parts.length >= 3) {
                const courseName = parts[2];
                titleInput.value = `حصة ${courseName}`;
            }
        }
    });
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const selectedDate = new Date(dateInput.value + 'T' + timeInput.value);
        const now = new Date();
        
        if (selectedDate <= now) {
            e.preventDefault();
            alert('يجب أن يكون موعد الحصة في المستقبل');
            return false;
        }
    });
});
</script>
{% endblock %}
