{% extends 'base.html' %}
{% load static %}
{% load math_filters %}

{% block title %}تقييمات الطلاب - قرآنيا{% endblock %}

{% block extra_css %}
<style>
    .performance-excellent { background: linear-gradient(135deg, #10b981, #059669); }
    .performance-very_good { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
    .performance-good { background: linear-gradient(135deg, #f59e0b, #d97706); }
    .performance-average { background: linear-gradient(135deg, #ef4444, #dc2626); }
    .performance-needs-improvement { background: linear-gradient(135deg, #6b7280, #4b5563); }

    .trend-improving { color: #10b981; }
    .trend-declining { color: #ef4444; }
    .trend-stable { color: #6b7280; }

    .star-rating { display: inline-flex; }
    .star-rating .star { color: #fbbf24; }
    .star-rating .star.empty { color: #d1d5db; }

    .progress-bar {
        transition: width 0.5s ease-in-out;
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .filter-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .student-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }

    .student-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .student-card.excellent { border-left-color: #10b981; }
    .student-card.very-good { border-left-color: #3b82f6; }
    .student-card.good { border-left-color: #f59e0b; }
    .student-card.average { border-left-color: #ef4444; }
    .student-card.needs-improvement { border-left-color: #6b7280; }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Enhanced Header -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 p-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold text-islamic-dark flex items-center">
                        <i class="fas fa-user-graduate text-islamic-primary ml-4"></i>
                        تقييمات الطلاب
                    </h1>
                    <p class="mt-3 text-lg text-gray-600">تقييمات المعلمين للطلاب بالتفصيل والتحليل الشامل</p>
                    {% if filter_applied %}
                    <div class="mt-2 flex items-center text-sm text-blue-600">
                        <i class="fas fa-filter ml-2"></i>
                        تم تطبيق فلاتر على البيانات
                    </div>
                    {% endif %}
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    {% if unified_stats %}
                    {% endif %}
                    <button onclick="generateReport()" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors duration-200 flex items-center">
                        <i class="fas fa-file-pdf ml-2"></i>
                        تقرير PDF
                    </button>
                    <a href="{% url 'admin_ratings_view' %}" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة لمركز التقييمات
                    </a>
                </div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-xl border border-gray-100 mb-8 overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-islamic-primary to-islamic-dark px-6 py-4">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center ml-3">
                        <i class="fas fa-filter text-white"></i>
                    </div>
                    فلاتر التقييمات المتقدمة
                    {% if time_filter != 'all' or student_filter != 'all' %}
                    <span class="mr-3 bg-yellow-400 text-yellow-900 text-xs px-2 py-1 rounded-full font-medium">
                        <i class="fas fa-check-circle ml-1"></i>
                        مطبق
                    </span>
                    {% endif %}
                </h3>
                <p class="text-islamic-light-gold text-sm mt-1">استخدم الفلاتر أدناه لتخصيص عرض التقييمات حسب احتياجاتك</p>
            </div>

            <!-- Content -->
            <div class="p-6">
                <form method="GET" class="space-y-6">
                    <!-- الصف الأول: الفلاتر الأساسية -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- فلتر زمني -->
                        <div class="space-y-2">
                            <label class="flex items-center text-sm font-semibold text-gray-700 mb-3">
                                <i class="fas fa-calendar-alt text-islamic-primary ml-2"></i>
                                الفترة الزمنية
                            </label>
                            <div class="relative">
                                <select name="time_filter" class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:ring-3 focus:ring-islamic-primary focus:ring-opacity-20 focus:border-islamic-primary transition-all duration-200 appearance-none cursor-pointer hover:border-gray-300">
                                    <option value="all" {% if time_filter == 'all' %}selected{% endif %}>🗓️ جميع الفترات</option>
                                    <option value="last_month" {% if time_filter == 'last_month' %}selected{% endif %}>📅 آخر شهر</option>
                                    <option value="last_3_months" {% if time_filter == 'last_3_months' %}selected{% endif %}>📊 آخر 3 أشهر</option>
                                    <option value="last_6_months" {% if time_filter == 'last_6_months' %}selected{% endif %}>📈 آخر 6 أشهر</option>
                                    <option value="current_year" {% if time_filter == 'current_year' %}selected{% endif %}>🗓️ السنة الحالية</option>
                                    <option value="custom" {% if time_filter == 'custom' %}selected{% endif %}>⚙️ فترة مخصصة</option>
                                </select>
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- فلتر الطالب -->
                        <div class="space-y-2">
                            <label class="flex items-center text-sm font-semibold text-gray-700 mb-3">
                                <i class="fas fa-user-graduate text-islamic-primary ml-2"></i>
                                الطالب
                            </label>
                            <div class="relative">
                                <select name="student_filter" class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:ring-3 focus:ring-islamic-primary focus:ring-opacity-20 focus:border-islamic-primary transition-all duration-200 appearance-none cursor-pointer hover:border-gray-300">
                                    <option value="all" {% if student_filter == 'all' %}selected{% endif %}>👥 جميع الطلاب</option>
                                    {% for student in all_students %}
                                    <option value="{{ student.id }}" {% if student_filter == student.id|stringformat:"s" %}selected{% endif %}>
                                        🎓 {{ student.get_full_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الصف الثاني: التواريخ المخصصة -->
                    <div class="custom-date-range transition-all duration-300 ease-in-out" style="display: {% if time_filter == 'custom' %}block{% else %}none{% endif %};">
                        <div class="bg-blue-50 border-2 border-blue-200 rounded-xl p-4">
                            <h4 class="text-sm font-semibold text-blue-800 mb-4 flex items-center">
                                <i class="fas fa-calendar-week text-blue-600 ml-2"></i>
                                تحديد الفترة المخصصة
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- تاريخ البداية -->
                                <div>
                                    <label class="block text-sm font-medium text-blue-700 mb-2">من تاريخ</label>
                                    <div class="relative">
                                        <input type="date" name="start_date" value="{{ start_date }}" class="w-full px-4 py-3 bg-white border-2 border-blue-200 rounded-lg focus:ring-3 focus:ring-blue-500 focus:ring-opacity-20 focus:border-blue-500 transition-all duration-200">
                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                            <i class="fas fa-calendar text-blue-400"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- تاريخ النهاية -->
                                <div>
                                    <label class="block text-sm font-medium text-blue-700 mb-2">إلى تاريخ</label>
                                    <div class="relative">
                                        <input type="date" name="end_date" value="{{ end_date }}" class="w-full px-4 py-3 bg-white border-2 border-blue-200 rounded-lg focus:ring-3 focus:ring-blue-500 focus:ring-opacity-20 focus:border-blue-500 transition-all duration-200">
                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                            <i class="fas fa-calendar text-blue-400"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 pt-4 border-t border-gray-200">
                        <button type="submit" class="flex-1 sm:flex-none bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-3 rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-200 flex items-center justify-center font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                            <i class="fas fa-search ml-2"></i>
                            تطبيق الفلاتر
                        </button>

                        <a href="{% url 'admin_student_ratings' %}" class="flex-1 sm:flex-none bg-gradient-to-r from-gray-500 to-gray-600 text-white px-8 py-3 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 flex items-center justify-center font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                            <i class="fas fa-times ml-2"></i>
                            إزالة الفلاتر
                        </a>

                        <div class="flex-1 sm:flex-none text-center sm:text-right">
                            <span class="text-sm text-gray-500">
                                {% if time_filter != 'all' or student_filter != 'all' %}
                                    <i class="fas fa-info-circle ml-1"></i>
                                    الفلاتر مطبقة حالياً
                                {% else %}
                                    <i class="fas fa-lightbulb ml-1"></i>
                                    اختر الفلاتر المناسبة
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Comparison Statistics -->
        {% if comparison_stats %}
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg shadow-sm border border-blue-200 p-6 text-center">
                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-users text-white"></i>
                </div>
                <div class="text-2xl font-bold text-blue-600 mb-1">{{ comparison_stats.total_students }}</div>
                <div class="text-sm font-medium text-gray-700">إجمالي الطلاب</div>
                <div class="text-xs text-gray-600 mt-1">لديهم تقييمات</div>
            </div>

            <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg shadow-sm border border-green-200 p-6 text-center">
                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-trophy text-white"></i>
                </div>
                <div class="text-2xl font-bold text-green-600 mb-1">{{ comparison_stats.excellent_students }}</div>
                <div class="text-sm font-medium text-gray-700">طلاب ممتازين</div>
                <div class="text-xs text-gray-600 mt-1">4.5+ نجوم</div>
            </div>

            <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg shadow-sm border border-purple-200 p-6 text-center">
                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-chart-line text-white"></i>
                </div>
                <div class="text-2xl font-bold text-purple-600 mb-1">{{ comparison_stats.improving_students }}</div>
                <div class="text-sm font-medium text-gray-700">طلاب متحسنين</div>
                <div class="text-xs text-gray-600 mt-1">اتجاه صاعد</div>
            </div>

            <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg shadow-sm border border-yellow-200 p-6 text-center">
                <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-star text-white"></i>
                </div>
                <div class="text-2xl font-bold text-yellow-600 mb-1">{{ comparison_stats.average_rating|floatformat:1 }}</div>
                <div class="text-sm font-medium text-gray-700">المتوسط العام</div>
                <div class="text-xs text-gray-600 mt-1">لجميع الطلاب</div>
            </div>

            {% if comparison_stats.top_performer %}
            <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg shadow-sm border border-indigo-200 p-6 text-center">
                <div class="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-crown text-white"></i>
                </div>
                <div class="text-lg font-bold text-indigo-600 mb-1">{{ comparison_stats.top_performer.overall_average|floatformat:1 }}/5</div>
                <div class="text-sm font-medium text-gray-700">أفضل طالب</div>
                <div class="text-xs text-gray-600 mt-1">{{ comparison_stats.top_performer.student.get_full_name }}</div>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- Unified Statistics Cards -->
        {% if unified_stats %}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- التقييم العام -->
            <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg shadow-sm border border-yellow-200 p-6 text-center">
                <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-star text-white"></i>
                </div>
                <div class="text-2xl font-bold text-yellow-600 mb-1">{{ unified_stats.avg_overall|floatformat:1 }}</div>
                <div class="text-sm font-medium text-gray-700">التقييم العام</div>
                <div class="text-xs text-gray-600 mt-1">من 5 نجوم</div>
            </div>

            <!-- جودة الحصة -->
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg shadow-sm border border-blue-200 p-6 text-center">
                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-book text-white"></i>
                </div>
                <div class="text-2xl font-bold text-blue-600 mb-1">{{ unified_stats.avg_quality|floatformat:1 }}</div>
                <div class="text-sm font-medium text-gray-700">جودة الحصة</div>
                <div class="text-xs text-gray-600 mt-1">المحتوى والشرح</div>
            </div>

            <!-- تفاعل الطالب -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg shadow-sm border border-green-200 p-6 text-center">
                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-comments text-white"></i>
                </div>
                <div class="text-2xl font-bold text-green-600 mb-1">{{ unified_stats.avg_interaction|floatformat:1 }}</div>
                <div class="text-sm font-medium text-gray-700">تفاعل الطالب</div>
                <div class="text-xs text-gray-600 mt-1">التفاعل والاستجابة</div>
            </div>

            <!-- الجودة التقنية -->
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg shadow-sm border border-purple-200 p-6 text-center">
                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-cog text-white"></i>
                </div>
                <div class="text-2xl font-bold text-purple-600 mb-1">{{ unified_stats.avg_technical|floatformat:1 }}</div>
                <div class="text-sm font-medium text-gray-700">الجودة التقنية</div>
                <div class="text-xs text-gray-600 mt-1">الصوت والصورة</div>
            </div>


        </div>
        {% else %}
        <!-- No Ratings State -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center mb-8">
            <div class="text-6xl text-gray-300 mb-4">
                <i class="fas fa-star"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">لا توجد تقييمات بعد</h3>
            <p class="text-gray-500">ستظهر تقييمات المعلمين للطلاب هنا بعد إكمال الحصص</p>
        </div>
        {% endif %}

        <!-- Enhanced Student Ratings Cards -->
        {% if unified_student_ratings %}
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-user-graduate text-islamic-primary ml-2"></i>
                    تقييمات الطلاب التفصيلية
                    <span class="bg-islamic-primary text-white text-xs px-2 py-1 rounded-full mr-2">محدث</span>
                </h3>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button onclick="toggleView('grid')" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button onclick="toggleView('list')" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="students-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for student_rating in unified_student_ratings %}
                    <div class="student-card {{ student_rating.performance_level }} bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-300">
                        <!-- Header with Performance Badge -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-islamic-primary rounded-full flex items-center justify-center ml-3">
                                    <span class="text-white font-bold">{{ student_rating.student.first_name|first }}{{ student_rating.student.last_name|first }}</span>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ student_rating.student.get_full_name }}</h4>
                                    <p class="text-sm text-gray-600">{{ student_rating.total_ratings }} تقييم</p>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="performance-{{ student_rating.performance_level }} text-white text-xs px-2 py-1 rounded-full font-medium">
                                    {% if student_rating.performance_level == 'excellent' %}ممتاز
                                    {% elif student_rating.performance_level == 'very-good' %}جيد جداً
                                    {% elif student_rating.performance_level == 'good' %}جيد
                                    {% elif student_rating.performance_level == 'average' %}متوسط
                                    {% else %}يحتاج تحسين{% endif %}
                                </div>
                                <div class="text-xs text-gray-500 mt-1 trend-{{ student_rating.trend_direction }}">
                                    {% if student_rating.trend_direction == 'improving' %}
                                        <i class="fas fa-arrow-up"></i> متحسن
                                    {% elif student_rating.trend_direction == 'declining' %}
                                        <i class="fas fa-arrow-down"></i> متراجع
                                    {% else %}
                                        <i class="fas fa-minus"></i> مستقر
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Overall Rating Circle -->
                        <div class="text-center mb-4">
                            <div class="relative inline-flex items-center justify-center w-20 h-20">
                                <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-islamic-primary" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="{% widthratio student_rating.overall_average 5 100 %}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-900">{{ student_rating.overall_average|floatformat:1 }}</span>
                                </div>
                            </div>
                            <div class="star-rating mt-2">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= student_rating.overall_average|floatformat:0 %}
                                        <span class="star text-lg">★</span>
                                    {% else %}
                                        <span class="star empty text-lg">★</span>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>

                        <!-- المعايير الأربعة مع شرائط التقدم -->
                        <div class="space-y-3 mb-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">التقييم العام</span>
                                <span class="text-sm font-medium">{{ student_rating.avg_overall|floatformat:1 }}/5</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: {% widthratio student_rating.avg_overall 5 100 %}%"></div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">جودة الحصة</span>
                                <span class="text-sm font-medium">{{ student_rating.avg_quality|floatformat:1 }}/5</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: {% widthratio student_rating.avg_quality 5 100 %}%"></div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">تفاعل الطالب</span>
                                <span class="text-sm font-medium">{{ student_rating.avg_interaction|floatformat:1 }}/5</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: {% widthratio student_rating.avg_interaction 5 100 %}%"></div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">الجودة التقنية</span>
                                <span class="text-sm font-medium">{{ student_rating.avg_technical|floatformat:1 }}/5</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: {% widthratio student_rating.avg_technical 5 100 %}%"></div>
                            </div>
                        </div>

                        <!-- توزيع النجوم -->
                        <div class="border-t pt-3 mb-3">
                            <h5 class="text-sm font-medium text-gray-700 mb-2">توزيع التقييمات</h5>
                            <div class="space-y-1">
                                {% for star, count in student_rating.star_distribution.items %}
                                <div class="flex items-center text-xs">
                                    <span class="w-8">{{ star }}★</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-1 mx-2">
                                        <div class="bg-yellow-400 h-1 rounded-full" style="width: {% if student_rating.total_ratings > 0 %}{% widthratio count student_rating.total_ratings 100 %}{% else %}0{% endif %}%"></div>
                                    </div>
                                    <span class="w-6 text-right">{{ count }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="border-t pt-3">
                            <div class="grid grid-cols-2 gap-4 text-xs text-gray-600">
                                <div>
                                    <span class="block">حصص مجدولة</span>
                                    <span class="font-medium text-gray-900">{{ student_rating.scheduled_count }}</span>
                                </div>
                                <div>
                                    <span class="block">حصص مباشرة</span>
                                    <span class="font-medium text-gray-900">{{ student_rating.live_count }}</span>
                                </div>
                                <div>
                                    <span class="block">تعليقات</span>
                                    <span class="font-medium text-gray-900">{{ student_rating.comments_count }}</span>
                                </div>
                                <div>
                                    <span class="block">آخر تقييم</span>
                                    <span class="font-medium text-gray-900">
                                        {% if student_rating.last_rating %}
                                            {{ student_rating.last_rating.created_at|date:"m/d" }}
                                        {% else %}
                                            --
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="border-t pt-3 mt-3">
                            <div class="flex space-x-2 space-x-reverse">
                                <button onclick="showStudentDetails({{ student_rating.student.id }})" class="flex-1 bg-blue-500 text-white text-xs py-2 px-3 rounded-lg hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-eye ml-1"></i>
                                    تفاصيل
                                </button>
                                <button onclick="generateStudentReport({{ student_rating.student.id }})" class="flex-1 bg-green-500 text-white text-xs py-2 px-3 rounded-lg hover:bg-green-600 transition-colors">
                                    <i class="fas fa-file-pdf ml-1"></i>
                                    تقرير
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Charts Section -->
        {% if unified_student_ratings %}
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-chart-bar text-islamic-primary ml-2"></i>
                    الرسوم البيانية والتحليلات
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Top Students Chart -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">أفضل 10 طلاب</h4>
                        <canvas id="topStudentsChart" width="400" height="300"></canvas>
                    </div>

                    <!-- Criteria Comparison Chart -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">مقارنة المعايير</h4>
                        <canvas id="criteriaChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Student Details Modal -->
<div id="studentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">تفاصيل تقييمات الطالب</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalContent" class="p-6">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// بيانات الرسوم البيانية
const chartData = {{ chart_data|safe }};

// رسم بياني لأفضل الطلاب
if (document.getElementById('topStudentsChart')) {
    const ctx1 = document.getElementById('topStudentsChart').getContext('2d');
    new Chart(ctx1, {
        type: 'bar',
        data: {
            labels: chartData.student_names,
            datasets: [{
                label: 'متوسط التقييم',
                data: chartData.student_averages,
                backgroundColor: 'rgba(45, 80, 22, 0.8)',
                borderColor: 'rgba(45, 80, 22, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 5
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// رسم بياني لمقارنة المعايير
if (document.getElementById('criteriaChart')) {
    const ctx2 = document.getElementById('criteriaChart').getContext('2d');
    new Chart(ctx2, {
        type: 'radar',
        data: {
            labels: ['التقييم العام', 'جودة الحصة', 'تفاعل المعلم', 'الجودة التقنية'],
            datasets: [{
                label: 'متوسط المعايير',
                data: [
                    chartData.criteria_averages.overall,
                    chartData.criteria_averages.quality,
                    chartData.criteria_averages.interaction,
                    chartData.criteria_averages.technical
                ],
                backgroundColor: 'rgba(45, 80, 22, 0.2)',
                borderColor: 'rgba(45, 80, 22, 1)',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 5
                }
            }
        }
    });
}

// وظائف التفاعل
function toggleView(viewType) {
    // تبديل العرض بين الشبكة والقائمة
    console.log('تبديل العرض إلى:', viewType);
}



function showStudentDetails(studentId) {
    // إظهار تفاصيل الطالب في نافذة منبثقة
    document.getElementById('studentModal').classList.remove('hidden');

    // إظهار رسالة تحميل
    document.getElementById('modalContent').innerHTML = `
        <div class="text-center py-8">
            <div class="text-blue-500 text-4xl mb-4"><i class="fas fa-spinner fa-spin"></i></div>
            <h4 class="text-lg font-medium text-gray-900 mb-2">جاري تحميل البيانات...</h4>
            <p class="text-gray-600">يرجى الانتظار</p>
        </div>
    `;

    // طلب البيانات من الخادم
    const currentUrl = new URL(window.location);
    const params = new URLSearchParams(currentUrl.search);
    params.set('action', 'student_details');
    params.set('student_id', studentId);

    fetch(`${currentUrl.pathname}?${params.toString()}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('modalContent').innerHTML = data.html;
        } else {
            document.getElementById('modalContent').innerHTML = `
                <div class="text-center py-8">
                    <div class="text-red-500 text-4xl mb-4"><i class="fas fa-exclamation-triangle"></i></div>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h4>
                    <p class="text-gray-600">${data.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('modalContent').innerHTML = `
            <div class="text-center py-8">
                <div class="text-red-500 text-4xl mb-4"><i class="fas fa-exclamation-triangle"></i></div>
                <h4 class="text-lg font-medium text-gray-900 mb-2">خطأ في الاتصال</h4>
                <p class="text-gray-600">حدث خطأ أثناء تحميل البيانات</p>
            </div>
        `;
    });
}

function generateStudentReport(studentId) {
    // إنشاء تقرير PDF للطالب مع الفلاتر المطبقة
    showLoadingMessage('جاري إنشاء التقرير...');

    // الحصول على الفلاتر الحالية
    const currentUrl = new URL(window.location);
    const params = new URLSearchParams(currentUrl.search);

    // إضافة معرف الطالب والإجراء
    params.set('action', 'generate_report');
    params.set('student_id', studentId);

    // إنشاء رابط مخفي للتحميل
    const link = document.createElement('a');
    link.href = `${currentUrl.pathname}?${params.toString()}`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // إزالة رسالة التحميل بعد ثانيتين
    setTimeout(() => {
        hideLoadingMessage();
    }, 2000);
}

function generateReport() {
    // إنشاء تقرير شامل لجميع الطلاب مع الفلاتر المطبقة
    showLoadingMessage('جاري إنشاء التقرير الشامل...');

    // الحصول على الفلاتر الحالية
    const currentUrl = new URL(window.location);
    const params = new URLSearchParams(currentUrl.search);

    // إضافة الإجراء
    params.set('action', 'generate_report');

    // إنشاء رابط مخفي للتحميل
    const link = document.createElement('a');
    link.href = `${currentUrl.pathname}?${params.toString()}`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // إزالة رسالة التحميل بعد ثانيتين
    setTimeout(() => {
        hideLoadingMessage();
    }, 2000);
}



function showLoadingMessage(message) {
    // إظهار رسالة تحميل
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loadingMessage';
    loadingDiv.className = 'fixed top-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    loadingDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-spinner fa-spin ml-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(loadingDiv);

    // إزالة الرسالة بعد 5 ثوانٍ
    setTimeout(() => {
        hideLoadingMessage();
    }, 5000);
}

function hideLoadingMessage() {
    // إخفاء رسالة التحميل
    const element = document.getElementById('loadingMessage');
    if (element) {
        element.remove();
    }
}

function closeModal() {
    document.getElementById('studentModal').classList.add('hidden');
}

// إظهار/إخفاء حقول التاريخ المخصص
document.querySelector('select[name="time_filter"]').addEventListener('change', function() {
    const customFields = document.querySelectorAll('.custom-date-range');
    if (this.value === 'custom') {
        customFields.forEach(field => field.style.display = 'block');
    } else {
        customFields.forEach(field => field.style.display = 'none');
    }
});
</script>
{% endblock %}
