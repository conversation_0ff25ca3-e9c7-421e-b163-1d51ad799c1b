# Generated by Django 4.2.7 on 2025-06-17 01:28

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0005_auto_20250617_0253'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # حذف الجداول القديمة نهائياً
        migrations.RunSQL(
            "DROP TABLE IF EXISTS lessons_lesson;",
            reverse_sql="-- لا يمكن التراجع عن حذف الجدول"
        ),

        migrations.RunSQL(
            "DROP TABLE IF EXISTS lessons_lessonrating;",
            reverse_sql="-- لا يمكن التراجع عن حذف الجدول"
        ),

        migrations.RunSQL(
            "DROP TABLE IF EXISTS lessons_teacherlessonreport;",
            reverse_sql="-- لا يمكن التراجع عن حذف الجدول"
        ),

        migrations.RunSQL(
            "DROP TABLE IF EXISTS lessons_livelessonrating;",
            reverse_sql="-- لا يمكن التراجع عن حذف الجدول"
        ),
    ]
