"""
Django management command لاختبار إرسال البريد الإلكتروني مع تشخيص مفصل
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.email_service import EmailService
from users.email_models import EmailTemplate, EmailSettings
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

User = get_user_model()


class Command(BaseCommand):
    help = 'اختبار إرسال البريد الإلكتروني مع تشخيص مفصل'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='البريد الإلكتروني المراد الإرسال إليه'
        )
        
        parser.add_argument(
            '--simple',
            action='store_true',
            help='اختبار بسيط بدون قوالب'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🧪 بدء اختبار إرسال البريد الإلكتروني')
        )
        
        # الحصول على إعدادات SMTP
        email_settings = EmailSettings.get_active_settings()
        if not email_settings:
            self.stdout.write(
                self.style.ERROR('❌ لا توجد إعدادات SMTP نشطة')
            )
            return
        
        # عرض الإعدادات
        self.stdout.write('\n📋 إعدادات SMTP:')
        self.stdout.write(f'   المزود: {email_settings.get_provider_display()}')
        self.stdout.write(f'   الخادم: {email_settings.smtp_host}')
        self.stdout.write(f'   المنفذ: {email_settings.smtp_port}')
        self.stdout.write(f'   TLS: {email_settings.use_tls}')
        self.stdout.write(f'   SSL: {email_settings.use_ssl}')
        self.stdout.write(f'   المرسل: {email_settings.from_email}')
        
        # تحديد المستلم
        recipient_email = options.get('email')
        if not recipient_email:
            # البحث عن مستخدم مدير
            admin_user = User.objects.filter(user_type='admin').first()
            if not admin_user:
                admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                admin_user = User.objects.first()
            
            if admin_user:
                recipient_email = admin_user.email
            else:
                self.stdout.write(
                    self.style.ERROR('❌ لا يوجد مستخدم للاختبار')
                )
                return
        
        self.stdout.write(f'\n👤 المستلم: {recipient_email}')
        
        if options.get('simple'):
            # اختبار بسيط
            self.test_simple_email(email_settings, recipient_email)
        else:
            # اختبار مع القوالب
            self.test_template_email(email_settings, recipient_email)

    def test_simple_email(self, email_settings, recipient_email):
        """اختبار إرسال بريد بسيط بدون قوالب"""
        self.stdout.write('\n📧 اختبار إرسال بريد بسيط...')
        
        try:
            # إنشاء الرسالة
            msg = MIMEMultipart('alternative')
            msg['Subject'] = 'اختبار إرسال البريد الإلكتروني - أكاديمية قرآنيا'
            msg['From'] = email_settings.from_email
            msg['To'] = recipient_email
            
            # محتوى الرسالة
            text_content = """
            مرحباً،
            
            هذه رسالة اختبار من نظام أكاديمية قرآنيا للتعليم الإسلامي.
            
            إذا وصلتك هذه الرسالة، فهذا يعني أن نظام البريد الإلكتروني يعمل بشكل صحيح.
            
            مع أطيب التحيات،
            فريق أكاديمية قرآنيا
            """
            
            html_content = """
            <html>
            <body dir="rtl" style="font-family: Arial, sans-serif;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2D5016;">مرحباً من أكاديمية قرآنيا</h2>
                    <p>هذه رسالة اختبار من نظام أكاديمية قرآنيا للتعليم الإسلامي.</p>
                    <p>إذا وصلتك هذه الرسالة، فهذا يعني أن نظام البريد الإلكتروني يعمل بشكل صحيح.</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <h4 style="color: #2D5016;">معلومات الاختبار:</h4>
                        <ul>
                            <li>التاريخ: {}</li>
                            <li>المرسل: {}</li>
                            <li>المستلم: {}</li>
                        </ul>
                    </div>
                    <p style="color: #666;">مع أطيب التحيات،<br>فريق أكاديمية قرآنيا</p>
                </div>
            </body>
            </html>
            """.format(
                '2025-01-02',
                email_settings.from_email,
                recipient_email
            )
            
            # إضافة المحتوى
            text_part = MIMEText(text_content, 'plain', 'utf-8')
            html_part = MIMEText(html_content, 'html', 'utf-8')
            
            msg.attach(text_part)
            msg.attach(html_part)
            
            # إرسال الرسالة
            self.send_email_direct(email_settings, msg, recipient_email)
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في إنشاء الرسالة: {str(e)}')
            )

    def test_template_email(self, email_settings, recipient_email):
        """اختبار إرسال بريد باستخدام القوالب"""
        self.stdout.write('\n📧 اختبار إرسال بريد باستخدام القوالب...')
        
        # البحث عن قالب
        template = EmailTemplate.objects.filter(
            template_type='welcome',
            is_active=True
        ).first()
        
        if not template:
            self.stdout.write(
                self.style.WARNING('⚠️ لا يوجد قالب ترحيب، سيتم إنشاء واحد...')
            )
            # إنشاء قالب بسيط للاختبار
            template = EmailTemplate.objects.create(
                name='قالب اختبار',
                template_type='welcome',
                subject='اختبار النظام - {{ academy_name }}',
                html_content='''
                <h2>مرحباً {{ user_name }}</h2>
                <p>هذه رسالة اختبار من {{ academy_name }}</p>
                <p>البريد الإلكتروني: {{ user_email }}</p>
                ''',
                text_content='مرحباً {{ user_name }}، هذه رسالة اختبار من {{ academy_name }}',
                is_active=True
            )
        
        # إعداد السياق
        context = {
            'user_name': 'مدير النظام',
            'user_email': recipient_email,
            'academy_name': 'أكاديمية قرآنيا للتعليم الإسلامي',
        }
        
        # إرسال البريد
        email_service = EmailService()
        success = email_service.send_email(
            recipient_email=recipient_email,
            template=template,
            context=context,
            immediate=True
        )
        
        if success:
            self.stdout.write(
                self.style.SUCCESS('✅ تم إرسال البريد بنجاح!')
            )
        else:
            self.stdout.write(
                self.style.ERROR('❌ فشل في إرسال البريد')
            )

    def send_email_direct(self, email_settings, msg, recipient_email):
        """إرسال البريد مباشرة بدون استخدام EmailService"""
        try:
            self.stdout.write('🔌 محاولة الاتصال بخادم SMTP...')
            
            # إنشاء الاتصال
            if email_settings.use_ssl:
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(
                    email_settings.smtp_host, 
                    email_settings.smtp_port,
                    context=context
                )
            else:
                server = smtplib.SMTP(
                    email_settings.smtp_host, 
                    email_settings.smtp_port
                )
                if email_settings.use_tls:
                    server.starttls()
            
            self.stdout.write('✅ تم الاتصال بالخادم')
            
            # تسجيل الدخول
            self.stdout.write('🔐 محاولة تسجيل الدخول...')
            server.login(
                email_settings.smtp_username,
                email_settings.smtp_password  # استخدام كلمة المرور مباشرة
            )
            self.stdout.write('✅ تم تسجيل الدخول بنجاح')
            
            # إرسال الرسالة
            self.stdout.write('📤 إرسال الرسالة...')
            server.send_message(msg)
            server.quit()
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ تم إرسال البريد بنجاح إلى {recipient_email}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في الإرسال: {str(e)}')
            )
            
            # تشخيص إضافي
            self.stdout.write('\n🔍 تشخيص إضافي:')
            if 'connection' in str(e).lower():
                self.stdout.write('   - مشكلة في الاتصال بالخادم')
                self.stdout.write('   - تأكد من صحة عنوان الخادم والمنفذ')
                self.stdout.write('   - تأكد من اتصال الإنترنت')
            elif 'authentication' in str(e).lower():
                self.stdout.write('   - مشكلة في المصادقة')
                self.stdout.write('   - تأكد من صحة اسم المستخدم وكلمة المرور')
                self.stdout.write('   - استخدم App Password للـ Gmail')
            elif 'ssl' in str(e).lower() or 'tls' in str(e).lower():
                self.stdout.write('   - مشكلة في إعدادات التشفير')
                self.stdout.write('   - جرب تغيير إعدادات SSL/TLS')
