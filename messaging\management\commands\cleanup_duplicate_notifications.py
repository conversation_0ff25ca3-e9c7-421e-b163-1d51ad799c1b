"""
أمر Django لتنظيف الإشعارات المكررة
"""

from django.core.management.base import BaseCommand
from django.db.models import Count
from notifications.models import Notification


class Command(BaseCommand):
    help = 'تنظيف الإشعارات المكررة للرسائل'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='عرض ما سيتم حذفه بدون تنفيذ الحذف الفعلي',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']

        self.stdout.write(
            self.style.SUCCESS('🔍 البحث عن الإشعارات المكررة...')
        )

        # البحث عن الإشعارات المكررة بناءً على المستقبل والمحادثة
        duplicates = Notification.objects.filter(
            notification_type='new_message'
        ).values(
            'recipient', 'action_url'
        ).annotate(
            count=Count('id')
        ).filter(count__gt=1)

        self.stdout.write(
            f"📊 عدد مجموعات الإشعارات المكررة: {duplicates.count()}"
        )

        if duplicates.count() == 0:
            self.stdout.write(
                self.style.SUCCESS('✅ لا توجد إشعارات مكررة!')
            )
            return

        # حذف الإشعارات المكررة (الاحتفاظ بأحدث واحد فقط)
        deleted_total = 0

        for dup in duplicates:
            notifications = Notification.objects.filter(
                notification_type='new_message',
                recipient=dup['recipient'],
                action_url=dup['action_url']
            ).order_by('-created_at')

            # حذف جميع الإشعارات ما عدا الأحدث
            to_delete = notifications[1:]  # كل شيء ما عدا الأول (الأحدث)

            if dry_run:
                self.stdout.write(
                    f"🗑️  سيتم حذف {len(to_delete)} إشعار مكرر للمحادثة: {dup['action_url']}"
                )
                deleted_total += len(to_delete)
            else:
                for notif in to_delete:
                    notif.delete()
                    deleted_total += 1

        if dry_run:
            self.stdout.write(
                self.style.WARNING(f"🔍 سيتم حذف {deleted_total} إشعار مكرر (تشغيل تجريبي)")
            )
            self.stdout.write(
                self.style.WARNING("لتنفيذ الحذف الفعلي، قم بتشغيل الأمر بدون --dry-run")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f"✅ تم حذف {deleted_total} إشعار مكرر")
            )

        # عرض العدد النهائي
        final_count = Notification.objects.filter(notification_type='new_message').count()
        self.stdout.write(
            f"📈 عدد إشعارات الرسائل النهائي: {final_count}"
        )

        self.stdout.write(
            self.style.SUCCESS('🎉 تم الانتهاء من تنظيف الإشعارات!')
        )
