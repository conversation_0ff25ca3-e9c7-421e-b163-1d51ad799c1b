"""
دوال مساعدة للعمليات الجماعية الآمنة
"""

import logging
from django.db import transaction, IntegrityError, OperationalError
from django.contrib import messages
from django.utils import timezone

logger = logging.getLogger(__name__)


class BulkOperationHandler:
    """معالج العمليات الجماعية الآمنة"""
    
    def __init__(self, request):
        self.request = request
        self.success_count = 0
        self.failed_count = 0
        self.errors = []

    def execute_bulk_operation(self, users, operation_func, operation_name):
        """
        تنفيذ عملية جماعية بشكل آمن مع معالجة محسنة

        Args:
            users: QuerySet من المستخدمين
            operation_func: دالة العملية التي ستطبق على كل مستخدم
            operation_name: اسم العملية للرسائل
        """
        # تطبيق إعدادات SQLite المحسنة
        self._optimize_database_for_bulk()

        # معالجة كل مستخدم على حدة بدون معاملة كبيرة
        for user in users:
            try:
                # استخدام معاملة صغيرة لكل مستخدم
                with transaction.atomic():
                    operation_func(user)
                    self.success_count += 1
            except (IntegrityError, OperationalError) as e:
                self.failed_count += 1
                error_msg = f"فشل في {operation_name} للمستخدم {user.get_full_name()}: {str(e)}"
                self.errors.append(error_msg)
                logger.error(error_msg)
            except Exception as e:
                self.failed_count += 1
                error_msg = f"خطأ غير متوقع في {operation_name} للمستخدم {user.get_full_name()}: {str(e)}"
                self.errors.append(error_msg)
                logger.error(error_msg)

    def _optimize_database_for_bulk(self):
        """تطبيق إعدادات قاعدة البيانات المحسنة للعمليات الجماعية"""
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("PRAGMA busy_timeout = 30000")
                cursor.execute("PRAGMA synchronous = NORMAL")
                cursor.execute("PRAGMA cache_size = 10000")
        except Exception as e:
            logger.warning(f"Failed to optimize database for bulk operation: {str(e)}")

    def add_messages(self, success_message_template, failed_message_template=None):
        """إضافة رسائل النتائج"""
        if self.success_count > 0:
            messages.success(self.request, success_message_template.format(count=self.success_count))
        
        if self.failed_count > 0:
            if failed_message_template:
                messages.warning(self.request, failed_message_template.format(count=self.failed_count))
            else:
                messages.warning(self.request, f"فشل في معالجة {self.failed_count} عنصر.")
        
        # إضافة رسائل الأخطاء التفصيلية (أول 3 أخطاء فقط)
        for error in self.errors[:3]:
            messages.error(self.request, error)
        
        if len(self.errors) > 3:
            messages.warning(self.request, f"وهناك {len(self.errors) - 3} أخطاء إضافية.")


def safe_bulk_approve(request, users, notes=None):
    """موافقة جماعية آمنة"""
    handler = BulkOperationHandler(request)
    
    def approve_user(user):
        user.approve_verification(request.user, notes)
    
    handler.execute_bulk_operation(users, approve_user, "الموافقة")
    handler.add_messages("تم قبول {count} حسابات بنجاح.", "فشل في قبول {count} حسابات.")
    
    return handler.success_count, handler.failed_count


def safe_bulk_reject(request, users, reason, notes=None):
    """رفض جماعي آمن"""
    handler = BulkOperationHandler(request)
    
    def reject_user(user):
        user.reject_verification(request.user, reason, notes)
    
    handler.execute_bulk_operation(users, reject_user, "الرفض")
    handler.add_messages("تم رفض {count} حسابات بنجاح.", "فشل في رفض {count} حسابات.")
    
    return handler.success_count, handler.failed_count


def safe_bulk_under_review(request, users):
    """وضع قيد المراجعة جماعي آمن"""
    handler = BulkOperationHandler(request)
    
    def set_under_review(user):
        user.verification_status = 'under_review'
        user.verified_by = request.user
        user.verified_at = timezone.now()
        user.verification_notes = "تم وضع الحساب قيد المراجعة من لوحة الإدارة"
        user.save()
        
        # إرسال الإشعار
        try:
            user._send_admin_action_notification('under_review', request.user, "حسابك قيد المراجعة")
        except Exception as e:
            logger.warning(f"فشل في إرسال إشعار للمستخدم {user.email}: {str(e)}")
    
    handler.execute_bulk_operation(users, set_under_review, "وضع قيد المراجعة")
    handler.add_messages("تم وضع {count} حسابات قيد المراجعة.", "فشل في وضع {count} حسابات قيد المراجعة.")
    
    return handler.success_count, handler.failed_count


def safe_bulk_ban(request, users, ban_type, ban_reason, banned_until=None):
    """حظر جماعي آمن"""
    handler = BulkOperationHandler(request)
    
    def ban_user(user):
        user.ban_user(request.user, ban_type, ban_reason, banned_until)
    
    handler.execute_bulk_operation(users, ban_user, "الحظر")
    
    ban_type_text = "مؤقتاً" if ban_type == 'temporary' else "نهائياً"
    handler.add_messages(f"تم حظر {{count}} حسابات {ban_type_text}.", "فشل في حظر {count} حسابات.")
    
    return handler.success_count, handler.failed_count


def safe_bulk_delete(request, users, delete_reason):
    """حذف جماعي آمن باستخدام الحذف النووي"""
    handler = BulkOperationHandler(request)

    def delete_user(user):
        # حفظ معلومات الحذف في سجل النظام
        user_info = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.get_full_name(),
            'user_type': user.user_type,
            'verification_status': user.verification_status,
            'is_active': user.is_active,
            'is_banned': user.is_banned,
            'deleted_at': timezone.now().isoformat(),
            'deleted_by': request.user.username,
            'delete_reason': delete_reason
        }

        # تسجيل الحدث في سجل النظام
        try:
            from logs.models import SystemLog
            SystemLog.objects.create(
                user=request.user,
                action='delete_user',
                details=f'تم حذف المستخدم {user.get_full_name() or user.username} (ID: {user.id}) بواسطة {request.user.get_full_name() or request.user.username}',
                data=user_info,
                ip_address=request.META.get('REMOTE_ADDR')
            )
        except Exception as log_error:
            logger.warning(f"فشل في تسجيل حذف المستخدم {user.username}: {str(log_error)}")

        # استخدام الحذف النووي
        from django.db import connection

        logger.info(f"بدء الحذف النووي للمستخدم {user.username} (ID: {user.id})")

        try:
            with connection.cursor() as cursor:
                # تعطيل Foreign Key constraints
                cursor.execute("PRAGMA foreign_keys = OFF")

                # حذف جميع البيانات المرتبطة بالمستخدم مباشرة
                tables_and_fields = [
                    ('users_emailnotification', 'recipient_id'),
                    ('messaging_chatmessage', 'sender_id'),
                    ('messaging_conversation', 'participant1_id'),
                    ('messaging_conversation', 'participant2_id'),
                    ('support_supportticket', 'created_by_id'),
                    ('support_supportticketresponse', 'created_by_id'),
                    ('support_systemmessagerecipient', 'recipient_id'),
                    ('lessons_lesson', 'student_id'),
                    ('lessons_lesson', 'teacher_id'),
                    ('lessons_livelesson', 'student_id'),
                    ('lessons_livelesson', 'teacher_id'),
                    ('lessons_lessonrating', 'student_id'),
                    ('lessons_livelessonrating', 'student_id'),
                    ('lessons_livelessonrating', 'teacher_id'),
                    ('lessons_unifiedlessonrating', 'student_id'),
                    ('lessons_unifiedlessonrating', 'teacher_id'),
                    ('subscriptions_studentsubscription', 'student_id'),
                    ('subscriptions_scheduledlesson', 'teacher_id'),
                    ('logs_systemlog', 'user_id'),
                    ('notifications_notification', 'recipient_id'),
                    ('notifications_notification', 'sender_id'),
                ]

                total_deleted = 0

                # حذف البيانات من كل جدول
                for table_name, field_name in tables_and_fields:
                    try:
                        cursor.execute(f"DELETE FROM {table_name} WHERE {field_name} = {user.id}")
                        deleted_count = cursor.rowcount
                        total_deleted += deleted_count
                        if deleted_count > 0:
                            logger.info(f"حذف {deleted_count} سجل من {table_name}")
                    except Exception as e:
                        logger.warning(f"خطأ في {table_name}: {str(e)}")

                # حذف الحصص المجدولة عبر الاشتراك
                try:
                    cursor.execute(f"""
                        DELETE FROM subscriptions_scheduledlesson
                        WHERE subscription_id IN (
                            SELECT id FROM subscriptions_studentsubscription
                            WHERE student_id = {user.id}
                        )
                    """)
                    deleted_scheduled = cursor.rowcount
                    total_deleted += deleted_scheduled
                    if deleted_scheduled > 0:
                        logger.info(f"حذف {deleted_scheduled} حصة مجدولة")
                except Exception as e:
                    logger.warning(f"خطأ في الحصص المجدولة: {str(e)}")

                # حذف مدفوعات الاشتراكات
                try:
                    cursor.execute(f"""
                        DELETE FROM subscriptions_subscriptionpayment
                        WHERE subscription_id IN (
                            SELECT id FROM subscriptions_studentsubscription
                            WHERE student_id = {user.id}
                        )
                    """)
                    deleted_payments = cursor.rowcount
                    total_deleted += deleted_payments
                    if deleted_payments > 0:
                        logger.info(f"حذف {deleted_payments} مدفوعة")
                except Exception as e:
                    logger.warning(f"خطأ في المدفوعات: {str(e)}")

                # تنظيف المراجع في جدول المستخدمين
                cursor.execute(f"UPDATE users_user SET verified_by_id = NULL WHERE verified_by_id = {user.id}")
                cursor.execute(f"UPDATE users_user SET banned_by_id = NULL WHERE banned_by_id = {user.id}")
                cursor.execute(f"UPDATE support_supportticket SET assigned_to_id = NULL WHERE assigned_to_id = {user.id}")

                # حذف المستخدم نفسه
                cursor.execute(f"DELETE FROM users_user WHERE id = {user.id}")
                user_deleted = cursor.rowcount

                # إعادة تفعيل Foreign Key constraints
                cursor.execute("PRAGMA foreign_keys = ON")

                if user_deleted > 0:
                    logger.info(f"تم الحذف النووي بنجاح للمستخدم {user.username}! حذف {total_deleted + user_deleted} سجل إجمالي")
                else:
                    raise Exception("فشل في حذف المستخدم")

        except Exception as e:
            logger.error(f"خطأ أثناء الحذف النووي للمستخدم {user.username}: {str(e)}")
            raise e

    handler.execute_bulk_operation(users, delete_user, "الحذف")
    handler.add_messages("تم حذف {count} حسابات بنجاح.", "فشل في حذف {count} حسابات.")

    return handler.success_count, handler.failed_count
