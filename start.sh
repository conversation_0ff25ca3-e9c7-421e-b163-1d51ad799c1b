#!/usr/bin/env bash
# Start script for Render.com deployment

echo "🚀 Starting Qurania LMS application..."

# Set Django settings module
export DJANGO_SETTINGS_MODULE=qurania_lms.settings_render

# Get port from environment or default to 8000
PORT=${PORT:-8000}

echo "📡 Starting Gunicorn on port $PORT..."
echo "⚙️ Using settings: $DJANGO_SETTINGS_MODULE"

# Start Gunicorn with proper configuration
exec gunicorn qurania_lms.wsgi:application \
    --bind 0.0.0.0:$PORT \
    --workers 2 \
    --timeout 120 \
    --keep-alive 2 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --log-level info \
    --access-logfile - \
    --error-logfile -
