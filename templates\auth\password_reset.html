<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - نظام قرآنيا التعليمي</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .arabic-text {
            font-family: '<PERSON>i', serif;
        }

        /* Islamic Design Colors */
        :root {
            --primary-green: #2D5016;
            --light-green: #4A7C59;
            --gold: #D4AF37;
            --light-gold: #F4E4BC;
            --dark-blue: #1B365D;
            --light-blue: #E8F4FD;
        }

        .bg-islamic-primary { background-color: var(--primary-green); }
        .bg-islamic-light { background-color: var(--light-green); }
        .bg-islamic-gold { background-color: var(--gold); }
        .bg-islamic-light-gold { background-color: var(--light-gold); }
        .bg-islamic-dark { background-color: var(--dark-blue); }
        .bg-islamic-light-blue { background-color: var(--light-blue); }

        .text-islamic-primary { color: var(--primary-green); }
        .text-islamic-gold { color: var(--gold); }
        .text-islamic-dark { color: var(--dark-blue); }

        .border-islamic-gold { border-color: var(--gold); }

        /* Islamic Pattern Background */
        .islamic-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="bg-islamic-light-blue islamic-pattern min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- Password Reset Card -->
        <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
            <!-- Header -->
            <div class="bg-islamic-primary p-8 text-center">
                <div class="w-20 h-20 bg-islamic-gold rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-key text-islamic-primary text-3xl"></i>
                </div>
                <h1 class="text-white text-2xl font-bold arabic-text mb-2">إعادة تعيين كلمة المرور</h1>
                <p class="text-islamic-light-gold">{{ ACADEMY_SLOGAN }}</p>
            </div>

            <!-- Password Reset Form -->
            <div class="p-8">
                <p class="text-gray-600 text-center mb-6">
                    أدخل عنوان بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور
                </p>

                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="mb-4 p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" class="space-y-6">
                    {% csrf_token %}

                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-islamic-dark mb-2">
                            <i class="fas fa-envelope ml-2"></i>
                            البريد الإلكتروني
                        </label>
                        <input type="email"
                               id="email"
                               name="email"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent transition-all duration-200"
                               placeholder="أدخل عنوان بريدك الإلكتروني">
                    </div>

                    <!-- Submit Button -->
                    <button type="submit"
                            class="w-full bg-islamic-primary text-white py-3 px-4 rounded-lg hover:bg-islamic-light transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال رابط إعادة التعيين
                    </button>
                </form>

                <!-- Back to Login -->
                <div class="text-center mt-6">
                    <a href="{% url 'login' %}" class="text-islamic-primary hover:text-islamic-light font-semibold transition-colors duration-200">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة إلى تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>

        <!-- Islamic Quote -->
        <div class="text-center mt-8">
            <p class="text-islamic-primary arabic-text text-lg font-semibold">
                "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا"
            </p>
            <p class="text-islamic-dark text-sm mt-1">
                سورة الطلاق - آية 2
            </p>
        </div>
    </div>

    <script>
        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const messages = document.querySelectorAll('.mb-4.p-4.rounded-lg');
            messages.forEach(message => {
                message.style.transition = 'opacity 0.5s';
                message.style.opacity = '0';
                setTimeout(() => message.remove(), 500);
            });
        }, 5000);
    </script>
</body>
</html>
