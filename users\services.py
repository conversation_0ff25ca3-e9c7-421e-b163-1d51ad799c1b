"""
خدمات نظام البريد الإلكتروني الموحد والإشعارات
"""

import time
import logging
from django.conf import settings
from cryptography.fernet import Fernet

logger = logging.getLogger(__name__)

class PasswordEncryption:
    """كلاس تشفير كلمات المرور للخدمات الخارجية"""

    def __init__(self):
        # استخدام مفتاح تشفير من الإعدادات أو إنشاء واحد جديد
        self.key = getattr(settings, 'EMAIL_ENCRYPTION_KEY', getattr(settings, 'SMTP_ENCRYPTION_KEY', Fernet.generate_key()))
        self.cipher = Fernet(self.key)
    
    def encrypt_password(self, password):
        """تشفير كلمة المرور"""
        if not password:
            return ''
        try:
            return self.cipher.encrypt(password.encode()).decode()
        except Exception as e:
            logger.error(f"خطأ في تشفير كلمة المرور: {e}")
            return password
    
    def decrypt_password(self, encrypted_password):
        """فك تشفير كلمة المرور"""
        if not encrypted_password:
            return ''
        try:
            return self.cipher.decrypt(encrypted_password.encode()).decode()
        except Exception as e:
            logger.error(f"خطأ في فك تشفير كلمة المرور: {e}")
            return encrypted_password




    

    



class BackgroundLessonScheduler:
    """مجدول تذكيرات الحصص في الخلفية"""

    def __init__(self, interval=60):
        self.interval = interval  # فترة الفحص بالثواني
        self.running = False
        # استبدال نظام البريد الإلكتروني بنظام WhatsApp
        from users.whatsapp_service import whatsapp_service
        self.notification_service = whatsapp_service

    def start(self):
        """بدء المجدول"""
        self.running = True
        logger.info("🚀 بدء مجدول تذكيرات الحصص في الخلفية")

        while self.running:
            try:
                self._check_and_send_reminders()
                time.sleep(self.interval)
            except Exception as e:
                logger.error(f"خطأ في المجدول: {e}")
                time.sleep(self.interval)

    def stop(self):
        """إيقاف المجدول"""
        self.running = False
        logger.info("🛑 تم إيقاف مجدول تذكيرات الحصص")

    def _check_and_send_reminders(self):
        """فحص وإرسال التذكيرات"""
        from django.utils import timezone
        from datetime import timedelta
        from lessons.models import LiveLesson
        from .models import WhatsAppMessage

        now = timezone.now()

        # فحص تذكيرات 30 دقيقة
        sent_30min = self._send_30min_reminders(now)

        # فحص تذكيرات يوم واحد (فقط في الساعة 9)
        sent_1day = 0
        if now.hour == 9 and now.minute < 2:
            sent_1day = self._send_1day_reminders(now)

        # تسجيل النتائج فقط إذا تم إرسال شيء
        if sent_30min > 0 or sent_1day > 0:
            logger.info(f"📧 تم إرسال {sent_30min + sent_1day} تذكير")

    def _send_30min_reminders(self, now):
        """إرسال تذكيرات 30 دقيقة"""
        from django.utils import timezone
        from datetime import timedelta
        from lessons.models import LiveLesson
        from .models import WhatsAppMessage

        # البحث عن حصص خلال 25-35 دقيقة
        start_time = now + timedelta(minutes=25)
        end_time = now + timedelta(minutes=35)

        lessons = LiveLesson.objects.filter(
            scheduled_date__range=(start_time, end_time),
            status='scheduled'
        )

        sent_count = 0

        for lesson in lessons:
            # التحقق من عدم إرسال التذكير مسبقاً
            existing_notification = WhatsAppMessage.objects.filter(
                lesson=lesson,
                message_type='lesson_reminder_30min',
                status__in=['sent', 'pending']
            ).exists()

            if existing_notification:
                continue

            # إرسال تذكير للطالب
            if self._send_reminder(lesson, lesson.student, 'lesson_reminder_30min', 'student'):
                sent_count += 1

            # إرسال تذكير للمعلم
            if self._send_reminder(lesson, lesson.teacher, 'lesson_reminder_30min', 'teacher'):
                sent_count += 1

        return sent_count

    def _send_1day_reminders(self, now):
        """إرسال تذكيرات يوم واحد"""
        from django.utils import timezone
        from datetime import timedelta
        from lessons.models import LiveLesson
        from .models import WhatsAppMessage

        # البحث عن حصص غداً
        tomorrow = now + timedelta(days=1)
        start_time = tomorrow.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = tomorrow.replace(hour=23, minute=59, second=59, microsecond=999999)

        lessons = LiveLesson.objects.filter(
            scheduled_date__range=(start_time, end_time),
            status='scheduled'
        )

        sent_count = 0

        for lesson in lessons:
            # التحقق من عدم إرسال التذكير مسبقاً
            existing_notification = WhatsAppMessage.objects.filter(
                lesson=lesson,
                message_type='lesson_reminder_1day',
                status__in=['sent', 'pending']
            ).exists()

            if existing_notification:
                continue

            # إرسال تذكير للطالب
            if self._send_reminder(lesson, lesson.student, 'lesson_reminder_1day', 'student'):
                sent_count += 1

            # إرسال تذكير للمعلم
            if self._send_reminder(lesson, lesson.teacher, 'lesson_reminder_1day', 'teacher'):
                sent_count += 1

        return sent_count

    def _send_reminder(self, lesson, recipient, notification_type, recipient_type):
        """إرسال تذكير واحد"""
        try:
            # إنشاء محتوى الرسالة مع الروابط المخصصة
            if notification_type == 'lesson_reminder_30min':
                if recipient_type == 'student':
                    lesson_url = lesson.get_student_jitsi_url()
                    subject = f"حصتك تبدأ خلال 30 دقيقة - أكاديمية القرآنيا"
                    message = f"""بسم الله الرحمن الرحيم
السلام عليكم ورحمة الله وبركاته

عزيزي الطالب {recipient.get_full_name()},

حصتك في أكاديمية القرآنيا ستبدأ خلال 30 دقيقة.

معلومات الحصة:
═══════════════════════════════════════
📖 موضوع الحصة: {lesson.title}
👨‍🏫 اسم المعلم: {lesson.teacher.get_full_name()}
🕐 وقت البداية: {lesson.scheduled_date.strftime('%H:%M')}
⏱️ مدة الحصة: {lesson.duration_minutes} دقيقة
═══════════════════════════════════════

للدخول إلى الحصة الآن (رابط الطالب):
{lesson_url}

تذكير سريع:
• ادخل للحصة قبل 5 دقائق من البداية
• تأكد من عمل الميكروفون والكاميرا
• جهز المصحف والقلم والورقة

بارك الله فيك ونفع بك
فريق أكاديمية القرآنيا للتحفيظ والتعليم"""
                else:  # teacher
                    lesson_url = lesson.get_teacher_jitsi_url()
                    subject = f"حصتك تبدأ خلال 30 دقيقة - أكاديمية القرآنيا"
                    message = f"""بسم الله الرحمن الرحيم
السلام عليكم ورحمة الله وبركاته

فضيلة الأستاذ {recipient.get_full_name()},

حصتكم في أكاديمية القرآنيا ستبدأ خلال 30 دقيقة.

معلومات الحصة:
═══════════════════════════════════════
📖 موضوع الحصة: {lesson.title}
👨‍🎓 اسم الطالب: {lesson.student.get_full_name()}
🕐 وقت البداية: {lesson.scheduled_date.strftime('%H:%M')}
⏱️ مدة الحصة: {lesson.duration_minutes} دقيقة
═══════════════════════════════════════

للدخول إلى الحصة الآن (رابط المعلم - صلاحيات مشرف):
{lesson_url}

تذكير للمعلم:
• ادخل للحصة قبل 5 دقائق من البداية
• تأكد من عمل الميكروفون والكاميرا
• جهز المواد التعليمية والمصحف

وفقكم الله وبارك في جهودكم
إدارة أكاديمية القرآنيا للتحفيظ والتعليم"""

            else:  # lesson_reminder_1day
                if recipient_type == 'student':
                    lesson_url = lesson.get_student_jitsi_url()
                    subject = f"موعد حصتك غداً - أكاديمية القرآنيا"
                    message = f"""بسم الله الرحمن الرحيم
السلام عليكم ورحمة الله وبركاته

عزيزي الطالب {recipient.get_full_name()},

نود تذكيرك بموعد حصتك غداً في أكاديمية القرآنيا.

معلومات الحصة:
═══════════════════════════════════════
📖 موضوع الحصة: {lesson.title}
👨‍🏫 اسم المعلم: {lesson.teacher.get_full_name()}
📅 تاريخ الحصة: {lesson.scheduled_date.strftime('%Y-%m-%d')}
🕐 وقت البداية: {lesson.scheduled_date.strftime('%H:%M')}
⏱️ مدة الحصة: {lesson.duration_minutes} دقيقة
═══════════════════════════════════════

رابط الحصة (رابط الطالب):
{lesson_url}

ملاحظات مهمة:
• احرص على الدخول قبل 5 دقائق من البداية
• جهز المصحف والقلم والورقة
• تأكد من اتصال الإنترنت

بارك الله فيك ونفع بك
فريق أكاديمية القرآنيا للتحفيظ والتعليم"""
                else:  # teacher
                    lesson_url = lesson.get_teacher_jitsi_url()
                    subject = f"تذكير بحصة غداً - أكاديمية القرآنيا"
                    message = f"""بسم الله الرحمن الرحيم
السلام عليكم ورحمة الله وبركاته

فضيلة الأستاذ {recipient.get_full_name()},

نود تذكيركم بموعد حصتكم غداً في أكاديمية القرآنيا.

معلومات الحصة:
═══════════════════════════════════════
📖 موضوع الحصة: {lesson.title}
👨‍🎓 اسم الطالب: {lesson.student.get_full_name()}
📅 تاريخ الحصة: {lesson.scheduled_date.strftime('%Y-%m-%d')}
🕐 وقت البداية: {lesson.scheduled_date.strftime('%H:%M')}
⏱️ مدة الحصة: {lesson.duration_minutes} دقيقة
═══════════════════════════════════════

رابط الحصة (رابط المعلم - صلاحيات مشرف):
{lesson_url}

ملاحظات للمعلم:
• احرص على الدخول قبل 5 دقائق من البداية
• جهز المواد التعليمية والمصحف
• تأكد من عمل الميكروفون والكاميرا

وفقكم الله وبارك في جهودكم
إدارة أكاديمية القرآنيا للتحفيظ والتعليم"""

            # إرسال الإشعار عبر WhatsApp
            whatsapp_message = f"🔔 {subject}\n\n{message}"

            result = self.notification_service.send_message(
                recipient_user=recipient,
                message_type=notification_type,
                content=whatsapp_message
            )

            if result:
                logger.info(f"✅ تم إرسال تذكير WhatsApp لـ {recipient.get_full_name()}")
                return True
            else:
                logger.error(f"❌ فشل إرسال تذكير WhatsApp لـ {recipient.get_full_name()}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال تذكير لـ {recipient.get_full_name()}: {e}")
            return False


# تم حذف خدمة البريد الإلكتروني الموحدة - تم استبدالها بنظام WhatsApp


# تم حذف خدمة إعادة تعيين كلمة المرور عبر البريد الإلكتروني - تم استبدالها بنظام WhatsApp
