{% extends 'base.html' %}

{% block title %}طلابي - {{ ACADEMY_SLOGAN }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-6">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-islamic-dark">إدارة الطلاب</h1>
                    <p class="text-gray-600 mt-1">عرض وإدارة جميع طلابك المسجلين</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-plus ml-1"></i>
                        إضا<PERSON>ة طالب جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Students Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {% for enrollment in enrollments %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-islamic-light-blue rounded-full flex items-center justify-center ml-4">
                    <i class="fas fa-user text-islamic-primary text-xl"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900">{{ enrollment.student.get_full_name }}</h3>
                    <p class="text-sm text-gray-600">{{ enrollment.student.student_level|default:"مبتدئ" }}</p>
                </div>
            </div>

            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">الدورة:</span>
                    <span class="text-sm font-medium">{{ enrollment.course.title }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">الحصص المتبقية:</span>
                    <span class="text-sm font-medium text-green-600">{{ enrollment.remaining_lessons_count }} حصة</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">التقدم:</span>
                    <span class="text-sm font-medium">{{ enrollment.progress_percentage }}%</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">آخر حصة:</span>
                    <span class="text-sm font-medium">{{ enrollment.last_lesson_date|default:"لا توجد" }}</span>
                </div>
            </div>

            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex space-x-2 space-x-reverse">
                    <button class="flex-1 bg-islamic-primary text-white px-3 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200 text-sm">
                        <i class="fas fa-video ml-1"></i>
                        بدء حصة
                    </button>
                    <button class="flex-1 border border-gray-300 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm">
                        <i class="fas fa-chart-line ml-1"></i>
                        التقدم
                    </button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full text-center py-12">
            <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا يوجد طلاب بعد</h3>
            <p class="text-gray-500">سيظهر طلابك هنا عند تسجيلهم في دوراتك</p>
        </div>
        {% endfor %}
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-3xl font-bold text-islamic-primary mb-2">{{ total_students }}</div>
            <div class="text-sm text-gray-600">إجمالي الطلاب</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">{{ active_students }}</div>
            <div class="text-sm text-gray-600">طلاب نشطون</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-3xl font-bold text-yellow-600 mb-2">{{ new_students }}</div>
            <div class="text-sm text-gray-600">طلاب جدد</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-3xl font-bold text-blue-600 mb-2">{{ completed_students }}</div>
            <div class="text-sm text-gray-600">طلاب مكملون</div>
        </div>
    </div>
</div>
{% endblock %}
