# 🚀 دليل النشر على Render.com

## 📋 المتطلبات الأساسية

1. **حساب GitHub** - لرفع الكود
2. **حساب Render.com** - للنشر المجاني
3. **المشروع جاهز** - تم تنظيفه وإعداده

## 🔧 خطوات النشر

### 1. رفع المشروع على GitHub

```bash
# إنشاء repository جديد
git init
git add .
git commit -m "Initial commit - Qurania LMS ready for deployment"

# ربط مع GitHub (استبدل YOUR_USERNAME بالاسم الخاص بك)
git remote add origin https://github.com/YOUR_USERNAME/qurania-lms.git
git branch -M main
git push -u origin main
```

### 2. إنشاء Web Service على Render

1. **اذهب إلى** [render.com](https://render.com)
2. **سجل دخول** أو أنشئ حساب جديد
3. **اضغط "New"** ثم **"Web Service"**
4. **اربط GitHub** إذا لم تفعل ذلك
5. **اختر Repository** الخاص بك

### 3. إعدادات Web Service

```
Name: qurania-lms
Environment: Python 3
Build Command: ./build.sh
Start Command: gunicorn qurania_lms.wsgi:application
```

### 4. إنشاء قاعدة البيانات PostgreSQL

1. **اضغط "New"** ثم **"PostgreSQL"**
2. **اختر الخطة المجانية**
3. **اسم قاعدة البيانات**: qurania-db
4. **انتظر** حتى تكتمل العملية

### 5. ربط قاعدة البيانات مع Web Service

1. **اذهب إلى Web Service**
2. **اضغط "Environment"**
3. **أضف متغير البيئة**:
   ```
   Key: DATABASE_URL
   Value: [اختر من قاعدة البيانات]
   ```

### 6. متغيرات البيئة الإضافية

أضف المتغيرات التالية في قسم Environment:

```
DJANGO_SETTINGS_MODULE=qurania_lms.settings_render
DEBUG=False
SECRET_KEY=[سيتم إنشاؤه تلقائياً]
SITE_URL=https://your-app-name.onrender.com
```

### 7. النشر

1. **اضغط "Create Web Service"**
2. **انتظر** حتى يكتمل البناء (5-10 دقائق)
3. **تحقق من السجلات** للتأكد من عدم وجود أخطاء

## ✅ التحقق من النشر

### 1. فحص الموقع
- افتح الرابط المعطى من Render
- تأكد من تحميل الصفحة الرئيسية

### 2. تسجيل الدخول كمدير
```
Username: admin
Password: admin123456
Email: <EMAIL>
```

### 3. اختبار الوظائف الأساسية
- ✅ تسجيل الدخول
- ✅ لوحة التحكم
- ✅ إنشاء مستخدم جديد
- ✅ إعدادات النظام

## 🔧 إعدادات إضافية

### تخصيص الدومين
1. **اذهب إلى Settings** في Web Service
2. **أضف Custom Domain**
3. **اتبع التعليمات** لإعداد DNS

### إعداد البريد الإلكتروني
أضف متغيرات البيئة التالية:
```
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>
```

### إعداد WhatsApp
1. **اذهب إلى إعدادات الأكاديمية**
2. **فعل خدمة WhatsApp**
3. **اختبر الإرسال**

## 🚨 استكشاف الأخطاء

### خطأ في البناء
```bash
# تحقق من السجلات في Render
# تأكد من وجود جميع الملفات المطلوبة
```

### خطأ في قاعدة البيانات
```bash
# تأكد من ربط DATABASE_URL
# تحقق من إعدادات PostgreSQL
```

### خطأ في الملفات الثابتة
```bash
# تأكد من تشغيل collectstatic في build.sh
# تحقق من إعدادات STATIC_ROOT
```

## 📞 الدعم

إذا واجهت أي مشاكل:

1. **تحقق من السجلات** في Render Dashboard
2. **راجع هذا الدليل** مرة أخرى
3. **تواصل معنا** عبر GitHub Issues

## 🎉 تهانينا!

تم نشر نظام أكاديمية القرآنية بنجاح! 🎊

الآن يمكن للمستخدمين الوصول إلى النظام من أي مكان في العالم.

---

**"وَقُلْ رَبِّ زِدْنِي عِلْمًا"** - سورة طه، آية 114
