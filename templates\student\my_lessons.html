{% extends 'lessons/base_lesson.html' %}
{% load static %}

{% block page_title %}حصصي{% endblock %}
{% block header_icon %}fas fa-user-graduate{% endblock %}
{% block page_description %}عرض وإدارة حصصك التعليمية{% endblock %}

{% block header_actions %}
<div class="flex flex-col sm:flex-row gap-2 sm:gap-4">
    {% if not active_subscription %}
        <a href="{% url 'lessons:student_trial_lesson' %}"
           class="btn-primary flex items-center justify-center">
            <i class="fas fa-flask ml-2"></i>
            احجز حصة تجريبية
        </a>
        <a href="{% url 'subscription_plans' %}"
           class="btn-secondary flex items-center justify-center">
            <i class="fas fa-shopping-cart ml-2"></i>
            اشترك الآن
        </a>
    {% else %}
        <a href="{% url 'lessons:student_book_lesson' %}"
           class="btn-primary flex items-center justify-center">
            <i class="fas fa-calendar-plus ml-2"></i>
            احجز حصة جديدة
        </a>
    {% endif %}
    
    <div class="relative">
        <button onclick="toggleDropdown('student-quick-actions')" 
                class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors flex items-center">
            <i class="fas fa-user-cog ml-2"></i>
            حسابي
        </button>
        <div id="student-quick-actions" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
            <a href="{% url 'lessons:student_profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <i class="fas fa-user ml-2"></i>
                الملف الشخصي
            </a>
            <a href="{% url 'lessons:student_progress' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <i class="fas fa-chart-line ml-2"></i>
                تقدمي
            </a>
            <a href="{% url 'lessons:student_certificates' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <i class="fas fa-certificate ml-2"></i>
                شهاداتي
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block lesson_stats %}
{% include 'components/lesson_stats.html' with stats=student_stats user_type='student' %}
{% endblock %}

{% block lesson_content %}
<div class="space-y-6">
    
    <!-- معلومات الاشتراك -->
    {% if active_subscription %}
    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-green-900">
                <i class="fas fa-crown text-yellow-500 ml-2"></i>
                اشتراكك النشط
            </h3>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                <span class="w-2 h-2 bg-green-600 rounded-full ml-2"></span>
                نشط
            </span>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 border border-green-100">
                <p class="text-sm text-green-600 mb-1">الباقة</p>
                <p class="font-semibold text-green-900">{{ active_subscription.plan.name }}</p>
            </div>
            <div class="bg-white rounded-lg p-4 border border-green-100">
                <p class="text-sm text-green-600 mb-1">الحصص المتبقية</p>
                <p class="font-semibold text-green-900">{{ active_subscription.remaining_lessons }} حصة</p>
            </div>
            <div class="bg-white rounded-lg p-4 border border-green-100">
                <p class="text-sm text-green-600 mb-1">تاريخ الانتهاء</p>
                <p class="font-semibold text-green-900">{{ active_subscription.end_date|date:"Y/m/d" }}</p>
            </div>
        </div>
        
        {% if active_subscription.remaining_lessons <= 3 %}
        <div class="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded-lg">
            <p class="text-sm text-yellow-800">
                <i class="fas fa-exclamation-triangle ml-1"></i>
                تنبيه: لديك {{ active_subscription.remaining_lessons }} حصص متبقية فقط. 
                <a href="{% url 'renew_subscription' %}" class="underline font-semibold">جدد اشتراكك الآن</a>
            </p>
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- الحصة القادمة -->
    {% if next_lesson %}
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-blue-900">
                <i class="fas fa-star text-yellow-500 ml-2"></i>
                حصتك القادمة
            </h3>
            <span class="text-sm text-blue-700" data-time-until="{{ next_lesson.scheduled_date|date:'c' }}">
                <!-- سيتم تحديثها بـ JavaScript -->
            </span>
        </div>
        
        <div class="bg-white rounded-lg p-4 border border-blue-100">
            {% include 'components/lesson_card.html' with lesson=next_lesson user_type='student' %}
        </div>
        
        {% if next_lesson.scheduled_date|time_until_minutes <= 15 %}
        <div class="mt-4 flex items-center justify-center">
            <a href="{% url 'lessons:lesson_room' next_lesson.id %}"
               class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center">
                <i class="fas fa-video ml-2"></i>
                دخول الحصة الآن
            </a>
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- الحصص الجارية -->
    {% if live_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-green-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-green-900">
                <i class="fas fa-video text-green-600 ml-2"></i>
                الحصص الجارية
            </h3>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 animate-pulse">
                <span class="w-2 h-2 bg-green-600 rounded-full ml-2"></span>
                جارية الآن
            </span>
        </div>
        <div class="space-y-3">
            {% for lesson in live_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='student' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- حصص الاشتراك -->
    {% if subscription_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-calendar text-blue-600 ml-2"></i>
                حصص الاشتراك ({{ subscription_lessons|length }})
            </h3>
            <button onclick="toggleSection('subscription-lessons')" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div id="subscription-lessons" class="space-y-3">
            {% for lesson in subscription_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='student' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- الحصص التجريبية -->
    {% if trial_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-orange-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-orange-900">
                <i class="fas fa-flask text-orange-600 ml-2"></i>
                الحصص التجريبية ({{ trial_lessons|length }})
            </h3>
            <button onclick="toggleSection('trial-lessons')" class="text-orange-600 hover:text-orange-800">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div id="trial-lessons" class="space-y-3">
            {% for lesson in trial_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='student' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- الحصص التعويضية -->
    {% if makeup_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-pink-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-pink-900">
                <i class="fas fa-redo text-pink-600 ml-2"></i>
                الحصص التعويضية ({{ makeup_lessons|length }})
            </h3>
            <button onclick="toggleSection('makeup-lessons')" class="text-pink-600 hover:text-pink-800">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div id="makeup-lessons" class="space-y-3">
            {% for lesson in makeup_lessons %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='student' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- الحصص المكتملة التي تحتاج تقييم -->
    {% if pending_evaluations %}
    <div class="bg-purple-50 rounded-lg border border-purple-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-purple-900">
                <i class="fas fa-star text-purple-600 ml-2"></i>
                حصص تحتاج تقييم ({{ pending_evaluations|length }})
            </h3>
            <button onclick="evaluateAllLessons()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm">
                <i class="fas fa-star ml-1"></i>
                تقييم جميع الحصص
            </button>
        </div>
        <div class="space-y-3">
            {% for lesson in pending_evaluations %}
                {% include 'components/lesson_card.html' with lesson=lesson user_type='student' %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- جميع الحصص -->
    {% if not subscription_lessons and not trial_lessons and not makeup_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="text-center py-12">
            <i class="fas fa-calendar-times text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد حصص</h3>
            <p class="text-gray-500 mb-6">لم تحجز أي حصص بعد</p>
            
            {% if not active_subscription %}
                <div class="space-y-3">
                    <a href="{% url 'lessons:student_trial_lesson' %}" class="btn-primary inline-flex items-center">
                        <i class="fas fa-flask ml-2"></i>
                        احجز حصة تجريبية مجانية
                    </a>
                    <p class="text-sm text-gray-600">أو</p>
                    <a href="{% url 'subscription_plans' %}" class="btn-secondary inline-flex items-center">
                        <i class="fas fa-shopping-cart ml-2"></i>
                        اشترك في إحدى الباقات
                    </a>
                </div>
            {% else %}
                <a href="{% url 'lessons:student_book_lesson' %}" class="btn-primary inline-flex items-center">
                    <i class="fas fa-calendar-plus ml-2"></i>
                    احجز حصة جديدة
                </a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block lesson_js %}
<script>
document.body.dataset.userType = 'student';

function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    const button = event.target;
    
    if (section.style.display === 'none') {
        section.style.display = 'block';
        button.innerHTML = '<i class="fas fa-chevron-down"></i>';
    } else {
        section.style.display = 'none';
        button.innerHTML = '<i class="fas fa-chevron-up"></i>';
    }
}

function evaluateAllLessons() {
    // فتح modal لتقييم جميع الحصص
    alert('سيتم إضافة هذه الميزة قريباً');
}

// تحديث العد التنازلي للحصة القادمة
function updateNextLessonCountdown() {
    const countdownElement = document.querySelector('[data-time-until]');
    if (countdownElement) {
        const targetTime = new Date(countdownElement.dataset.timeUntil);
        const now = new Date();
        const diff = targetTime - now;
        
        if (diff > 0) {
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            
            if (hours > 0) {
                countdownElement.textContent = `خلال ${hours} ساعة و ${minutes} دقيقة`;
            } else if (minutes > 0) {
                countdownElement.textContent = `خلال ${minutes} دقيقة`;
            } else {
                countdownElement.textContent = 'حان الوقت!';
                countdownElement.classList.add('text-red-600', 'font-bold', 'animate-pulse');
            }
        } else {
            countdownElement.textContent = 'انتهى الوقت';
        }
    }
}

// تحديث العد التنازلي كل دقيقة
setInterval(updateNextLessonCountdown, 60000);
updateNextLessonCountdown(); // تشغيل فوري

// تحديث تلقائي للحصص الجارية
setInterval(() => {
    if (document.querySelector('.animate-pulse')) {
        window.lessonsManager?.refreshLessonsData();
    }
}, 30000);

// تنبيه قبل الحصة بـ 5 دقائق
function checkUpcomingLessons() {
    const nextLessonElement = document.querySelector('[data-time-until]');
    if (nextLessonElement) {
        const targetTime = new Date(nextLessonElement.dataset.timeUntil);
        const now = new Date();
        const diff = targetTime - now;
        const minutes = Math.floor(diff / (1000 * 60));
        
        if (minutes === 5) {
            // إظهار تنبيه
            if (Notification.permission === 'granted') {
                new Notification('تذكير بالحصة', {
                    body: 'حصتك ستبدأ خلال 5 دقائق',
                    icon: '/static/images/logo.png'
                });
            }
        }
    }
}

// طلب إذن الإشعارات
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}

// فحص الحصص القادمة كل دقيقة
setInterval(checkUpcomingLessons, 60000);
</script>
{% endblock %}
