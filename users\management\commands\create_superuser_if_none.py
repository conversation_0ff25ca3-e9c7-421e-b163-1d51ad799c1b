"""
أمر Django لإنشاء superuser إذا لم يكن موجوداً
يتم تشغيله تلقائياً عند النشر على Render
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
import os

User = get_user_model()


class Command(BaseCommand):
    help = 'إنشاء superuser إذا لم يكن موجوداً'

    def handle(self, *args, **options):
        if User.objects.filter(is_superuser=True).exists():
            self.stdout.write(
                self.style.SUCCESS('✅ يوجد superuser بالفعل')
            )
            return

        # إنشاء superuser من متغيرات البيئة أو قيم افتراضية
        username = os.environ.get('DJANGO_SUPERUSER_USERNAME', 'admin')
        email = os.environ.get('DJANGO_SUPERUSER_EMAIL', '<EMAIL>')
        password = os.environ.get('DJANGO_SUPERUSER_PASSWORD', 'admin123456')

        try:
            user = User.objects.create_superuser(
                username=username,
                email=email,
                password=password,
                first_name='مدير',
                last_name='النظام'
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ تم إنشاء superuser: {username}')
            )
            self.stdout.write(f'📧 البريد الإلكتروني: {email}')
            self.stdout.write('🔑 كلمة المرور: admin123456 (يرجى تغييرها)')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في إنشاء superuser: {str(e)}')
            )
