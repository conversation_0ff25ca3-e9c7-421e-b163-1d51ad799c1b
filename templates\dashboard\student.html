{% extends 'base.html' %}
{% load math_filters %}

{% block title %}لوحة تحكم الطالب - {{ ACADEMY_SLOGAN }}{% endblock %}

{% block extra_css %}
<style>
    /* تحسينات التجاوب للوحة تحكم الطالب */
    @media (max-width: 768px) {
        .stats-card {
            padding: 1rem !important;
        }

        .stats-icon {
            width: 2.5rem !important;
            height: 2.5rem !important;
            font-size: 1rem !important;
        }

        .enhanced-card-body {
            padding: 1rem !important;
        }

        .islamic-content-card {
            margin: 0 1rem 2rem 1rem !important;
        }

        .islamic-content-card .bg-gradient-to-r {
            padding: 1.5rem !important;
        }

        .islamic-content-card h3 {
            font-size: 1.25rem !important;
        }

        .islamic-content-card .w-14 {
            width: 2.5rem !important;
            height: 2.5rem !important;
        }

        .islamic-content-card .text-2xl {
            font-size: 1rem !important;
        }

        .islamic-content-card .grid-cols-1 {
            grid-template-columns: 1fr !important;
        }

        .islamic-content-card .lg\:grid-cols-2 {
            grid-template-columns: 1fr !important;
        }

        /* تحسينات للبطاقات الجديدة */
        .grid.grid-cols-1.lg\:grid-cols-3 {
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .enhanced-card h3 {
            font-size: 1.125rem !important;
        }

        .enhanced-card .w-12 {
            width: 2.5rem !important;
            height: 2.5rem !important;
        }

        .enhanced-card .text-xl {
            font-size: 1rem !important;
        }

        .enhanced-card .w-10 {
            width: 2rem !important;
            height: 2rem !important;
        }

        .enhanced-card .space-y-4 > * {
            padding: 0.75rem !important;
        }

        .enhanced-card .space-y-4 .font-medium {
            font-size: 0.875rem !important;
        }

        .enhanced-card .space-y-4 .text-sm {
            font-size: 0.75rem !important;
        }

        /* تحسين تباين الأيقونات */
        .enhanced-card .w-16 {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }

        .enhanced-card .w-12 {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        }

        /* إزالة الألوان الخضراء الغامقة */
        .badge-success-enhanced {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
            color: white !important;
        }

        /* تحسين الأزرار */
        .enhanced-card a {
            font-weight: 600 !important;
            letter-spacing: 0.025em !important;
        }

        /* تحسين النصوص */
        .enhanced-card .text-gray-700 {
            color: #374151 !important;
            font-weight: 500 !important;
        }

        .enhanced-card .font-semibold {
            font-weight: 700 !important;
        }

        /* إزالة الخطوط الخضراء من أسفل البطاقات */
        .enhanced-card::before,
        .enhanced-card::after,
        .stats-card::before,
        .stats-card::after {
            display: none !important;
        }

        /* إزالة الخطوط الملونة من البطاقات المحسنة */
        .bg-gradient-to-r.from-green-50.to-emerald-50::before,
        .bg-gradient-to-r.from-emerald-50.to-teal-50::before,
        .stats-card .bg-gradient-to-br.from-green-50::before {
            display: none !important;
        }

        /* إزالة جميع الخطوط الملونة من البطاقات */
        .bg-gradient-to-r::before,
        .bg-gradient-to-br::before {
            display: none !important;
        }

        /* إزالة الخطوط من بطاقات الإحصائيات */
        .stats-card .bg-gradient-to-br.from-blue-50::before,
        .stats-card .bg-gradient-to-br.from-purple-50::before,
        .stats-card .bg-gradient-to-br.from-orange-50::before,
        .stats-card .bg-gradient-to-br.from-yellow-50::before,
        .stats-card .bg-gradient-to-br.from-indigo-50::before,
        .stats-card .bg-gradient-to-br.from-pink-50::before,
        .stats-card .bg-gradient-to-br.from-teal-50::before,
        .stats-card .bg-gradient-to-br.from-cyan-50::before {
            display: none !important;
        }

        /* إزالة جميع العناصر الزائفة من البطاقات */
        .enhanced-card-body::before,
        .enhanced-card-body::after,
        .enhanced-card-header::before,
        .enhanced-card-header::after {
            display: none !important;
        }

        /* إزالة الخطوط من البطاقات الإسلامية */
        .stats-card-islamic::before,
        .stats-card-islamic::after {
            display: none !important;
        }

        /* تنظيف البطاقات من أي عناصر زائفة */
        .card::before,
        .card::after,
        .bg-white.rounded-lg.shadow-sm::before,
        .bg-white.rounded-lg.shadow-sm::after {
            display: none !important;
        }

        .enhanced-card-header h1 {
            font-size: 1.5rem !important;
        }

        .enhanced-card-header .text-3xl {
            font-size: 1.5rem !important;
        }

        .enhanced-card-header .ml-3 {
            margin-left: 0.5rem !important;
        }

        .enhanced-card-header .space-x-4 > * + * {
            margin-right: 0.5rem !important;
        }

        .enhanced-card-header .px-4 {
            padding-left: 0.75rem !important;
            padding-right: 0.75rem !important;
        }

        .enhanced-card-header .py-2 {
            padding-top: 0.5rem !important;
            padding-bottom: 0.5rem !important;
        }

        .grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-4 {
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .grid.grid-cols-1.lg\:grid-cols-2 {
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .grid.grid-cols-1.lg\:grid-cols-3 {
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .text-3xl {
            font-size: 1.5rem !important;
        }

        .text-4xl {
            font-size: 2rem !important;
        }

        .text-6xl {
            font-size: 3rem !important;
        }

        .p-6 {
            padding: 1rem !important;
        }

        .p-8 {
            padding: 1.5rem !important;
        }

        .mb-8 {
            margin-bottom: 1.5rem !important;
        }

        .space-y-6 > * + * {
            margin-top: 1rem !important;
        }

        .bg-white.rounded-lg.shadow-sm {
            margin-bottom: 1rem !important;
        }

        /* تحسين الأزرار للموبايل */
        .bg-islamic-primary,
        .bg-white.text-green-600,
        .bg-white.text-blue-600,
        .bg-white.text-red-600 {
            padding: 0.75rem 1rem !important;
            font-size: 0.875rem !important;
            min-height: 44px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        /* تحسين النصوص للموبايل */
        .text-lg {
            font-size: 1rem !important;
        }

        .text-xl {
            font-size: 1.125rem !important;
        }

        .text-2xl {
            font-size: 1.25rem !important;
        }

        /* تحسين المحتوى الإسلامي للموبايل */
        .islamic-content-card .flex.items-center.justify-between {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 1rem !important;
        }

        .islamic-content-card .flex.items-center.space-x-2 {
            flex-direction: column !important;
            gap: 0.5rem !important;
            width: 100% !important;
        }

        .islamic-content-card button,
        .islamic-content-card .bg-white.bg-opacity-20 {
            width: 100% !important;
            justify-content: center !important;
        }
    }

    @media (max-width: 640px) {
        /* تحسينات الإجراءات السريعة للموبايل */
        .space-y-3 > * + * {
            margin-top: 0.5rem !important;
        }

        .space-y-3 a {
            padding: 0.75rem !important;
            font-size: 0.75rem !important;
        }

        .space-y-3 .fas {
            font-size: 0.875rem !important;
        }

        /* تحسين بطاقات الدورات */
        .grid.grid-cols-1.md\\:grid-cols-2 {
            grid-template-columns: 1fr !important;
            gap: 0.75rem !important;
        }

        .border.border-gray-200.rounded-lg.p-4 {
            padding: 0.75rem !important;
        }

        .border.border-gray-200.rounded-lg.p-4 h5 {
            font-size: 0.875rem !important;
        }

        .border.border-gray-200.rounded-lg.p-4 p {
            font-size: 0.75rem !important;
        }
    }

    @media (max-width: 1024px) {
        /* تحسينات للأجهزة اللوحية */
        .grid.grid-cols-1.lg\\:grid-cols-2 {
            grid-template-columns: 1fr !important;
        }

        .grid.grid-cols-1.lg\\:grid-cols-3 {
            grid-template-columns: repeat(2, 1fr) !important;
        }
    }

    @media (max-width: 480px) {
        .enhanced-card-header {
            padding: 1rem !important;
        }

        .enhanced-card-header .flex.items-center.justify-between {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 1rem !important;
        }

        .enhanced-card-header .flex.items-center.space-x-4 {
            flex-direction: column !important;
            gap: 0.5rem !important;
            width: 100% !important;
        }

        .enhanced-card-header .bg-white.bg-opacity-20 {
            width: 100% !important;
            text-align: center !important;
        }

        .stats-card .text-3xl {
            font-size: 1.25rem !important;
        }

        .islamic-content-card .grid.gap-6 {
            gap: 1rem !important;
        }

        .islamic-content-card .p-6 {
            padding: 1rem !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<script>
document.body.classList.add('student-dashboard');

// تطبيق الألوان بقوة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق اللون على القائمة الجانبية
    const sidebar = document.querySelector('.new-sidebar');
    if (sidebar) {
        sidebar.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
        sidebar.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
    }

    // تطبيق اللون على header البطاقة
    const cardHeader = document.querySelector('.enhanced-card-header');
    if (cardHeader) {
        cardHeader.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
        cardHeader.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
    }

    // تطبيق اللون على البطاقة الرئيسية
    const enhancedCard = document.querySelector('.enhanced-card');
    if (enhancedCard) {
        enhancedCard.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
        enhancedCard.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
    }

    // تطبيق اللون على زر القائمة الجانبية
    const sidebarToggle = document.querySelector('.new-sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
        sidebarToggle.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
    }
});
</script>
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <!-- Enhanced Header -->
    <div class="enhanced-card mb-8 animate-fade-in-up">
        <div class="enhanced-card-header">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-white flex items-center">
                        <i class="fas fa-user-graduate ml-2 md:ml-3 text-islamic-gold text-xl md:text-2xl"></i>
                        لوحة تحكم الطالب
                    </h1>
                    <p class="text-islamic-light-gold mt-2">مرحباً بك {{ user.get_full_name }}، إليك نظرة عامة على تقدمك وحصصك</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                        <i class="fas fa-layer-group ml-2 text-islamic-gold"></i>
                        <span class="text-white font-medium">
                            {% if user.student_level %}
                                {{ user.get_student_level_display }}
                            {% else %}
                                مبتدئ
                            {% endif %}
                        </span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                        <i class="fas fa-graduation-cap ml-2 text-islamic-gold"></i>
                        <span class="text-white font-medium">طالب علم</span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                        <i class="fas fa-clock ml-2 text-islamic-gold"></i>
                        <span class="text-white font-medium" id="current-time-date">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8 px-4 md:px-0">
        <!-- Total Courses -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.1s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                    <i class="fas fa-book text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">دوراتي</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ total_courses|default:"2" }}</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="badge-info-enhanced">دورة نشطة</span>
            </div>
        </div>

        <!-- Today's Lessons -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                    <i class="fas fa-calendar-day text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">حصص اليوم</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ today_lessons_count|default:"3" }}</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="badge-info-enhanced">{{ upcoming_lessons_count|default:"5" }} حصة قادمة</span>
            </div>
        </div>

        <!-- Completed Lessons -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-yellow-500 to-orange-500 text-white">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">الحصص المكتملة</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ total_lessons_completed|default:"24" }}</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="badge-warning-enhanced">حصة مكتملة</span>
            </div>
        </div>

        <!-- Progress Percentage -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-purple-500 to-violet-600 text-white">
                    <i class="fas fa-chart-pie text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">نسبة التقدم</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ progress_percentage|default:"60" }}%</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="progress-enhanced">
                    <div class="progress-bar-enhanced" style="width: {{ progress_percentage|default:"60" }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Lessons Section -->
    {% if live_lessons or scheduled_live_lessons or subscription_scheduled_lessons %}
    <div class="mb-8">
        <div class="bg-gradient-to-r from-red-500 to-pink-600 rounded-lg shadow-lg p-6 text-white">
            <h3 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-video text-white ml-2"></i>
                الحصص المباشرة والمجدولة
            </h3>

            <!-- Live Lessons -->
            {% if live_lessons %}
                <div class="mb-4">
                    <h4 class="text-lg font-semibold mb-3 flex items-center">
                        <div class="w-3 h-3 bg-white rounded-full animate-pulse ml-2"></div>
                        مباشرة الآن
                    </h4>
                    <div class="grid gap-3">
                        {% for lesson in live_lessons %}
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="font-bold">{{ lesson.title }}</h5>
                                    <p class="text-sm opacity-90">مع {{ lesson.teacher.get_full_name }}</p>
                                    <p class="text-xs opacity-75">بدأت منذ {{ lesson.started_at|timesince }}</p>
                                </div>
                                <a href="{% url 'student_live_lesson' lesson.id %}"
                                   class="bg-white text-islamic-primary px-4 py-2 rounded-lg font-medium hover:bg-islamic-light transition-colors border border-islamic-primary">
                                    <i class="fas fa-video ml-1"></i>
                                    دخول الحصة
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Scheduled Live Lessons -->
            {% if scheduled_live_lessons %}
                <div class="mb-4">
                    <h4 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-clock text-white ml-2"></i>
                        حصص مباشرة مجدولة
                    </h4>
                    <div class="grid gap-3">
                        {% for lesson in scheduled_live_lessons %}
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="font-bold">{{ lesson.title }}</h5>
                                    <p class="text-sm opacity-90">مع {{ lesson.teacher.get_full_name }}</p>
                                    <p class="text-xs opacity-75">{{ lesson.scheduled_date|date:"Y-m-d H:i" }}</p>
                                </div>
                                <a href="{% url 'student_live_lesson' lesson.id %}"
                                   class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                                    <i class="fas fa-calendar ml-1"></i>
                                    الاستعداد
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Subscription Scheduled Lessons -->
            {% if subscription_scheduled_lessons %}
                <div>
                    <h4 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-calendar-alt text-white ml-2"></i>
                        حصص مجدولة من اشتراكي
                        <span class="bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full mr-2">جديد</span>
                    </h4>
                    <div class="grid gap-3">
                        {% for lesson in subscription_scheduled_lessons %}
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm border-l-4 border-blue-300">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="font-bold">حصة رقم {{ lesson.lesson_number }}</h5>
                                    {% if lesson.teacher %}
                                        <p class="text-sm opacity-90">مع {{ lesson.teacher.get_full_name }}</p>
                                    {% else %}
                                        <p class="text-sm opacity-90">لم يتم تعيين معلم بعد</p>
                                    {% endif %}
                                    <p class="text-xs opacity-75">{{ lesson.scheduled_date|date:"Y-m-d H:i" }} - {{ lesson.duration_minutes }} دقيقة</p>
                                    <p class="text-xs opacity-75">باقة: {{ lesson.subscription.plan.name }}</p>
                                </div>
                                <div class="flex flex-col gap-2">
                                    <span class="bg-islamic-primary text-white px-3 py-1 rounded-full text-xs font-medium">
                                        {{ lesson.status|default:"مجدولة" }}
                                    </span>
                                    <a href="{% url 'student_lessons' %}"
                                       class="bg-white text-islamic-primary px-4 py-2 rounded-lg font-medium hover:bg-islamic-light transition-colors text-center border border-islamic-primary">
                                        <i class="fas fa-calendar-check ml-1"></i>
                                        عرض في التقويم
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Islamic Content Card - Azkar & Quran -->
    <div class="mb-8 animate-fade-in-up islamic-content-card" style="animation-delay: 0.9s">
        <div class="bg-gradient-to-r from-islamic-primary to-emerald-600 rounded-xl p-8 text-white relative overflow-hidden shadow-lg">
            <!-- Islamic Pattern Background -->
            <div class="absolute inset-0 opacity-10">
                <div class="islamic-pattern w-full h-full"></div>
            </div>

            <!-- Content Container -->
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <div class="w-14 h-14 bg-islamic-gold rounded-full flex items-center justify-center ml-4 shadow-xl border-2 border-white border-opacity-30">
                            <i class="fas fa-quran text-islamic-primary text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-white">الأذكار والقرآن الكريم</h3>
                            <p class="text-islamic-light-gold text-sm">تذكير روحاني يومي</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="refreshIslamicContent()" class="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg px-4 py-2 transition-all duration-300 flex items-center">
                            <i class="fas fa-sync-alt text-islamic-gold ml-2" id="refresh-icon-student"></i>
                            <span class="text-white text-sm">تجديد</span>
                        </button>
                        <div class="bg-white bg-opacity-20 rounded-lg px-3 py-2">
                            <i class="fas fa-clock text-islamic-gold ml-1"></i>
                            <span class="text-white text-xs" id="islamic-timer-student">يتجدد كل 30 ثانية</span>
                        </div>
                    </div>
                </div>

                <!-- Content Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Quran Verse Section -->
                    <div class="bg-white bg-opacity-10 rounded-xl p-6 backdrop-blur-sm border border-white border-opacity-20">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-islamic-gold rounded-full flex items-center justify-center ml-3 shadow-lg">
                                <i class="fas fa-book-open text-islamic-primary text-lg font-bold"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-white">آية كريمة</h4>
                        </div>
                        <div id="quran-content-student" class="text-center">
                            <p class="text-xl arabic-text font-semibold text-islamic-light-gold mb-3 leading-relaxed" id="quran-verse-student">
                                "وَقُلْ رَبِّ زِدْنِي عِلْمًا"
                            </p>
                            <p class="text-sm text-white opacity-80" id="quran-reference-student">
                                سورة طه - آية 114
                            </p>
                        </div>
                    </div>

                    <!-- Azkar Section -->
                    <div class="bg-white bg-opacity-10 rounded-xl p-6 backdrop-blur-sm border border-white border-opacity-20">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-islamic-gold rounded-full flex items-center justify-center ml-3 shadow-lg">
                                <i class="fas fa-hands text-islamic-primary text-lg font-bold"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-white">ذكر شريف</h4>
                        </div>
                        <div id="azkar-content-student" class="text-center">
                            <p class="text-lg arabic-text font-semibold text-islamic-light-gold mb-3 leading-relaxed" id="azkar-text-student">
                                "سبحان الله وبحمده، سبحان الله العظيم"
                            </p>
                            <p class="text-sm text-white opacity-80" id="azkar-benefit-student">
                                كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Islamic Quote at Bottom -->
                <div class="mt-6 text-center border-t border-white border-opacity-20 pt-6">
                    <p class="text-islamic-light-gold text-sm italic" id="islamic-quote-student">
                        "اللهم بارك لنا فيما علمتنا وعلمنا ما ينفعنا وزدنا علماً"
                    </p>
                </div>
            </div>

            <!-- Decorative Elements -->
            <div class="absolute top-6 right-6 w-16 h-16 border-2 border-islamic-gold border-opacity-40 rounded-full animate-pulse"></div>
            <div class="absolute bottom-6 left-6 w-12 h-12 border-2 border-islamic-gold border-opacity-40 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/2 right-12 w-3 h-3 bg-islamic-gold rounded-full opacity-70 animate-bounce" style="animation-delay: 0.5s;"></div>
            <div class="absolute top-1/4 left-12 w-4 h-4 bg-islamic-gold rounded-full opacity-60 animate-bounce" style="animation-delay: 1.5s;"></div>
            <div class="absolute bottom-1/3 right-20 w-2 h-2 bg-islamic-gold rounded-full opacity-50"></div>
            <div class="absolute top-3/4 left-20 w-2 h-2 bg-islamic-gold rounded-full opacity-50"></div>
        </div>
    </div>

    <!-- Today's Schedule and Progress -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Today's Lessons -->
        <div class="enhanced-card animate-slide-in-right" style="animation-delay: 0.5s">
            <div class="enhanced-card-body">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-islamic-primary rounded-xl flex items-center justify-center ml-4 shadow-lg">
                            <i class="fas fa-calendar-day text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">حصص اليوم</h3>
                            <p class="text-gray-600 text-sm">جدولك اليومي</p>
                        </div>
                    </div>
                </div>
                <div class="text-center py-8">
                    <i class="fas fa-calendar-check text-islamic-primary text-4xl mb-4"></i>
                    <p class="text-gray-600 font-medium"><span data-stat="today-lessons">{{ today_lessons_count }}</span> حصة مجدولة لليوم</p>
                    <p class="text-gray-500 text-sm mt-2">يمكنك عرض التفاصيل من صفحة "حصصي"</p>
                    <a href="{% url 'student_lessons' %}"
                       class="inline-block mt-4 bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-play-circle ml-1"></i>
                        عرض حصصي
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Progress -->
        <div class="enhanced-card animate-slide-in-right" style="animation-delay: 0.6s">
            <div class="enhanced-card-body">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-islamic-primary rounded-xl flex items-center justify-center ml-4 shadow-lg">
                            <i class="fas fa-chart-line text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">آخر التقدم</h3>
                            <p class="text-gray-600 text-sm">تقدمك الأكاديمي</p>
                        </div>
                    </div>
                </div>
                <div class="text-center py-8">
                    <i class="fas fa-chart-line text-islamic-primary text-4xl mb-4"></i>
                    <p class="text-gray-600 font-medium">{{ progress_percentage }}% من التقدم العام</p>
                    <p class="text-gray-500 text-sm mt-2">{{ completed_lessons }} حصة مكتملة من أصل {{ total_lessons }}</p>
                    <a href="{% url 'student_progress' %}"
                       class="inline-block mt-4 text-islamic-primary hover:text-islamic-light font-medium">
                        عرض التقدم التفصيلي
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Cards Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- My Active Subscriptions -->
        <div class="enhanced-card animate-slide-in-right" style="animation-delay: 0.7s">
            <div class="enhanced-card-body">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center ml-4 shadow-lg">
                            <i class="fas fa-book-open text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">اشتراكاتي النشطة</h3>
                            <p class="text-gray-600 text-sm">الباقات والاشتراكات</p>
                        </div>
                    </div>
                </div>
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <i class="fas fa-graduation-cap text-white text-2xl"></i>
                    </div>
                    <p class="text-gray-700 font-semibold text-lg"><span data-stat="active-subscriptions">{{ total_active_subscriptions }}</span> اشتراك نشط</p>
                    <p class="text-gray-600 text-sm mt-2"><span data-stat="remaining-lessons">{{ total_subscription_lessons_remaining }}</span> حصة متبقية</p>
                    <a href="{% url 'student_subscriptions' %}"
                       class="inline-block mt-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-md font-medium">
                        <i class="fas fa-eye ml-2"></i>
                        عرض اشتراكاتي
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="enhanced-card animate-slide-in-right" style="animation-delay: 0.8s">
            <div class="enhanced-card-body">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl flex items-center justify-center ml-4 shadow-lg">
                            <i class="fas fa-bolt text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">إجراءات سريعة</h3>
                            <p class="text-gray-600 text-sm">الوصول السريع</p>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <a href="{% url 'student_lessons' %}"
                       class="flex items-center p-4 rounded-xl bg-gradient-to-r from-islamic-light to-islamic-mint hover:from-islamic-mint hover:to-islamic-light transition-all duration-200 border border-islamic-primary shadow-sm hover:shadow-md">
                        <div class="w-12 h-12 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-xl flex items-center justify-center ml-3 shadow-md">
                            <i class="fas fa-play-circle text-white text-lg"></i>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-900">حصصي</span>
                            <p class="text-sm text-gray-700">عرض وإدارة الحصص</p>
                        </div>
                    </a>
                    <a href="{% url 'student_progress' %}"
                       class="flex items-center p-4 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 border border-blue-300 shadow-sm hover:shadow-md">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center ml-3 shadow-md">
                            <i class="fas fa-chart-line text-white text-lg"></i>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-900">تقدمي</span>
                            <p class="text-sm text-gray-700">متابعة التقدم الأكاديمي</p>
                        </div>
                    </a>
                    <a href="{% url 'ticket_list' %}"
                       class="flex items-center p-4 rounded-xl bg-gradient-to-r from-orange-50 to-amber-50 hover:from-orange-100 hover:to-amber-100 transition-all duration-200 border border-orange-300 shadow-sm hover:shadow-md">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center ml-3 shadow-md">
                            <i class="fas fa-question-circle text-white text-lg"></i>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-900">الدعم الفني</span>
                            <p class="text-sm text-gray-700">المساعدة والاستفسارات</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Upcoming Lessons -->
        <div class="enhanced-card animate-slide-in-right" style="animation-delay: 0.9s">
            <div class="enhanced-card-body">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center ml-4 shadow-lg">
                            <i class="fas fa-clock text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">الحصص القادمة</h3>
                            <p class="text-gray-600 text-sm">الجدول القادم</p>
                        </div>
                    </div>
                </div>
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gradient-to-br from-amber-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <i class="fas fa-calendar-alt text-white text-2xl"></i>
                    </div>
                    <p class="text-gray-700 font-semibold text-lg"><span data-stat="upcoming-lessons">{{ upcoming_lessons_count }}</span> حصة قادمة</p>
                    <p class="text-gray-600 text-sm mt-2">خلال الأسبوع القادم</p>
                    <a href="{% url 'student_lessons' %}"
                       class="inline-block mt-4 bg-gradient-to-r from-amber-500 to-orange-600 text-white px-6 py-3 rounded-lg hover:from-amber-600 hover:to-orange-700 transition-all duration-200 shadow-md font-medium">
                        <i class="fas fa-calendar-check ml-2"></i>
                        عرض جميع الحصص
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
// Islamic Content Data
const quranVerses = [
    {
        verse: "وَقُلْ رَبِّ زِدْنِي عِلْمًا",
        reference: "سورة طه - آية 114"
    },
    {
        verse: "وَمَا أُوتِيتُم مِّنَ الْعِلْمِ إِلَّا قَلِيلًا",
        reference: "سورة الإسراء - آية 85"
    },
    {
        verse: "يَرْفَعِ اللَّهُ الَّذِينَ آمَنُوا مِنكُمْ وَالَّذِينَ أُوتُوا الْعِلْمَ دَرَجَاتٍ",
        reference: "سورة المجادلة - آية 11"
    },
    {
        verse: "وَعَلَّمَكَ مَا لَمْ تَكُن تَعْلَمُ ۚ وَكَانَ فَضْلُ اللَّهِ عَلَيْكَ عَظِيمًا",
        reference: "سورة النساء - آية 113"
    },
    {
        verse: "اقْرَأْ بِاسْمِ رَبِّكَ الَّذِي خَلَقَ",
        reference: "سورة العلق - آية 1"
    },
    {
        verse: "وَقُل رَّبِّ أَدْخِلْنِي مُدْخَلَ صِدْقٍ وَأَخْرِجْنِي مُخْرَجَ صِدْقٍ",
        reference: "سورة الإسراء - آية 80"
    },
    {
        verse: "رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً",
        reference: "سورة البقرة - آية 201"
    },
    {
        verse: "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
        reference: "سورة الطلاق - آية 2"
    }
];

const azkarList = [
    {
        text: "سبحان الله وبحمده، سبحان الله العظيم",
        benefit: "كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن"
    },
    {
        text: "لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير",
        benefit: "من قالها في يوم مائة مرة كانت له عدل عشر رقاب"
    },
    {
        text: "اللهم أعني على ذكرك وشكرك وحسن عبادتك",
        benefit: "دعاء جامع للتوفيق في العبادة والذكر والشكر"
    },
    {
        text: "استغفر الله العظيم الذي لا إله إلا هو الحي القيوم وأتوب إليه",
        benefit: "الاستغفار يمحو الذنوب ويجلب الرزق والفرج"
    },
    {
        text: "اللهم صل وسلم على نبينا محمد",
        benefit: "من صلى على النبي صلاة واحدة صلى الله عليه بها عشراً"
    },
    {
        text: "حسبنا الله ونعم الوكيل",
        benefit: "كلمة التوكل والاعتماد على الله في جميع الأمور"
    },
    {
        text: "لا حول ولا قوة إلا بالله",
        benefit: "كنز من كنوز الجنة، تعين على تحمل المشاق"
    },
    {
        text: "اللهم بارك لنا فيما رزقتنا وقنا عذاب النار",
        benefit: "دعاء شامل للبركة في الرزق والوقاية من النار"
    }
];

const islamicQuotes = [
    "اللهم بارك لنا فيما علمتنا وعلمنا ما ينفعنا وزدنا علماً",
    "اللهم انفعني بما علمتني وعلمني ما ينفعني واكرمني بالعلم النافع",
    "اللهم أعني على ذكرك وشكرك وحسن عبادتك",
    "ربنا آتنا في الدنيا حسنة وفي الآخرة حسنة وقنا عذاب النار",
    "اللهم اهدني فيمن هديت وعافني فيمن عافيت",
    "اللهم أصلح لي ديني الذي هو عصمة أمري",
    "اللهم بارك لنا في أوقاتنا وأعمالنا وأرزاقنا"
];

let islamicContentInterval;
let timerInterval;
let timeLeft = 30;

// Function to get random content
function getRandomQuranVerse() {
    return quranVerses[Math.floor(Math.random() * quranVerses.length)];
}

function getRandomAzkar() {
    return azkarList[Math.floor(Math.random() * azkarList.length)];
}

function getRandomIslamicQuote() {
    return islamicQuotes[Math.floor(Math.random() * islamicQuotes.length)];
}

// Function to update Islamic content
function updateIslamicContent() {
    const quranVerse = getRandomQuranVerse();
    const azkar = getRandomAzkar();
    const quote = getRandomIslamicQuote();

    // Update Quran verse with animation
    const quranVerseElement = document.getElementById('quran-verse-student');
    const quranReferenceElement = document.getElementById('quran-reference-student');

    if (quranVerseElement && quranReferenceElement) {
        quranVerseElement.style.opacity = '0';
        quranReferenceElement.style.opacity = '0';

        setTimeout(() => {
            quranVerseElement.textContent = `"${quranVerse.verse}"`;
            quranReferenceElement.textContent = quranVerse.reference;
            quranVerseElement.style.opacity = '1';
            quranReferenceElement.style.opacity = '1';
        }, 300);
    }

    // Update Azkar with animation
    const azkarTextElement = document.getElementById('azkar-text-student');
    const azkarBenefitElement = document.getElementById('azkar-benefit-student');

    if (azkarTextElement && azkarBenefitElement) {
        azkarTextElement.style.opacity = '0';
        azkarBenefitElement.style.opacity = '0';

        setTimeout(() => {
            azkarTextElement.textContent = `"${azkar.text}"`;
            azkarBenefitElement.textContent = azkar.benefit;
            azkarTextElement.style.opacity = '1';
            azkarBenefitElement.style.opacity = '1';
        }, 300);
    }

    // Update Islamic quote with animation
    const quoteElement = document.getElementById('islamic-quote-student');
    if (quoteElement) {
        quoteElement.style.opacity = '0';

        setTimeout(() => {
            quoteElement.textContent = `"${quote}"`;
            quoteElement.style.opacity = '1';
        }, 300);
    }

    // Reset timer
    timeLeft = 30;
}

// Function to refresh Islamic content manually
function refreshIslamicContent() {
    const refreshIcon = document.getElementById('refresh-icon-student');
    if (refreshIcon) {
        refreshIcon.classList.add('fa-spin');

        updateIslamicContent();

        setTimeout(() => {
            refreshIcon.classList.remove('fa-spin');
        }, 1000);
    }
}

// Function to update timer display
function updateTimer() {
    const timerElement = document.getElementById('islamic-timer-student');
    if (timerElement) {
        if (timeLeft > 0) {
            timerElement.textContent = `يتجدد خلال ${timeLeft} ثانية`;
            timeLeft--;
        } else {
            updateIslamicContent();
        }
    }
}

// Initialize Islamic content functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth transitions to content elements
    const contentElements = [
        'quran-verse-student', 'quran-reference-student',
        'azkar-text-student', 'azkar-benefit-student',
        'islamic-quote-student'
    ];

    contentElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.style.transition = 'opacity 0.3s ease-in-out';
        }
    });

    // Start the automatic content update
    islamicContentInterval = setInterval(updateIslamicContent, 30000); // Every 30 seconds

    // Start the timer update
    timerInterval = setInterval(updateTimer, 1000); // Every second

    // Initial content load
    updateIslamicContent();

    // تحديث لوحة التحكم كل 15 ثانية للحصول على أحدث البيانات
    setInterval(function() {
        refreshDashboardData();
    }, 15000);

    // إضافة مستمع للتحديث الفوري عند التركيز على النافذة
    window.addEventListener('focus', function() {
        refreshDashboardData();
        console.log('تم تحديث بيانات الطالب عند التركيز على النافذة');
    });

    // تحديث فوري عند تغيير الصفحة
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            refreshDashboardData();
            console.log('تم تحديث بيانات الطالب عند عودة الرؤية للصفحة');
        }
    });
});

// دالة تحديث بيانات لوحة التحكم
function refreshDashboardData() {
    // تحديث عدد الحصص
    fetch('/dashboard/api/student/dashboard-stats/')
        .then(response => response.json())
        .then(data => {
            // تحديث عدد الحصص اليوم
            const todayLessonsElement = document.querySelector('[data-stat="today-lessons"]');
            if (todayLessonsElement && data.today_lessons_count !== undefined) {
                todayLessonsElement.textContent = data.today_lessons_count;
                // إضافة تأثير بصري للتحديث
                todayLessonsElement.style.color = '#10B981';
                setTimeout(() => {
                    todayLessonsElement.style.color = '';
                }, 1000);
            }

            // تحديث عدد الحصص القادمة
            const upcomingLessonsElement = document.querySelector('[data-stat="upcoming-lessons"]');
            if (upcomingLessonsElement && data.upcoming_lessons_count !== undefined) {
                upcomingLessonsElement.textContent = data.upcoming_lessons_count;
                // إضافة تأثير بصري للتحديث
                upcomingLessonsElement.style.color = '#10B981';
                setTimeout(() => {
                    upcomingLessonsElement.style.color = '';
                }, 1000);
            }

            // تحديث الحصص المتبقية
            const remainingLessonsElement = document.querySelector('[data-stat="remaining-lessons"]');
            if (remainingLessonsElement && data.total_subscription_lessons_remaining !== undefined) {
                remainingLessonsElement.textContent = data.total_subscription_lessons_remaining;
                // إضافة تأثير بصري للتحديث
                remainingLessonsElement.style.color = '#10B981';
                setTimeout(() => {
                    remainingLessonsElement.style.color = '';
                }, 1000);
            }

            // تحديث الاشتراكات النشطة
            const activeSubscriptionsElement = document.querySelector('[data-stat="active-subscriptions"]');
            if (activeSubscriptionsElement && data.total_active_subscriptions !== undefined) {
                activeSubscriptionsElement.textContent = data.total_active_subscriptions;
                // إضافة تأثير بصري للتحديث
                activeSubscriptionsElement.style.color = '#3B82F6';
                setTimeout(() => {
                    activeSubscriptionsElement.style.color = '';
                }, 1000);
            }

            // تحديث الحصص المكتملة
            const completedLessonsElement = document.querySelector('[data-stat="completed-lessons"]');
            if (completedLessonsElement && data.total_lessons_completed !== undefined) {
                completedLessonsElement.textContent = data.total_lessons_completed;
            }

            // إظهار رسالة تحديث
            showUpdateNotification('تم تحديث البيانات');
        })
        .catch(error => {
            console.log('تحديث بيانات لوحة التحكم:', error);
        });
}

// دالة إظهار إشعار التحديث
function showUpdateNotification(message) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-islamic-primary text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
    notification.textContent = message;
    notification.style.transform = 'translateX(100%)';

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Clean up intervals when page is unloaded
window.addEventListener('beforeunload', function() {
    if (islamicContentInterval) {
        clearInterval(islamicContentInterval);
    }
    if (timerInterval) {
        clearInterval(timerInterval);
    }
});
</script>

<style>
/* Islamic Content Card Custom Styles */
.islamic-content-card {
    background: transparent !important;
}

.islamic-content-card .bg-gradient-to-r {
    background: linear-gradient(to right, #2D5016, #10B981) !important;
}

/* Ensure no white background overrides */
.islamic-content-card * {
    background-color: transparent;
}

.islamic-content-card .bg-white {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Enhanced Islamic Gold Icons */
.islamic-content-card .bg-islamic-gold {
    background: linear-gradient(135deg, #D4AF37 0%, #F4E4BC 50%, #D4AF37 100%) !important;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.islamic-content-card .bg-islamic-gold:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Decorative Elements Animation */
.islamic-content-card .animate-pulse {
    animation: islamicPulse 2s infinite;
}

.islamic-content-card .animate-bounce {
    animation: islamicBounce 2s infinite;
}

@keyframes islamicPulse {
    0%, 100% {
        opacity: 0.4;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes islamicBounce {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-5px);
        opacity: 0.9;
    }
}
</style>

{% endblock %}