<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير تقييمات المعلم - {{ teacher.get_full_name }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2D5016;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .logo {
            color: #2D5016;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .report-title {
            font-size: 20px;
            font-weight: bold;
            color: #2D5016;
            margin-bottom: 5px;
        }
        
        .report-date {
            color: #666;
            font-size: 14px;
        }
        
        .teacher-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-right: 4px solid #2D5016;
        }
        
        .teacher-name {
            font-size: 18px;
            font-weight: bold;
            color: #2D5016;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2D5016;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .criteria-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #2D5016;
            margin-bottom: 15px;
            border-bottom: 2px solid #2D5016;
            padding-bottom: 5px;
        }
        
        .criteria-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .criteria-label {
            font-weight: 500;
        }
        
        .criteria-value {
            font-weight: bold;
            color: #2D5016;
        }
        
        .star-distribution {
            margin-bottom: 30px;
        }
        
        .star-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .star-label {
            width: 80px;
            font-size: 14px;
        }
        
        .star-bar {
            flex: 1;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            margin: 0 10px;
            position: relative;
        }
        
        .star-fill {
            height: 100%;
            background: #ffc107;
            border-radius: 10px;
        }
        
        .star-count {
            width: 30px;
            text-align: center;
            font-size: 12px;
        }
        
        .recent-ratings {
            margin-bottom: 30px;
        }
        
        .rating-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }
        
        .rating-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .student-name {
            font-weight: bold;
        }
        
        .rating-date {
            color: #666;
            font-size: 12px;
        }
        
        .rating-stars {
            color: #ffc107;
        }
        
        .rating-comment {
            font-style: italic;
            color: #666;
            margin-top: 10px;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body {
                padding: 0;
            }
            .header {
                page-break-after: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <div class="logo">أكاديمية القرآنية</div>
        <div class="report-title">تقرير تقييمات المعلم</div>
        <div class="report-date">تاريخ التقرير: {{ report_date|date:"d/m/Y H:i" }}</div>
    </div>

    <!-- معلومات المعلم -->
    <div class="teacher-info">
        <div class="teacher-name">{{ teacher.get_full_name }}</div>
        <div>البريد الإلكتروني: {{ teacher.email }}</div>
        <div>تاريخ الانضمام: {{ teacher.date_joined|date:"d/m/Y" }}</div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ stats.total_ratings }}</div>
            <div class="stat-label">إجمالي التقييمات</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ stats.overall_average }}</div>
            <div class="stat-label">المتوسط العام</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ stats.scheduled_count }}</div>
            <div class="stat-label">حصص مجدولة</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ stats.live_count }}</div>
            <div class="stat-label">حصص مباشرة</div>
        </div>
    </div>

    <!-- تفصيل المعايير -->
    <div class="criteria-section">
        <div class="section-title">تفصيل المعايير</div>
        <div class="criteria-item">
            <span class="criteria-label">التقييم العام</span>
            <span class="criteria-value">{{ stats.avg_overall|floatformat:1 }}/5</span>
        </div>
        <div class="criteria-item">
            <span class="criteria-label">جودة الحصة</span>
            <span class="criteria-value">{{ stats.avg_quality|floatformat:1 }}/5</span>
        </div>
        <div class="criteria-item">
            <span class="criteria-label">تفاعل المعلم</span>
            <span class="criteria-value">{{ stats.avg_interaction|floatformat:1 }}/5</span>
        </div>
        <div class="criteria-item">
            <span class="criteria-label">الجودة التقنية</span>
            <span class="criteria-value">{{ stats.avg_technical|floatformat:1 }}/5</span>
        </div>
    </div>

    <!-- توزيع النجوم -->
    <div class="star-distribution">
        <div class="section-title">توزيع التقييمات</div>
        {% for star, count in star_distribution.items %}
        <div class="star-item">
            <div class="star-label">{{ star }} نجوم</div>
            <div class="star-bar">
                <div class="star-fill" style="width: {% if stats.total_ratings > 0 %}{% widthratio count stats.total_ratings 100 %}{% else %}0{% endif %}%"></div>
            </div>
            <div class="star-count">{{ count }}</div>
        </div>
        {% endfor %}
    </div>

    <!-- آخر التقييمات -->
    <div class="recent-ratings">
        <div class="section-title">آخر التقييمات ({{ recent_ratings|length }})</div>
        {% for rating in recent_ratings %}
        <div class="rating-item">
            <div class="rating-header">
                <div>
                    <span class="student-name">{{ rating.student.get_full_name }}</span>
                    <span class="rating-date">- {{ rating.created_at|date:"d/m/Y H:i" }}</span>
                </div>
                <div class="rating-stars">
                    {% for i in "12345" %}
                        {% if forloop.counter <= rating.overall_rating %}★{% else %}☆{% endif %}
                    {% endfor %}
                    ({{ rating.overall_rating }}/5)
                </div>
            </div>
            {% if rating.comment %}
            <div class="rating-comment">"{{ rating.comment }}"</div>
            {% endif %}
            <div style="font-size: 12px; color: #666; margin-top: 5px;">
                نوع الحصة: {% if rating.lesson_type == 'scheduled' %}مجدولة{% else %}مباشرة{% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- تذييل التقرير -->
    <div class="footer">
        <div>تم إنشاء هذا التقرير بواسطة نظام إدارة أكاديمية القرآنية</div>
        <div>{{ report_date|date:"d/m/Y H:i" }}</div>
    </div>
</body>
</html>
