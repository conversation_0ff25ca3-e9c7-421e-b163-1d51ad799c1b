"""
URL configuration for qurania_lms project.
"""
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from users.views import CustomLoginView, dashboard_redirect, CustomPasswordResetView, CustomPasswordResetConfirmView
from qurania_lms import views

urlpatterns = [
    # Authentication URLs
    path('', dashboard_redirect, name='home'),
    path('login/', CustomLoginView.as_view(), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),

    # Password Reset URLs - يستخدم خدمة البريد الموحدة
    path('password-reset/', CustomPasswordResetView.as_view(
        template_name='auth/password_reset.html'
    ), name='password_reset'),
    path('password-reset/done/', auth_views.PasswordResetDoneView.as_view(
        template_name='auth/password_reset_done.html'
    ), name='password_reset_done'),
    path('reset/<uidb64>/<token>/', CustomPasswordResetConfirmView.as_view(
        template_name='auth/password_reset_confirm.html'
    ), name='password_reset_confirm'),
    path('reset/done/', auth_views.PasswordResetCompleteView.as_view(
        template_name='auth/password_reset_complete.html'
    ), name='password_reset_complete'),



    # Dashboard
    path('dashboard/', include('users.urls')),

    # Admin Reports (داخل لوحة التحكم)
    path('dashboard/admin/reports/', include('reports.urls')),

    # النظام الجديد الموحد للحصص - مسارات صحيحة
    path('lessons/', include('lessons.urls')),
    path('notifications/', include('notifications.urls')),
    path('support/', include('support.urls')),
    path('messages/', include('messaging.urls')),

    # Public Reports (خارج لوحة التحكم - للمستقبل)
    # path('reports/', include('reports.public_urls')),

    # API endpoints
    path('api/notifications-count/', views.notifications_count_api, name='notifications_count_api'),

    # صفحة مسح الإشعارات المنبثقة
    path('clear-notifications/', views.clear_notifications_page, name='clear_notifications'),

    # API URLs
    path('api/', include('users.api_urls')),

    # صفحات الحالة العامة (بدون تسجيل دخول)
    path('status/banned/', views.public_banned_page, name='public_banned_page'),
    path('status/pending/', views.public_pending_page, name='public_pending_page'),
    path('status/rejected/', views.public_rejected_page, name='public_rejected_page'),
    path('status/deleted/', views.public_deleted_page, name='public_deleted_page'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
