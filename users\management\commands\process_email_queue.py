"""
Django management command لمعالجة طابور البريد الإلكتروني
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from users.email_service import EmailService
from users.email_models import EmailQueue
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'معالجة طابور البريد الإلكتروني وإرسال الرسائل المعلقة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--limit',
            type=int,
            default=50,
            help='الحد الأقصى لعدد الرسائل المراد معالجتها (افتراضي: 50)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='تشغيل تجريبي بدون إرسال فعلي للرسائل'
        )

    def handle(self, *args, **options):
        limit = options['limit']
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS(f'بدء معالجة طابور البريد الإلكتروني...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('تشغيل تجريبي - لن يتم إرسال رسائل فعلية')
            )
        
        try:
            # إحصائيات قبل المعالجة
            pending_count = EmailQueue.objects.filter(
                status='pending',
                scheduled_at__lte=timezone.now()
            ).count()
            
            self.stdout.write(f'عدد الرسائل المعلقة: {pending_count}')
            
            if pending_count == 0:
                self.stdout.write(
                    self.style.SUCCESS('لا توجد رسائل معلقة للمعالجة')
                )
                return
            
            if not dry_run:
                # معالجة الطابور
                email_service = EmailService()
                result = email_service.process_queue(limit=limit)
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'تمت المعالجة بنجاح:\n'
                        f'- تم إرسال: {result["sent"]} رسالة\n'
                        f'- فشل في الإرسال: {result["failed"]} رسالة'
                    )
                )
                
                # إحصائيات بعد المعالجة
                remaining_count = EmailQueue.objects.filter(
                    status='pending',
                    scheduled_at__lte=timezone.now()
                ).count()
                
                self.stdout.write(f'الرسائل المتبقية: {remaining_count}')
                
            else:
                # عرض الرسائل التي ستتم معالجتها في التشغيل التجريبي
                pending_emails = EmailQueue.objects.filter(
                    status='pending',
                    scheduled_at__lte=timezone.now()
                ).select_related('recipient', 'template')[:limit]
                
                self.stdout.write('\nالرسائل التي ستتم معالجتها:')
                for email in pending_emails:
                    self.stdout.write(
                        f'- {email.recipient.email}: {email.subject}'
                    )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في معالجة الطابور: {str(e)}')
            )
            logger.error(f'خطأ في معالجة طابور البريد الإلكتروني: {str(e)}')
            raise
