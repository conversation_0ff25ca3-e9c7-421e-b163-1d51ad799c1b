from django.utils import timezone
from datetime import timedelta
from .invoice_models import Invoice, InvoiceItem


def create_invoice_for_subscription(subscription):
    """إنشاء فاتورة للاشتراك"""
    try:
        # التحقق من عدم وجود فاتورة مسبقاً
        if hasattr(subscription, 'invoice'):
            return subscription.invoice

        # إنشاء الفاتورة
        invoice = Invoice.objects.create(
            subscription=subscription,
            student_name=subscription.student.get_full_name(),
            student_email=subscription.student.email,
            student_phone=getattr(subscription.student, 'phone', ''),
            plan_name=subscription.plan.name,
            plan_description=subscription.plan.description,
            subtotal=subscription.plan.price,
            discount_amount=subscription.plan.price * (subscription.plan.discount_percentage / 100) if subscription.plan.discount_percentage else 0,
            tax_amount=0,  # يمكن إضافة حساب الضريبة هنا
            currency=subscription.plan.currency,
            due_date=timezone.now() + timedelta(days=30),  # استحقاق بعد 30 يوم
            status='sent',
            notes=f"فاتورة اشتراك في باقة {subscription.plan.name}"
        )

        # إنشاء عناصر الفاتورة
        InvoiceItem.objects.create(
            invoice=invoice,
            description=f"اشتراك في باقة {subscription.plan.name}",
            quantity=1,
            unit_price=subscription.plan.get_discounted_price(),
            total_price=subscription.plan.get_discounted_price()
        )

        # إضافة تفاصيل الباقة كعناصر منفصلة
        InvoiceItem.objects.create(
            invoice=invoice,
            description=f"عدد الحصص: {subscription.plan.lessons_count} حصة",
            quantity=subscription.plan.lessons_count,
            unit_price=subscription.plan.get_discounted_price() / subscription.plan.lessons_count,
            total_price=0  # عنصر وصفي فقط
        )

        InvoiceItem.objects.create(
            invoice=invoice,
            description=f"مدة كل حصة: {subscription.plan.lesson_duration} دقيقة",
            quantity=1,
            unit_price=0,
            total_price=0  # عنصر وصفي فقط
        )

        InvoiceItem.objects.create(
            invoice=invoice,
            description=f"صالحة لمدة: {subscription.plan.duration_days} يوم",
            quantity=1,
            unit_price=0,
            total_price=0  # عنصر وصفي فقط
        )

        return invoice

    except Exception as e:
        print(f"خطأ في إنشاء الفاتورة: {str(e)}")
        return None


def mark_invoice_as_paid(subscription):
    """تحديد فاتورة الاشتراك كمدفوعة"""
    try:
        if hasattr(subscription, 'invoice'):
            invoice = subscription.invoice
            invoice.mark_as_paid()
            return invoice
        return None
    except Exception as e:
        print(f"خطأ في تحديث حالة الفاتورة: {str(e)}")
        return None


def get_invoice_context(invoice):
    """الحصول على بيانات الفاتورة للعرض"""
    from users.models import AcademySettings

    # الحصول على إعدادات الأكاديمية
    try:
        academy_settings = AcademySettings.objects.first()
        if academy_settings:
            company_info = {
                'name': academy_settings.academy_name or 'أكاديمية القرآنيا',
                'address': academy_settings.academy_address or 'المملكة العربية السعودية',
                'phone': academy_settings.academy_phone or '+966 XX XXX XXXX',
                'email': academy_settings.academy_email or '<EMAIL>',
                'website': academy_settings.academy_website or 'www.qurania.com',
                'logo': getattr(academy_settings, 'academy_logo', None).url if getattr(academy_settings, 'academy_logo', None) else None,
                'description': academy_settings.academy_description or '',
            }
        else:
            # قيم افتراضية في حالة عدم وجود إعدادات
            company_info = {
                'name': 'أكاديمية القرآنيا',
                'address': 'المملكة العربية السعودية',
                'phone': '+966 XX XXX XXXX',
                'email': '<EMAIL>',
                'website': 'www.qurania.com',
                'logo': None,
                'description': '',
            }
    except Exception as e:
        # في حالة حدوث خطأ، استخدام القيم الافتراضية
        company_info = {
            'name': 'أكاديمية القرآنيا',
            'address': 'المملكة العربية السعودية',
            'phone': '+966 XX XXX XXXX',
            'email': '<EMAIL>',
            'website': 'www.qurania.com',
            'logo': None,
            'description': '',
        }

    return {
        'invoice': invoice,
        'items': invoice.items.all(),
        'company_info': company_info,
    }
