{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الفواتير{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-4xl font-bold text-islamic-primary mb-2 flex items-center">
                    <i class="fas fa-file-invoice text-islamic-gold ml-3"></i>
                    إدارة الفواتير
                </h1>
                <p class="text-gray-600">إدارة ومراقبة جميع فواتير الاشتراكات</p>
            </div>
            <a href="{% url 'admin_subscriptions' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للاشتراكات
            </a>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <!-- Total Invoices -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-blue-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الفواتير</p>
                        <p class="text-3xl font-bold text-blue-600">{{ total_invoices }}</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-invoice text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Paid Invoices -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-green-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">فواتير مدفوعة</p>
                        <p class="text-3xl font-bold text-green-600">{{ paid_invoices }}</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Pending Invoices -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-yellow-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">فواتير معلقة</p>
                        <p class="text-3xl font-bold text-yellow-600">{{ pending_invoices }}</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Overdue Invoices -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-red-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">فواتير متأخرة</p>
                        <p class="text-3xl font-bold text-red-600">{{ overdue_invoices }}</p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-purple-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                        <p class="text-3xl font-bold text-purple-600">{{ total_revenue|floatformat:0 }}</p>
                        <p class="text-sm text-gray-500">بعملات متنوعة</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-filter ml-2"></i>
                فلترة الفواتير
            </h3>
            <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select name="status" id="status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if value == status_filter %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <input type="text" name="search" id="search" value="{{ search_query }}" 
                           placeholder="رقم الفاتورة أو اسم الطالب..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-2 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-search ml-2"></i>
                        بحث
                    </button>
                    <a href="{% url 'admin_invoices' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg mr-2 transition-colors">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>

        <!-- Invoices Table -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-list ml-2"></i>
                    قائمة الفواتير
                    <span class="text-sm font-normal text-gray-500">({{ invoices.count }} فاتورة)</span>
                </h3>
            </div>
            
            {% if invoices %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الفاتورة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباقة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإصدار</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الاستحقاق</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for invoice in invoices %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ invoice.invoice_number }}</div>
                                <div class="text-sm text-gray-500">{{ invoice.created_at|date:"Y-m-d" }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-islamic-primary rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ invoice.student_name }}</div>
                                        <div class="text-sm text-gray-500">{{ invoice.student_email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ invoice.plan_name }}</div>
                                <div class="text-sm text-gray-500">{{ invoice.subscription.plan.get_plan_type_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div class="font-bold">{{ invoice.total_amount }} {{ invoice.get_currency_symbol }}</div>
                                {% if invoice.discount_amount > 0 %}
                                <div class="text-xs text-green-600">خصم: {{ invoice.discount_amount }} {{ invoice.get_currency_symbol }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ invoice.issue_date|date:"Y-m-d" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ invoice.due_date|date:"Y-m-d" }}
                                {% if invoice.is_overdue %}
                                <div class="text-xs text-red-600">متأخرة</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if invoice.status == 'paid' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        مدفوعة
                                    </span>
                                {% elif invoice.status == 'sent' %}
                                    {% if invoice.is_overdue %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            <i class="fas fa-exclamation-triangle ml-1"></i>
                                            متأخرة
                                        </span>
                                    {% else %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-clock ml-1"></i>
                                            مرسلة
                                        </span>
                                    {% endif %}
                                {% elif invoice.status == 'draft' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        <i class="fas fa-edit ml-1"></i>
                                        مسودة
                                    </span>
                                {% elif invoice.status == 'cancelled' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        <i class="fas fa-ban ml-1"></i>
                                        ملغية
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="{% url 'admin_invoice_detail' invoice.id %}" 
                                       class="text-blue-600 hover:text-blue-900 transition-colors" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'admin_invoice_print' invoice.id %}" 
                                       target="_blank"
                                       class="text-green-600 hover:text-green-900 transition-colors" title="طباعة">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <a href="{% url 'admin_invoice_pdf' invoice.id %}" 
                                       class="text-red-600 hover:text-red-900 transition-colors" title="تحميل PDF">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-file-invoice text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد فواتير</h3>
                {% if status_filter or search_query %}
                <p class="text-gray-500 mb-6">لم يتم العثور على فواتير تطابق معايير البحث</p>
                <a href="{% url 'admin_invoices' %}" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors">
                    <i class="fas fa-times ml-2"></i>
                    إزالة الفلاتر
                </a>
                {% else %}
                <p class="text-gray-500 mb-6">لم يتم إنشاء أي فواتير بعد</p>
                <a href="{% url 'admin_subscriptions' %}" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors">
                    <i class="fas fa-credit-card ml-2"></i>
                    إدارة الاشتراكات
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
