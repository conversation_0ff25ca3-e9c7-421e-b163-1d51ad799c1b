from django.urls import path
from . import views

urlpatterns = [
    # قائمة المحادثات
    path('', views.conversations_list, name='conversations_list'),

    # تفاصيل المحادثة
    path('conversation/<int:conversation_id>/', views.conversation_detail, name='conversation_detail'),

    # بدء محادثة جديدة
    path('start/', views.start_conversation, name='start_conversation'),

    # تمييز المحادثة كمقروءة (AJAX)
    path('conversation/<int:conversation_id>/mark-read/', views.mark_conversation_read, name='mark_conversation_read'),

    # API للتحديثات
    path('api/conversations-update/', views.conversations_update_api, name='conversations_update_api'),
    path('api/conversation/<int:conversation_id>/messages/', views.conversation_messages_api, name='conversation_messages_api'),
    path('api/conversation/<int:conversation_id>/send/', views.send_message_api, name='send_message_api'),

    # إدارة الرسائل
    path('message/<int:message_id>/delete/', views.delete_message, name='delete_message'),
    path('message/<int:message_id>/restore/', views.restore_message, name='restore_message'),
]
