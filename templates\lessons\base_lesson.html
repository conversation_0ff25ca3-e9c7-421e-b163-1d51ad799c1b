{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link href="{% static 'css/lessons.css' %}" rel="stylesheet">
<style>
    /* تحسينات خاصة بالحصص */
    .lesson-card {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
    }
    
    .lesson-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        border-color: #3b82f6;
    }
    
    .lesson-type-trial {
        background: linear-gradient(135deg, #fef3c7, #fbbf24);
    }
    
    .lesson-type-subscription {
        background: linear-gradient(135deg, #dbeafe, #3b82f6);
    }
    
    .lesson-type-makeup {
        background: linear-gradient(135deg, #fce7f3, #ec4899);
    }
    
    .status-scheduled {
        background-color: #dbeafe;
        color: #1e40af;
    }
    
    .status-live {
        background-color: #dcfce7;
        color: #166534;
        animation: pulse 2s infinite;
    }
    
    .status-completed {
        background-color: #f0fdf4;
        color: #15803d;
    }
    
    .status-cancelled {
        background-color: #fef2f2;
        color: #dc2626;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        transform: translateY(-1px);
    }
    
    .btn-secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: linear-gradient(135deg, #4b5563, #374151);
        transform: translateY(-1px);
    }
    
    .form-control {
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        outline: none;
    }
    
    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .stats-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #f3f4f6;
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
    
    .islamic-primary {
        color: #059669;
    }
    
    .islamic-gold {
        color: #d97706;
    }
    
    .bg-islamic-primary {
        background-color: #059669;
    }
    
    .bg-islamic-light {
        background-color: #10b981;
    }
    
    .text-islamic-primary {
        color: #059669;
    }
    
    .border-islamic-primary {
        border-color: #059669;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
        {% block lesson_header %}
        <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
                <h1 class="header-title text-2xl md:text-3xl font-bold text-islamic-primary mb-2">
                    <i class="{% block header_icon %}fas fa-calendar{% endblock %} text-islamic-gold ml-2 md:ml-3"></i>
                    {% block page_title %}الحصص{% endblock %}
                </h1>
                <p class="text-sm md:text-base text-gray-600">{% block page_description %}إدارة الحصص{% endblock %}</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-2 sm:gap-4">
                {% block header_actions %}{% endblock %}
            </div>
        </div>
        {% endblock %}
    </div>

    <!-- Stats Section -->
    {% block lesson_stats %}{% endblock %}

    <!-- Filters Section -->
    {% block lesson_filters %}{% endblock %}

    <!-- Main Content -->
    {% block lesson_content %}{% endblock %}

    <!-- Modals -->
    {% block lesson_modals %}{% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/lessons-common.js' %}"></script>
{% block lesson_js %}{% endblock %}
{% endblock %}
