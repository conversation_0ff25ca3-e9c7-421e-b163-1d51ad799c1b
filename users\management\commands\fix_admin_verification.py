"""
أمر Django لإصلاح حالة التحقق للمديرين
يضمن أن جميع المديرين معتمدين ونشطين
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class Command(BaseCommand):
    help = 'إصلاح حالة التحقق للمديرين'

    def handle(self, *args, **options):
        # البحث عن جميع المديرين
        admin_users = User.objects.filter(
            user_type='admin'
        ).exclude(
            verification_status='approved'
        )
        
        if not admin_users.exists():
            self.stdout.write(
                self.style.SUCCESS('✅ جميع المديرين معتمدين بالفعل')
            )
            return
        
        # تحديث حالة المديرين
        updated_count = 0
        for admin in admin_users:
            admin.verification_status = 'approved'
            admin.verified_at = timezone.now()
            admin.is_active = True
            admin.save()
            
            updated_count += 1
            self.stdout.write(
                self.style.SUCCESS(f'✅ تم تحديث المدير: {admin.username}')
            )
        
        self.stdout.write(
            self.style.SUCCESS(f'🎉 تم تحديث {updated_count} مدير بنجاح')
        )
