from django.contrib import admin
from .models import SubscriptionPlan, StudentSubscription, SubscriptionPayment, BankTransferProof


@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = ['name', 'plan_type', 'duration_type', 'price', 'lessons_count', 'is_active', 'is_featured']
    list_filter = ['plan_type', 'duration_type', 'is_active', 'is_featured']
    search_fields = ['name', 'description']
    ordering = ['plan_type', 'price']


@admin.register(StudentSubscription)
class StudentSubscriptionAdmin(admin.ModelAdmin):
    list_display = ['student', 'plan', 'status', 'start_date', 'end_date', 'remaining_lessons']
    list_filter = ['status', 'plan__plan_type', 'start_date']
    search_fields = ['student__first_name', 'student__last_name', 'plan__name']
    ordering = ['-created_at']


@admin.register(SubscriptionPayment)
class SubscriptionPaymentAdmin(admin.ModelAdmin):
    list_display = ['subscription', 'amount', 'payment_method', 'status', 'payment_date']
    list_filter = ['payment_method', 'status', 'payment_date']
    search_fields = ['subscription__student__first_name', 'subscription__student__last_name', 'transaction_id']
    ordering = ['-payment_date']


@admin.register(BankTransferProof)
class BankTransferProofAdmin(admin.ModelAdmin):
    list_display = ['payment', 'sender_name', 'transfer_amount', 'bank_name', 'is_verified', 'uploaded_at']
    list_filter = ['is_verified', 'bank_name', 'uploaded_at']
    search_fields = ['sender_name', 'payment__subscription__student__first_name', 'payment__subscription__student__last_name']
    ordering = ['-uploaded_at']
