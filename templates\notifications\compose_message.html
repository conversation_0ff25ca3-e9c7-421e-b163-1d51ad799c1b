{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء رسالة جديدة{% endblock %}

{% block extra_css %}
<style>
    .compose-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    .form-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .user-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .user-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .user-card.selected {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="compose-form">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">إنشاء رسالة جديدة</h1>
            <p class="text-white text-opacity-90 text-lg">تواصل مع المستخدمين الآخرين</p>
        </div>

        <!-- Form Card -->
        <div class="max-w-4xl mx-auto">
            <div class="form-card rounded-2xl shadow-2xl p-8">
                <form method="POST" class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Recipient Selection -->
                    <div>
                        <label class="block text-lg font-bold text-gray-800 mb-4">
                            <i class="fas fa-user text-blue-600 ml-2"></i>
                            المستقبل
                        </label>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {% for user in users %}
                            <div class="user-card bg-white border-2 border-gray-200 rounded-xl p-4 text-center" 
                                 onclick="selectUser('{{ user.id }}', this)">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    {% if user.user_type == 'admin' %}
                                        <i class="fas fa-crown text-yellow-600"></i>
                                    {% elif user.user_type == 'teacher' %}
                                        <i class="fas fa-chalkboard-teacher text-green-600"></i>
                                    {% else %}
                                        <i class="fas fa-user-graduate text-blue-600"></i>
                                    {% endif %}
                                </div>
                                <h4 class="font-bold text-gray-800 mb-1">{{ user.get_full_name }}</h4>
                                <p class="text-gray-600 text-sm">
                                    {% if user.user_type == 'admin' %}
                                        مدير النظام
                                    {% elif user.user_type == 'teacher' %}
                                        معلم
                                    {% else %}
                                        طالب
                                    {% endif %}
                                </p>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <input type="hidden" name="recipient" id="recipient" required>
                    </div>

                    <!-- Message Type -->
                    <div>
                        <label for="message_type" class="block text-lg font-bold text-gray-800 mb-2">
                            <i class="fas fa-tag text-purple-600 ml-2"></i>
                            نوع الرسالة
                        </label>
                        <select id="message_type" name="message_type" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-purple-500 focus:outline-none transition-colors">
                            {% for type_key, type_name in message_types %}
                                <option value="{{ type_key }}">{{ type_name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Subject -->
                    <div>
                        <label for="subject" class="block text-lg font-bold text-gray-800 mb-2">
                            <i class="fas fa-heading text-green-600 ml-2"></i>
                            الموضوع *
                        </label>
                        <input type="text" 
                               id="subject" 
                               name="subject" 
                               required
                               placeholder="اكتب موضوع الرسالة..."
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-green-500 focus:outline-none transition-colors">
                    </div>

                    <!-- Content -->
                    <div>
                        <label for="content" class="block text-lg font-bold text-gray-800 mb-2">
                            <i class="fas fa-align-left text-orange-600 ml-2"></i>
                            محتوى الرسالة *
                        </label>
                        <textarea id="content" 
                                  name="content" 
                                  required
                                  rows="8"
                                  placeholder="اكتب محتوى الرسالة هنا..."
                                  class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none transition-colors resize-none"></textarea>
                        <div class="flex justify-between items-center mt-2">
                            <p class="text-sm text-gray-500">
                                <i class="fas fa-info-circle ml-1"></i>
                                سيتم إرسال إشعار للمستقبل
                            </p>
                            <span id="char-count" class="text-sm text-gray-500">0 حرف</span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                        <a href="{% url 'notifications:message_list' %}" 
                           class="bg-gray-100 text-gray-600 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-arrow-right ml-2"></i>
                            إلغاء
                        </a>
                        
                        <button type="submit"
                                class="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                                id="send-btn" disabled>
                            <i class="fas fa-paper-plane ml-2"></i>
                            إرسال الرسالة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let selectedUserId = null;

function selectUser(userId, element) {
    // Remove selection from all cards
    document.querySelectorAll('.user-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Add selection to clicked card
    element.classList.add('selected');
    
    // Set the recipient value
    selectedUserId = userId;
    document.getElementById('recipient').value = userId;
    
    // Enable send button if all required fields are filled
    checkFormValidity();
}

function checkFormValidity() {
    const recipient = document.getElementById('recipient').value;
    const subject = document.getElementById('subject').value;
    const content = document.getElementById('content').value;
    const sendBtn = document.getElementById('send-btn');
    
    if (recipient && subject.trim() && content.trim()) {
        sendBtn.disabled = false;
    } else {
        sendBtn.disabled = true;
    }
}

// Character counter
const contentTextarea = document.getElementById('content');
const charCount = document.getElementById('char-count');

contentTextarea.addEventListener('input', function() {
    const length = this.value.length;
    charCount.textContent = `${length} حرف`;
    
    if (length > 500) {
        charCount.classList.add('text-red-500');
    } else if (length > 300) {
        charCount.classList.add('text-yellow-500');
        charCount.classList.remove('text-red-500');
    } else {
        charCount.classList.remove('text-red-500', 'text-yellow-500');
        charCount.classList.add('text-gray-500');
    }
    
    checkFormValidity();
});

// Check form validity on subject change
document.getElementById('subject').addEventListener('input', checkFormValidity);

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    if (!selectedUserId) {
        e.preventDefault();
        alert('يرجى اختيار مستقبل للرسالة');
        return false;
    }
    
    const subject = document.getElementById('subject').value.trim();
    const content = document.getElementById('content').value.trim();
    
    if (!subject || !content) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
});
</script>
{% endblock %}
