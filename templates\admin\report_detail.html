{% extends 'base.html' %}

{% block title %}تفاصيل التقرير - {{ custom_report.title }} - نظام قرآنيا التعليمي{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-gradient-to-r from-islamic-primary to-islamic-light shadow-lg border-b border-islamic-gold mb-6">
        <div class="px-6 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <a href="{% url 'admin_reports' %}" class="text-white hover:text-islamic-gold transition-colors ml-3">
                            <i class="fas fa-arrow-right text-xl"></i>
                        </a>
                        <h1 class="text-3xl font-bold text-white">
                            تفاصيل التقرير
                        </h1>
                    </div>
                    <p class="text-islamic-light-blue">{{ custom_report.title }}</p>
                </div>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <button class="bg-islamic-gold text-islamic-dark px-4 py-2 rounded-lg hover:bg-yellow-400 transition-colors font-semibold">
                        <i class="fas fa-edit ml-1"></i>
                        تعديل التقرير
                    </button>
                    <button class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition-colors font-semibold">
                        <i class="fas fa-copy ml-1"></i>
                        نسخ التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="px-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Report Information -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-info-circle text-islamic-primary ml-2"></i>
                        معلومات التقرير
                    </h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-gray-600">العنوان</label>
                            <p class="text-gray-900 font-medium">{{ custom_report.title }}</p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">الوصف</label>
                            <p class="text-gray-700">{{ custom_report.description|default:"لا يوجد وصف" }}</p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">الفئة</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-islamic-primary text-white">
                                {{ custom_report.get_category_display }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">منشئ التقرير</label>
                            <p class="text-gray-900">{{ custom_report.created_by.get_full_name }}</p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">تاريخ الإنشاء</label>
                            <p class="text-gray-700">{{ custom_report.created_at|date:"Y-m-d H:i" }}</p>
                        </div>
                        
                        {% if custom_report.date_from or custom_report.date_to %}
                        <div>
                            <label class="text-sm font-medium text-gray-600">الفترة الزمنية</label>
                            <p class="text-gray-700">
                                {% if custom_report.date_from %}من {{ custom_report.date_from }}{% endif %}
                                {% if custom_report.date_to %}إلى {{ custom_report.date_to }}{% endif %}
                            </p>
                        </div>
                        {% endif %}
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">الفلاتر</label>
                            <p class="text-gray-700">{{ custom_report.get_filters_display }}</p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-600">الحالة</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if custom_report.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if custom_report.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </div>
                        
                        {% if custom_report.is_scheduled %}
                        <div>
                            <label class="text-sm font-medium text-gray-600">الجدولة</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ custom_report.get_schedule_frequency_display }}
                            </span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-bolt text-islamic-gold ml-2"></i>
                        إجراءات سريعة
                    </h3>
                    <div class="space-y-3">
                        <button class="w-full bg-islamic-primary text-white py-2 px-4 rounded-lg hover:bg-islamic-light transition-colors">
                            <i class="fas fa-file-pdf ml-2"></i>
                            توليد PDF
                        </button>
                        <button class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-file-excel ml-2"></i>
                            توليد Excel
                        </button>
                        <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-file-csv ml-2"></i>
                            توليد CSV
                        </button>
                        <button class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-calendar-plus ml-2"></i>
                            جدولة التقرير
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Generated Reports -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-download text-islamic-primary ml-2"></i>
                            التقارير المولدة
                        </h3>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <span class="text-sm text-gray-600">
                                إجمالي: {{ generated_reports.count }} تقرير
                            </span>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        {% if generated_reports %}
                        <div class="space-y-4">
                            {% for report in generated_reports %}
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                {% if report.file_format == 'pdf' %}bg-red-100 text-red-800
                                                {% elif report.file_format == 'excel' %}bg-green-100 text-green-800
                                                {% else %}bg-blue-100 text-blue-800{% endif %}">
                                                {{ report.get_file_format_display }}
                                            </span>
                                            <span class="mr-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                {% if report.is_ready %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                                {% if report.is_ready %}جاهز{% else %}قيد المعالجة{% endif %}
                                            </span>
                                        </div>
                                        
                                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                                            <div>
                                                <span class="font-medium">تاريخ التوليد:</span>
                                                <p>{{ report.generated_at|date:"Y-m-d H:i" }}</p>
                                            </div>
                                            <div>
                                                <span class="font-medium">حجم الملف:</span>
                                                <p>{{ report.get_file_size_display }}</p>
                                            </div>
                                            <div>
                                                <span class="font-medium">عدد السجلات:</span>
                                                <p>{{ report.total_records }}</p>
                                            </div>
                                            <div>
                                                <span class="font-medium">مرات التحميل:</span>
                                                <p>{{ report.download_count }}</p>
                                            </div>
                                        </div>
                                        
                                        {% if report.last_downloaded %}
                                        <p class="text-xs text-gray-500 mt-2">
                                            آخر تحميل: {{ report.last_downloaded|date:"Y-m-d H:i" }}
                                        </p>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        {% if report.is_ready %}
                                        <button class="download-report-btn text-green-600 hover:text-green-800 p-2" 
                                                data-report-id="{{ report.id }}" data-format="{{ report.file_format }}" 
                                                title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        {% endif %}
                                        <button class="text-red-600 hover:text-red-800 p-2" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-8">
                            <i class="fas fa-file-alt text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">لم يتم توليد أي تقارير بعد</p>
                            <p class="text-sm text-gray-400 mt-2">استخدم الإجراءات السريعة لتوليد تقرير جديد</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Download report buttons
    document.querySelectorAll('.download-report-btn').forEach(button => {
        button.addEventListener('click', function() {
            const reportId = this.getAttribute('data-report-id');
            const format = this.getAttribute('data-format');
            
            // Show loading
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.disabled = true;
            
            // Download report
            fetch(`{% url "admin_reports_download" 0 %}`.replace('0', reportId) + `?format=${format}`)
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('فشل في تحميل التقرير');
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `report_${reportId}.${format}`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                
                // Reset button
                this.innerHTML = '<i class="fas fa-download"></i>';
                this.disabled = false;
            })
            .catch(error => {
                alert('حدث خطأ في التحميل: ' + error.message);
                // Reset button
                this.innerHTML = '<i class="fas fa-download"></i>';
                this.disabled = false;
            });
        });
    });
});
</script>

{% csrf_token %}
{% endblock %}
