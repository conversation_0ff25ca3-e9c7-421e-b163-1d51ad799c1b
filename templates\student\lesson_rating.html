{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تقييم الحصة - {{ live_lesson.title }}{% endblock %}

{% block extra_css %}
<style>
.rating-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.rating-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

.rating-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.rating-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-right: 4px solid #007bff;
}

.star-rating {
    display: flex;
    gap: 0.5rem;
}

.star {
    font-size: 1.5rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s;
}

.star.active,
.star:hover {
    color: #ffc107;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.btn-submit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50px;
    padding: 1rem 3rem;
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
    transition: transform 0.3s;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.lesson-info {
    background: #e3f2fd;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-right: 4px solid #2196f3;
}

.optional-note {
    background: #fff3cd;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-right: 4px solid #ffc107;
}
</style>
{% endblock %}

{% block content %}
<div class="rating-form">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="rating-card">
                    <!-- Header -->
                    <div class="rating-header">
                        <h2><i class="fas fa-star ml-2"></i>تقييم الحصة</h2>
                        <p class="mb-0">{{ live_lesson.title }}</p>
                        <small>مع المعلم {{ live_lesson.teacher.get_full_name }}</small>
                    </div>

                    <div class="p-4">
                        <!-- معلومات الحصة -->
                        <div class="lesson-info">
                            <h5><i class="fas fa-info-circle text-primary ml-2"></i>معلومات الحصة</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>المعلم:</strong> {{ live_lesson.teacher.get_full_name }}</p>
                                    <p><strong>المدة:</strong> {{ live_lesson.duration_minutes }} دقيقة</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>التاريخ:</strong> {{ live_lesson.scheduled_date|date:"Y/m/d H:i" }}</p>
                                    <p><strong>الحالة:</strong> 
                                        <span class="badge badge-success">مكتملة</span>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظة اختيارية -->
                        <div class="optional-note">
                            <h6><i class="fas fa-lightbulb text-warning ml-2"></i>ملاحظة مهمة</h6>
                            <p class="mb-0">
                                <strong>التقييم اختياري:</strong> الحصة محتسبة لك حتى لو لم تقم بالتقييم، 
                                لكن تقييمك يساعد في تحسين جودة التدريس.
                            </p>
                        </div>

                        <!-- نموذج التقييم -->
                        <form id="student-rating-form" method="post">
                            {% csrf_token %}
                            
                            <!-- تقييم المعلم والحصة -->
                            <div class="form-group">
                                <h5><i class="fas fa-user-tie text-primary ml-2"></i>تقييم المعلم والحصة</h5>
                                
                                <div class="rating-group">
                                    <label>التقييم العام للحصة:</label>
                                    <div class="star-rating" data-field="overall_rating">
                                        <span class="star" data-value="1">★</span>
                                        <span class="star" data-value="2">★</span>
                                        <span class="star" data-value="3">★</span>
                                        <span class="star" data-value="4">★</span>
                                        <span class="star" data-value="5">★</span>
                                    </div>
                                </div>

                                <div class="rating-group">
                                    <label>جودة المحتوى والشرح:</label>
                                    <div class="star-rating" data-field="lesson_quality">
                                        <span class="star" data-value="1">★</span>
                                        <span class="star" data-value="2">★</span>
                                        <span class="star" data-value="3">★</span>
                                        <span class="star" data-value="4">★</span>
                                        <span class="star" data-value="5">★</span>
                                    </div>
                                </div>

                                <div class="rating-group">
                                    <label>تفاعل المعلم واستجابته:</label>
                                    <div class="star-rating" data-field="teacher_interaction">
                                        <span class="star" data-value="1">★</span>
                                        <span class="star" data-value="2">★</span>
                                        <span class="star" data-value="3">★</span>
                                        <span class="star" data-value="4">★</span>
                                        <span class="star" data-value="5">★</span>
                                    </div>
                                </div>

                                <div class="rating-group">
                                    <label>الجودة التقنية (صوت وصورة):</label>
                                    <div class="star-rating" data-field="technical_quality">
                                        <span class="star" data-value="1">★</span>
                                        <span class="star" data-value="2">★</span>
                                        <span class="star" data-value="3">★</span>
                                        <span class="star" data-value="4">★</span>
                                        <span class="star" data-value="5">★</span>
                                    </div>
                                </div>
                            </div>

                            <!-- التعليق الاختياري -->
                            <div class="form-group">
                                <label for="comment"><i class="fas fa-comment text-info ml-2"></i>تعليق إضافي (اختياري)</label>
                                <textarea class="form-control" id="comment" name="comment" rows="4" 
                                    placeholder="شاركنا رأيك حول الحصة، ما أعجبك، وما يمكن تحسينه..."></textarea>
                            </div>

                            <!-- أزرار الإرسال -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-submit">
                                    <i class="fas fa-paper-plane ml-2"></i>
                                    إرسال التقييم
                                </button>
                                
                                <div class="mt-3">
                                    <a href="/dashboard/" class="btn btn-secondary">
                                        <i class="fas fa-arrow-right ml-2"></i>
                                        تخطي التقييم والعودة للوحة التحكم
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden inputs for ratings -->
<input type="hidden" id="overall_rating" name="overall_rating" value="">
<input type="hidden" id="lesson_quality" name="lesson_quality" value="">
<input type="hidden" id="teacher_interaction" name="teacher_interaction" value="">
<input type="hidden" id="technical_quality" name="technical_quality" value="">
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // نظام التقييم بالنجوم
    const starRatings = document.querySelectorAll('.star-rating');
    
    starRatings.forEach(rating => {
        const stars = rating.querySelectorAll('.star');
        const fieldName = rating.getAttribute('data-field');
        
        stars.forEach((star, index) => {
            star.addEventListener('click', () => {
                const value = parseInt(star.getAttribute('data-value'));
                
                // تحديث النجوم
                stars.forEach((s, i) => {
                    if (i < value) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
                
                // تحديث الحقل المخفي
                const hiddenInput = document.getElementById(fieldName);
                if (hiddenInput) {
                    hiddenInput.value = value;
                }
            });
            
            star.addEventListener('mouseover', () => {
                const value = parseInt(star.getAttribute('data-value'));
                stars.forEach((s, i) => {
                    if (i < value) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
        
        rating.addEventListener('mouseleave', () => {
            const hiddenInput = document.getElementById(fieldName);
            const currentValue = hiddenInput ? parseInt(hiddenInput.value) : 0;
            
            stars.forEach((s, i) => {
                if (i < currentValue) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
    });
    
    // إرسال النموذج
    const form = document.getElementById('student-rating-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // التحقق من وجود تقييم واحد على الأقل
        const requiredRatings = ['overall_rating', 'lesson_quality', 'teacher_interaction', 'technical_quality'];
        let hasAnyRating = false;
        
        requiredRatings.forEach(field => {
            const input = document.getElementById(field);
            if (input.value) {
                hasAnyRating = true;
            }
        });
        
        if (!hasAnyRating) {
            alert('يرجى تقييم نقطة واحدة على الأقل');
            return;
        }
        
        // إضافة التقييمات للنموذج
        requiredRatings.forEach(field => {
            const input = document.getElementById(field);
            if (input.value) {
                const formInput = document.createElement('input');
                formInput.type = 'hidden';
                formInput.name = field;
                formInput.value = input.value;
                form.appendChild(formInput);
            }
        });
        
        // إرسال النموذج
        form.submit();
    });
});
</script>
{% endblock %}
