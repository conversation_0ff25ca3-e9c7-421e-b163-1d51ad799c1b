{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء باقة جديدة{% endblock %}

{% block extra_css %}
<style>
    .feature-input {
        transition: all 0.3s ease;
    }
    .feature-input:focus {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-4xl font-bold text-islamic-primary mb-2 flex items-center">
                    <i class="fas fa-plus-circle text-islamic-gold ml-3"></i>
                    إنشاء باقة جديدة
                </h1>
                <p class="text-gray-600">إنشاء باقة اشتراك جديدة للطلاب</p>
            </div>
            <a href="{% url 'admin_plans_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للقائمة
            </a>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <form method="post" class="space-y-8">
                {% csrf_token %}

                <!-- Basic Information -->
                <div class="border-b border-gray-200 pb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">المعلومات الأساسية</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم الباقة *</label>
                            <input type="text" id="name" name="name" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="مثال: الباقة الأساسية">
                        </div>

                        <div>
                            <label for="plan_type" class="block text-sm font-medium text-gray-700 mb-2">نوع الباقة *</label>
                            <select id="plan_type" name="plan_type" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="">اختر نوع الباقة</option>
                                {% for value, label in plan_types %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">وصف الباقة *</label>
                            <textarea id="description" name="description" rows="3" required
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                      placeholder="وصف مفصل للباقة ومميزاتها"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Duration and Pricing -->
                <div class="border-b border-gray-200 pb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">المدة والتسعير</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="duration_type" class="block text-sm font-medium text-gray-700 mb-2">نوع المدة *</label>
                            <select id="duration_type" name="duration_type" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="">اختر نوع المدة</option>
                                {% for value, label in duration_types %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div>
                            <label for="duration_days" class="block text-sm font-medium text-gray-700 mb-2">المدة بالأيام *</label>
                            <input type="number" id="duration_days" name="duration_days" required min="1"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="30">
                        </div>

                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">السعر *</label>
                            <input type="number" id="price" name="price" required min="0" step="0.01"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="150.00">
                        </div>

                        <div>
                            <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">العملة *</label>
                            <select id="currency" name="currency" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                {% for value, label in currency_choices %}
                                <option value="{{ value }}" {% if value == 'SAR' %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div>
                            <label for="discount_percentage" class="block text-sm font-medium text-gray-700 mb-2">نسبة الخصم (%)</label>
                            <input type="number" id="discount_percentage" name="discount_percentage" min="0" max="100" step="0.01"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="0">
                        </div>
                    </div>
                </div>

                <!-- Lessons Configuration -->
                <div class="border-b border-gray-200 pb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">إعدادات الحصص</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="lessons_count" class="block text-sm font-medium text-gray-700 mb-2">عدد الحصص *</label>
                            <input type="number" id="lessons_count" name="lessons_count" required min="1"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="8">
                        </div>

                        <div>
                            <label for="lesson_duration" class="block text-sm font-medium text-gray-700 mb-2">مدة الحصة (دقيقة) *</label>
                            <select id="lesson_duration" name="lesson_duration" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="">اختر مدة الحصة</option>
                                {% for value, label in lesson_durations %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="border-b border-gray-200 pb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">مميزات الباقة</h3>
                    <div id="features-container">
                        <div class="feature-item flex items-center space-x-2 space-x-reverse mb-3">
                            <input type="text" name="features[]"
                                   class="feature-input flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                                   placeholder="أدخل ميزة من مميزات الباقة">
                            <button type="button" onclick="removeFeature(this)"
                                    class="bg-red-500 hover:bg-red-600 text-white px-3 py-3 rounded-lg transition-colors">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <button type="button" onclick="addFeature()"
                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة ميزة
                    </button>
                </div>

                <!-- Settings -->
                <div class="pb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">الإعدادات</h3>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" checked
                                   class="w-4 h-4 text-islamic-primary bg-gray-100 border-gray-300 rounded focus:ring-islamic-primary focus:ring-2">
                            <label for="is_active" class="mr-2 text-sm font-medium text-gray-700">باقة نشطة</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="is_featured" name="is_featured"
                                   class="w-4 h-4 text-islamic-primary bg-gray-100 border-gray-300 rounded focus:ring-islamic-primary focus:ring-2">
                            <label for="is_featured" class="mr-2 text-sm font-medium text-gray-700">باقة مميزة</label>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4 space-x-reverse">
                    <a href="{% url 'admin_plans_list' %}"
                       class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors">
                        إلغاء
                    </a>
                    <button type="submit"
                            class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        حفظ الباقة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function addFeature() {
    const container = document.getElementById('features-container');
    const featureItem = document.createElement('div');
    featureItem.className = 'feature-item flex items-center space-x-2 space-x-reverse mb-3';
    featureItem.innerHTML = `
        <input type="text" name="features[]"
               class="feature-input flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
               placeholder="أدخل ميزة من مميزات الباقة">
        <button type="button" onclick="removeFeature(this)"
                class="bg-red-500 hover:bg-red-600 text-white px-3 py-3 rounded-lg transition-colors">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(featureItem);
}

function removeFeature(button) {
    const container = document.getElementById('features-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

// Auto-calculate duration days based on duration type
document.getElementById('duration_type').addEventListener('change', function() {
    const durationDaysInput = document.getElementById('duration_days');
    const value = this.value;

    switch(value) {
        case 'monthly':
            durationDaysInput.value = 30;
            break;
        case 'quarterly':
            durationDaysInput.value = 90;
            break;
        case 'semi_annual':
            durationDaysInput.value = 180;
            break;
        case 'annual':
            durationDaysInput.value = 365;
            break;
    }
});
</script>
{% endblock %}
