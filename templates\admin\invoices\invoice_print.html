<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة {{ invoice.invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #2c5aa0;
            color: white;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .company-info h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .company-info p {
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .invoice-info {
            text-align: left;
        }

        .invoice-info h2 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .invoice-info p {
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .customer-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c5aa0;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 5px;
        }

        .info-box {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .info-box p {
            margin-bottom: 8px;
        }

        .info-box .label {
            font-weight: bold;
            color: #666;
            font-size: 14px;
        }

        .info-box .value {
            font-size: 16px;
            color: #333;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 1px solid #ddd;
        }

        .items-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: right;
            border-bottom: 2px solid #ddd;
            font-weight: bold;
            color: #2c5aa0;
        }

        .items-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .items-table tr:nth-child(even) {
            background: #f9f9f9;
        }

        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 30px;
        }

        .totals-box {
            width: 300px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }

        .total-row.final {
            border-top: 2px solid #2c5aa0;
            padding-top: 15px;
            margin-top: 15px;
            font-size: 18px;
            font-weight: bold;
            color: #2c5aa0;
        }

        .notes-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-bottom: 30px;
        }

        .subscription-details {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bbdefb;
        }

        .subscription-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-paid {
            background: #d4edda;
            color: #155724;
        }

        .status-sent {
            background: #fff3cd;
            color: #856404;
        }

        .status-overdue {
            background: #f8d7da;
            color: #721c24;
        }

        .status-draft {
            background: #e2e3e5;
            color: #383d41;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
            }

            .container {
                max-width: none;
                padding: 0;
            }

            .header {
                margin-bottom: 20px;
            }

            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="company-info">
                    <div style="display: flex; align-items: flex-start; gap: 15px;">
                        {% if company_info.logo %}
                        <div style="flex-shrink: 0;">
                            <img src="{{ company_info.logo }}" alt="{{ company_info.name }}" style="width: 60px; height: 60px; border-radius: 8px; background: white; padding: 5px;">
                        </div>
                        {% endif %}
                        <div>
                            <h1>{{ company_info.name }}</h1>
                            {% if company_info.description %}
                            <p style="font-size: 12px; margin-bottom: 8px;">{{ company_info.description }}</p>
                            {% endif %}
                            <p>{{ company_info.address }}</p>
                            <p>{{ company_info.phone }}</p>
                            <p>{{ company_info.email }}</p>
                            {% if company_info.website %}
                            <p>{{ company_info.website }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="invoice-info">
                    <h2>فاتورة</h2>
                    <p>رقم: {{ invoice.invoice_number }}</p>
                    <p>التاريخ: {{ invoice.issue_date|date:"Y-m-d" }}</p>
                    <p>الاستحقاق: {{ invoice.due_date|date:"Y-m-d" }}</p>
                    <p>
                        الحالة:
                        <span class="status-badge status-{{ invoice.status }}">
                            {{ invoice.get_status_display }}
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Customer and Invoice Info -->
        <div class="customer-section">
            <div>
                <h3 class="section-title">معلومات العميل</h3>
                <div class="info-box">
                    <p><span class="label">الاسم:</span> <span class="value">{{ invoice.student_name }}</span></p>
                    <p><span class="label">البريد الإلكتروني:</span> <span class="value">{{ invoice.student_email }}</span></p>
                    {% if invoice.student_phone %}
                    <p><span class="label">الهاتف:</span> <span class="value">{{ invoice.student_phone }}</span></p>
                    {% endif %}
                </div>
            </div>

            <div>
                <h3 class="section-title">تفاصيل الاشتراك</h3>
                <div class="info-box">
                    <p><span class="label">الباقة:</span> <span class="value">{{ invoice.plan_name }}</span></p>
                    <p><span class="label">تاريخ البداية:</span> <span class="value">{{ invoice.subscription.start_date|date:"Y-m-d" }}</span></p>
                    <p><span class="label">تاريخ النهاية:</span> <span class="value">{{ invoice.subscription.end_date|date:"Y-m-d" }}</span></p>
                </div>
            </div>
        </div>

        <!-- Invoice Items -->
        <h3 class="section-title">تفاصيل الفاتورة</h3>
        <table class="items-table">
            <thead>
                <tr>
                    <th>الوصف</th>
                    <th>الكمية</th>
                    <th>سعر الوحدة</th>
                    <th>المجموع</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ item.description }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>
                        {% if item.unit_price > 0 %}
                            {{ item.unit_price }} {{ invoice.get_currency_symbol }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if item.total_price > 0 %}
                            {{ item.total_price }} {{ invoice.get_currency_symbol }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="totals-box">
                <div class="total-row">
                    <span>المبلغ الفرعي:</span>
                    <span>{{ invoice.subtotal }} {{ invoice.get_currency_symbol }}</span>
                </div>
                {% if invoice.discount_amount > 0 %}
                <div class="total-row" style="color: #28a745;">
                    <span>الخصم:</span>
                    <span>-{{ invoice.discount_amount }} {{ invoice.get_currency_symbol }}</span>
                </div>
                {% endif %}
                {% if invoice.tax_amount > 0 %}
                <div class="total-row">
                    <span>الضريبة:</span>
                    <span>{{ invoice.tax_amount }} {{ invoice.get_currency_symbol }}</span>
                </div>
                {% endif %}
                <div class="total-row final">
                    <span>المجموع الكلي:</span>
                    <span>{{ invoice.total_amount }} {{ invoice.get_currency_symbol }}</span>
                </div>
            </div>
        </div>

        <!-- Notes -->
        {% if invoice.notes %}
        <h3 class="section-title">ملاحظات</h3>
        <div class="notes-section">
            <p>{{ invoice.notes }}</p>
        </div>
        {% endif %}



        <!-- Footer -->
        <div class="footer">
            <p>شكراً لك على اختيار {{ company_info.name }}</p>
            <p>تم إنشاء هذه الفاتورة إلكترونياً في {{ invoice.created_at|date:"Y-m-d H:i" }}</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
