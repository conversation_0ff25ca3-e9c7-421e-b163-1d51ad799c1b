{% extends 'base.html' %}
{% load static %}
{% load math_filters %}

{% block title %}لوحة تحكم المدير - {{ ACADEMY_SLOGAN }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/admin-sidebar.css' %}">
<style>
    /* تحسينات التجاوب للوحة تحكم المدير */
    @media (max-width: 768px) {
        .stats-card {
            padding: 1rem !important;
        }

        .stats-icon {
            width: 2.5rem !important;
            height: 2.5rem !important;
            font-size: 1rem !important;
        }

        .enhanced-card-body {
            padding: 1rem !important;
        }

        .islamic-content-card {
            margin: 0 1rem 2rem 1rem !important;
        }

        .islamic-content-card .bg-gradient-to-r {
            padding: 1.5rem !important;
        }

        .islamic-content-card h3 {
            font-size: 1.25rem !important;
        }
    }

    @media (max-width: 640px) {
        .grid.grid-cols-2.md\\:grid-cols-4.lg\\:grid-cols-8 {
            grid-template-columns: repeat(2, 1fr) !important;
        }

        .quick-action-item {
            padding: 0.75rem !important;
        }

        .quick-action-item span {
            font-size: 0.75rem !important;
        }

        .quick-action-item .w-12 {
            width: 2.5rem !important;
            height: 2.5rem !important;
        }
    }

    @media (max-width: 1024px) {
        .grid.grid-cols-2.md\\:grid-cols-4.lg\\:grid-cols-8 {
            grid-template-columns: repeat(4, 1fr) !important;
        }
    }

    @media (max-width: 480px) {
        .stats-card {
            padding: 0.75rem !important;
        }

        .text-2xl.md\\:text-3xl {
            font-size: 1.5rem !important;
        }

        .islamic-content-card .grid {
            grid-template-columns: 1fr !important;
        }
    }

    /* Islamic Action Cards */
    .islamic-action-card {
        @apply bg-white rounded-2xl p-5 border border-gray-100 hover:border-gray-200 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        text-decoration: none !important;
    }

    .islamic-action-card:hover {
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        text-decoration: none !important;
    }

    .islamic-action-card span {
        text-decoration: none !important;
    }

    /* Islamic Stat Cards */
    .islamic-stat-card {
        @apply bg-white rounded-xl p-4 border transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1;
        position: relative;
        overflow: hidden;
    }

    .islamic-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-radius: 50%;
        transform: translate(20px, -20px);
    }

    /* Enhanced animations */
    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slide-in-right {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .animate-fade-in-up {
        animation: fade-in-up 0.6s ease-out forwards;
    }

    .animate-slide-in-right {
        animation: slide-in-right 0.6s ease-out forwards;
    }

    /* Enhanced Stat Cards */
    .enhanced-stat-card {
        position: relative;
        overflow: hidden;
    }

    .enhanced-stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
        border-radius: 50%;
        transform: rotate(45deg);
        opacity: 0.5;
    }

    .enhanced-stat-card:hover::before {
        animation: shimmer 2s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) rotate(45deg); }
        100% { transform: translateX(100%) rotate(45deg); }
    }

    /* Remove underlines from all action card links */
    .islamic-action-card,
    .islamic-action-card:hover,
    .islamic-action-card:focus,
    .islamic-action-card:active,
    .islamic-action-card:visited {
        text-decoration: none !important;
    }

    .islamic-action-card *,
    .islamic-action-card *:hover,
    .islamic-action-card *:focus {
        text-decoration: none !important;
    }

    /* Enhanced Activity Items */
    .enhanced-activity-item {
        @apply flex items-center p-4 rounded-xl border transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1;
    }

    /* تخصيص ألوان المدير - أولوية قصوى */
    .new-sidebar,
    .new-sidebar-content,
    .new-sidebar-header,
    .new-sidebar-nav {
        background: #2D5016 !important;
        background-image: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    .new-sidebar-toggle,
    .new-sidebar-toggle button {
        background: #2D5016 !important;
        background-image: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    .enhanced-card-header {
        background: #2D5016 !important;
        background-image: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    .enhanced-card {
        background: #2D5016 !important;
        background-image: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
    }

    /* تأكيد الألوان للموبايل */
    @media (max-width: 768px) {
        .new-sidebar,
        .new-sidebar-content {
            background: #2D5016 !important;
            background-image: linear-gradient(135deg, #2D5016 0%, #1A3009 100%) !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<script>
document.body.classList.add('admin-dashboard');

// تطبيق الألوان بقوة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق اللون على القائمة الجانبية
    const sidebar = document.querySelector('.new-sidebar');
    if (sidebar) {
        sidebar.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
        sidebar.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
    }

    // تطبيق اللون على header البطاقة
    const cardHeader = document.querySelector('.enhanced-card-header');
    if (cardHeader) {
        cardHeader.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
        cardHeader.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
    }

    // تطبيق اللون على البطاقة الرئيسية
    const enhancedCard = document.querySelector('.enhanced-card');
    if (enhancedCard) {
        enhancedCard.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
        enhancedCard.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
    }

    // تطبيق اللون على زر القائمة الجانبية
    const sidebarToggle = document.querySelector('.new-sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
        sidebarToggle.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
    }
});
</script>
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <!-- Enhanced Header -->
    <div class="enhanced-card mb-8 animate-fade-in-up">
        <div class="enhanced-card-header">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-white flex items-center">
                        <i class="fas fa-crown ml-2 md:ml-3 text-islamic-gold text-xl md:text-2xl"></i>
                        لوحة تحكم المدير
                    </h1>
                    <p class="text-islamic-light-gold mt-2 text-sm md:text-base">مرحباً بك {{ user.get_full_name }}، إليك نظرة عامة على النظام</p>
                </div>
                <div class="flex items-center space-x-2 md:space-x-4 space-x-reverse">
                    <div class="bg-white bg-opacity-20 rounded-lg px-2 md:px-4 py-1 md:py-2">
                        <i class="fas fa-user-shield ml-1 md:ml-2 text-islamic-gold text-sm md:text-base"></i>
                        <span class="text-white font-medium text-xs md:text-sm">مدير النظام</span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg px-2 md:px-4 py-1 md:py-2">
                        <i class="fas fa-clock ml-1 md:ml-2 text-islamic-gold text-sm md:text-base"></i>
                        <span class="text-white font-medium text-xs md:text-sm" id="current-time-date">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8 px-4 md:px-0">
        <!-- حصص اليوم -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.1s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                    <i class="fas fa-calendar-day text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">حصص اليوم</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ today_lessons|default:0 }}</p>
                </div>
            </div>
            <div class="mt-3 md:mt-4 space-y-2">
                {% if today_lessons > 0 %}
                    <div class="flex items-center justify-between text-xs">
                        <span class="text-gray-600">حصص عادية:</span>
                        <span class="font-semibold text-blue-600">{{ regular_lessons_today|default:0 }}</span>
                    </div>
                    <div class="flex items-center justify-between text-xs">
                        <span class="text-gray-600">حصص مباشرة:</span>
                        <span class="font-semibold text-green-600">{{ live_lessons_today|default:0 }}</span>
                    </div>
                    <div class="flex items-center justify-between text-xs">
                        <span class="text-gray-600">حصص اشتراكات:</span>
                        <span class="font-semibold text-purple-600">{{ subscription_lessons_today|default:0 }}</span>
                    </div>
                    <div class="pt-1 border-t border-gray-200">
                        <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium animate-pulse">
                            <i class="fas fa-check-circle ml-1"></i>
                            إجمالي {{ today_lessons }} حصة
                        </span>
                    </div>
                {% else %}
                    <span class="px-2 md:px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                        <i class="fas fa-calendar-times ml-1"></i>
                        لا توجد حصص اليوم
                    </span>
                {% endif %}
            </div>
        </div>

        <!-- المعلمون النشطون -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                    <i class="fas fa-chalkboard-teacher text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">المعلمون النشطون</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ active_teachers_count|default:0 }}</p>
                </div>
            </div>
            <div class="mt-3 md:mt-4 flex items-center text-sm">
                <span class="px-2 md:px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium">
                    <i class="fas fa-user-check ml-1"></i>
                    من أصل {{ total_teachers|default:0 }} معلم
                </span>
            </div>
        </div>

        <!-- الطلاب النشطون -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-yellow-500 to-orange-500 text-white">
                    <i class="fas fa-user-graduate text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">الطلاب النشطون</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ active_students_count|default:0 }}</p>
                </div>
            </div>
            <div class="mt-3 md:mt-4 flex items-center text-sm">
                <span class="px-2 md:px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                    <i class="fas fa-graduation-cap ml-1"></i>
                    {{ active_subscriptions_count|default:0 }} اشتراك نشط
                </span>
            </div>
        </div>

        <!-- إجمالي الإيرادات -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-purple-500 to-violet-600 text-white">
                    <i class="fas fa-dollar-sign text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-bold text-gray-800">إجمالي الإيرادات</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">${{ total_revenue_amount|floatformat:0|default:0 }}</p>
                </div>
            </div>
            <div class="mt-3 md:mt-4 flex items-center text-sm">
                <span class="px-2 md:px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                    <i class="fas fa-chart-line ml-1"></i>
                    من المدفوعات المكتملة
                </span>
            </div>
        </div>
    </div>

    <!-- إحصائيات إضافية مهمة -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8 px-4 md:px-0">
        <!-- طلبات التحقق المعلقة -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.5s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                    <i class="fas fa-user-clock text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">طلبات التحقق المعلقة</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ pending_verifications_count|default:0 }}</p>
                </div>
            </div>
            <div class="mt-3 md:mt-4 flex items-center text-sm">
                {% if pending_verifications_count > 0 %}
                    <span class="px-2 md:px-3 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium animate-pulse">
                        <i class="fas fa-exclamation-triangle ml-1"></i>
                        يحتاج مراجعة فورية
                    </span>
                {% else %}
                    <span class="px-2 md:px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                        <i class="fas fa-check-circle ml-1"></i>
                        جميع الطلبات محدثة
                    </span>
                {% endif %}
            </div>
        </div>

        <!-- تذاكر الدعم المفتوحة -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.6s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                    <i class="fas fa-headset text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">تذاكر الدعم المفتوحة</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ open_tickets|default:0 }}</p>
                </div>
            </div>
            <div class="mt-3 md:mt-4 flex items-center text-sm">
                {% if open_tickets > 0 %}
                    <span class="px-2 md:px-3 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium animate-pulse">
                        <i class="fas fa-exclamation-triangle ml-1"></i>
                        يحتاج رد سريع
                    </span>
                {% else %}
                    <span class="px-2 md:px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                        <i class="fas fa-check-circle ml-1"></i>
                        لا توجد تذاكر مفتوحة
                    </span>
                {% endif %}
            </div>
        </div>

        <!-- الحصص المكتملة هذا الشهر -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.7s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-yellow-500 to-orange-500 text-white">
                    <i class="fas fa-check-double text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">الحصص المكتملة هذا الشهر</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ completed_lessons_this_month|default:0 }}</p>
                </div>
            </div>
            <div class="mt-3 md:mt-4 flex items-center text-sm">
                {% if completed_lessons_this_month > 0 %}
                    <span class="px-2 md:px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full text-xs font-medium">
                        <i class="fas fa-trophy ml-1"></i>
                        إنجاز ممتاز هذا الشهر
                    </span>
                {% else %}
                    <span class="px-2 md:px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                        <i class="fas fa-calendar-times ml-1"></i>
                        لا توجد حصص مكتملة
                    </span>
                {% endif %}
            </div>
        </div>

        <!-- الاشتراكات النشطة -->
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.8s">
            <div class="flex items-center">
                <div class="stats-icon bg-gradient-to-br from-purple-500 to-violet-600 text-white">
                    <i class="fas fa-crown text-white"></i>
                </div>
                <div class="mr-3 md:mr-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">الاشتراكات النشطة</p>
                    <p class="text-2xl md:text-3xl font-bold text-gray-900">{{ active_subscriptions_count|default:0 }}</p>
                </div>
            </div>
            <div class="mt-3 md:mt-4 flex items-center text-sm">
                {% if active_subscriptions_count > 0 %}
                    <span class="px-2 md:px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-medium">
                        <i class="fas fa-star ml-1"></i>
                        {{ active_subscriptions_count }} اشتراك فعال
                    </span>
                {% else %}
                    <span class="px-2 md:px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                        <i class="fas fa-times-circle ml-1"></i>
                        لا توجد اشتراكات نشطة
                    </span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Islamic Content Card - Azkar & Quran -->
    <div class="mb-8 animate-fade-in-up islamic-content-card" style="animation-delay: 0.9s">
        <div class="bg-gradient-to-r from-islamic-primary to-emerald-600 rounded-xl p-4 md:p-8 text-white relative overflow-hidden shadow-lg">
            <!-- Islamic Pattern Background -->
            <div class="absolute inset-0 opacity-10">
                <div class="islamic-pattern w-full h-full"></div>
            </div>

            <!-- Content Container -->
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4 md:mb-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 md:w-14 md:h-14 bg-islamic-gold rounded-full flex items-center justify-center ml-3 md:ml-4 shadow-xl border-2 border-white border-opacity-30">
                            <i class="fas fa-quran text-islamic-primary text-lg md:text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl md:text-2xl font-bold text-white">الأذكار والقرآن الكريم</h3>
                            <p class="text-islamic-light-gold text-xs md:text-sm">تذكير روحاني يومي</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="refreshIslamicContent()" class="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg px-3 md:px-4 py-2 transition-all duration-300 flex items-center">
                            <i class="fas fa-sync-alt text-islamic-gold ml-1 md:ml-2 text-sm md:text-base" id="refresh-icon"></i>
                            <span class="text-white text-xs md:text-sm">تجديد</span>
                        </button>
                        <div class="bg-white bg-opacity-20 rounded-lg px-2 md:px-3 py-2">
                            <i class="fas fa-clock text-islamic-gold ml-1 text-xs md:text-sm"></i>
                            <span class="text-white text-xs" id="islamic-timer">يتجدد كل 30 ثانية</span>
                        </div>
                    </div>
                </div>

                <!-- Content Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                    <!-- Quran Verse Section -->
                    <div class="bg-white bg-opacity-10 rounded-xl p-4 md:p-6 backdrop-blur-sm border border-white border-opacity-20">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 md:w-10 md:h-10 bg-islamic-gold rounded-full flex items-center justify-center ml-2 md:ml-3 shadow-lg">
                                <i class="fas fa-book-open text-islamic-primary text-sm md:text-lg font-bold"></i>
                            </div>
                            <h4 class="text-base md:text-lg font-semibold text-white">آية كريمة</h4>
                        </div>
                        <div id="quran-content" class="text-center">
                            <p class="text-lg md:text-xl arabic-text font-semibold text-islamic-light-gold mb-3 leading-relaxed" id="quran-verse">
                                "وَقُلْ رَبِّ زِدْنِي عِلْمًا"
                            </p>
                            <p class="text-xs md:text-sm text-white opacity-80" id="quran-reference">
                                سورة طه - آية 114
                            </p>
                        </div>
                    </div>

                    <!-- Azkar Section -->
                    <div class="bg-white bg-opacity-10 rounded-xl p-4 md:p-6 backdrop-blur-sm border border-white border-opacity-20">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 md:w-10 md:h-10 bg-islamic-gold rounded-full flex items-center justify-center ml-2 md:ml-3 shadow-lg">
                                <i class="fas fa-hands text-islamic-primary text-sm md:text-lg font-bold"></i>
                            </div>
                            <h4 class="text-base md:text-lg font-semibold text-white">ذكر شريف</h4>
                        </div>
                        <div id="azkar-content" class="text-center">
                            <p class="text-base md:text-lg arabic-text font-semibold text-islamic-light-gold mb-3 leading-relaxed" id="azkar-text">
                                "سبحان الله وبحمده، سبحان الله العظيم"
                            </p>
                            <p class="text-xs md:text-sm text-white opacity-80" id="azkar-benefit">
                                كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Islamic Quote at Bottom -->
                <div class="mt-6 text-center border-t border-white border-opacity-20 pt-6">
                    <p class="text-islamic-light-gold text-xs md:text-sm italic" id="islamic-quote">
                        "اللهم بارك لنا فيما علمتنا وعلمنا ما ينفعنا وزدنا علماً"
                    </p>
                </div>
            </div>

            <!-- Decorative Elements -->
            <div class="absolute top-6 right-6 w-16 h-16 border-2 border-islamic-gold border-opacity-40 rounded-full animate-pulse"></div>
            <div class="absolute bottom-6 left-6 w-12 h-12 border-2 border-islamic-gold border-opacity-40 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/2 right-12 w-3 h-3 bg-islamic-gold rounded-full opacity-70 animate-bounce" style="animation-delay: 0.5s;"></div>
            <div class="absolute top-1/4 left-12 w-4 h-4 bg-islamic-gold rounded-full opacity-60 animate-bounce" style="animation-delay: 1.5s;"></div>
            <div class="absolute bottom-1/3 right-20 w-2 h-2 bg-islamic-gold rounded-full opacity-50"></div>
            <div class="absolute top-3/4 left-20 w-2 h-2 bg-islamic-gold rounded-full opacity-50"></div>
        </div>
    </div>

    <!-- Enhanced Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Quick Actions Card -->
        <div class="enhanced-card animate-slide-in-right" style="animation-delay: 0.5s">
            <div class="enhanced-card-body">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-islamic-primary rounded-xl flex items-center justify-center ml-4 shadow-lg">
                            <i class="fas fa-bolt text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">الإجراءات السريعة</h3>
                            <p class="text-sm text-gray-500 mt-1">الوصول المباشر للمهام الأساسية</p>
                        </div>
                    </div>
                    <div class="hidden md:flex items-center space-x-2 space-x-reverse">
                        <div class="w-2 h-2 bg-islamic-gold rounded-full animate-pulse"></div>
                        <div class="w-2 h-2 bg-islamic-light rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                        <div class="w-2 h-2 bg-islamic-primary rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                    </div>
                </div>

                <!-- Actions Grid -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <!-- طلبات التحقق -->
                    <a href="{% url 'admin_user_verifications' %}" class="islamic-action-card group relative overflow-hidden">
                        <div class="absolute inset-0 bg-red-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        <div class="relative z-10 flex flex-col items-center p-5">
                            <div class="w-14 h-14 bg-red-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                                <i class="fas fa-user-check text-white text-lg"></i>
                            </div>
                            <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">طلبات التحقق</span>
                            {% if pending_verifications_count > 0 %}
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold animate-bounce">
                                    {{ pending_verifications_count }}
                                </div>
                            {% endif %}
                        </div>

                    </a>





                    <!-- مراقبة الحصص -->
                    <a href="{% url 'admin_lessons' %}" class="islamic-action-card group relative overflow-hidden">
                        <div class="absolute inset-0 bg-islamic-primary opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        <div class="relative z-10 flex flex-col items-center p-5">
                            <div class="w-14 h-14 bg-islamic-primary rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                                <i class="fas fa-chalkboard-teacher text-white text-lg"></i>
                            </div>
                            <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">مراقبة الحصص</span>
                        </div>

                    </a>

                    <!-- الاشتراكات -->
                    <a href="{% url 'admin_subscriptions' %}" class="islamic-action-card group relative overflow-hidden">
                        <div class="absolute inset-0 bg-islamic-gold opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        <div class="relative z-10 flex flex-col items-center p-5">
                            <div class="w-14 h-14 bg-islamic-gold rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                                <i class="fas fa-crown text-white text-lg"></i>
                            </div>
                            <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">الاشتراكات</span>
                        </div>

                    </a>

                    <!-- الدعم الفني -->
                    <a href="{% url 'admin_support' %}" class="islamic-action-card group relative overflow-hidden">
                        <div class="absolute inset-0 bg-purple-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        <div class="relative z-10 flex flex-col items-center p-5">
                            <div class="w-14 h-14 bg-purple-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                                <i class="fas fa-headset text-white text-lg"></i>
                            </div>
                            <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">الدعم الفني</span>
                            {% if open_tickets > 0 %}
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold animate-bounce">
                                    {{ open_tickets }}
                                </div>
                            {% endif %}
                        </div>

                    </a>

                    <!-- الحصص المباشرة -->
                    <a href="{% url 'admin_live_lessons' %}" class="islamic-action-card group relative overflow-hidden">
                        <div class="absolute inset-0 bg-teal-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        <div class="relative z-10 flex flex-col items-center p-5">
                            <div class="w-14 h-14 bg-teal-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                                <i class="fas fa-video text-white text-lg"></i>
                            </div>
                            <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">الحصص المباشرة</span>
                            {% if active_live_lessons_count > 0 %}
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold animate-pulse">
                                    {{ active_live_lessons_count }}
                                </div>
                                <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
                                    <div class="w-2 h-2 bg-red-500 rounded-full animate-ping"></div>
                                </div>
                            {% endif %}
                        </div>

                    </a>

                    <!-- التقارير -->
                    <a href="{% url 'admin_reports' %}" class="islamic-action-card group relative overflow-hidden">
                        <div class="absolute inset-0 bg-green-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        <div class="relative z-10 flex flex-col items-center p-5">
                            <div class="w-14 h-14 bg-green-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                                <i class="fas fa-chart-line text-white text-lg"></i>
                            </div>
                            <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">التقارير</span>
                        </div>

                    </a>

                    <!-- إدارة المستخدمين -->
                    <a href="{% url 'admin_user_verifications' %}" class="islamic-action-card group relative overflow-hidden">
                        <div class="absolute inset-0 bg-indigo-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        <div class="relative z-10 flex flex-col items-center p-5">
                            <div class="w-14 h-14 bg-indigo-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                                <i class="fas fa-users-cog text-white text-lg"></i>
                            </div>
                            <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">إدارة المستخدمين</span>
                        </div>

                    </a>

                    <!-- إدارة الفواتير -->
                    <a href="{% url 'admin_invoices' %}" class="islamic-action-card group relative overflow-hidden">
                        <div class="absolute inset-0 bg-amber-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        <div class="relative z-10 flex flex-col items-center p-5">
                            <div class="w-14 h-14 bg-amber-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                                <i class="fas fa-file-invoice-dollar text-white text-lg"></i>
                            </div>
                            <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">إدارة الفواتير</span>
                        </div>

                    </a>
                </div>


            </div>
        </div>

        <!-- Enhanced Recent Activity -->
        <div class="enhanced-card animate-slide-in-right" style="animation-delay: 0.6s">
            <div class="enhanced-card-body">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-islamic-primary rounded-xl flex items-center justify-center ml-4 shadow-lg">
                            <i class="fas fa-history text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">النشاط الأخير</h3>
                            <p class="text-sm text-gray-500 mt-1">آخر النشاطات في النظام</p>
                        </div>
                    </div>
                    <div class="hidden md:flex items-center space-x-2 space-x-reverse">
                        <div class="w-2 h-2 bg-islamic-gold rounded-full animate-pulse"></div>
                        <div class="w-2 h-2 bg-islamic-light rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                        <div class="w-2 h-2 bg-islamic-primary rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                    </div>
                </div>
                <div class="space-y-4">
                    <!-- نشاطات المستخدمين الجدد -->
                    {% if new_users_today %}
                        {% for user in new_users_today|slice:":3" %}
                            <div class="enhanced-activity-item bg-green-50 border-green-200">
                                <div class="w-12 h-12 bg-green-500 rounded-2xl flex items-center justify-center ml-4 shadow-lg">
                                    <i class="fas fa-user-plus text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-semibold text-gray-900">انضم مستخدم جديد</p>
                                    <p class="text-xs text-gray-600 mt-1">{{ user.get_full_name|default:user.username }} - {{ user.get_user_type_display }}</p>
                                    <p class="text-xs text-gray-500 flex items-center mt-1">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>{{ user.date_joined|timesince }} مضت</span>
                                    </p>
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- نشاطات الحصص الجديدة -->
                    {% if recent_lessons %}
                        {% for lesson in recent_lessons|slice:":2" %}
                            <div class="enhanced-activity-item bg-green-50 border-green-200">
                                <div class="w-12 h-12 bg-green-500 rounded-2xl flex items-center justify-center ml-4 shadow-lg">
                                    <i class="fas fa-calendar-plus text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-semibold text-gray-900">تم جدولة حصة جديدة</p>
                                    <p class="text-xs text-gray-600 mt-1">{{ lesson.teacher.get_full_name }} - {{ lesson.student.get_full_name }}</p>
                                    <p class="text-xs text-gray-500 flex items-center mt-1">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>{{ lesson.created_at|timesince }} مضت</span>
                                    </p>
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- نشاطات الاشتراكات الجديدة -->
                    {% if recent_subscriptions %}
                        {% for subscription in recent_subscriptions|slice:":2" %}
                            <div class="enhanced-activity-item bg-purple-50 border-purple-200">
                                <div class="w-12 h-12 bg-purple-500 rounded-2xl flex items-center justify-center ml-4 shadow-lg">
                                    <i class="fas fa-crown text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-semibold text-gray-900">اشتراك جديد</p>
                                    <p class="text-xs text-gray-600 mt-1">{{ subscription.student.get_full_name }} - {{ subscription.plan.name }}</p>
                                    <p class="text-xs text-gray-500 flex items-center mt-1">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>{{ subscription.created_at|timesince }} مضت</span>
                                    </p>
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- نشاطات طلبات التحقق -->
                    {% if recent_verifications %}
                        {% for verification in recent_verifications|slice:":2" %}
                            <div class="enhanced-activity-item bg-yellow-50 border-yellow-200">
                                <div class="w-12 h-12 bg-yellow-500 rounded-2xl flex items-center justify-center ml-4 shadow-lg">
                                    <i class="fas fa-user-check text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-semibold text-gray-900">طلب تحقق جديد</p>
                                    <p class="text-xs text-gray-600 mt-1">{{ verification.get_full_name }} - {{ verification.get_user_type_display }}</p>
                                    <p class="text-xs text-gray-500 flex items-center mt-1">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>{{ verification.created_at|timesince }} مضت</span>
                                    </p>
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- نشاطات تذاكر الدعم -->
                    {% if recent_tickets %}
                        {% for ticket in recent_tickets|slice:":1" %}
                            <div class="enhanced-activity-item bg-red-50 border-red-200">
                                <div class="w-12 h-12 bg-red-500 rounded-2xl flex items-center justify-center ml-4 shadow-lg">
                                    <i class="fas fa-headset text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-semibold text-gray-900">تذكرة دعم جديدة</p>
                                    <p class="text-xs text-gray-600 mt-1">{{ ticket.subject|truncatechars:50 }}</p>
                                    <p class="text-xs text-gray-500 flex items-center mt-1">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>{{ ticket.created_at|timesince }} مضت</span>
                                    </p>
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- رسالة عدم وجود نشاطات -->
                    {% if not new_users_today and not recent_lessons and not recent_subscriptions and not recent_verifications and not recent_tickets %}
                        <div class="flex items-center justify-center p-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                            <div class="text-center">
                                <i class="fas fa-history text-gray-400 text-3xl mb-3"></i>
                                <p class="text-gray-500 font-medium">لا توجد نشاطات حديثة</p>
                                <p class="text-gray-400 text-sm mt-1">ستظهر النشاطات الجديدة هنا</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Reports & Analytics Section -->
    <div class="enhanced-card animate-fade-in-up" style="animation-delay: 0.9s">
        <div class="enhanced-card-body">
            <div class="flex items-center justify-between mb-8">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-islamic-primary rounded-xl flex items-center justify-center ml-4 shadow-lg">
                        <i class="fas fa-chart-bar text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900">التقارير والإحصائيات</h3>
                        <p class="text-sm text-gray-500 mt-1">الوصول السريع للتقارير المختلفة</p>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-2 space-x-reverse">
                    <div class="w-2 h-2 bg-islamic-gold rounded-full animate-pulse"></div>
                    <div class="w-2 h-2 bg-islamic-light rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                    <div class="w-2 h-2 bg-islamic-primary rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                </div>
            </div>

            <!-- Reports Grid -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <!-- تقرير الحصص -->
                <a href="{% url 'admin_reports' %}" class="islamic-action-card group relative overflow-hidden">
                    <div class="absolute inset-0 bg-indigo-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative z-10 flex flex-col items-center p-5">
                        <div class="w-14 h-14 bg-indigo-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                            <i class="fas fa-calendar-alt text-white text-lg"></i>
                        </div>
                        <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">تقرير الحصص</span>
                    </div>
                </a>

                <!-- تقرير الأرباح -->
                <a href="{% url 'admin_reports' %}" class="islamic-action-card group relative overflow-hidden">
                    <div class="absolute inset-0 bg-emerald-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative z-10 flex flex-col items-center p-5">
                        <div class="w-14 h-14 bg-emerald-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                            <i class="fas fa-dollar-sign text-white text-lg"></i>
                        </div>
                        <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">تقرير الأرباح</span>
                    </div>
                </a>

                <!-- تقرير التقييمات -->
                <a href="{% url 'admin_reports' %}" class="islamic-action-card group relative overflow-hidden">
                    <div class="absolute inset-0 bg-amber-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative z-10 flex flex-col items-center p-5">
                        <div class="w-14 h-14 bg-amber-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                            <i class="fas fa-star text-white text-lg"></i>
                        </div>
                        <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">تقرير التقييمات</span>
                    </div>
                </a>

                <!-- تقرير المستخدمين -->
                <a href="{% url 'admin_reports' %}" class="islamic-action-card group relative overflow-hidden">
                    <div class="absolute inset-0 bg-purple-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative z-10 flex flex-col items-center p-5">
                        <div class="w-14 h-14 bg-purple-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg">
                            <i class="fas fa-users text-white text-lg"></i>
                        </div>
                        <span class="font-semibold text-gray-800 text-center text-xs sm:text-sm md:text-sm lg:text-sm leading-tight">تقرير المستخدمين</span>
                    </div>
                </a>
            </div>


        </div>
    </div>




</div>

<script>
// Islamic Content Data
const quranVerses = [
    {
        verse: "وَقُلْ رَبِّ زِدْنِي عِلْمًا",
        reference: "سورة طه - آية 114"
    },
    {
        verse: "وَمَا أُوتِيتُم مِّنَ الْعِلْمِ إِلَّا قَلِيلًا",
        reference: "سورة الإسراء - آية 85"
    },
    {
        verse: "يَرْفَعِ اللَّهُ الَّذِينَ آمَنُوا مِنكُمْ وَالَّذِينَ أُوتُوا الْعِلْمَ دَرَجَاتٍ",
        reference: "سورة المجادلة - آية 11"
    },
    {
        verse: "وَعَلَّمَكَ مَا لَمْ تَكُن تَعْلَمُ ۚ وَكَانَ فَضْلُ اللَّهِ عَلَيْكَ عَظِيمًا",
        reference: "سورة النساء - آية 113"
    },
    {
        verse: "اقْرَأْ بِاسْمِ رَبِّكَ الَّذِي خَلَقَ",
        reference: "سورة العلق - آية 1"
    },
    {
        verse: "وَقُل رَّبِّ أَدْخِلْنِي مُدْخَلَ صِدْقٍ وَأَخْرِجْنِي مُخْرَجَ صِدْقٍ",
        reference: "سورة الإسراء - آية 80"
    },
    {
        verse: "رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً",
        reference: "سورة البقرة - آية 201"
    },
    {
        verse: "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
        reference: "سورة الطلاق - آية 2"
    }
];

const azkarList = [
    {
        text: "سبحان الله وبحمده، سبحان الله العظيم",
        benefit: "كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن"
    },
    {
        text: "لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير",
        benefit: "من قالها في يوم مائة مرة كانت له عدل عشر رقاب"
    },
    {
        text: "اللهم أعني على ذكرك وشكرك وحسن عبادتك",
        benefit: "دعاء جامع للتوفيق في العبادة والذكر والشكر"
    },
    {
        text: "استغفر الله العظيم الذي لا إله إلا هو الحي القيوم وأتوب إليه",
        benefit: "الاستغفار يمحو الذنوب ويجلب الرزق والفرج"
    },
    {
        text: "اللهم صل وسلم على نبينا محمد",
        benefit: "من صلى على النبي صلاة واحدة صلى الله عليه بها عشراً"
    },
    {
        text: "حسبنا الله ونعم الوكيل",
        benefit: "كلمة التوكل والاعتماد على الله في جميع الأمور"
    },
    {
        text: "لا حول ولا قوة إلا بالله",
        benefit: "كنز من كنوز الجنة، تعين على تحمل المشاق"
    },
    {
        text: "اللهم بارك لنا فيما رزقتنا وقنا عذاب النار",
        benefit: "دعاء شامل للبركة في الرزق والوقاية من النار"
    }
];

const islamicQuotes = [
    "اللهم بارك لنا فيما علمتنا وعلمنا ما ينفعنا وزدنا علماً",
    "اللهم انفعني بما علمتني وعلمني ما ينفعني واكرمني بالعلم النافع",
    "اللهم أعني على ذكرك وشكرك وحسن عبادتك",
    "ربنا آتنا في الدنيا حسنة وفي الآخرة حسنة وقنا عذاب النار",
    "اللهم اهدني فيمن هديت وعافني فيمن عافيت",
    "اللهم أصلح لي ديني الذي هو عصمة أمري",
    "اللهم بارك لنا في أوقاتنا وأعمالنا وأرزاقنا"
];

let islamicContentInterval;
let timerInterval;
let timeLeft = 30;

// Function to get random content
function getRandomQuranVerse() {
    return quranVerses[Math.floor(Math.random() * quranVerses.length)];
}

function getRandomAzkar() {
    return azkarList[Math.floor(Math.random() * azkarList.length)];
}

function getRandomIslamicQuote() {
    return islamicQuotes[Math.floor(Math.random() * islamicQuotes.length)];
}

// Function to update Islamic content
function updateIslamicContent() {
    const quranVerse = getRandomQuranVerse();
    const azkar = getRandomAzkar();
    const quote = getRandomIslamicQuote();

    // Update Quran verse with animation
    const quranVerseElement = document.getElementById('quran-verse');
    const quranReferenceElement = document.getElementById('quran-reference');

    quranVerseElement.style.opacity = '0';
    quranReferenceElement.style.opacity = '0';

    setTimeout(() => {
        quranVerseElement.textContent = `"${quranVerse.verse}"`;
        quranReferenceElement.textContent = quranVerse.reference;
        quranVerseElement.style.opacity = '1';
        quranReferenceElement.style.opacity = '1';
    }, 300);

    // Update Azkar with animation
    const azkarTextElement = document.getElementById('azkar-text');
    const azkarBenefitElement = document.getElementById('azkar-benefit');

    azkarTextElement.style.opacity = '0';
    azkarBenefitElement.style.opacity = '0';

    setTimeout(() => {
        azkarTextElement.textContent = `"${azkar.text}"`;
        azkarBenefitElement.textContent = azkar.benefit;
        azkarTextElement.style.opacity = '1';
        azkarBenefitElement.style.opacity = '1';
    }, 300);

    // Update Islamic quote with animation
    const quoteElement = document.getElementById('islamic-quote');
    quoteElement.style.opacity = '0';

    setTimeout(() => {
        quoteElement.textContent = `"${quote}"`;
        quoteElement.style.opacity = '1';
    }, 300);

    // Reset timer
    timeLeft = 30;
}

// Function to refresh Islamic content manually
function refreshIslamicContent() {
    const refreshIcon = document.getElementById('refresh-icon');
    refreshIcon.classList.add('fa-spin');

    updateIslamicContent();

    setTimeout(() => {
        refreshIcon.classList.remove('fa-spin');
    }, 1000);
}

// Function to update timer display
function updateTimer() {
    const timerElement = document.getElementById('islamic-timer');
    if (timeLeft > 0) {
        timerElement.textContent = `يتجدد خلال ${timeLeft} ثانية`;
        timeLeft--;
    } else {
        updateIslamicContent();
    }
}

// Initialize Islamic content functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth transitions to content elements
    const contentElements = [
        'quran-verse', 'quran-reference',
        'azkar-text', 'azkar-benefit',
        'islamic-quote'
    ];

    contentElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.style.transition = 'opacity 0.3s ease-in-out';
        }
    });

    // Start the automatic content update
    islamicContentInterval = setInterval(updateIslamicContent, 30000); // Every 30 seconds

    // Start the timer update
    timerInterval = setInterval(updateTimer, 1000); // Every second

    // Initial content load
    updateIslamicContent();
});

// Clean up intervals when page is unloaded
window.addEventListener('beforeunload', function() {
    if (islamicContentInterval) {
        clearInterval(islamicContentInterval);
    }
    if (timerInterval) {
        clearInterval(timerInterval);
    }
});
</script>

<style>
/* Islamic Content Card Custom Styles */
.islamic-content-card {
    background: transparent !important;
}

.islamic-content-card .bg-gradient-to-r {
    background: linear-gradient(to right, #2D5016, #10B981) !important;
}

/* Ensure no white background overrides */
.islamic-content-card * {
    background-color: transparent;
}

.islamic-content-card .bg-white {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Enhanced Islamic Gold Icons */
.islamic-content-card .bg-islamic-gold {
    background: linear-gradient(135deg, #D4AF37 0%, #F4E4BC 50%, #D4AF37 100%) !important;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.islamic-content-card .bg-islamic-gold:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Decorative Elements Animation */
.islamic-content-card .animate-pulse {
    animation: islamicPulse 2s infinite;
}

.islamic-content-card .animate-bounce {
    animation: islamicBounce 2s infinite;
}

@keyframes islamicPulse {
    0%, 100% {
        opacity: 0.4;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes islamicBounce {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-5px);
        opacity: 0.9;
    }
}
</style>



{% endblock %}
