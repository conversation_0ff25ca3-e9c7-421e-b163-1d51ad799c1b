"""
نماذج إدارة إعدادات البريد الإلكتروني
"""

from django import forms
from django.core.exceptions import ValidationError
from .email_models import EmailSettings, EmailTemplate, EmailEvent, EmailSubscription


class EmailSettingsForm(forms.ModelForm):
    """نموذج إعدادات البريد الإلكتروني"""
    
    # إضافة حقل كلمة المرور غير مشفرة للعرض
    smtp_password_plain = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'كلمة مرور SMTP'
        }),
        label='كلمة مرور SMTP',
        required=False,
        help_text='اتركه فارغاً للاحتفاظ بكلمة المرور الحالية'
    )
    
    class Meta:
        model = EmailSettings
        fields = [
            'provider', 'smtp_host', 'smtp_port', 'smtp_username',
            'use_tls', 'use_ssl', 'from_email', 'from_name',
            'max_emails_per_hour', 'is_active'
        ]
        
        widgets = {
            'provider': forms.Select(attrs={
                'class': 'form-select',
                'onchange': 'updateSMTPSettings(this.value)'
            }),
            'smtp_host': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'smtp.gmail.com'
            }),
            'smtp_port': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '587'
            }),
            'smtp_username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'from_email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'from_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أكاديمية القرآنية'
            }),
            'max_emails_per_hour': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '1000'
            }),
            'use_tls': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'use_ssl': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        
        labels = {
            'provider': 'مزود خدمة SMTP',
            'smtp_host': 'خادم SMTP',
            'smtp_port': 'منفذ SMTP',
            'smtp_username': 'اسم المستخدم',
            'from_email': 'البريد المرسل',
            'from_name': 'اسم المرسل',
            'max_emails_per_hour': 'الحد الأقصى للرسائل في الساعة',
            'use_tls': 'استخدام TLS',
            'use_ssl': 'استخدام SSL',
            'is_active': 'نشط',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # إضافة CSS classes
        for field_name, field in self.fields.items():
            if field_name not in ['use_tls', 'use_ssl', 'is_active']:
                field.widget.attrs.update({'class': 'form-control'})
    
    def clean_smtp_password_plain(self):
        """التحقق من كلمة المرور"""
        password = self.cleaned_data.get('smtp_password_plain')
        if not password and not self.instance.pk:
            raise ValidationError('كلمة المرور مطلوبة للإعدادات الجديدة')
        return password
    
    def save(self, commit=True):
        """حفظ مع تشفير كلمة المرور"""
        instance = super().save(commit=False)
        
        # تحديث كلمة المرور إذا تم إدخالها
        password = self.cleaned_data.get('smtp_password_plain')
        if password:
            instance.smtp_password = password  # سيتم تشفيرها في النموذج
        
        if commit:
            instance.save()
        return instance


class EmailTemplateForm(forms.ModelForm):
    """نموذج قوالب البريد الإلكتروني"""

    class Meta:
        model = EmailTemplate
        fields = [
            'name', 'template_type', 'subject', 'html_content',
            'text_content', 'is_active'
        ]
        
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم القالب'
            }),
            'template_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'subject': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'موضوع الرسالة'
            }),
            'html_content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 15,
                'placeholder': 'المحتوى HTML للقالب'
            }),
            'text_content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 8,
                'placeholder': 'المحتوى النصي (اختياري)'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_default': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        
        labels = {
            'name': 'اسم القالب',
            'template_type': 'نوع القالب',
            'subject': 'موضوع الرسالة',
            'html_content': 'المحتوى HTML',
            'text_content': 'المحتوى النصي',
            'is_active': 'نشط',
        }
    
    def clean_template_type(self):
        """التحقق من نوع القالب"""
        template_type = self.cleaned_data.get('template_type')

        if not template_type:
            raise ValidationError('يجب اختيار نوع للقالب')

        return template_type


class EmailEventForm(forms.ModelForm):
    """نموذج أحداث البريد الإلكتروني"""
    
    class Meta:
        model = EmailEvent
        fields = ['event_type', 'template', 'is_active', 'send_delay']
        
        widgets = {
            'event_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'template': forms.Select(attrs={
                'class': 'form-select'
            }),
            'send_delay': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': '0'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        
        labels = {
            'event_type': 'نوع الحدث',
            'template': 'القالب المستخدم',
            'send_delay': 'تأخير الإرسال (بالدقائق)',
            'is_active': 'نشط',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تصفية القوالب النشطة فقط
        self.fields['template'].queryset = EmailTemplate.objects.filter(is_active=True)


class EmailSubscriptionForm(forms.ModelForm):
    """نموذج اشتراكات البريد الإلكتروني"""
    
    class Meta:
        model = EmailSubscription
        fields = [
            'email_notifications_enabled', 'lesson_reminders',
            'subscription_notifications', 'lesson_reports',
            'payment_notifications', 'teacher_notifications',
            'student_updates', 'performance_reports',
            'admin_reports', 'system_alerts', 'daily_summaries'
        ]
        
        widgets = {
            'email_notifications_enabled': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'lesson_reminders': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'subscription_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'lesson_reports': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'payment_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'teacher_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'student_updates': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'performance_reports': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'admin_reports': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'system_alerts': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'daily_summaries': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        
        labels = {
            'email_notifications_enabled': 'تفعيل إشعارات البريد الإلكتروني',
            'lesson_reminders': 'تذكيرات الحصص',
            'subscription_notifications': 'إشعارات الاشتراك',
            'lesson_reports': 'تقارير الحصص',
            'payment_notifications': 'إشعارات الدفع',
            'teacher_notifications': 'إشعارات المعلم',
            'student_updates': 'تحديثات الطلاب',
            'performance_reports': 'تقارير الأداء',
            'admin_reports': 'التقارير الإدارية',
            'system_alerts': 'تنبيهات النظام',
            'daily_summaries': 'الملخصات اليومية',
        }


class TestEmailForm(forms.Form):
    """نموذج اختبار إرسال البريد الإلكتروني"""
    
    recipient_email = forms.EmailField(
        label='البريد الإلكتروني للمستلم',
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    
    template = forms.ModelChoiceField(
        queryset=EmailTemplate.objects.filter(is_active=True),
        label='القالب المستخدم',
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    test_data = forms.CharField(
        label='بيانات الاختبار (JSON)',
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 5,
            'placeholder': '{"user_name": "أحمد محمد", "lesson_date": "2024-01-15"}'
        }),
        required=False,
        help_text='بيانات إضافية لاختبار القالب (تنسيق JSON)'
    )
    
    def clean_test_data(self):
        """التحقق من صحة بيانات JSON"""
        test_data = self.cleaned_data.get('test_data')
        if test_data:
            try:
                import json
                json.loads(test_data)
            except json.JSONDecodeError:
                raise ValidationError('تنسيق JSON غير صحيح')
        return test_data
