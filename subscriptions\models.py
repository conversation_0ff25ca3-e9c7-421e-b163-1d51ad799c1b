from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

User = get_user_model()


class SubscriptionPlan(models.Model):
    """نموذج باقات الاشتراك"""

    PLAN_TYPES = (
        ('basic', _('باقة أساسية')),
        ('standard', _('باقة قياسية')),
        ('premium', _('باقة مميزة')),
        ('vip', _('باقة VIP')),
    )

    DURATION_TYPES = (
        ('monthly', _('شهرية')),
        ('quarterly', _('ربع سنوية')),
        ('semi_annual', _('نصف سنوية')),
        ('annual', _('سنوية')),
    )

    CURRENCY_CHOICES = (
        ('SAR', _('ريال سعودي (SAR)')),
        ('USD', _('دولار أمريكي (USD)')),
        ('EUR', _('يورو (EUR)')),
        ('GBP', _('جنيه إسترليني (GBP)')),
        ('AED', _('درهم إماراتي (AED)')),
        ('KWD', _('دينار كويتي (KWD)')),
        ('QAR', _('ريال قطري (QAR)')),
        ('BHD', _('دينار بحريني (BHD)')),
        ('OMR', _('ريال عماني (OMR)')),
        ('JOD', _('دينار أردني (JOD)')),
        ('EGP', _('جنيه مصري (EGP)')),
        ('LBP', _('ليرة لبنانية (LBP)')),
        ('SYP', _('ليرة سورية (SYP)')),
        ('IQD', _('دينار عراقي (IQD)')),
        ('YER', _('ريال يمني (YER)')),
        ('MAD', _('درهم مغربي (MAD)')),
        ('TND', _('دينار تونسي (TND)')),
        ('DZD', _('دينار جزائري (DZD)')),
        ('LYD', _('دينار ليبي (LYD)')),
        ('SDG', _('جنيه سوداني (SDG)')),
        ('JPY', _('ين ياباني (JPY)')),
        ('CNY', _('يوان صيني (CNY)')),
        ('INR', _('روبية هندية (INR)')),
        ('KRW', _('وون كوري جنوبي (KRW)')),
        ('SGD', _('دولار سنغافوري (SGD)')),
        ('MYR', _('رينغت ماليزي (MYR)')),
        ('THB', _('بات تايلاندي (THB)')),
        ('IDR', _('روبية إندونيسية (IDR)')),
        ('PHP', _('بيزو فلبيني (PHP)')),
        ('VND', _('دونغ فيتنامي (VND)')),
        ('PKR', _('روبية باكستانية (PKR)')),
        ('BDT', _('تاكا بنغلاديشية (BDT)')),
        ('LKR', _('روبية سريلانكية (LKR)')),
        ('NPR', _('روبية نيبالية (NPR)')),
        ('AFN', _('أفغاني أفغانستاني (AFN)')),
        ('IRR', _('ريال إيراني (IRR)')),
        ('TRY', _('ليرة تركية (TRY)')),
        ('RUB', _('روبل روسي (RUB)')),
        ('UAH', _('هريفنيا أوكرانية (UAH)')),
        ('PLN', _('زلوتي بولندي (PLN)')),
        ('CZK', _('كورونا تشيكية (CZK)')),
        ('HUF', _('فورنت هنغاري (HUF)')),
        ('RON', _('ليو روماني (RON)')),
        ('BGN', _('ليف بلغاري (BGN)')),
        ('CAD', _('دولار كندي (CAD)')),
        ('AUD', _('دولار أسترالي (AUD)')),
        ('NZD', _('دولار نيوزيلندي (NZD)')),
        ('CHF', _('فرنك سويسري (CHF)')),
        ('SEK', _('كرونا سويدية (SEK)')),
        ('NOK', _('كرونا نرويجية (NOK)')),
        ('DKK', _('كرونا دنماركية (DKK)')),
        ('BRL', _('ريال برازيلي (BRL)')),
        ('ARS', _('بيزو أرجنتيني (ARS)')),
        ('CLP', _('بيزو تشيلي (CLP)')),
        ('COP', _('بيزو كولومبي (COP)')),
        ('PEN', _('سول بيروفي (PEN)')),
        ('MXN', _('بيزو مكسيكي (MXN)')),
        ('ZAR', _('راند جنوب أفريقي (ZAR)')),
        ('KES', _('شلن كيني (KES)')),
        ('NGN', _('نايرا نيجيرية (NGN)')),
        ('GHS', _('سيدي غاني (GHS)')),
        ('XOF', _('فرنك غرب أفريقي (XOF)')),
        ('XAF', _('فرنك وسط أفريقي (XAF)')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم الباقة')
    )

    description = models.TextField(
        verbose_name=_('وصف الباقة')
    )

    plan_type = models.CharField(
        max_length=20,
        choices=PLAN_TYPES,
        verbose_name=_('نوع الباقة')
    )

    duration_type = models.CharField(
        max_length=20,
        choices=DURATION_TYPES,
        verbose_name=_('مدة الاشتراك')
    )

    duration_days = models.PositiveIntegerField(
        verbose_name=_('مدة الباقة بالأيام')
    )

    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('سعر الباقة')
    )

    currency = models.CharField(
        max_length=3,
        choices=CURRENCY_CHOICES,
        default='SAR',
        verbose_name=_('العملة')
    )

    lessons_count = models.PositiveIntegerField(
        verbose_name=_('عدد الحصص')
    )

    lesson_duration = models.PositiveIntegerField(
        choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')],
        default=45,
        verbose_name=_('مدة الحصة (بالدقائق)')
    )

    # حقول جديدة للجدولة التلقائية
    lessons_per_week = models.PositiveIntegerField(
        default=2,
        verbose_name=_('عدد الحصص أسبوعياً'),
        help_text=_('عدد الحصص في الأسبوع الواحد')
    )

    preferred_days = models.JSONField(
        default=list,
        verbose_name=_('أيام الأسبوع المفضلة'),
        help_text=_('أرقام أيام الأسبوع: 0=أحد، 1=اثنين، 2=ثلاثاء، 3=أربعاء، 4=خميس، 5=جمعة، 6=سبت')
    )

    preferred_times = models.JSONField(
        default=list,
        verbose_name=_('الأوقات المفضلة'),
        help_text=_('قائمة بالأوقات المفضلة بصيغة HH:MM مثل: ["16:00", "18:00"]')
    )

    auto_schedule = models.BooleanField(
        default=True,
        verbose_name=_('جدولة تلقائية'),
        help_text=_('تفعيل الجدولة التلقائية للحصص عند الموافقة على الاشتراك')
    )

    features = models.JSONField(
        default=list,
        verbose_name=_('مميزات الباقة'),
        help_text=_('قائمة بمميزات الباقة')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشطة')
    )

    is_featured = models.BooleanField(
        default=False,
        verbose_name=_('مميزة')
    )

    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_('نسبة الخصم')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('باقة اشتراك')
        verbose_name_plural = _('باقات الاشتراك')
        ordering = ['plan_type', 'price']

    def __str__(self):
        return f"{self.name} - {self.get_duration_type_display()}"

    def get_discounted_price(self):
        """حساب السعر بعد الخصم"""
        if self.discount_percentage > 0:
            discount_amount = (self.price * self.discount_percentage) / 100
            return self.price - discount_amount
        return self.price

    def get_price_per_lesson(self):
        """حساب سعر الحصة الواحدة"""
        return self.get_discounted_price() / self.lessons_count if self.lessons_count > 0 else 0

    def get_formatted_price(self):
        """عرض السعر منسق بخانتين عشريتين"""
        return f"{self.price:.2f}"

    def get_formatted_discounted_price(self):
        """عرض السعر بعد الخصم منسق بخانتين عشريتين"""
        return f"{self.get_discounted_price():.2f}"

    def get_formatted_price_per_lesson(self):
        """عرض سعر الحصة الواحدة منسق بخانتين عشريتين"""
        return f"{self.get_price_per_lesson():.2f}"

    def get_price_with_currency(self):
        """عرض السعر مع العملة"""
        return f"{self.get_formatted_price()} {self.get_currency_display()}"

    def get_discounted_price_with_currency(self):
        """عرض السعر بعد الخصم مع العملة"""
        return f"{self.get_formatted_discounted_price()} {self.get_currency_display()}"

    def get_currency_symbol(self):
        """الحصول على رمز العملة"""
        currency_symbols = {
            'SAR': 'ر.س',
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق',
            'BHD': 'د.ب',
            'OMR': 'ر.ع',
            'JOD': 'د.أ',
            'EGP': 'ج.م',
            'JPY': '¥',
            'CNY': '¥',
            'INR': '₹',
            'KRW': '₩',
            'TRY': '₺',
            'RUB': '₽',
            'CAD': 'C$',
            'AUD': 'A$',
            'CHF': 'CHF',
            'BRL': 'R$',
            'ZAR': 'R',
        }
        return currency_symbols.get(self.currency, self.currency)


class StudentSubscription(models.Model):
    """نموذج اشتراك الطالب"""

    STATUS_CHOICES = (
        ('pending', _('في الانتظار')),
        ('payment_pending', _('في انتظار الدفع')),
        ('pending_approval', _('في انتظار الموافقة')),
        ('active', _('نشط')),
        ('expired', _('منتهي')),
        ('cancelled', _('ملغي')),
        ('suspended', _('معلق')),
    )

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='subscriptions',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    plan = models.ForeignKey(
        SubscriptionPlan,
        on_delete=models.CASCADE,
        related_name='subscriptions',
        verbose_name=_('الباقة')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    start_date = models.DateField(
        verbose_name=_('تاريخ البداية')
    )

    end_date = models.DateField(
        verbose_name=_('تاريخ النهاية')
    )

    remaining_lessons = models.PositiveIntegerField(
        verbose_name=_('الحصص المتبقية')
    )

    amount_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ المدفوع')
    )

    auto_renewal = models.BooleanField(
        default=False,
        verbose_name=_('تجديد تلقائي')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الاشتراك')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('اشتراك طالب')
        verbose_name_plural = _('اشتراكات الطلاب')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.plan.name}"

    def is_active(self):
        """التحقق من كون الاشتراك نشط"""
        from django.utils import timezone
        return (
            self.status == 'active' and
            self.end_date >= timezone.now().date() and
            self.remaining_lessons > 0
        )

    def days_remaining(self):
        """حساب الأيام المتبقية"""
        from django.utils import timezone
        if self.end_date > timezone.now().date():
            return (self.end_date - timezone.now().date()).days
        return 0

    def get_completed_lessons_count(self):
        """حساب عدد الحصص المكتملة"""
        return self.plan.lessons_count - self.remaining_lessons

    def is_lessons_exhausted(self):
        """التحقق من انتهاء جميع الحصص"""
        return self.remaining_lessons <= 0

    def needs_renewal(self):
        """التحقق من الحاجة للتجديد (انتهت الحصص أو قارب انتهاء التاريخ)"""
        from django.utils import timezone
        days_left = self.days_remaining()
        return (self.is_lessons_exhausted() or
                days_left <= 7 or  # أقل من أسبوع
                self.status == 'expired')

    def can_renew(self):
        """التحقق من إمكانية التجديد"""
        return self.status in ['active', 'expired'] and self.is_lessons_exhausted()

    def get_renewal_status(self):
        """الحصول على حالة التجديد"""
        if self.is_lessons_exhausted():
            return 'lessons_exhausted'  # انتهت الحصص
        elif self.status == 'expired':
            return 'expired'  # انتهى التاريخ
        elif self.days_remaining() <= 7:
            return 'expiring_soon'  # قارب الانتهاء
        else:
            return 'active'  # نشط

    def get_progress_percentage(self):
        """حساب نسبة التقدم في الاشتراك"""
        if self.plan.lessons_count == 0:
            return 0
        completed_lessons = self.get_completed_lessons_count()
        return (completed_lessons / self.plan.lessons_count) * 100


class SubscriptionPayment(models.Model):
    """نموذج دفعات الاشتراكات"""

    PAYMENT_METHODS = (
        ('paypal', _('PayPal')),
        ('stripe', _('Stripe')),
        ('bank_transfer', _('تحويل بنكي')),
        ('credit_card', _('بطاقة ائتمان')),
        ('wallet', _('محفظة إلكترونية')),
    )

    STATUS_CHOICES = (
        ('pending', _('في الانتظار')),
        ('processing', _('قيد المعالجة')),
        ('completed', _('مكتمل')),
        ('failed', _('فاشل')),
        ('refunded', _('مسترد')),
        ('cancelled', _('ملغي')),
    )

    subscription = models.ForeignKey(
        StudentSubscription,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('الاشتراك')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ')
    )

    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHODS,
        verbose_name=_('طريقة الدفع')
    )

    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة الدفع')
    )

    transaction_id = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('معرف المعاملة')
    )

    gateway_response = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('استجابة بوابة الدفع')
    )

    payment_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الدفع')
    )

    processed_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ المعالجة')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    class Meta:
        verbose_name = _('دفعة اشتراك')
        verbose_name_plural = _('دفعات الاشتراكات')
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.subscription.student.get_full_name()} - {self.amount} ريال"

    def is_successful(self):
        """التحقق من نجاح الدفع"""
        return self.status == 'completed'


class BankTransferProof(models.Model):
    """نموذج إثبات التحويل البنكي"""

    payment = models.OneToOneField(
        SubscriptionPayment,
        on_delete=models.CASCADE,
        related_name='bank_proof',
        verbose_name=_('الدفعة')
    )

    transfer_receipt = models.ImageField(
        upload_to='bank_transfers/',
        verbose_name=_('إيصال التحويل')
    )

    sender_name = models.CharField(
        max_length=100,
        verbose_name=_('اسم المرسل')
    )

    transfer_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('مبلغ التحويل')
    )

    transfer_date = models.DateField(
        verbose_name=_('تاريخ التحويل')
    )

    bank_name = models.CharField(
        max_length=100,
        verbose_name=_('اسم البنك')
    )

    reference_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('رقم المرجع')
    )

    is_verified = models.BooleanField(
        default=False,
        verbose_name=_('تم التحقق')
    )

    verified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='verified_transfers',
        verbose_name=_('تم التحقق بواسطة')
    )

    verified_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ التحقق')
    )

    uploaded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الرفع')
    )

    class Meta:
        verbose_name = _('إثبات تحويل بنكي')
        verbose_name_plural = _('إثباتات التحويلات البنكية')
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"إثبات تحويل - {self.payment.subscription.student.get_full_name()}"


class ScheduledLesson(models.Model):
    """نموذج الحصص المجدولة تلقائياً من الاشتراكات"""

    STATUS_CHOICES = (
        ('scheduled', _('مجدولة')),
        ('converted_to_live', _('تم تحويلها إلى حصة مباشرة')),
        ('completed', _('مكتملة')),
        ('cancelled', _('ملغية')),
        ('rescheduled', _('معاد جدولتها')),
        ('no_show', _('غياب بدون عذر')),
    )

    subscription = models.ForeignKey(
        StudentSubscription,
        on_delete=models.CASCADE,
        related_name='scheduled_lessons',
        verbose_name=_('الاشتراك')
    )

    lesson_number = models.PositiveIntegerField(
        verbose_name=_('رقم الحصة'),
        help_text=_('رقم الحصة ضمن الاشتراك')
    )

    scheduled_date = models.DateTimeField(
        verbose_name=_('تاريخ ووقت الحصة')
    )

    duration_minutes = models.PositiveIntegerField(
        default=60,
        verbose_name=_('مدة الحصة بالدقائق')
    )

    teacher = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'teacher'},
        related_name='scheduled_lessons_as_teacher',
        verbose_name=_('المعلم')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_('حالة الحصة')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    # ربط مع الحصة المباشرة عند التحويل
    live_lesson_id = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_('معرف الحصة المباشرة'),
        help_text=_('معرف الحصة المباشرة المرتبطة عند التحويل')
    )

    completed_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الإكمال')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('حصة مجدولة')
        verbose_name_plural = _('حصص مجدولة')
        unique_together = ['subscription', 'lesson_number']
        ordering = ['scheduled_date']

    def __str__(self):
        return f"حصة {self.lesson_number} - {self.subscription.student.get_full_name()}"

    @property
    def student(self):
        """الحصول على الطالب من الاشتراك"""
        return self.subscription.student

    def is_upcoming(self):
        """التحقق من كون الحصة قادمة"""
        from django.utils import timezone
        return self.scheduled_date > timezone.now() and self.status == 'scheduled'

    def is_today(self):
        """التحقق من كون الحصة اليوم"""
        from django.utils import timezone
        return self.scheduled_date.date() == timezone.now().date()

    def can_be_completed(self):
        """التحقق من إمكانية إكمال الحصة"""
        return self.status == 'scheduled'

    def mark_completed(self):
        """تحديد الحصة كمكتملة"""
        from django.utils import timezone
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save()

        # تقليل عدد الحصص المتبقية في الاشتراك
        if self.subscription.remaining_lessons > 0:
            self.subscription.remaining_lessons -= 1
            self.subscription.save()

    def can_start_now(self, user):
        """التحقق من إمكانية بدء الحصة الآن"""
        from django.utils import timezone

        if self.status != 'scheduled':
            return False, "الحصة ليست مجدولة"

        now = timezone.now()
        time_diff = (self.scheduled_date - now).total_seconds() / 60  # بالدقائق

        # المدير يمكنه البدء في أي وقت، المعلم فقط قبل 5 دقائق
        if user.is_teacher() and time_diff > 5:
            return False, f"لا يمكن بدء الحصة قبل {int(time_diff)} دقيقة من موعدها. يمكنك البدء قبل 5 دقائق فقط من الموعد المحدد."

        return True, "يمكن بدء الحصة"

    def convert_to_live_lesson(self, created_by):
        """تحويل الحصة المجدولة إلى حصة مباشرة"""
        from lessons.models import LiveLesson

        # إنشاء حصة مباشرة جديدة (سيتم توليد jitsi_room_id تلقائياً)
        live_lesson = LiveLesson.objects.create(
            title=f"حصة رقم {self.lesson_number} - {self.subscription.student.get_full_name()}",
            teacher=self.teacher,
            student=self.subscription.student,
            scheduled_date=self.scheduled_date,
            duration_minutes=self.duration_minutes,
            status='scheduled',  # ستصبح live عند بدء الحصة
            created_by=created_by
            # لا نحتاج لتحديد jitsi_room_id - سيتم توليده تلقائياً في save()
        )

        # تحديث الحصة المجدولة
        self.status = 'converted_to_live'
        self.live_lesson_id = live_lesson.id
        self.save()

        return live_lesson

    def get_or_create_live_lesson(self, created_by):
        """الحصول على الحصة المباشرة أو إنشاؤها"""
        from lessons.models import LiveLesson

        if self.live_lesson_id:
            try:
                return LiveLesson.objects.get(id=self.live_lesson_id)
            except LiveLesson.DoesNotExist:
                pass

        # إنشاء حصة مباشرة جديدة
        return self.convert_to_live_lesson(created_by)

    def save(self, *args, **kwargs):
        """حفظ الحصة مع إرسال إشعارات"""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        # إرسال إشعارات عند إنشاء حصة جديدة
        if is_new:
            self._send_lesson_notifications()

    def _send_lesson_notifications(self):
        """إرسال إشعارات للطالب والمعلم عند إنشاء حصة جديدة"""
        try:
            from notifications.models import Notification
            from django.utils import timezone

            # إشعار للطالب
            Notification.objects.create(
                recipient=self.subscription.student,
                title="حصة جديدة مجدولة",
                message=f"تم جدولة حصة رقم {self.lesson_number} يوم {self.scheduled_date.strftime('%Y-%m-%d')} الساعة {self.scheduled_date.strftime('%H:%M')}",
                notification_type='lesson_scheduled'
            )

            # إشعار للمعلم إذا كان معين
            if self.teacher:
                Notification.objects.create(
                    recipient=self.teacher,
                    title="حصة جديدة مجدولة",
                    message=f"تم جدولة حصة رقم {self.lesson_number} مع الطالب {self.subscription.student.get_full_name()} يوم {self.scheduled_date.strftime('%Y-%m-%d')} الساعة {self.scheduled_date.strftime('%H:%M')}",
                    notification_type='lesson_scheduled'
                )
        except Exception as e:
            # تجاهل أخطاء الإشعارات لتجنب فشل إنشاء الحصة
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"فشل في إرسال إشعار إنشاء الحصة المجدولة: {e}")


# استيراد نماذج الفواتير
from .invoice_models import Invoice, InvoiceItem
