from django.core.management.base import BaseCommand
from django.utils import timezone
from subscriptions.services import ScheduledLessonToLiveService


class Command(BaseCommand):
    help = 'تحويل الحصص المجدولة التي حان موعدها إلى حصص مباشرة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='عرض الحصص التي سيتم تحويلها بدون تنفيذ التحويل',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS(
                f'بدء فحص الحصص المجدولة في {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'
            )
        )

        if options['dry_run']:
            self.stdout.write('تشغيل تجريبي - لن يتم تحويل أي حصص فعلياً')
            # يمكن إضافة منطق العرض التجريبي هنا
            return

        try:
            converted_count = ScheduledLessonToLiveService.check_and_convert_lessons()
            
            if converted_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'تم تحويل {converted_count} حصة مجدولة إلى حصص مباشرة بنجاح'
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING('لا توجد حصص مجدولة جاهزة للتحويل')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في تحويل الحصص: {str(e)}')
            )
