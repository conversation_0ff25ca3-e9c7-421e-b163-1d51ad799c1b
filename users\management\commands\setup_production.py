"""
أمر Django لإعداد النظام للإنتاج
يتم تشغيله تلقائياً عند النشر على Render
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.utils import timezone
from django.contrib.auth import get_user_model
from users.email_models import SMTPSettings, EmailTemplate, EmailEvent


class Command(BaseCommand):
    help = 'إعداد النظام للإنتاج - إنشاء البيانات الافتراضية'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-templates',
            action='store_true',
            help='تخطي إنشاء قوالب البريد الإلكتروني',
        )
        parser.add_argument(
            '--skip-smtp',
            action='store_true',
            help='تخطي إعداد SMTP',
        )

    def handle(self, *args, **options):
        self.stdout.write('🚀 بدء إعداد النظام للإنتاج...')
        self.stdout.write('=' * 60)
        
        # 1. تشغيل migrations
        self.stdout.write('📋 تشغيل migrations...')
        try:
            call_command('migrate', verbosity=0)
            self.stdout.write('✅ تم تشغيل migrations بنجاح')
        except Exception as e:
            self.stdout.write(f'❌ خطأ في migrations: {str(e)}')
        
        # 2. جمع الملفات الثابتة
        self.stdout.write('📁 جمع الملفات الثابتة...')
        try:
            call_command('collectstatic', '--noinput', verbosity=0)
            self.stdout.write('✅ تم جمع الملفات الثابتة بنجاح')
        except Exception as e:
            self.stdout.write(f'❌ خطأ في جمع الملفات الثابتة: {str(e)}')
        
        # 3. إعداد إعدادات SMTP
        if not options['skip_smtp']:
            self.stdout.write('📧 إعداد إعدادات البريد الإلكتروني...')
            try:
                call_command('setup_default_email_settings', verbosity=0)
                self.stdout.write('✅ تم إعداد إعدادات البريد الإلكتروني')
            except Exception as e:
                self.stdout.write(f'❌ خطأ في إعداد البريد الإلكتروني: {str(e)}')
        
        # 4. إنشاء قوالب البريد الإلكتروني
        if not options['skip_templates']:
            self.stdout.write('📝 إنشاء قوالب البريد الإلكتروني...')
            try:
                call_command('create_default_email_templates', verbosity=0)
                self.stdout.write('✅ تم إنشاء قوالب البريد الإلكتروني')
            except Exception as e:
                self.stdout.write(f'❌ خطأ في إنشاء القوالب: {str(e)}')
        
        # 5. التحقق من المدير العام
        self.stdout.write('👤 التحقق من المدير العام...')
        User = get_user_model()
        admin_users = User.objects.filter(user_type='admin', is_superuser=True)
        if admin_users.exists():
            self.stdout.write(f'✅ يوجد {admin_users.count()} مدير عام')
        else:
            self.stdout.write('⚠️ لا يوجد مدير عام - يرجى إنشاء مدير عام من Django Admin')
        
        # 6. عرض إحصائيات النظام
        self.stdout.write('📊 إحصائيات النظام:')
        self.stdout.write('-' * 40)
        
        # إحصائيات المستخدمين
        total_users = User.objects.count()
        admin_count = User.objects.filter(user_type='admin').count()
        teacher_count = User.objects.filter(user_type='teacher').count()
        student_count = User.objects.filter(user_type='student').count()
        
        self.stdout.write(f'👥 إجمالي المستخدمين: {total_users}')
        self.stdout.write(f'   - المديرين: {admin_count}')
        self.stdout.write(f'   - المعلمين: {teacher_count}')
        self.stdout.write(f'   - الطلاب: {student_count}')
        
        # إحصائيات البريد الإلكتروني
        smtp_count = SMTPSettings.objects.count()
        template_count = EmailTemplate.objects.count()
        event_count = EmailEvent.objects.count()
        active_templates = EmailTemplate.objects.filter(is_active=True).count()
        
        self.stdout.write(f'📧 نظام البريد الإلكتروني:')
        self.stdout.write(f'   - إعدادات SMTP: {smtp_count}')
        self.stdout.write(f'   - الأحداث: {event_count}')
        self.stdout.write(f'   - القوالب: {template_count}')
        self.stdout.write(f'   - القوالب النشطة: {active_templates}')
        
        # التحقق من حالة النظام
        smtp_active = SMTPSettings.objects.filter(is_active=True).exists()
        
        self.stdout.write('🔍 حالة النظام:')
        self.stdout.write('-' * 40)
        
        if smtp_active:
            self.stdout.write('✅ نظام البريد الإلكتروني: نشط')
        else:
            self.stdout.write('⚠️ نظام البريد الإلكتروني: غير نشط')
            self.stdout.write('   يرجى تفعيله من الإعدادات التقنية')
        
        if active_templates > 0:
            self.stdout.write(f'✅ قوالب البريد: {active_templates} قالب نشط')
        else:
            self.stdout.write('⚠️ قوالب البريد: لا توجد قوالب نشطة')
        
        # رسائل مهمة للمدير
        self.stdout.write('=' * 60)
        self.stdout.write('📋 خطوات مهمة بعد النشر:')
        self.stdout.write('1. الدخول إلى لوحة الإدارة')
        self.stdout.write('2. الذهاب إلى: الإعدادات التقنية > إعدادات البريد الإلكتروني')
        self.stdout.write('3. تعبئة بيانات SMTP (Gmail, Outlook, إلخ)')
        self.stdout.write('4. تفعيل نظام البريد الإلكتروني')
        self.stdout.write('5. اختبار إرسال بريد تجريبي')
        self.stdout.write('6. مراجعة قوالب البريد وتخصيصها حسب الحاجة')
        
        self.stdout.write('=' * 60)
        self.stdout.write(
            self.style.SUCCESS(
                '🎉 تم إعداد النظام للإنتاج بنجاح!'
                '\n🚀 النظام جاهز للاستخدام!'
            )
        )
