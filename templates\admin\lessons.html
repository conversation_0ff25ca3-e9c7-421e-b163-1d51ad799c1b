{% extends 'base.html' %}
{% load static %}

{% block title %}مركز مراقبة الحصص - {{ SITE_NAME }}{% endblock %}

{% block extra_css %}
<style>
    /* تحسينات التجاوب للجدول */
    @media (max-width: 768px) {
        .table-responsive {
            font-size: 0.875rem;
        }

        .table-responsive th,
        .table-responsive td {
            padding: 0.5rem 0.75rem !important;
        }

        .table-responsive .text-sm {
            font-size: 0.75rem !important;
        }

        /* إخفاء بعض الأعمدة على الشاشات الصغيرة */
        .hide-mobile {
            display: none;
        }
    }

    @media (max-width: 640px) {
        .stats-grid {
            grid-template-columns: 1fr !important;
        }

        .filter-grid {
            grid-template-columns: 1fr !important;
        }

        .action-buttons {
            flex-direction: column;
            gap: 0.5rem;
        }

        .action-buttons a,
        .action-buttons button {
            width: 100%;
            text-align: center;
        }
    }

    /* تحسين الأزرار على الشاشات الصغيرة */
    @media (max-width: 480px) {
        .btn-responsive {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.75rem !important;
        }

        .card-responsive {
            padding: 1rem !important;
        }

        .text-responsive {
            font-size: 1.5rem !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <!-- Enhanced Header -->
    <div class="bg-white shadow-lg border-b border-gray-200 mb-6">
        <div class="px-4 md:px-6 py-4 md:py-6">
            <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div class="flex-1">
                    <h1 class="text-2xl md:text-3xl font-bold text-islamic-primary flex items-center">
                        <i class="fas fa-desktop text-islamic-gold ml-2 md:ml-3"></i>
                        مركز مراقبة الحصص
                    </h1>
                    <p class="text-gray-600 mt-2 text-sm md:text-base">مراقبة شاملة للحصص المباشرة والعادية في الوقت الفعلي</p>
                </div>
                <div class="flex items-center space-x-2 md:space-x-4 space-x-reverse">
                    <div class="bg-green-100 text-green-800 px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm font-medium">
                        <i class="fas fa-wifi ml-1"></i>
                        متصل
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Live Lessons Statistics -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 md:gap-6 mb-8 px-4 md:px-0">
        <!-- الحصص المباشرة الجارية -->
        <div class="bg-red-600 text-white rounded-lg shadow-lg p-4 md:p-6 transform hover:scale-105 transition-transform">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <p class="text-white text-xs md:text-sm font-medium">الحصص المباشرة الجارية</p>
                    <p class="text-2xl md:text-3xl font-bold text-white" id="live-lessons-count">{{ live_lessons.count }}</p>
                    <p class="text-white text-xs mt-1">
                        <i class="fas fa-circle animate-pulse ml-1"></i>
                        مباشر الآن
                    </p>
                </div>
                <div class="p-2 md:p-3 bg-white bg-opacity-20 rounded-full">
                    <i class="fas fa-broadcast-tower text-lg md:text-2xl animate-pulse"></i>
                </div>
            </div>
        </div>

        <!-- الحصص المجدولة -->
        <div class="bg-blue-600 text-white rounded-lg shadow-lg p-4 md:p-6 transform hover:scale-105 transition-transform">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <p class="text-white text-xs md:text-sm font-medium">الحصص المجدولة</p>
                    <p class="text-2xl md:text-3xl font-bold text-white" id="scheduled-lessons-count">{{ scheduled_live_lessons.count }}</p>
                    <p class="text-white text-xs mt-1">
                        <i class="fas fa-calendar-alt ml-1"></i>
                        في الانتظار
                    </p>
                </div>
                <div class="p-2 md:p-3 bg-white bg-opacity-20 rounded-full">
                    <i class="fas fa-clock text-lg md:text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- الحصص العادية الجارية -->
        <div class="bg-green-600 text-white rounded-lg shadow-lg p-4 md:p-6 transform hover:scale-105 transition-transform">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <p class="text-white text-xs md:text-sm font-medium">الحصص العادية الجارية</p>
                    <p class="text-2xl md:text-3xl font-bold text-white" id="regular-lessons-count">{{ regular_lessons.count }}</p>
                    <p class="text-white text-xs mt-1">
                        <i class="fas fa-play ml-1"></i>
                        قيد التنفيذ
                    </p>
                </div>
                <div class="p-2 md:p-3 bg-white bg-opacity-20 rounded-full">
                    <i class="fas fa-chalkboard-teacher text-lg md:text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- إجمالي المعلمين النشطين -->
        <div class="bg-purple-600 text-white rounded-lg shadow-lg p-4 md:p-6 transform hover:scale-105 transition-transform">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <p class="text-white text-xs md:text-sm font-medium">المعلمون النشطون</p>
                    <p class="text-2xl md:text-3xl font-bold text-white" id="active-teachers-count">{{ active_teachers_count }}</p>
                    <p class="text-white text-xs mt-1">
                        <i class="fas fa-user-check ml-1"></i>
                        متصل الآن
                    </p>
                </div>
                <div class="p-2 md:p-3 bg-white bg-opacity-20 rounded-full">
                    <i class="fas fa-users text-lg md:text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- إجمالي الطلاب النشطين -->
        <div class="bg-orange-600 text-white rounded-lg shadow-lg p-4 md:p-6 transform hover:scale-105 transition-transform">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <p class="text-white text-xs md:text-sm font-medium">الطلاب النشطون</p>
                    <p class="text-2xl md:text-3xl font-bold text-white" id="active-students-count">{{ active_students_count }}</p>
                    <p class="text-white text-xs mt-1">
                        <i class="fas fa-graduation-cap ml-1"></i>
                        في الحصص
                    </p>
                </div>
                <div class="p-2 md:p-3 bg-white bg-opacity-20 rounded-full">
                    <i class="fas fa-user-graduate text-lg md:text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Lessons Real-time Monitoring -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-8 px-4 md:px-0">
        <!-- الحصص المباشرة الجارية -->
        <div class="bg-white rounded-lg shadow-lg border border-red-200">
            <div class="px-6 py-4 border-b border-red-200 bg-red-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-red-800 flex items-center">
                        <i class="fas fa-circle text-red-500 animate-pulse ml-2"></i>
                        الحصص المباشرة الجارية
                    </h3>
                    <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        {{ live_lessons.count }} حصة
                    </span>
                </div>
            </div>
            <div class="p-4 max-h-96 overflow-y-auto" id="live-lessons-container">
                {% if live_lessons %}
                    {% for lesson in live_lessons %}
                        <div class="border border-red-200 rounded-lg p-4 mb-3 bg-red-50 live-lesson-card" data-lesson-id="{{ lesson.id }}">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <h4 class="font-bold text-gray-800 mb-2">{{ lesson.title }}</h4>
                                    <div class="grid grid-cols-2 gap-2 text-sm">
                                        <div class="flex items-center">
                                            <i class="fas fa-chalkboard-teacher text-green-600 ml-1"></i>
                                            <span class="text-gray-800 font-semibold">{{ lesson.teacher.get_full_name }}</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-user-graduate text-blue-600 ml-1"></i>
                                            <span class="text-gray-800 font-semibold">{{ lesson.student.get_full_name }}</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-clock text-orange-600 ml-1"></i>
                                            <span class="text-gray-800 font-semibold">{{ lesson.duration_minutes }} دقيقة</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-play text-green-600 ml-1"></i>
                                            <span class="lesson-duration text-gray-800 font-semibold" data-start-time="{{ lesson.started_at|date:'c' }}">
                                                جارية منذ {{ lesson.started_at|timesince }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-2 mr-4">
                                    <a href="{% url 'live_lesson_room' lesson.id %}" target="_blank"
                                       class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors text-center">
                                        <i class="fas fa-eye ml-1"></i>
                                        مراقبة
                                    </a>
                                    <a href="{{ lesson.get_jitsi_url }}" target="_blank"
                                       class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors text-center">
                                        <i class="fas fa-video ml-1"></i>
                                        Jitsi
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-8 text-gray-600">
                        <i class="fas fa-video-slash text-4xl mb-4 text-gray-400"></i>
                        <h4 class="text-lg font-bold mb-2 text-gray-700">لا توجد حصص مباشرة جارية</h4>
                        <p class="text-gray-600 font-medium">سيتم عرض الحصص المباشرة هنا عند بدئها</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- الحصص المجدولة القادمة -->
        <div class="bg-white rounded-lg shadow-lg border border-blue-200">
            <div class="px-6 py-4 border-b border-blue-200 bg-blue-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-blue-800 flex items-center">
                        <i class="fas fa-calendar-alt text-blue-500 ml-2"></i>
                        الحصص المجدولة القادمة
                    </h3>
                    <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        {{ scheduled_live_lessons.count }} حصة
                    </span>
                </div>
            </div>
            <div class="p-4 max-h-96 overflow-y-auto" id="scheduled-lessons-container">
                {% if scheduled_live_lessons %}
                    {% for lesson in scheduled_live_lessons|slice:":10" %}
                        <div class="border border-blue-200 rounded-lg p-4 mb-3 bg-blue-50 scheduled-lesson-card" data-lesson-id="{{ lesson.id }}" data-scheduled-time="{{ lesson.scheduled_date|date:'c' }}">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center gap-2 mb-2">
                                        <h4 class="font-bold text-gray-800">{{ lesson.title }}</h4>
                                        {% if lesson.scheduled_date|timeuntil|slice:":1" == "-" %}
                                        <span class="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                                            متأخرة!
                                        </span>
                                        {% endif %}
                                    </div>
                                    <div class="grid grid-cols-2 gap-2 text-sm">
                                        <div class="flex items-center">
                                            <i class="fas fa-chalkboard-teacher text-green-600 ml-1"></i>
                                            <span class="text-gray-800 font-semibold">{{ lesson.teacher.get_full_name }}</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-user-graduate text-blue-600 ml-1"></i>
                                            <span class="text-gray-800 font-semibold">{{ lesson.student.get_full_name }}</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-calendar ml-1"></i>
                                            <span class="text-gray-800 font-semibold">{{ lesson.scheduled_date|date:"H:i" }}</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-hourglass-half ml-1"></i>
                                            <span class="countdown-timer text-gray-800 font-semibold" data-target="{{ lesson.scheduled_date|date:'c' }}">
                                                {% if lesson.scheduled_date|timeuntil|slice:":1" == "-" %}
                                                    متأخرة {{ lesson.scheduled_date|timesince }}
                                                {% else %}
                                                    تبدأ خلال {{ lesson.scheduled_date|timeuntil }}
                                                {% endif %}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-2 mr-4">
                                    {% if lesson.scheduled_date|timeuntil|slice:":1" == "-" %}
                                    <button onclick="startLiveLessonNow({{ lesson.id }})"
                                            class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors">
                                        <i class="fas fa-play ml-1"></i>
                                        بدء الآن
                                    </button>
                                    {% endif %}
                                    <a href="{% url 'live_lesson_room' lesson.id %}" target="_blank"
                                       class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors text-center">
                                        <i class="fas fa-eye ml-1"></i>
                                        عرض
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-8 text-gray-600">
                        <i class="fas fa-calendar-times text-4xl mb-4 text-gray-400"></i>
                        <h4 class="text-lg font-bold mb-2 text-gray-700">لا توجد حصص مجدولة</h4>
                        <p class="text-gray-600 font-medium">سيتم عرض الحصص المجدولة هنا</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- All Lessons Table -->
    <div class="bg-white rounded-lg shadow-lg border border-gray-200 mx-4 md:mx-0">
        <div class="px-4 md:px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <h3 class="text-lg md:text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-list text-islamic-primary ml-2"></i>
                    سجل جميع الحصص
                </h3>
                <div class="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4 md:space-x-reverse">
                    <form method="GET" class="w-full">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-4">
                            <!-- فلترة حسب الحالة -->
                            <select name="status" onchange="this.form.submit()"
                                    class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="">جميع الحالات</option>
                                {% for status_code, status_name in live_lesson_statuses %}
                                    <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>
                                        {{ status_name }}
                                    </option>
                                {% endfor %}
                            </select>

                            <!-- فلترة حسب المعلم -->
                            <select name="teacher" onchange="this.form.submit()"
                                    class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="">جميع المعلمين</option>
                                {% for teacher in available_teachers %}
                                    <option value="{{ teacher.id }}" {% if teacher_filter == teacher.id|stringformat:"s" %}selected{% endif %}>
                                        {{ teacher.get_full_name }}
                                    </option>
                                {% endfor %}
                            </select>

                            <!-- فلترة حسب التاريخ -->
                            <select name="date_filter" onchange="this.form.submit()"
                                    class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <option value="">جميع التواريخ</option>
                                <option value="today" {% if date_filter == 'today' %}selected{% endif %}>اليوم</option>
                                <option value="week" {% if date_filter == 'week' %}selected{% endif %}>هذا الأسبوع</option>
                                <option value="month" {% if date_filter == 'month' %}selected{% endif %}>هذا الشهر</option>
                            </select>

                            <!-- البحث -->
                            <div class="relative">
                                <input type="text" name="search" value="{{ search_query|default:'' }}"
                                       placeholder="البحث في الحصص..."
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 pr-10 text-sm focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <!-- زر البحث -->
                            <button type="submit" class="bg-islamic-primary hover:bg-islamic-light text-white px-4 py-2 rounded-lg text-sm transition-colors">
                                <i class="fas fa-search ml-1"></i>
                                بحث
                            </button>

                            <!-- زر إعادة تعيين -->
                            {% if status_filter or teacher_filter or date_filter or search_query %}
                                <a href="{% url 'admin_lessons' %}"
                                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                                    <i class="fas fa-times ml-1"></i>
                                    إعادة تعيين
                                </a>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto table-responsive">
            <table class="min-w-full divide-y divide-gray-200 text-sm">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 md:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحصة المباشرة</th>
                        <th class="px-3 md:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعلم</th>
                        <th class="px-3 md:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hide-mobile">الطالب</th>
                        <th class="px-3 md:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hide-mobile">التاريخ والوقت</th>
                        <th class="px-3 md:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hide-mobile">المدة</th>
                        <th class="px-3 md:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-3 md:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hide-mobile">النوع</th>
                        <th class="px-3 md:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="live-lessons-table">
                    {% if all_live_lessons %}
                        {% for lesson in all_live_lessons %}
                            <tr class="hover:bg-gray-50 {% if lesson.status == 'live' %}bg-red-50{% elif lesson.status == 'scheduled' %}bg-blue-50{% elif lesson.status == 'completed' %}bg-green-50{% elif lesson.status == 'cancelled' %}bg-gray-50{% endif %}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ lesson.title }}</div>
                                        <div class="text-sm text-gray-500">
                                            {% if lesson.description %}
                                                {{ lesson.description|truncatechars:50 }}
                                            {% else %}
                                                حصة مباشرة
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-gray-900">{{ lesson.teacher.get_full_name }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ lesson.student.get_full_name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        {% if lesson.status == 'live' and lesson.started_at %}
                                            <div class="font-medium text-red-600">بدأت: {{ lesson.started_at|date:"Y-m-d H:i" }}</div>
                                            <div class="text-xs text-gray-500">جارية منذ {{ lesson.started_at|timesince }}</div>
                                        {% elif lesson.scheduled_date %}
                                            <div>{{ lesson.scheduled_date|date:"Y-m-d H:i" }}</div>
                                            {% if lesson.status == 'scheduled' %}
                                                <div class="text-xs text-gray-500">
                                                    {% if lesson.scheduled_date|timeuntil|slice:":1" == "-" %}
                                                        متأخرة {{ lesson.scheduled_date|timesince }}
                                                    {% else %}
                                                        تبدأ خلال {{ lesson.scheduled_date|timeuntil }}
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-gray-400">غير محدد</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ lesson.duration_minutes }} دقيقة
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if lesson.status == 'live' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 animate-pulse">
                                            <i class="fas fa-circle text-xs ml-1"></i>
                                            جارية الآن
                                        </span>
                                    {% elif lesson.status == 'scheduled' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            <i class="fas fa-clock text-xs ml-1"></i>
                                            مجدولة
                                        </span>
                                    {% elif lesson.status == 'completed' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            <i class="fas fa-check text-xs ml-1"></i>
                                            مكتملة
                                        </span>
                                    {% elif lesson.status == 'cancelled' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            <i class="fas fa-times text-xs ml-1"></i>
                                            ملغية
                                        </span>
                                    {% else %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            {{ lesson.get_status_display }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                        <i class="fas fa-video text-xs ml-1"></i>
                                        حصة مباشرة
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2 space-x-reverse">
                                        {% if lesson.status == 'live' %}
                                            <a href="{% url 'live_lesson_room' lesson.id %}" target="_blank"
                                               class="text-green-600 hover:text-green-900 bg-green-100 px-2 py-1 rounded text-xs">
                                                <i class="fas fa-eye ml-1"></i>
                                                مراقبة
                                            </a>
                                            <a href="{{ lesson.get_jitsi_url }}" target="_blank"
                                               class="text-blue-600 hover:text-blue-900 bg-blue-100 px-2 py-1 rounded text-xs">
                                                <i class="fas fa-video ml-1"></i>
                                                Jitsi
                                            </a>
                                        {% elif lesson.status == 'scheduled' %}
                                            {% if lesson.scheduled_date|timeuntil|slice:":1" == "-" %}
                                                <button onclick="startLiveLessonNow({{ lesson.id }})"
                                                        class="text-green-600 hover:text-green-900 bg-green-100 px-2 py-1 rounded text-xs">
                                                    <i class="fas fa-play ml-1"></i>
                                                    بدء الآن
                                                </button>
                                            {% endif %}
                                            <a href="{% url 'live_lesson_room' lesson.id %}" target="_blank"
                                               class="text-blue-600 hover:text-blue-900 bg-blue-100 px-2 py-1 rounded text-xs">
                                                <i class="fas fa-eye ml-1"></i>
                                                عرض
                                            </a>
                                        {% else %}
                                            <a href="{% url 'live_lesson_room' lesson.id %}" target="_blank"
                                               class="text-gray-600 hover:text-gray-900 bg-gray-100 px-2 py-1 rounded text-xs">
                                                <i class="fas fa-info-circle ml-1"></i>
                                                تفاصيل
                                            </a>
                                        {% endif %}

                                        <!-- زر تقرير المعلم -->
                                        {% if lesson.status == 'ended' %}
                                            {% if lesson.teacher_report %}
                                                <a href="{% url 'admin_lesson_report_view' lesson.id %}"
                                                   class="text-blue-600 hover:text-blue-900 bg-blue-100 px-2 py-1 rounded text-xs">
                                                    <i class="fas fa-file-alt ml-1"></i>
                                                    تقرير الحصة
                                                </a>
                                            {% else %}
                                                <span class="text-gray-400 bg-gray-100 px-2 py-1 rounded text-xs cursor-not-allowed">
                                                    <i class="fas fa-file-alt ml-1"></i>
                                                    لا يوجد تقرير
                                                </span>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="8" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-video-slash text-4xl mb-4 text-gray-300"></i>
                                    <h3 class="text-lg font-medium mb-2">لا توجد حصص مباشرة</h3>
                                    <p class="text-gray-400">لم يتم إنشاء أي حصص مباشرة بعد</p>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    السابق
                </a>
                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    التالي
                </a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        عرض <span class="font-medium">1</span> إلى <span class="font-medium">4</span> من <span class="font-medium">156</span> نتيجة
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            1
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            2
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            3
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Updates JavaScript -->
<script>
// تحديث الوقت الفعلي
function updateRealTimeData() {
    // تحديث عدادات الوقت للحصص الجارية
    document.querySelectorAll('.lesson-duration').forEach(function(element) {
        const startTime = new Date(element.getAttribute('data-start-time'));
        const now = new Date();
        const diff = Math.floor((now - startTime) / 1000 / 60); // بالدقائق

        if (diff < 60) {
            element.textContent = `جارية منذ ${diff} دقيقة`;
        } else {
            const hours = Math.floor(diff / 60);
            const minutes = diff % 60;
            element.textContent = `جارية منذ ${hours} ساعة و ${minutes} دقيقة`;
        }
    });

    // تحديث العد التنازلي للحصص المجدولة
    document.querySelectorAll('.countdown-timer').forEach(function(element) {
        const targetTime = new Date(element.getAttribute('data-target'));
        const now = new Date();
        const diff = Math.floor((targetTime - now) / 1000 / 60); // بالدقائق

        if (diff < 0) {
            const overdue = Math.abs(diff);
            if (overdue < 60) {
                element.textContent = `متأخرة ${overdue} دقيقة`;
            } else {
                const hours = Math.floor(overdue / 60);
                const minutes = overdue % 60;
                element.textContent = `متأخرة ${hours} ساعة و ${minutes} دقيقة`;
            }
            element.parentElement.parentElement.parentElement.classList.add('border-orange-300', 'bg-orange-50');
        } else if (diff < 60) {
            element.textContent = `تبدأ خلال ${diff} دقيقة`;
        } else {
            const hours = Math.floor(diff / 60);
            const minutes = diff % 60;
            element.textContent = `تبدأ خلال ${hours} ساعة و ${minutes} دقيقة`;
        }
    });
}



// بدء حصة مباشرة الآن
function startLiveLessonNow(lessonId) {
    if (confirm('هل تريد بدء هذه الحصة الآن؟')) {
        fetch(`/api/lessons/live/${lessonId}/start/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم بدء الحصة بنجاح', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'فشل في بدء الحصة', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في بدء الحصة:', error);
            showNotification('حدث خطأ أثناء بدء الحصة', 'error');
        });
    }
}

// إظهار الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// تشغيل التحديثات التلقائية
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الوقت كل 30 ثانية
    updateRealTimeData();
    setInterval(updateRealTimeData, 30000);

    // إضافة CSRF token للطلبات
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (!csrfToken) {
        const token = document.createElement('input');
        token.type = 'hidden';
        token.name = 'csrfmiddlewaretoken';
        token.value = '{{ csrf_token }}';
        document.body.appendChild(token);
    }
});

// تحديث حالة الاتصال
function updateConnectionStatus() {
    const statusElement = document.querySelector('.bg-green-100');
    if (navigator.onLine) {
        statusElement.className = 'bg-green-100 text-green-800 px-4 py-2 rounded-lg text-sm font-medium';
        statusElement.innerHTML = '<i class="fas fa-wifi ml-1"></i> متصل';
    } else {
        statusElement.className = 'bg-red-100 text-red-800 px-4 py-2 rounded-lg text-sm font-medium';
        statusElement.innerHTML = '<i class="fas fa-wifi-slash ml-1"></i> غير متصل';
    }
}

// مراقبة حالة الاتصال
window.addEventListener('online', updateConnectionStatus);
window.addEventListener('offline', updateConnectionStatus);



// تحديث المؤشر عند تحميل الصفحة وكل دقيقة
document.addEventListener('DOMContentLoaded', function() {
    updateQualityAlertsIndicator();
    setInterval(updateQualityAlertsIndicator, 60000);
});
</script>
{% endblock %}
