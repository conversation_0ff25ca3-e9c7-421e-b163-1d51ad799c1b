services:
  - type: web
    name: qurania-lms
    env: python
    buildCommand: "pip install -r requirements.txt"
    startCommand: "chmod +x start.sh && ./start.sh"
    plan: free
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: DJAN<PERSON>O_SETTINGS_MODULE
        value: qurania_lms.settings_render
      - key: DEBUG
        value: "False"
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: qurania-db
          property: connectionString
      - key: SITE_URL
        value: "https://qurania-lms.onrender.com"
      - key: DJANGO_SUPERUSER_USERNAME
        value: "admin"
      - key: DJAN<PERSON>O_SUPERUSER_EMAIL
        value: "<EMAIL>"
      - key: DJANGO_SUPERUSER_PASSWORD
        value: "admin123456"
    
  - type: pserv
    name: qurania-db
    env: postgresql
    plan: free
    databaseName: qurania_lms
    user: qurania_user
