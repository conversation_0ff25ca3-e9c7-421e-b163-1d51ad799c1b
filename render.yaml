services:
  - type: web
    name: qurania-lms
    env: python
    buildCommand: "./build.sh"
    startCommand: "gunicorn qurania_lms.wsgi:application"
    plan: free
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: DJANGO_SETTINGS_MODULE
        value: qurania_lms.settings_render
      - key: DEBUG
        value: "False"
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: qurania-db
          property: connectionString
      - key: SITE_URL
        value: "https://qurania-lms.onrender.com"
    
  - type: pserv
    name: qurania-db
    env: postgresql
    plan: free
    databaseName: qurania_lms
    user: qurania_user
