{% extends 'base.html' %}
{% load static %}

{% block title %}إعدادات بوابات الدفع - {{ ACADEMY_SLOGAN }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
    .payment-gateway-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
    }

    .gateway-tab {
        border: 2px solid #e5e7eb;
        border-radius: 10px;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .gateway-tab:hover {
        border-color: #3b82f6;
        background-color: #f8fafc;
    }

    .gateway-tab.active {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }

    .gateway-content {
        display: none;
        background: white;
        border-radius: 10px;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .gateway-content.active {
        display: block;
    }

    /* أزرار الحالة الجديدة */
    .status-btn {
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .status-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .status-btn.active {
        border-color: rgba(255, 255, 255, 0.3);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .test-connection-btn {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .test-connection-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    .status-indicator {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .status-enabled {
        background-color: #d1fae5;
        color: #065f46;
    }

    .status-disabled {
        background-color: #fee2e2;
        color: #991b1b;
    }

    /* تحسين مظهر Toggle Switches */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
        margin: 0 8px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #e5e7eb;
        transition: all 0.3s ease;
        border-radius: 12px;
        border: 1px solid #d1d5db;
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: all 0.3s ease;
        border-radius: 50%;
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .toggle-switch input:checked + .toggle-slider {
        background-color: #10b981;
        border-color: #059669;
    }

    .toggle-switch input:checked + .toggle-slider:before {
        transform: translateX(26px);
        box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    }

    .toggle-switch input:focus + .toggle-slider {
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }

    /* ألوان مختلفة للبوابات */
    .paypal-toggle input:checked + .toggle-slider {
        background-color: #0070ba;
        border-color: #005ea6;
    }

    .paypal-toggle input:focus + .toggle-slider {
        box-shadow: 0 0 0 3px rgba(0, 112, 186, 0.1);
    }

    .stripe-toggle input:checked + .toggle-slider {
        background-color: #635bff;
        border-color: #5b52f0;
    }

    .stripe-toggle input:focus + .toggle-slider {
        box-shadow: 0 0 0 3px rgba(99, 91, 255, 0.1);
    }

    .bank-toggle input:checked + .toggle-slider {
        background-color: #16a34a;
        border-color: #15803d;
    }

    .bank-toggle input:focus + .toggle-slider {
        box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
    }

    /* تحسين النص بجانب Toggle */
    .toggle-status-text {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        margin-right: 8px;
        min-width: 40px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="payment-gateway-card">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">
                        <i class="fas fa-credit-card ml-3"></i>
                        إعدادات بوابات الدفع
                    </h1>
                    <p class="text-islamic-light-gold">إدارة وتكوين بوابات الدفع المختلفة للمنصة</p>
                </div>
                <div class="text-right">
                    <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2 mb-2">
                        <span class="text-islamic-gold font-semibold">الحالة العامة</span>
                    </div>
                    <div class="text-sm text-islamic-light-gold">
                        <i class="fas fa-shield-alt ml-1"></i>
                        جميع البيانات محمية ومشفرة
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Gateway Tabs -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- PayPal Tab -->
            <div class="gateway-tab" data-gateway="paypal" onclick="showGateway('paypal')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fab fa-paypal text-blue-600 text-2xl ml-3"></i>
                        <div>
                            <h3 class="font-bold text-gray-900">PayPal</h3>
                            <p class="text-sm text-gray-600">بوابة دفع PayPal</p>
                        </div>
                    </div>
                    <div class="status-indicator {% if payment_settings.paypal_enabled %}status-enabled{% else %}status-disabled{% endif %}">
                        {% if payment_settings.paypal_enabled %}
                            <i class="fas fa-check-circle ml-1"></i>مفعل
                        {% else %}
                            <i class="fas fa-times-circle ml-1"></i>معطل
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Stripe Tab -->
            <div class="gateway-tab" data-gateway="stripe" onclick="showGateway('stripe')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fab fa-stripe text-purple-600 text-2xl ml-3"></i>
                        <div>
                            <h3 class="font-bold text-gray-900">Stripe</h3>
                            <p class="text-sm text-gray-600">بوابة دفع Stripe</p>
                        </div>
                    </div>
                    <div class="status-indicator {% if payment_settings.stripe_enabled %}status-enabled{% else %}status-disabled{% endif %}">
                        {% if payment_settings.stripe_enabled %}
                            <i class="fas fa-check-circle ml-1"></i>مفعل
                        {% else %}
                            <i class="fas fa-times-circle ml-1"></i>معطل
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Bank Transfer Tab -->
            <div class="gateway-tab" data-gateway="bank" onclick="showGateway('bank')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-university text-green-600 text-2xl ml-3"></i>
                        <div>
                            <h3 class="font-bold text-gray-900">التحويل البنكي</h3>
                            <p class="text-sm text-gray-600">الدفع بالتحويل البنكي</p>
                        </div>
                    </div>
                    <div class="status-indicator {% if payment_settings.bank_transfer_enabled %}status-enabled{% else %}status-disabled{% endif %}">
                        {% if payment_settings.bank_transfer_enabled %}
                            <i class="fas fa-check-circle ml-1"></i>مفعل
                        {% else %}
                            <i class="fas fa-times-circle ml-1"></i>معطل
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- PayPal Settings -->
        <div id="paypal-content" class="gateway-content active">
            <form method="post" action="{% url 'save_payment_gateway_settings' %}" class="space-y-6">
                {% csrf_token %}
                <input type="hidden" name="gateway_type" value="paypal">
                
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 flex items-center">
                            <i class="fab fa-paypal text-blue-600 ml-3"></i>
                            إعدادات PayPal
                        </h3>
                        <p class="text-gray-600 mt-1">تكوين بوابة دفع PayPal</p>
                    </div>
                    <div class="flex items-center justify-end space-x-3">
                        <span class="toggle-status-text">
                            {% if payment_settings.paypal_enabled %}مفعل{% else %}معطل{% endif %}
                        </span>
                        <label class="toggle-switch paypal-toggle">
                            <input type="checkbox" name="paypal_enabled" value="1"
                                   {% if payment_settings.paypal_enabled %}checked{% endif %}
                                   id="paypal_enabled_input"
                                   onchange="togglePaymentStatus('paypal', this.checked)">
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="text-sm font-medium text-gray-700">الحالة</span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Client ID <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="paypal_client_id" 
                               value="{{ payment_settings.paypal_client_id|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="أدخل PayPal Client ID">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Client Secret <span class="text-red-500">*</span>
                        </label>
                        <input type="password" name="paypal_client_secret" 
                               value="{{ payment_settings.paypal_client_secret|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="أدخل PayPal Client Secret">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Webhook ID
                        </label>
                        <input type="text" name="paypal_webhook_id" 
                               value="{{ payment_settings.paypal_webhook_id|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="أدخل PayPal Webhook ID">
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" name="paypal_sandbox_mode" 
                               {% if payment_settings.paypal_sandbox_mode %}checked{% endif %}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-3">
                        <label class="text-sm font-medium text-gray-700">
                            وضع التجربة (Sandbox Mode)
                        </label>
                    </div>
                </div>

                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <button type="button" onclick="testConnection('paypal')" 
                            class="test-connection-btn">
                        <i class="fas fa-plug ml-2"></i>
                        اختبار الاتصال
                    </button>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        حفظ إعدادات PayPal
                    </button>
                </div>
            </form>
        </div>

        <!-- Stripe Settings -->
        <div id="stripe-content" class="gateway-content">
            <form method="post" action="{% url 'save_payment_gateway_settings' %}" class="space-y-6">
                {% csrf_token %}
                <input type="hidden" name="gateway_type" value="stripe">

                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 flex items-center">
                            <i class="fab fa-stripe text-purple-600 ml-3"></i>
                            إعدادات Stripe
                        </h3>
                        <p class="text-gray-600 mt-1">تكوين بوابة دفع Stripe</p>
                    </div>
                    <div class="flex items-center justify-end space-x-3">
                        <span class="toggle-status-text">
                            {% if payment_settings.stripe_enabled %}مفعل{% else %}معطل{% endif %}
                        </span>
                        <label class="toggle-switch stripe-toggle">
                            <input type="checkbox" name="stripe_enabled" value="1"
                                   {% if payment_settings.stripe_enabled %}checked{% endif %}
                                   id="stripe_enabled_input"
                                   onchange="togglePaymentStatus('stripe', this.checked)">
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="text-sm font-medium text-gray-700">الحالة</span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Publishable Key <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="stripe_publishable_key"
                               value="{{ payment_settings.stripe_publishable_key|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                               placeholder="أدخل Stripe Publishable Key">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Secret Key <span class="text-red-500">*</span>
                        </label>
                        <input type="password" name="stripe_secret_key"
                               value="{{ payment_settings.stripe_secret_key|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                               placeholder="أدخل Stripe Secret Key">
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Webhook Secret
                        </label>
                        <input type="password" name="stripe_webhook_secret"
                               value="{{ payment_settings.stripe_webhook_secret|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                               placeholder="أدخل Stripe Webhook Secret">
                    </div>
                </div>

                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <button type="button" onclick="testConnection('stripe')"
                            class="test-connection-btn">
                        <i class="fas fa-plug ml-2"></i>
                        اختبار الاتصال
                    </button>
                    <button type="submit"
                            class="bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        حفظ إعدادات Stripe
                    </button>
                </div>
            </form>
        </div>

        <!-- Bank Transfer Settings -->
        <div id="bank-content" class="gateway-content">
            <form method="post" action="{% url 'save_payment_gateway_settings' %}" class="space-y-6">
                {% csrf_token %}
                <input type="hidden" name="gateway_type" value="bank">

                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 flex items-center">
                            <i class="fas fa-university text-green-600 ml-3"></i>
                            إعدادات التحويل البنكي
                        </h3>
                        <p class="text-gray-600 mt-1">تكوين بيانات الحساب البنكي</p>
                    </div>
                    <div class="flex items-center justify-end space-x-3">
                        <span class="toggle-status-text">
                            {% if payment_settings.bank_transfer_enabled %}مفعل{% else %}معطل{% endif %}
                        </span>
                        <label class="toggle-switch bank-toggle">
                            <input type="checkbox" name="bank_transfer_enabled" value="1"
                                   {% if payment_settings.bank_transfer_enabled %}checked{% endif %}
                                   id="bank_enabled_input"
                                   onchange="togglePaymentStatus('bank', this.checked)">
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="text-sm font-medium text-gray-700">الحالة</span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            اسم البنك <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="bank_name"
                               value="{{ payment_settings.bank_name|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="مثال: البنك الأهلي السعودي">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الحساب <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="bank_account_number"
                               value="{{ payment_settings.bank_account_number|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="أدخل رقم الحساب البنكي">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            اسم صاحب الحساب <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="bank_account_name"
                               value="{{ payment_settings.bank_account_name|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="أدخل اسم صاحب الحساب">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            رقم IBAN
                        </label>
                        <input type="text" name="bank_iban"
                               value="{{ payment_settings.bank_iban|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="*********************">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            رمز SWIFT
                        </label>
                        <input type="text" name="bank_swift_code"
                               value="{{ payment_settings.bank_swift_code|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="أدخل رمز SWIFT">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            الفرع
                        </label>
                        <input type="text" name="bank_branch"
                               value="{{ payment_settings.bank_branch|default:'' }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="أدخل اسم الفرع">
                    </div>
                </div>

                <div class="flex items-center justify-end pt-6 border-t border-gray-200">
                    <button type="submit"
                            class="bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        حفظ إعدادات التحويل البنكي
                    </button>
                </div>
            </form>
        </div>

    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات النظام
let isManualUpdate = false;
let currentGateway = 'paypal';
let paymentGatewaySettings = {
    paypal: { enabled: false },
    stripe: { enabled: false },
    bank: { enabled: false }
};

// إدارة التبويبات
function showGateway(gateway) {
    // إخفاء جميع المحتويات
    document.querySelectorAll('.gateway-content').forEach(content => {
        content.classList.remove('active');
    });

    // إزالة التحديد من جميع التبويبات
    document.querySelectorAll('.gateway-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // إظهار المحتوى المحدد
    document.getElementById(gateway + '-content').classList.add('active');

    // تحديد التبويب المحدد
    document.querySelector(`[data-gateway="${gateway}"]`).classList.add('active');
}

// اختبار الاتصال مع بوابات الدفع
function testConnection(gateway) {
    const button = event.target;
    const originalText = button.innerHTML;

    // تغيير النص إلى حالة التحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الاختبار...';
    button.disabled = true;

    // إرسال طلب اختبار الاتصال
    fetch(`/dashboard/admin/test-payment-gateway/${gateway}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            gateway: gateway
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            Swal.fire({
                title: 'نجح الاختبار!',
                text: data.message,
                icon: 'success',
                confirmButtonText: 'ممتاز',
                confirmButtonColor: '#10b981'
            });
        } else {
            Swal.fire({
                title: 'فشل الاختبار',
                text: data.message,
                icon: 'error',
                confirmButtonText: 'حسناً',
                confirmButtonColor: '#ef4444'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        let errorMessage = 'حدث خطأ أثناء اختبار الاتصال';

        if (error.message.includes('404')) {
            errorMessage = 'خطأ في الرابط - تأكد من أن الخادم يعمل بشكل صحيح';
        } else if (error.message.includes('500')) {
            errorMessage = 'خطأ في الخادم - تحقق من إعدادات الخادم';
        } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
            errorMessage = 'خطأ في الشبكة - تحقق من الاتصال بالإنترنت';
        }

        Swal.fire({
            title: 'خطأ في الاتصال',
            text: errorMessage,
            icon: 'error',
            confirmButtonText: 'حسناً',
            confirmButtonColor: '#ef4444'
        });
    })
    .finally(() => {
        // إعادة النص الأصلي
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// تأكيد الحفظ
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const gatewayType = this.querySelector('[name="gateway_type"]').value;
        let gatewayName = '';

        switch(gatewayType) {
            case 'paypal':
                gatewayName = 'PayPal';
                break;
            case 'stripe':
                gatewayName = 'Stripe';
                break;
            case 'bank':
                gatewayName = 'التحويل البنكي';
                break;
        }

        Swal.fire({
            title: 'تأكيد الحفظ',
            text: `هل تريد حفظ إعدادات ${gatewayName}؟`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، احفظ',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#3b82f6',
            cancelButtonColor: '#6b7280'
        }).then((result) => {
            if (result.isConfirmed) {
                // إرسال النموذج
                const formData = new FormData(this);

                fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: 'تم الحفظ!',
                            text: data.message,
                            icon: 'success',
                            confirmButtonText: 'ممتاز',
                            confirmButtonColor: '#10b981'
                        }).then(() => {
                            console.log('تم حفظ الإعدادات بنجاح:', data.current_status);

                            // تحديث الإعدادات المحلية
                            if (data.current_status) {
                                paymentGatewaySettings.paypal.enabled = data.current_status.paypal;
                                paymentGatewaySettings.stripe.enabled = data.current_status.stripe;
                                paymentGatewaySettings.bank.enabled = data.current_status.bank;

                                console.log('الإعدادات المحدثة:', paymentGatewaySettings);
                            }

                            // إرسال تحديث فوري لصفحة الاشتراكات
                            if (window.BroadcastChannel) {
                                const channel = new BroadcastChannel('payment_gateway_updates');
                                channel.postMessage({
                                    type: 'payment_gateway_status_updated',
                                    data: {
                                        paypal: { enabled: paymentGatewaySettings.paypal.enabled },
                                        stripe: { enabled: paymentGatewaySettings.stripe.enabled },
                                        bank_transfer: { enabled: paymentGatewaySettings.bank.enabled }
                                    },
                                    source: 'admin_settings'
                                });
                            }

                            // إعادة تعيين علامة التحديث اليدوي بعد فترة قصيرة
                            setTimeout(() => {
                                isManualUpdate = false;
                                console.log('تم إعادة تعيين علامة التحديث اليدوي بعد الحفظ');
                            }, 2000);
                        });
                    } else {
                        Swal.fire({
                            title: 'خطأ في الحفظ',
                            text: data.message || 'حدث خطأ أثناء حفظ الإعدادات',
                            icon: 'error',
                            confirmButtonText: 'حسناً',
                            confirmButtonColor: '#ef4444'
                        });
                    }
                })
                .catch(error => {
                    console.error('خطأ في الحفظ:', error);
                    Swal.fire({
                        title: 'خطأ في الحفظ',
                        text: 'حدث خطأ أثناء حفظ الإعدادات',
                        icon: 'error',
                        confirmButtonText: 'حسناً',
                        confirmButtonColor: '#ef4444'
                    });
                });
            }
        });
    });
});

// دالة تبديل حالة بوابة الدفع
function togglePaymentStatus(gateway, enabled) {
    console.log(`تبديل حالة ${gateway} إلى ${enabled ? 'مفعل' : 'معطل'}`);

    // تعيين علامة التحديث اليدوي
    isManualUpdate = true;

    // تحديث الإعدادات المحلية فوراً
    paymentGatewaySettings[gateway].enabled = enabled;

    // تحديث الحقل المخفي
    const hiddenInput = document.getElementById(`${gateway}_enabled_input`);
    if (hiddenInput) {
        if (hiddenInput.type === 'checkbox') {
            hiddenInput.checked = enabled;
        } else {
            hiddenInput.value = enabled ? '1' : '0';
        }
    }

    // تحديث النص بجانب toggle switch
    const toggleContainer = hiddenInput?.closest('div');
    const toggleLabel = toggleContainer?.querySelector('.toggle-status-text');
    if (toggleLabel) {
        toggleLabel.textContent = enabled ? 'مفعل' : 'معطل';
    }

    // تحديث مؤشر التبويب
    const statusIndicator = document.querySelector(`[data-gateway="${gateway}"] .status-indicator`);
    if (statusIndicator) {
        if (enabled) {
            statusIndicator.className = 'status-indicator status-enabled';
            statusIndicator.innerHTML = '<i class="fas fa-check-circle ml-1"></i>مفعل';
        } else {
            statusIndicator.className = 'status-indicator status-disabled';
            statusIndicator.innerHTML = '<i class="fas fa-times-circle ml-1"></i>معطل';
        }
    }

    // إرسال إشعار فوري لصفحة الاشتراكات عند التبديل
    if (window.BroadcastChannel) {
        const channel = new BroadcastChannel('payment_gateway_updates');
        channel.postMessage({
            type: 'payment_gateway_status_updated',
            data: {
                paypal: { enabled: paymentGatewaySettings.paypal.enabled },
                stripe: { enabled: paymentGatewaySettings.stripe.enabled },
                bank_transfer: { enabled: paymentGatewaySettings.bank.enabled }
            },
            source: 'admin_toggle',
            gateway_changed: gateway,
            new_status: enabled
        });
        console.log(`📡 تم إرسال إشعار التبديل لـ ${gateway} (${enabled ? 'مفعل' : 'معطل'})`);
    }

    // إعادة تعيين علامة التحديث اليدوي بعد فترة قصيرة
    setTimeout(() => {
        isManualUpdate = false;
        console.log('تم إعادة تعيين علامة التحديث اليدوي');
    }, 3000);
}

// تحديث حالة بوابات الدفع من الخادم
function updatePaymentGatewayStatus() {
    // تجاهل التحديث إذا كان هناك تحديث يدوي جاري
    if (isManualUpdate) {
        console.log('تجاهل التحديث التلقائي - يوجد تحديث يدوي جاري');
        return;
    }

    fetch('/api/payment-gateway-status/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // التحقق من وجود تغييرات فعلية قبل التحديث
                const hasChanges =
                    paymentGatewaySettings.paypal.enabled !== data.payment_methods.paypal.enabled ||
                    paymentGatewaySettings.stripe.enabled !== data.payment_methods.stripe.enabled ||
                    paymentGatewaySettings.bank.enabled !== data.payment_methods.bank_transfer.enabled;

                if (hasChanges) {
                    console.log('تم اكتشاف تغييرات في إعدادات الدفع - تحديث الواجهة');

                    // تحديث الإعدادات المحلية
                    paymentGatewaySettings.paypal.enabled = data.payment_methods.paypal.enabled;
                    paymentGatewaySettings.stripe.enabled = data.payment_methods.stripe.enabled;
                    paymentGatewaySettings.bank.enabled = data.payment_methods.bank_transfer.enabled;

                    // تحديث حالة PayPal
                    updateGatewayUI('paypal', data.payment_methods.paypal.enabled);

                    // تحديث حالة Stripe
                    updateGatewayUI('stripe', data.payment_methods.stripe.enabled);

                    // تحديث حالة التحويل البنكي
                    updateGatewayUI('bank', data.payment_methods.bank_transfer.enabled);

                    // إرسال إشعار للصفحات الأخرى (إذا كانت مفتوحة)
                    if (window.BroadcastChannel) {
                        const channel = new BroadcastChannel('payment_gateway_updates');
                        channel.postMessage({
                            type: 'payment_gateway_status_updated',
                            data: data.payment_methods,
                            source: 'auto_update'
                        });
                    }
                }
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث حالة بوابات الدفع:', error);
        });
}

// دالة موحدة لتحديث واجهة المستخدم لبوابة دفع
function updateGatewayUI(gateway, enabled) {
    // تجنب التحديث إذا كان هناك تحديث يدوي جاري
    if (isManualUpdate) {
        console.log(`تجاهل تحديث واجهة ${gateway} - يوجد تحديث يدوي جاري`);
        return;
    }

    // التحقق من وجود تغيير فعلي
    if (paymentGatewaySettings[gateway].enabled === enabled) {
        return; // لا يوجد تغيير
    }

    console.log(`تحديث واجهة ${gateway} إلى ${enabled ? 'مفعل' : 'معطل'}`);

    // تحديث الإعدادات المحلية
    paymentGatewaySettings[gateway].enabled = enabled;

    // تحديث toggle switch
    const toggleInput = document.getElementById(`${gateway}_enabled_input`);
    if (toggleInput) {
        if (toggleInput.type === 'checkbox') {
            toggleInput.checked = enabled;
        } else {
            toggleInput.value = enabled ? '1' : '0';
        }
    }

    // تحديث النص بجانب toggle switch
    const toggleContainer = toggleInput?.closest('div');
    const toggleLabel = toggleContainer?.querySelector('.toggle-status-text');
    if (toggleLabel) {
        toggleLabel.textContent = enabled ? 'مفعل' : 'معطل';
    }

    // تحديث مؤشر التبويب
    const statusIndicator = document.querySelector(`[data-gateway="${gateway}"] .status-indicator`);
    if (statusIndicator) {
        if (enabled) {
            statusIndicator.className = 'status-indicator status-enabled';
            statusIndicator.innerHTML = '<i class="fas fa-check-circle ml-1"></i>مفعل';
        } else {
            statusIndicator.className = 'status-indicator status-disabled';
            statusIndicator.innerHTML = '<i class="fas fa-times-circle ml-1"></i>معطل';
        }
    }
}


// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة صفحة إعدادات بوابات الدفع...');

    // تهيئة الإعدادات المحلية من الخادم
    paymentGatewaySettings = {
        paypal: { enabled: {{ payment_settings.paypal_enabled|yesno:"true,false" }} },
        stripe: { enabled: {{ payment_settings.stripe_enabled|yesno:"true,false" }} },
        bank: { enabled: {{ payment_settings.bank_transfer_enabled|yesno:"true,false" }} }
    };

    console.log('الإعدادات الأولية:', paymentGatewaySettings);

    // عرض التبويب الأول (PayPal)
    showGateway('paypal');

    // تحديث حالة بوابات الدفع عند تحميل الصفحة (بعد تأخير قصير)
    setTimeout(() => {
        updatePaymentGatewayStatus();
    }, 1000);

    // إرسال الحالة الحالية لصفحة الاشتراكات
    if (window.BroadcastChannel) {
        const channel = new BroadcastChannel('payment_gateway_updates');
        channel.postMessage({
            type: 'payment_gateway_status_updated',
            data: {
                paypal: { enabled: paymentGatewaySettings.paypal.enabled },
                stripe: { enabled: paymentGatewaySettings.stripe.enabled },
                bank_transfer: { enabled: paymentGatewaySettings.bank.enabled }
            },
            source: 'admin_settings_init'
        });
    }

    console.log('✅ تم تهيئة صفحة إعدادات بوابات الدفع بنجاح');
});
</script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{% endblock %}
