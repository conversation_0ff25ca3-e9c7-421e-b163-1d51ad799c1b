<!-- نافذة منبثقة لتقرير المعلم بعد انتهاء الحصة -->
<div id="teacherReportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[9999]" style="display: none;">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white z-[10000]">
        <div class="mt-3">
            <!-- Header -->
            <div class="flex items-center justify-between pb-4 border-b">
                <h3 class="text-lg font-bold text-islamic-primary">
                    <i class="fas fa-clipboard-list ml-2"></i>
                    تقرير الحصة
                </h3>
                <button type="button" onclick="closeTeacherReportModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Form -->
            <form id="teacherReportForm" class="mt-6">
                {% csrf_token %}
                <input type="hidden" id="lessonId" name="lesson_id" value="">

                <!-- تقييمات الطالب -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-800 mb-4 border-b pb-2">تقييم أداء الطالب</h4>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <!-- أداء الطالب -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">أداء الطالب</label>
                            <div class="flex space-x-1 space-x-reverse">
                                {% for i in "12345" %}
                                <button type="button" class="star-rating" data-field="student_performance" data-value="{{ i }}">
                                    <i class="fas fa-star text-gray-300 hover:text-yellow-400 text-xl"></i>
                                </button>
                                {% endfor %}
                            </div>
                            <input type="hidden" name="student_performance" id="student_performance" required>
                        </div>

                        <!-- مستوى التفاعل -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">مستوى التفاعل</label>
                            <div class="flex space-x-1 space-x-reverse">
                                {% for i in "12345" %}
                                <button type="button" class="star-rating" data-field="student_participation" data-value="{{ i }}">
                                    <i class="fas fa-star text-gray-300 hover:text-yellow-400 text-xl"></i>
                                </button>
                                {% endfor %}
                            </div>
                            <input type="hidden" name="student_participation" id="student_participation" required>
                        </div>

                        <!-- مستوى الفهم -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">مستوى الفهم</label>
                            <div class="flex space-x-1 space-x-reverse">
                                {% for i in "12345" %}
                                <button type="button" class="star-rating" data-field="student_understanding" data-value="{{ i }}">
                                    <i class="fas fa-star text-gray-300 hover:text-yellow-400 text-xl"></i>
                                </button>
                                {% endfor %}
                            </div>
                            <input type="hidden" name="student_understanding" id="student_understanding" required>
                        </div>
                    </div>
                </div>

                <!-- نقاط القوة -->
                <div class="mb-4">
                    <label for="strengths" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-thumbs-up text-green-500 ml-1"></i>
                        نقاط القوة
                    </label>
                    <textarea name="strengths" id="strengths" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary"
                              placeholder="اذكر نقاط القوة التي لاحظتها على الطالب..."></textarea>
                </div>

                <!-- نقاط تحتاج تحسين -->
                <div class="mb-4">
                    <label for="areas_for_improvement" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-arrow-up text-orange-500 ml-1"></i>
                        نقاط تحتاج تحسين
                    </label>
                    <textarea name="areas_for_improvement" id="areas_for_improvement" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary"
                              placeholder="اذكر النقاط التي يحتاج الطالب لتحسينها..."></textarea>
                </div>

                <!-- ملخص الحصة -->
                <div class="mb-4">
                    <label for="lesson_summary" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-book text-blue-500 ml-1"></i>
                        ملخص الحصة
                    </label>
                    <textarea name="lesson_summary" id="lesson_summary" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary"
                              placeholder="ملخص مختصر عما تم تدريسه في الحصة..."></textarea>
                </div>

                <!-- الواجبات المطلوبة -->
                <div class="mb-4">
                    <label for="homework_assigned" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-tasks text-purple-500 ml-1"></i>
                        الواجبات المطلوبة (اختياري)
                    </label>
                    <textarea name="homework_assigned" id="homework_assigned" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary"
                              placeholder="الواجبات أو المهام المطلوبة من الطالب..."></textarea>
                </div>

                <!-- توصيات للحصص القادمة -->
                <div class="mb-4">
                    <label for="recommendations" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lightbulb text-yellow-500 ml-1"></i>
                        توصيات للحصص القادمة (اختياري)
                    </label>
                    <textarea name="recommendations" id="recommendations" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary"
                              placeholder="توصيات لتحسين الحصص القادمة..."></textarea>
                </div>

                <!-- تقييم عام للحصة -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-star text-yellow-500 ml-1"></i>
                        تقييم عام للحصة
                    </label>
                    <div class="flex space-x-1 space-x-reverse">
                        {% for i in "12345" %}
                        <button type="button" class="star-rating" data-field="overall_lesson_rating" data-value="{{ i }}">
                            <i class="fas fa-star text-gray-300 hover:text-yellow-400 text-2xl"></i>
                        </button>
                        {% endfor %}
                    </div>
                    <input type="hidden" name="overall_lesson_rating" id="overall_lesson_rating" required>
                </div>

                <!-- ملاحظات إضافية -->
                <div class="mb-6">
                    <label for="additional_notes" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-sticky-note text-gray-500 ml-1"></i>
                        ملاحظات إضافية (اختياري)
                    </label>
                    <textarea name="additional_notes" id="additional_notes" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary"
                              placeholder="أي ملاحظات إضافية..."></textarea>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="flex justify-end space-x-3 space-x-reverse pt-4 border-t">
                    <button type="button" onclick="closeTeacherReportModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                        إلغاء
                    </button>
                    <button type="submit"
                            class="px-6 py-2 bg-islamic-primary text-white rounded-md hover:bg-islamic-light transition-colors">
                        <i class="fas fa-save ml-1"></i>
                        حفظ التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let currentLessonId = null;

// فتح النافذة المنبثقة
function openTeacherReportModal(lessonId) {
    currentLessonId = lessonId;
    document.getElementById('lessonId').value = lessonId;

    const modal = document.getElementById('teacherReportModal');
    modal.style.display = 'block';
    modal.style.zIndex = '99999';
    modal.style.position = 'fixed';

    // التأكد من أن النافذة في المقدمة
    setTimeout(() => {
        modal.style.zIndex = '99999';
        const innerDiv = modal.querySelector('div');
        if (innerDiv) {
            innerDiv.style.zIndex = '100000';
            innerDiv.style.position = 'relative';
        }
    }, 10);

    document.body.style.overflow = 'hidden'; // منع التمرير في الخلفية
}

// إغلاق النافذة المنبثقة
function closeTeacherReportModal() {
    document.getElementById('teacherReportModal').style.display = 'none';
    document.body.style.overflow = 'auto'; // إعادة التمرير
    resetForm();
}

// إضافة CSS لضمان ظهور النافذة في المقدمة
const modalStyle = document.createElement('style');
modalStyle.textContent = `
    #teacherReportModal {
        z-index: 99999 !important;
        position: fixed !important;
    }
    #teacherReportModal > div {
        z-index: 100000 !important;
        position: relative !important;
    }
`;
document.head.appendChild(modalStyle);

// إعادة تعيين النموذج
function resetForm() {
    document.getElementById('teacherReportForm').reset();
    // إعادة تعيين النجوم
    document.querySelectorAll('.star-rating i').forEach(star => {
        star.className = 'fas fa-star text-gray-300 hover:text-yellow-400 text-xl';
    });
    // إعادة تعيين الحقول المخفية
    document.querySelectorAll('input[type="hidden"]').forEach(input => {
        if (input.name !== 'lesson_id') {
            input.value = '';
        }
    });
}

// معالجة تقييم النجوم
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.star-rating').forEach(button => {
        button.addEventListener('click', function() {
            const field = this.dataset.field;
            const value = this.dataset.value;
            const container = this.parentElement;

            // تحديث الحقل المخفي
            document.getElementById(field).value = value;

            // تحديث النجوم
            container.querySelectorAll('.star-rating').forEach((star, index) => {
                const starIcon = star.querySelector('i');
                if (index < value) {
                    starIcon.className = 'fas fa-star text-yellow-400 text-xl';
                } else {
                    starIcon.className = 'fas fa-star text-gray-300 hover:text-yellow-400 text-xl';
                }
            });
        });
    });

    // معالجة إرسال النموذج
    document.getElementById('teacherReportForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitTeacherReport();
    });
});

// إرسال التقرير
function submitTeacherReport() {
    const form = document.getElementById('teacherReportForm');
    const formData = new FormData(form);

    // التحقق من التقييمات المطلوبة
    const requiredRatings = ['student_performance', 'student_participation', 'student_understanding', 'overall_lesson_rating'];
    let isValid = true;
    let missingFields = [];

    requiredRatings.forEach(field => {
        if (!formData.get(field)) {
            isValid = false;
            missingFields.push(field);
        }
    });

    // التحقق من النصوص المطلوبة
    const requiredTexts = ['strengths', 'areas_for_improvement', 'lesson_summary'];
    requiredTexts.forEach(field => {
        const value = formData.get(field);
        if (!value || !value.trim()) {
            isValid = false;
            missingFields.push(field);
        }
    });

    if (!isValid) {
        alert('يرجى ملء جميع الحقول المطلوبة: ' + missingFields.join(', '));
        return;
    }

    // التحقق من وجود lesson_id
    if (!formData.get('lesson_id')) {
        alert('خطأ: معرف الحصة غير موجود');
        return;
    }

    // إظهار مؤشر التحميل
    const submitBtn = document.querySelector('#teacherReportForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري الحفظ...';
    submitBtn.disabled = true;

    console.log('Submitting report with data:', Object.fromEntries(formData));

    // الحصول على CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    if (!csrfToken) {
        alert('خطأ: CSRF token غير موجود');
        return;
    }

    fetch('/api/teacher/lesson-report/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': csrfToken,
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            alert('تم حفظ التقرير بنجاح!');
            closeTeacherReportModal();
            // إعادة توجيه إلى لوحة تحكم المعلم
            window.location.href = '/dashboard/teacher/';
        } else {
            alert('حدث خطأ: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('Error details:', error);
        alert('حدث خطأ في الاتصال: ' + error.message);
    })
    .finally(() => {
        // إعادة تعيين الزر
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// إغلاق النافذة عند الضغط خارجها
document.getElementById('teacherReportModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeTeacherReportModal();
    }
});
</script>
