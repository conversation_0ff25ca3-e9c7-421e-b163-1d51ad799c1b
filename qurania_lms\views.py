"""
Views for API endpoints
"""

from django.http import JsonResponse
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Q, Count
from django.utils import timezone
from datetime import timedelta

from notifications.models import Notification
from messaging.models import Conversation, ChatMessage


@login_required
def notifications_count_api(request):
    """API لإرجاع عدد الإشعارات والرسائل غير المقروءة"""
    if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'error': 'Invalid request'}, status=400)

    user = request.user

    # عدد الإشعارات غير المقروءة
    unread_notifications = Notification.objects.filter(
        recipient=user,
        is_read=False
    ).count()

    # عدد الرسائل غير المقروءة
    if user.user_type == 'admin':
        conversations = Conversation.objects.filter(is_active=True)
    else:
        conversations = Conversation.objects.filter(
            Q(participant1=user) | Q(participant2=user) |
            Q(student=user) | Q(teacher=user),
            is_active=True
        )

    unread_messages = 0
    for conversation in conversations:
        unread_messages += conversation.get_unread_count_for_user(user)

    # التحقق من المسار الحالي لمنع الإشعارات المنبثقة في صفحات المحادثات
    referer = request.META.get('HTTP_REFERER', '')
    is_in_messages_page = '/messages/' in referer

    # لا نرسل إشعارات منبثقة إذا كان المستخدم في صفحة المحادثات
    new_messages = []
    new_notifications = []

    if not is_in_messages_page:
        # الرسائل الجديدة فقط خارج صفحات المحادثات
        recent_threshold = timezone.now() - timedelta(minutes=1)
        recent_chat_messages = ChatMessage.objects.filter(
            created_at__gte=recent_threshold,
            is_read=False
        ).exclude(sender=user)

        for message in recent_chat_messages:
            if message.conversation.can_user_access(user):
                new_messages.append({
                    'sender_name': message.sender.get_full_name(),
                    'preview': message.content[:50] + '...' if len(message.content) > 50 else message.content,
                    'conversation_id': message.conversation.id
                })

    return JsonResponse({
        'unread_notifications': unread_notifications,
        'unread_messages': unread_messages,
        'new_messages': new_messages,
        'new_notifications': new_notifications,
        'is_in_messages_page': is_in_messages_page,
        'timestamp': timezone.now().isoformat()
    })


def clear_notifications_page(request):
    """صفحة مسح الإشعارات المنبثقة"""
    return render(request, 'clear_notifications.html')


def public_banned_page(request):
    """صفحة الحظر العامة (بدون تسجيل دخول)"""
    # تحديد مصدر معلومات الحظر (المستخدم المسجل أو الجلسة)
    if request.user.is_authenticated and request.user.is_currently_banned():
        # استخدام معلومات المستخدم المسجل
        username = request.user.username
        ban_reason = request.user.ban_reason
        ban_type = request.user.ban_type
        banned_until = request.user.banned_until.strftime('%Y-%m-%d %H:%M') if request.user.banned_until else ''

        # تحديث معلومات الجلسة
        request.session['banned_username'] = username
        request.session['ban_reason'] = ban_reason
        request.session['ban_type'] = ban_type
        if request.user.banned_until:
            request.session['banned_until'] = banned_until
    else:
        # استخدام معلومات الجلسة
        username = request.session.get('banned_username', '')
        ban_reason = request.session.get('ban_reason', '')
        ban_type = request.session.get('ban_type', '')
        banned_until = request.session.get('banned_until', '')

    # إعداد السياق
    context = {
        'username': username,
        'ban_reason': ban_reason,
        'ban_type': ban_type,
        'banned_until': banned_until,
        'is_permanent': ban_type == 'permanent',
        'is_temporary': ban_type == 'temporary'
    }

    # تقديم الصفحة
    response = render(request, 'public/banned.html', context)

    # إضافة رأس لمنع التخزين المؤقت
    response['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'

    return response


def public_pending_page(request):
    """صفحة انتظار المراجعة العامة (بدون تسجيل دخول)"""
    # تحديد مصدر معلومات الانتظار (المستخدم المسجل أو الجلسة)
    if request.user.is_authenticated and request.user.is_pending_verification():
        # استخدام معلومات المستخدم المسجل
        username = request.user.username
        user_type = request.user.user_type

        # تحديث معلومات الجلسة
        request.session['pending_username'] = username
        request.session['pending_user_type'] = user_type
    else:
        # استخدام معلومات الجلسة
        username = request.session.get('pending_username', '')
        user_type = request.session.get('pending_user_type', '')

    context = {
        'username': username,
        'user_type': user_type,
        'user_type_display': 'معلم' if user_type == 'teacher' else 'طالب'
    }

    # تقديم الصفحة
    response = render(request, 'public/pending.html', context)

    # إضافة رأس لمنع التخزين المؤقت
    response['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'

    return response


def public_rejected_page(request):
    """صفحة رفض التحقق العامة (بدون تسجيل دخول)"""
    # تحديد مصدر معلومات الرفض (المستخدم المسجل أو الجلسة)
    if request.user.is_authenticated and request.user.is_rejected():
        # استخدام معلومات المستخدم المسجل
        username = request.user.username
        user_type = request.user.user_type
        rejection_reason = request.user.rejection_reason

        # تحديث معلومات الجلسة
        request.session['rejected_username'] = username
        request.session['rejected_user_type'] = user_type
        request.session['rejection_reason'] = rejection_reason
    else:
        # استخدام معلومات الجلسة
        username = request.session.get('rejected_username', '')
        user_type = request.session.get('rejected_user_type', '')
        rejection_reason = request.session.get('rejection_reason', '')

    context = {
        'username': username,
        'user_type': user_type,
        'user_type_display': 'معلم' if user_type == 'teacher' else 'طالب',
        'rejection_reason': rejection_reason
    }

    # تقديم الصفحة
    response = render(request, 'public/rejected.html', context)

    # إضافة رأس لمنع التخزين المؤقت
    response['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'

    return response


def public_deleted_page(request):
    """صفحة الحسابات المحذوفة العامة (بدون تسجيل دخول)"""
    # استخدام معلومات الجلسة
    username = request.session.get('deleted_username', '')
    user_type = request.session.get('deleted_user_type', '')
    delete_reason = request.session.get('delete_reason', '')
    deleted_at = request.session.get('deleted_at', '')
    deleted_by = request.session.get('deleted_by', '')

    context = {
        'username': username,
        'user_type': user_type,
        'user_type_display': 'معلم' if user_type == 'teacher' else 'طالب',
        'delete_reason': delete_reason,
        'deleted_at': deleted_at,
        'deleted_by': deleted_by
    }

    # تقديم الصفحة
    response = render(request, 'public/deleted.html', context)

    # إضافة رأس لمنع التخزين المؤقت
    response['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'

    return response
