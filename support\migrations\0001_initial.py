# Generated by Django 4.2.7 on 2025-06-01 18:13

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SupportTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket_number', models.CharField(max_length=20, unique=True, verbose_name='رقم التذكرة')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التذكرة')),
                ('description', models.TextField(verbose_name='وصف المشكلة')),
                ('category', models.CharField(choices=[('technical', 'مشكلة تقنية'), ('account', 'مشكلة في الحساب'), ('payment', 'مشكلة في الدفع'), ('lesson', 'مشكلة في الحصة'), ('general', 'استفسار عام'), ('complaint', 'شكوى'), ('suggestion', 'اقتراح')], default='general', max_length=20, verbose_name='فئة التذكرة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('open', 'مفتوحة'), ('in_progress', 'قيد المعالجة'), ('waiting_response', 'في انتظار الرد'), ('resolved', 'محلولة'), ('closed', 'مغلقة')], default='open', max_length=20, verbose_name='حالة التذكرة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('closed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإغلاق')),
                ('is_read_by_admin', models.BooleanField(default=False, verbose_name='مقروءة من المدير')),
                ('is_read_by_user', models.BooleanField(default=True, verbose_name='مقروءة من المستخدم')),
                ('admin_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات المدير')),
            ],
            options={
                'verbose_name': 'تذكرة دعم',
                'verbose_name_plural': 'تذاكر الدعم',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SupportTicketResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرد')),
                ('is_admin_response', models.BooleanField(default=False, verbose_name='رد من المدير')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='support_attachments/', verbose_name='مرفق')),
            ],
            options={
                'verbose_name': 'رد تذكرة دعم',
                'verbose_name_plural': 'ردود تذاكر الدعم',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الرسالة')),
                ('content', models.TextField(verbose_name='محتوى الرسالة')),
                ('message_type', models.CharField(choices=[('announcement', 'إعلان عام'), ('warning', 'تحذير مهم'), ('reminder', 'تذكير'), ('update', 'تحديث النظام'), ('maintenance', 'إشعار صيانة'), ('policy', 'سياسة جديدة'), ('feature', 'ميزة جديدة'), ('urgent', 'عاجل')], default='announcement', max_length=20, verbose_name='نوع الرسالة')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('urgent', 'عاجل')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'رسالة النظام',
                'verbose_name_plural': 'رسائل النظام',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemMessageRecipient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الاستلام')),
            ],
            options={
                'verbose_name': 'مستقبل رسالة النظام',
                'verbose_name_plural': 'مستقبلو رسائل النظام',
            },
        ),
    ]
