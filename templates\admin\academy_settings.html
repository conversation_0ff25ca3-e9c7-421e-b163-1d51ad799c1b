{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}إعدادات الأكاديمية - {{ SITE_NAME }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-islamic-primary mb-2">إعدادات الأكاديمية</h1>
            <p class="text-gray-600">تعديل معلومات الأكاديمية الأساسية</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{% url 'admin_dashboard' %}" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg inline-flex items-center transition-all duration-300">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <div class="border-b border-gray-200 pb-4 mb-6">
            <h2 class="text-xl font-bold text-islamic-primary">معلومات الأكاديمية الأساسية</h2>
            <p class="text-gray-600 text-sm mt-1">هذه المعلومات ستظهر في جميع أنحاء النظام (الصفحات الداخلية والخارجية)</p>
        </div>

        <form method="post" enctype="multipart/form-data" class="space-y-6">
            {% csrf_token %}

            <!-- Current Logo Preview -->
            {% if academy_settings.academy_logo %}
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">الشعار الحالي</label>
                <div class="flex items-center">
                    <img src="{{ academy_settings.academy_logo.url }}" alt="شعار الأكاديمية" class="w-32 h-32 object-contain border border-gray-300 rounded-lg">
                </div>
            </div>
            {% endif %}

            <!-- Form Fields -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- اسم الأكاديمية -->
                <div class="flex flex-col">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">
                            <i class="fas fa-building ml-2 text-islamic-primary"></i>
                            اسم الأكاديمية
                        </label>
                    </div>
                    <div class="relative">
                        {{ form.academy_name|safe }}
                        {% if form.academy_name.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.academy_name.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- البريد الإلكتروني للأكاديمية -->
                <div class="flex flex-col">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">
                            <i class="fas fa-envelope ml-2 text-islamic-primary"></i>
                            البريد الإلكتروني للأكاديمية
                        </label>
                    </div>
                    <div class="relative">
                        {{ form.academy_email|safe }}
                        {% if form.academy_email.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.academy_email.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- رقم هاتف الأكاديمية -->
                <div class="flex flex-col">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">
                            <i class="fas fa-phone ml-2 text-islamic-primary"></i>
                            رقم هاتف الأكاديمية
                        </label>
                    </div>
                    <div class="relative">
                        {{ form.academy_phone|safe }}
                        {% if form.academy_phone.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.academy_phone.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- رقم واتساب الأكاديمية -->
                <div class="flex flex-col">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">
                            <i class="fab fa-whatsapp ml-2 text-islamic-primary"></i>
                            رقم واتساب الأكاديمية
                        </label>
                    </div>
                    <div class="relative">
                        {{ form.academy_whatsapp|safe }}
                        {% if form.academy_whatsapp.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.academy_whatsapp.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- البريد الإلكتروني للدعم الفني -->
                <div class="flex flex-col">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">
                            <i class="fas fa-envelope-open-text ml-2 text-islamic-primary"></i>
                            البريد الإلكتروني للدعم الفني
                        </label>
                    </div>
                    <div class="relative">
                        {{ form.academy_support_email|safe }}
                        {% if form.academy_support_email.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.academy_support_email.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- موقع الأكاديمية الإلكتروني -->
                <div class="flex flex-col">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">
                            <i class="fas fa-globe ml-2 text-islamic-primary"></i>
                            موقع الأكاديمية الإلكتروني
                        </label>
                    </div>
                    <div class="relative">
                        {{ form.academy_website|safe }}
                        {% if form.academy_website.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.academy_website.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- شعار الأكاديمية -->
                <div class="flex flex-col">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">
                            <i class="fas fa-image ml-2 text-islamic-primary"></i>
                            شعار الأكاديمية
                        </label>
                    </div>
                    <div class="relative">
                        {{ form.academy_logo|safe }}
                        {% if form.academy_logo.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.academy_logo.errors.0 }}</p>
                        {% endif %}
                        <p class="text-gray-500 text-xs mt-1">يفضل أن يكون الشعار بحجم 200×200 بكسل</p>
                    </div>
                </div>
            </div>

            <!-- عنوان الأكاديمية -->
            <div class="mt-6">
                <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700">
                        <i class="fas fa-map-marker-alt ml-2 text-islamic-primary"></i>
                        عنوان الأكاديمية
                    </label>
                </div>
                <div class="relative">
                    {{ form.academy_address|safe }}
                    {% if form.academy_address.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.academy_address.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- وصف الأكاديمية -->
            <div class="mt-6">
                <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700">
                        <i class="fas fa-info-circle ml-2 text-islamic-primary"></i>
                        وصف الأكاديمية
                    </label>
                </div>
                <div class="relative">
                    {{ form.academy_description|safe }}
                    {% if form.academy_description.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.academy_description.errors.0 }}</p>
                    {% endif %}
                    <p class="text-gray-500 text-xs mt-1">وصف موجز للأكاديمية سيظهر في الصفحات العامة</p>
                </div>
            </div>

            <!-- سلوجان الأكاديمية -->
            <div class="mt-6">
                <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700">
                        <i class="fas fa-quote-right ml-2 text-islamic-primary"></i>
                        سلوجان الأكاديمية
                    </label>
                </div>
                <div class="relative">
                    {{ form.academy_slogan|safe }}
                    {% if form.academy_slogan.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.academy_slogan.errors.0 }}</p>
                    {% endif %}
                    <p class="text-gray-500 text-xs mt-1">يظهر في عناوين الصفحات وفي أماكن متعددة في النظام</p>
                </div>
            </div>

            <!-- ساعات العمل -->
            <div class="mt-6">
                <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700">
                        <i class="fas fa-clock ml-2 text-islamic-primary"></i>
                        ساعات العمل
                    </label>
                </div>
                <div class="relative">
                    {{ form.academy_working_hours|safe }}
                    {% if form.academy_working_hours.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.academy_working_hours.errors.0 }}</p>
                    {% endif %}
                    <p class="text-gray-500 text-xs mt-1">مثال: الأحد - الخميس: 8:00 ص - 10:00 م، الجمعة: 2:00 م - 10:00 م</p>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="mt-8 flex justify-end">
                <button type="submit" class="bg-islamic-primary hover:bg-islamic-light text-white font-bold py-3 px-6 rounded-lg shadow-md transition-all duration-300 flex items-center">
                    <i class="fas fa-save ml-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>

    <!-- Preview Section -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <div class="border-b border-gray-200 pb-4 mb-6">
            <h2 class="text-xl font-bold text-islamic-primary">معاينة</h2>
            <p class="text-gray-600 text-sm mt-1">هكذا ستظهر معلومات الأكاديمية في النظام</p>
        </div>

        <div class="flex flex-col md:flex-row items-center p-4 bg-gray-50 rounded-lg">
            <div class="w-32 h-32 flex-shrink-0 mb-4 md:mb-0 md:ml-6">
                {% if academy_settings.academy_logo %}
                    <img src="{{ academy_settings.academy_logo.url }}" alt="{{ academy_settings.academy_name }}" class="w-full h-full object-contain">
                {% else %}
                    <div class="w-full h-full bg-islamic-light-gold rounded-lg flex items-center justify-center">
                        <i class="fas fa-quran text-islamic-primary text-4xl"></i>
                    </div>
                {% endif %}
            </div>

            <div>
                <h3 class="text-2xl font-bold text-islamic-primary">{{ academy_settings.academy_name }}</h3>
                <div class="mt-2 space-y-1 text-gray-600">
                    <p class="flex items-center">
                        <i class="fas fa-envelope ml-2 text-islamic-primary"></i>
                        {{ academy_settings.academy_email }}
                    </p>
                    {% if academy_settings.academy_support_email %}
                    <p class="flex items-center">
                        <i class="fas fa-envelope-open-text ml-2 text-islamic-primary"></i>
                        {{ academy_settings.academy_support_email }} (الدعم)
                    </p>
                    {% endif %}
                    {% if academy_settings.academy_phone %}
                    <p class="flex items-center">
                        <i class="fas fa-phone ml-2 text-islamic-primary"></i>
                        {{ academy_settings.academy_phone }}
                    </p>
                    {% endif %}
                    {% if academy_settings.academy_whatsapp %}
                    <p class="flex items-center">
                        <i class="fab fa-whatsapp ml-2 text-islamic-primary"></i>
                        {{ academy_settings.academy_whatsapp }}
                    </p>
                    {% endif %}
                    {% if academy_settings.academy_address %}
                    <p class="flex items-center">
                        <i class="fas fa-map-marker-alt ml-2 text-islamic-primary"></i>
                        {{ academy_settings.academy_address }}
                    </p>
                    {% endif %}
                </div>
                {% if academy_settings.academy_description %}
                <div class="mt-4 p-3 bg-islamic-light-blue rounded-lg">
                    <p class="text-gray-700">{{ academy_settings.academy_description }}</p>
                </div>
                {% endif %}

                {% if academy_settings.academy_working_hours %}
                <div class="mt-4 p-3 bg-green-50 rounded-lg">
                    <h4 class="text-sm font-medium text-green-800 mb-2">ساعات العمل</h4>
                    <p class="text-gray-700">{{ academy_settings.academy_working_hours }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Database Backup Section -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <div class="border-b border-gray-200 pb-4 mb-6">
            <h2 class="text-xl font-bold text-islamic-primary flex items-center">
                <i class="fas fa-database ml-3"></i>
                النسخ الاحتياطي لقاعدة البيانات
            </h2>
            <p class="text-gray-600 text-sm mt-1">إنشاء واستعادة النسخ الاحتياطية لقاعدة البيانات في الوقت الفعلي</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Create Backup Section -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center ml-4">
                        <i class="fas fa-download text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-blue-900">إنشاء نسخة احتياطية</h3>
                        <p class="text-blue-700 text-sm">تحميل نسخة احتياطية كاملة من قاعدة البيانات</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="bg-white rounded-lg p-4 border border-blue-200">
                        <h4 class="font-semibold text-gray-900 mb-2">ما يتم تضمينه في النسخة الاحتياطية:</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 ml-2"></i>
                                جميع بيانات المستخدمين (الطلاب والمعلمين)
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 ml-2"></i>
                                الحصص والدروس والتقييمات
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 ml-2"></i>
                                الاشتراكات والمدفوعات
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 ml-2"></i>
                                إعدادات الأكاديمية والنظام
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-times text-red-500 ml-2"></i>
                                جلسات المستخدمين (لأسباب أمنية)
                            </li>
                        </ul>
                    </div>

                    <button id="createBackupBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition-all duration-300 flex items-center justify-center">
                        <i class="fas fa-download ml-2"></i>
                        <span>إنشاء وتحميل النسخة الاحتياطية</span>
                    </button>

                    <div id="backupProgress" class="hidden">
                        <div class="bg-blue-100 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 ml-3"></div>
                                <span class="text-blue-800 font-medium">جاري إنشاء النسخة الاحتياطية...</span>
                            </div>
                            <div class="mt-2 bg-blue-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Restore Backup Section -->
            <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 border border-orange-100">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center ml-4">
                        <i class="fas fa-upload text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-orange-900">استعادة نسخة احتياطية</h3>
                        <p class="text-orange-700 text-sm">رفع واستعادة نسخة احتياطية سابقة</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="bg-white rounded-lg p-4 border border-orange-200">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-600 ml-2"></i>
                                <span class="text-red-800 font-semibold text-sm">تحذير مهم!</span>
                            </div>
                            <p class="text-red-700 text-xs mt-1">
                                استعادة النسخة الاحتياطية ستحل محل جميع البيانات الحالية. تأكد من إنشاء نسخة احتياطية حديثة قبل المتابعة.
                            </p>
                        </div>

                        <form id="restoreForm" enctype="multipart/form-data">
                            {% csrf_token %}
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-file-archive ml-2"></i>
                                    اختر ملف النسخة الاحتياطية (ZIP)
                                </label>
                                <input type="file" id="backupFile" name="backup_file" accept=".zip"
                                       class="block w-full text-sm text-gray-500 file:ml-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-orange-50 file:text-orange-700 hover:file:bg-orange-100 border border-gray-300 rounded-lg cursor-pointer">
                            </div>

                            <button type="submit" id="restoreBtn" disabled
                                    class="w-full bg-gray-400 text-white font-bold py-3 px-6 rounded-lg shadow-md transition-all duration-300 flex items-center justify-center cursor-not-allowed">
                                <i class="fas fa-upload ml-2"></i>
                                <span>استعادة النسخة الاحتياطية</span>
                            </button>
                        </form>
                    </div>

                    <div id="restoreProgress" class="hidden">
                        <div class="bg-orange-100 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-600 ml-3"></div>
                                <span class="text-orange-800 font-medium">جاري استعادة النسخة الاحتياطية...</span>
                            </div>
                            <div class="mt-2 bg-orange-200 rounded-full h-2">
                                <div class="bg-orange-600 h-2 rounded-full animate-pulse" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Tips -->
        <div class="mt-6 bg-gray-50 rounded-lg p-4">
            <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <i class="fas fa-lightbulb text-yellow-500 ml-2"></i>
                نصائح مهمة للنسخ الاحتياطي
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                    <h5 class="font-medium text-gray-800 mb-1">التكرار المنتظم:</h5>
                    <p>قم بإنشاء نسخة احتياطية يومياً أو أسبوعياً حسب نشاط النظام</p>
                </div>
                <div>
                    <h5 class="font-medium text-gray-800 mb-1">التخزين الآمن:</h5>
                    <p>احفظ النسخ الاحتياطية في مكان آمن خارج الخادم الرئيسي</p>
                </div>
                <div>
                    <h5 class="font-medium text-gray-800 mb-1">اختبار الاستعادة:</h5>
                    <p>اختبر عملية الاستعادة بشكل دوري للتأكد من سلامة النسخ</p>
                </div>
                <div>
                    <h5 class="font-medium text-gray-800 mb-1">التوثيق:</h5>
                    <p>احتفظ بسجل لتواريخ النسخ الاحتياطية والتغييرات المهمة</p>
                </div>
            </div>
        </div>
    </div>


</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Create Backup Functionality
    const createBackupBtn = document.getElementById('createBackupBtn');
    const backupProgress = document.getElementById('backupProgress');

    createBackupBtn.addEventListener('click', function() {
        // Show progress
        createBackupBtn.style.display = 'none';
        backupProgress.classList.remove('hidden');

        // Create form data
        const formData = new FormData();
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        // Send request
        fetch('{% url "admin_database_backup" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => {
            if (response.ok) {
                // If successful, trigger download
                return response.blob();
            } else {
                throw new Error('فشل في إنشاء النسخة الاحتياطية');
            }
        })
        .then(blob => {
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = 'qurania_backup_' + new Date().toISOString().slice(0,19).replace(/:/g, '-') + '.zip';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);

            // Show success message
            showMessage('تم إنشاء النسخة الاحتياطية وتحميلها بنجاح!', 'success');
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' + error.message, 'error');
        })
        .finally(() => {
            // Hide progress and show button
            backupProgress.classList.add('hidden');
            createBackupBtn.style.display = 'flex';
        });
    });

    // Restore Backup Functionality
    const restoreForm = document.getElementById('restoreForm');
    const backupFile = document.getElementById('backupFile');
    const restoreBtn = document.getElementById('restoreBtn');
    const restoreProgress = document.getElementById('restoreProgress');

    // Enable/disable restore button based on file selection
    backupFile.addEventListener('change', function() {
        if (this.files.length > 0) {
            restoreBtn.disabled = false;
            restoreBtn.classList.remove('bg-gray-400', 'cursor-not-allowed');
            restoreBtn.classList.add('bg-orange-600', 'hover:bg-orange-700', 'cursor-pointer');
        } else {
            restoreBtn.disabled = true;
            restoreBtn.classList.add('bg-gray-400', 'cursor-not-allowed');
            restoreBtn.classList.remove('bg-orange-600', 'hover:bg-orange-700', 'cursor-pointer');
        }
    });

    // Handle restore form submission
    restoreForm.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!backupFile.files.length) {
            showMessage('يرجى اختيار ملف النسخة الاحتياطية أولاً', 'error');
            return;
        }

        // Confirm action
        if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية!')) {
            return;
        }

        // Show progress
        restoreBtn.style.display = 'none';
        restoreProgress.classList.remove('hidden');

        // Create form data
        const formData = new FormData();
        formData.append('backup_file', backupFile.files[0]);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        // Send request
        fetch('{% url "admin_database_restore" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('تم استعادة النسخة الاحتياطية بنجاح!', 'success');
                // Reload page after 2 seconds
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showMessage('فشل في استعادة النسخة الاحتياطية: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('حدث خطأ أثناء استعادة النسخة الاحتياطية', 'error');
        })
        .finally(() => {
            // Hide progress and show button
            restoreProgress.classList.add('hidden');
            restoreBtn.style.display = 'flex';
        });
    });

    // Helper function to show messages
    function showMessage(message, type) {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-md ${
            type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'
        }`;
        messageDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} ml-2"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="mr-auto text-lg font-bold">&times;</button>
            </div>
        `;

        // Add to page
        document.body.appendChild(messageDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentElement) {
                messageDiv.remove();
            }
        }, 5000);
    }
});
</script>

{% endblock %}
