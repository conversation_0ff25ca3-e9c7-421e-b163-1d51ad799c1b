<!-- قالب تفاصيل المعلم للنافذة المنبثقة -->
<div class="max-w-4xl mx-auto">
    <!-- معلومات المعلم الأساسية -->
    <div class="bg-gradient-to-r from-islamic-primary to-islamic-dark text-white rounded-lg p-6 mb-6">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center ml-4">
                <span class="text-2xl font-bold text-islamic-primary">{{ teacher.first_name|first }}{{ teacher.last_name|first }}</span>
            </div>
            <div>
                <h2 class="text-2xl font-bold">{{ teacher.get_full_name }}</h2>
                <p class="text-islamic-light-gold">{{ teacher.email }}</p>
                <p class="text-sm text-islamic-light-gold">
                    انضم في: {{ teacher.date_joined|date:"d/m/Y" }}
                </p>
            </div>
        </div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600">{{ stats.total_ratings }}</div>
            <div class="text-sm text-gray-600">إجمالي التقييمات</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600">{{ stats.avg_overall|floatformat:1 }}</div>
            <div class="text-sm text-gray-600">التقييم العام</div>
        </div>
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-purple-600">{{ stats.scheduled_count }}</div>
            <div class="text-sm text-gray-600">حصص مجدولة</div>
        </div>
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-orange-600">{{ stats.live_count }}</div>
            <div class="text-sm text-gray-600">حصص مباشرة</div>
        </div>
    </div>

    <!-- المعايير التفصيلية -->
    <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">تفصيل المعايير</h3>
        <div class="space-y-4">
            <div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">التقييم العام</span>
                    <span class="text-sm text-gray-600">{{ stats.avg_overall|floatformat:1 }}/5</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: {% widthratio stats.avg_overall 5 100 %}%"></div>
                </div>
            </div>
            
            <div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">جودة الحصة</span>
                    <span class="text-sm text-gray-600">{{ stats.avg_quality|floatformat:1 }}/5</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: {% widthratio stats.avg_quality 5 100 %}%"></div>
                </div>
            </div>
            
            <div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">تفاعل المعلم</span>
                    <span class="text-sm text-gray-600">{{ stats.avg_interaction|floatformat:1 }}/5</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-purple-500 h-2 rounded-full" style="width: {% widthratio stats.avg_interaction 5 100 %}%"></div>
                </div>
            </div>
            
            <div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">الجودة التقنية</span>
                    <span class="text-sm text-gray-600">{{ stats.avg_technical|floatformat:1 }}/5</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-orange-500 h-2 rounded-full" style="width: {% widthratio stats.avg_technical 5 100 %}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- توزيع النجوم -->
    <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">توزيع التقييمات</h3>
        <div class="space-y-3">
            {% for star, count in star_distribution.items %}
            <div class="flex items-center">
                <span class="w-12 text-sm font-medium">{{ star }} نجوم</span>
                <div class="flex-1 bg-gray-200 rounded-full h-3 mx-3">
                    <div class="bg-yellow-400 h-3 rounded-full" style="width: {% if stats.total_ratings > 0 %}{% widthratio count stats.total_ratings 100 %}{% else %}0{% endif %}%"></div>
                </div>
                <span class="w-8 text-sm text-gray-600">{{ count }}</span>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- آخر التقييمات -->
    <div class="bg-white border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">آخر التقييمات</h3>
        {% if recent_ratings %}
        <div class="space-y-4 max-h-64 overflow-y-auto">
            {% for rating in recent_ratings %}
            <div class="border-b border-gray-100 pb-3 last:border-b-0">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center">
                        <span class="font-medium text-gray-900">{{ rating.student.get_full_name }}</span>
                        <span class="mx-2 text-gray-400">•</span>
                        <span class="text-sm text-gray-600">{{ rating.created_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="flex items-center">
                        {% for i in "12345" %}
                            {% if forloop.counter <= rating.overall_rating %}
                                <span class="text-yellow-400 text-sm">★</span>
                            {% else %}
                                <span class="text-gray-300 text-sm">★</span>
                            {% endif %}
                        {% endfor %}
                        <span class="ml-2 text-sm font-medium">{{ rating.overall_rating }}/5</span>
                    </div>
                </div>
                {% if rating.comment %}
                <p class="text-sm text-gray-600 italic">"{{ rating.comment }}"</p>
                {% endif %}
                <div class="flex items-center text-xs text-gray-500 mt-1">
                    <span class="bg-gray-100 px-2 py-1 rounded">
                        {% if rating.lesson_type == 'scheduled' %}حصة مجدولة{% else %}حصة مباشرة{% endif %}
                    </span>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <p class="text-gray-500 text-center py-4">لا توجد تقييمات حتى الآن</p>
        {% endif %}
    </div>

    <!-- أزرار الإجراءات -->
    <div class="flex justify-end space-x-3 space-x-reverse mt-6">
        <button onclick="generateTeacherReport({{ teacher.id }})" class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors">
            <i class="fas fa-file-pdf ml-2"></i>
            تحميل تقرير PDF
        </button>
        <button onclick="closeModal()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
            إغلاق
        </button>
    </div>
</div>
