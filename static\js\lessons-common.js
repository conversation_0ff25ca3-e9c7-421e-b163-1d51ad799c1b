/**
 * نظام إدارة الحصص الموحد
 * يدعم جميع أنواع المستخدمين والحصص
 */

class LessonsManager {
    constructor(userType) {
        this.userType = userType;
        this.currentFilters = new URLSearchParams(window.location.search);
        this.init();
    }

    init() {
        this.initializeEventListeners();
        this.initializeModals();
        this.initializeDropdowns();
        this.startRealTimeUpdates();
    }

    initializeEventListeners() {
        // أزرار الإجراءات
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[onclick]');
            if (!target) return;

            const onclick = target.getAttribute('onclick');
            if (onclick.includes('showLessonDetails')) {
                e.preventDefault();
                const lessonId = this.extractLessonId(onclick);
                this.showLessonDetails(lessonId);
            }
        });

        // تحديث تلقائي للوقت
        this.updateTimeDisplays();
        setInterval(() => this.updateTimeDisplays(), 60000); // كل دقيقة
    }

    initializeModals() {
        // إنشاء modal للتفاصيل إذا لم يكن موجوداً
        if (!document.getElementById('lessonDetailsModal')) {
            this.createLessonDetailsModal();
        }

        // إنشاء modal للتقييم إذا لم يكن موجوداً
        if (!document.getElementById('evaluationModal')) {
            this.createEvaluationModal();
        }
    }

    initializeDropdowns() {
        // إغلاق القوائم المنسدلة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.relative')) {
                document.querySelectorAll('[id^="actions-"], [id^="teacher-actions-"], [id^="student-actions-"]')
                    .forEach(dropdown => dropdown.classList.add('hidden'));
            }
        });
    }

    startRealTimeUpdates() {
        // تحديث البيانات كل 30 ثانية
        setInterval(() => {
            this.refreshLessonsData();
        }, 30000);
    }

    // ===== إدارة البيانات =====

    async fetchLessons(params = null) {
        try {
            const searchParams = params || this.currentFilters;
            const response = await fetch(`/api/lessons/?${searchParams.toString()}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            this.updateLessonsContainer(data.lessons);
            this.updateStats(data.stats);
            this.currentFilters = searchParams;
            
        } catch (error) {
            console.error('Error fetching lessons:', error);
            this.showNotification('حدث خطأ في تحميل البيانات', 'error');
        }
    }

    updateLessonsContainer(lessons) {
        const container = document.getElementById('lessons-container');
        if (!container) return;

        if (lessons.length === 0) {
            container.innerHTML = this.getEmptyState();
            return;
        }

        container.innerHTML = lessons.map(lesson => this.renderLessonCard(lesson)).join('');
    }

    updateStats(stats) {
        // تحديث بطاقات الإحصائيات
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                element.textContent = stats[key];
            }
        });
    }

    // ===== إجراءات الحصص =====

    async showLessonDetails(lessonId) {
        try {
            const response = await fetch(`/api/lessons/${lessonId}/`);
            const lesson = await response.json();
            
            this.populateLessonDetailsModal(lesson);
            this.showModal('lessonDetailsModal');
            
        } catch (error) {
            console.error('Error fetching lesson details:', error);
            this.showNotification('حدث خطأ في تحميل تفاصيل الحصة', 'error');
        }
    }

    async cancelLesson(lessonId) {
        const reason = await this.showCancelDialog();
        if (!reason) return;

        try {
            const response = await fetch(`/api/lessons/${lessonId}/cancel/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    reason: reason.reason,
                    note: reason.note
                })
            });

            if (response.ok) {
                this.showNotification('تم إلغاء الحصة بنجاح', 'success');
                this.refreshLessonsData();
            } else {
                throw new Error('Failed to cancel lesson');
            }

        } catch (error) {
            console.error('Error canceling lesson:', error);
            this.showNotification('حدث خطأ في إلغاء الحصة', 'error');
        }
    }

    async rescheduleLesson(lessonId) {
        const newDate = await this.showRescheduleDialog();
        if (!newDate) return;

        try {
            const response = await fetch(`/api/lessons/${lessonId}/reschedule/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    new_date: newDate.date,
                    reason: newDate.reason
                })
            });

            if (response.ok) {
                this.showNotification('تم إعادة جدولة الحصة بنجاح', 'success');
                this.refreshLessonsData();
            } else {
                throw new Error('Failed to reschedule lesson');
            }

        } catch (error) {
            console.error('Error rescheduling lesson:', error);
            this.showNotification('حدث خطأ في إعادة جدولة الحصة', 'error');
        }
    }

    async submitTeacherReport(lessonId) {
        const report = await this.showTeacherReportDialog();
        if (!report) return;

        try {
            const response = await fetch(`/api/lessons/${lessonId}/teacher-report/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({ report })
            });

            if (response.ok) {
                this.showNotification('تم إرسال التقرير بنجاح', 'success');
                this.refreshLessonsData();
            } else {
                throw new Error('Failed to submit report');
            }

        } catch (error) {
            console.error('Error submitting teacher report:', error);
            this.showNotification('حدث خطأ في إرسال التقرير', 'error');
        }
    }

    async submitStudentEvaluation(lessonId) {
        const evaluation = await this.showEvaluationDialog(lessonId);
        if (!evaluation) return;

        try {
            const response = await fetch(`/api/lessons/${lessonId}/student-evaluation/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(evaluation)
            });

            if (response.ok) {
                this.showNotification('تم إرسال التقييم بنجاح', 'success');
                this.refreshLessonsData();
            } else {
                throw new Error('Failed to submit evaluation');
            }

        } catch (error) {
            console.error('Error submitting evaluation:', error);
            this.showNotification('حدث خطأ في إرسال التقييم', 'error');
        }
    }

    // ===== مساعدات UI =====

    toggleDropdown(dropdownId) {
        const dropdown = document.getElementById(dropdownId);
        if (dropdown) {
            dropdown.classList.toggle('hidden');
            
            // إغلاق القوائم الأخرى
            document.querySelectorAll('[id^="actions-"], [id^="teacher-actions-"], [id^="student-actions-"]')
                .forEach(other => {
                    if (other.id !== dropdownId) {
                        other.classList.add('hidden');
                    }
                });
        }
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    }

    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    }

    showNotification(message, type = 'info') {
        // إنشاء إشعار مؤقت
        const notification = document.createElement('div');
        notification.className = `fixed top-4 left-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // ===== إنشاء Modals =====

    createLessonDetailsModal() {
        const modal = document.createElement('div');
        modal.id = 'lessonDetailsModal';
        modal.className = 'hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
        modal.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-900">تفاصيل الحصة</h3>
                    <button onclick="window.lessonsManager.hideModal('lessonDetailsModal')" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="lessonDetailsContent">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    createEvaluationModal() {
        const modal = document.createElement('div');
        modal.id = 'evaluationModal';
        modal.className = 'hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
        modal.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-900">تقييم الحصة</h3>
                    <button onclick="window.lessonsManager.hideModal('evaluationModal')" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="evaluationContent">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    populateLessonDetailsModal(lesson) {
        const content = document.getElementById('lessonDetailsContent');
        content.innerHTML = `
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">نوع الحصة</label>
                        <p class="mt-1 text-sm text-gray-900">${this.getLessonTypeDisplay(lesson.lesson_type)}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">الحالة</label>
                        <p class="mt-1 text-sm text-gray-900">${lesson.status_display}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">المعلم</label>
                        <p class="mt-1 text-sm text-gray-900">${lesson.teacher_name}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">الطالب</label>
                        <p class="mt-1 text-sm text-gray-900">${lesson.student_name}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">التاريخ والوقت</label>
                        <p class="mt-1 text-sm text-gray-900">${lesson.scheduled_date_display}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">المدة</label>
                        <p class="mt-1 text-sm text-gray-900">${lesson.duration_minutes} دقيقة</p>
                    </div>
                </div>

                ${lesson.description ? `
                    <div>
                        <label class="block text-sm font-medium text-gray-700">الوصف</label>
                        <p class="mt-1 text-sm text-gray-900">${lesson.description}</p>
                    </div>
                ` : ''}

                ${lesson.teacher_report ? `
                    <div>
                        <label class="block text-sm font-medium text-gray-700">تقرير المعلم</label>
                        <p class="mt-1 text-sm text-gray-900">${lesson.teacher_report}</p>
                    </div>
                ` : ''}

                ${lesson.cancellation_note ? `
                    <div>
                        <label class="block text-sm font-medium text-gray-700">سبب الإلغاء</label>
                        <p class="mt-1 text-sm text-red-600">${lesson.cancellation_note}</p>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // ===== مساعدات عامة =====

    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
               document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    }

    extractLessonId(onclickString) {
        const match = onclickString.match(/\d+/);
        return match ? parseInt(match[0]) : null;
    }

    getLessonTypeDisplay(type) {
        const types = {
            'subscription': 'حصة من الباقة',
            'trial': 'حصة تجريبية',
            'makeup': 'حصة تعويضية'
        };
        return types[type] || type;
    }

    updateTimeDisplays() {
        document.querySelectorAll('[data-time-until]').forEach(element => {
            const targetTime = new Date(element.dataset.timeUntil);
            const now = new Date();
            const diff = targetTime - now;

            if (diff > 0) {
                const minutes = Math.floor(diff / 60000);
                const hours = Math.floor(minutes / 60);

                if (hours > 0) {
                    element.textContent = `${hours} ساعة و ${minutes % 60} دقيقة`;
                } else {
                    element.textContent = `${minutes} دقيقة`;
                }
            } else {
                element.textContent = 'حان الوقت';
                element.classList.add('text-red-600', 'font-bold');
            }
        });
    }

    async refreshLessonsData() {
        await this.fetchLessons();
    }

    getEmptyState() {
        return `
            <div class="text-center py-12">
                <i class="fas fa-calendar-times text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد حصص</h3>
                <p class="text-gray-500">لا توجد حصص تطابق المعايير المحددة</p>
            </div>
        `;
    }
}

// ===== دوال عامة =====

function toggleDropdown(dropdownId) {
    if (window.lessonsManager) {
        window.lessonsManager.toggleDropdown(dropdownId);
    }
}

function showLessonDetails(lessonId) {
    if (window.lessonsManager) {
        window.lessonsManager.showLessonDetails(lessonId);
    }
}

function cancelLesson(lessonId) {
    if (window.lessonsManager) {
        window.lessonsManager.cancelLesson(lessonId);
    }
}

function rescheduleLesson(lessonId) {
    if (window.lessonsManager) {
        window.lessonsManager.rescheduleLesson(lessonId);
    }
}

function submitTeacherReport(lessonId) {
    if (window.lessonsManager) {
        window.lessonsManager.submitTeacherReport(lessonId);
    }
}

function submitStudentEvaluation(lessonId) {
    if (window.lessonsManager) {
        window.lessonsManager.submitStudentEvaluation(lessonId);
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const userType = document.body.dataset.userType || 'student';
    window.lessonsManager = new LessonsManager(userType);
});
