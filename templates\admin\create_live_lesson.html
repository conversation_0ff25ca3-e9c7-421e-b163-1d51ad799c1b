{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء حصة مباشرة - {{ SITE_NAME }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-islamic-primary mb-2">
                    <i class="fas fa-video text-islamic-gold ml-3"></i>
                    إنشاء حصة مباشرة
                </h1>
                <p class="text-gray-600">إنشاء حصة مباشرة جديدة مع Jitsi Meet</p>
            </div>
            <a href="{% url 'admin_live_lessons' %}"
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للحصص المباشرة
            </a>
        </div>
    </div>

    <!-- Create Form -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <form method="POST" class="space-y-6">
            {% csrf_token %}

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-heading text-blue-600 ml-1"></i>
                        عنوان الحصة *
                    </label>
                    <input type="text"
                           id="title"
                           name="title"
                           required
                           placeholder="مثال: حصة تحفيظ سورة البقرة"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div>
                    <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-clock text-green-600 ml-1"></i>
                        مدة الحصة
                    </label>
                    <select id="duration_minutes"
                            name="duration_minutes"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        {% for value, label in duration_choices %}
                        <option value="{{ value }}" {% if value == 45 %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-align-left text-purple-600 ml-1"></i>
                    وصف الحصة
                </label>
                <textarea id="description"
                          name="description"
                          rows="3"
                          placeholder="وصف مختصر عن محتوى الحصة..."
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
            </div>

            <!-- Participants -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="teacher_id" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-chalkboard-teacher text-blue-600 ml-1"></i>
                        المعلم *
                    </label>
                    <select id="teacher_id"
                            name="teacher_id"
                            required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">اختر المعلم</option>
                        {% for teacher in teachers %}
                        <option value="{{ teacher.id }}">{{ teacher.get_full_name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user-graduate text-green-600 ml-1"></i>
                        الطالب *
                    </label>
                    <select id="student_id"
                            name="student_id"
                            required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">اختر الطالب</option>
                        {% for student in students %}
                        <option value="{{ student.id }}">{{ student.get_full_name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- Schedule -->
            <div>
                <label for="scheduled_date" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-calendar-alt text-red-600 ml-1"></i>
                    موعد الحصة *
                </label>
                <input type="datetime-local"
                       id="scheduled_date"
                       name="scheduled_date"
                       value="{{ default_time }}"
                       required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <p class="text-sm text-gray-500 mt-1">
                    <i class="fas fa-info-circle ml-1"></i>
                    سيتم إرسال إشعارات للمعلم والطالب بموعد الحصة
                </p>
            </div>



            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        إنشاء الحصة المباشرة
                    </button>

                    <a href="{% url 'admin_live_lessons' %}"
                       class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-times ml-2"></i>
                        إلغاء
                    </a>
                </div>

                <div class="text-sm text-gray-500">
                    <i class="fas fa-shield-alt text-green-600 ml-1"></i>
                    سيتم إنشاء غرفة Jitsi آمنة تلقائياً
                </div>
            </div>
        </form>
    </div>

    <!-- Information Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center mb-3">
                <i class="fas fa-video text-blue-600 text-xl ml-2"></i>
                <h3 class="font-bold text-blue-800">Jitsi Meet</h3>
            </div>
            <p class="text-blue-700 text-sm">
                سيتم إنشاء غرفة مؤتمرات فيديو آمنة مع معرف فريد وكلمة مرور
            </p>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-center mb-3">
                <i class="fas fa-user-shield text-green-600 text-xl ml-2"></i>
                <h3 class="font-bold text-green-800">صلاحيات المعلم</h3>
            </div>
            <p class="text-green-700 text-sm">
                المعلم سيحصل على صلاحيات مشرف في الغرفة مع إمكانية التحكم الكامل
            </p>
        </div>

        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div class="flex items-center mb-3">
                <i class="fas fa-bell text-purple-600 text-xl ml-2"></i>
                <h3 class="font-bold text-purple-800">الإشعارات</h3>
            </div>
            <p class="text-purple-700 text-sm">
                سيتم إرسال إشعارات فورية للمعلم والطالب مع رابط الانضمام
            </p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');

    form.addEventListener('submit', function(e) {
        // تعطيل الزر لمنع الإرسال المتكرر
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الإنشاء...';

        // التحقق من البيانات
        const title = document.getElementById('title').value.trim();
        const teacherId = document.getElementById('teacher_id').value;
        const studentId = document.getElementById('student_id').value;
        const scheduledDate = document.getElementById('scheduled_date').value;

        if (!title || !teacherId || !studentId || !scheduledDate) {
            e.preventDefault();
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-plus ml-2"></i>إنشاء الحصة المباشرة';
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // التحقق من أن المعلم والطالب مختلفان
        if (teacherId === studentId) {
            e.preventDefault();
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-plus ml-2"></i>إنشاء الحصة المباشرة';
            alert('لا يمكن أن يكون المعلم والطالب نفس الشخص');
            return;
        }

        // التحقق من أن الموعد في المستقبل
        const selectedDate = new Date(scheduledDate);
        const now = new Date();

        if (selectedDate <= now) {
            e.preventDefault();
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-plus ml-2"></i>إنشاء الحصة المباشرة';
            alert('يجب أن يكون موعد الحصة في المستقبل');
            return;
        }
    });

    // تحديث الوقت الافتراضي كل دقيقة
    setInterval(function() {
        const now = new Date();
        now.setMinutes(now.getMinutes() + 30);
        const defaultTime = now.toISOString().slice(0, 16);

        const dateInput = document.getElementById('scheduled_date');
        if (!dateInput.value || new Date(dateInput.value) <= new Date()) {
            dateInput.value = defaultTime;
        }
    }, 60000);
});
</script>
{% endblock %}
