"""
إشارات نظام الاشتراكات
"""

from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


@receiver(post_delete, sender='subscriptions.ScheduledLesson')
def scheduled_lesson_deleted(sender, instance, **kwargs):
    """إشعار عند حذف حصة مجدولة"""
    try:
        from notifications.models import Notification

        # إشعار للطالب
        Notification.objects.create(
            recipient=instance.subscription.student,
            title="تم حذف حصة مجدولة",
            message=f"تم حذف حصة رقم {instance.lesson_number} التي كانت مجدولة يوم {instance.scheduled_date.strftime('%Y-%m-%d')} الساعة {instance.scheduled_date.strftime('%H:%M')}",
            notification_type='lesson_cancelled'
        )

        # إشعار للمعلم إذا كان معين
        if instance.teacher:
            Notification.objects.create(
                recipient=instance.teacher,
                title="تم حذف حصة مجدولة",
                message=f"تم حذف حصة رقم {instance.lesson_number} مع الطالب {instance.subscription.student.get_full_name()} التي كانت مجدولة يوم {instance.scheduled_date.strftime('%Y-%m-%d')} الساعة {instance.scheduled_date.strftime('%H:%M')}",
                notification_type='lesson_cancelled'
            )

        # مسح الكاش المرتبط بلوحات التحكم
        _clear_dashboard_cache(instance)

    except Exception as e:
        logger.warning(f"فشل في إرسال إشعار حذف الحصة المجدولة: {e}")


@receiver(post_save, sender='subscriptions.ScheduledLesson')
def scheduled_lesson_saved(sender, instance, created, **kwargs):
    """إشعار عند حفظ حصة مجدولة"""
    try:
        # مسح الكاش المرتبط بلوحات التحكم
        _clear_dashboard_cache(instance)

        # إذا تم تحديث الحصة (وليس إنشاؤها)
        if not created:
            from notifications.models import Notification

            # إشعار للطالب
            Notification.objects.create(
                recipient=instance.subscription.student,
                title="تم تحديث حصة مجدولة",
                message=f"تم تحديث حصة رقم {instance.lesson_number} - الموعد الجديد: {instance.scheduled_date.strftime('%Y-%m-%d')} الساعة {instance.scheduled_date.strftime('%H:%M')}",
                notification_type='lesson_updated'
            )

            # إشعار للمعلم إذا كان معين
            if instance.teacher:
                Notification.objects.create(
                    recipient=instance.teacher,
                    title="تم تحديث حصة مجدولة",
                    message=f"تم تحديث حصة رقم {instance.lesson_number} مع الطالب {instance.subscription.student.get_full_name()} - الموعد الجديد: {instance.scheduled_date.strftime('%Y-%m-%d')} الساعة {instance.scheduled_date.strftime('%H:%M')}",
                    notification_type='lesson_updated'
                )

    except Exception as e:
        logger.warning(f"فشل في إرسال إشعار تحديث الحصة المجدولة: {e}")


def _clear_dashboard_cache(lesson_instance):
    """مسح الكاش المرتبط بلوحات التحكم"""
    try:
        # مسح كاش لوحة تحكم الطالب
        student_cache_key = f"student_dashboard_{lesson_instance.subscription.student.id}"
        cache.delete(student_cache_key)

        # مسح كاش لوحة تحكم المعلم
        if lesson_instance.teacher:
            teacher_cache_key = f"teacher_dashboard_{lesson_instance.teacher.id}"
            cache.delete(teacher_cache_key)

        # مسح كاش لوحة تحكم الإدارة
        cache.delete("admin_dashboard_lessons")
        cache.delete("admin_live_lessons")
        cache.delete("admin_scheduled_lessons")

        logger.debug("تم مسح كاش لوحات التحكم بنجاح")

    except Exception as e:
        logger.warning(f"فشل في مسح كاش لوحات التحكم: {e}")
