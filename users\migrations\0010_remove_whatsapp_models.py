# Generated by Django 4.2.7 on 2025-06-30 04:08

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0009_create_whatsapp_gupshup_models'),
    ]

    operations = [
        # حذف جداول WhatsApp
        migrations.RunSQL(
            "DROP TABLE IF EXISTS users_scheduledwhatsappmessage;",
            reverse_sql="-- لا يمكن استرداد الجدول"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS users_whatsappmessage;",
            reverse_sql="-- لا يمكن استرداد الجدول"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS users_whatsapptemplate;",
            reverse_sql="-- لا يمكن استرداد الجدول"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS users_whatsappsettings;",
            reverse_sql="-- لا يمكن استرداد الجدول"
        ),
    ]
