from django.contrib import admin
from .models import CustomReport, GeneratedReport, ReportTemplate, ReportSchedule

@admin.register(CustomReport)
class CustomReportAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'created_by', 'created_at', 'is_active']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['title', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'description', 'category', 'created_by')
        }),
        ('إعدادات التقرير', {
            'fields': ('date_from', 'date_to', 'filters')
        }),
        ('الجدولة', {
            'fields': ('is_scheduled', 'schedule_frequency')
        }),
        ('الحالة', {
            'fields': ('is_active', 'created_at', 'updated_at')
        }),
    )

@admin.register(GeneratedReport)
class GeneratedReportAdmin(admin.ModelAdmin):
    list_display = ['custom_report', 'file_format', 'generated_by', 'generated_at', 'is_ready', 'download_count']
    list_filter = ['file_format', 'is_ready', 'generated_at']
    search_fields = ['custom_report__title']
    readonly_fields = ['generated_at', 'file_size', 'processing_time', 'download_count', 'last_downloaded']

@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'is_active', 'is_system_template', 'created_at']
    list_filter = ['category', 'is_active', 'is_system_template']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(ReportSchedule)
class ReportScheduleAdmin(admin.ModelAdmin):
    list_display = ['custom_report', 'frequency', 'next_execution', 'is_active']
    list_filter = ['frequency', 'is_active']
    readonly_fields = ['last_execution', 'created_at', 'updated_at']
