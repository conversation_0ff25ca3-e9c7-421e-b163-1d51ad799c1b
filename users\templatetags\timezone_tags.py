"""
Template tags للتعامل مع المناطق الزمنية
"""

from django import template
from django.utils import timezone
import pytz

register = template.Library()


@register.filter
def user_timezone(datetime_obj, user):
    """
    تحويل التاريخ والوقت إلى المنطقة الزمنية للمستخدم

    الاستخدام: {{ some_datetime|user_timezone:request.user }}
    """
    if not datetime_obj:
        return ''

    # الحصول على المنطقة الزمنية للمستخدم
    user_timezone_str = 'Asia/Riyadh'  # افتراضي
    try:
        if hasattr(user, 'profile'):
            profile = getattr(user, 'profile', None)
            if profile and hasattr(profile, 'timezone') and profile.timezone:
                user_timezone_str = profile.timezone
    except:
        pass

    try:
        user_tz = pytz.timezone(user_timezone_str)

        # تحويل الوقت إذا كان UTC
        if timezone.is_aware(datetime_obj):
            local_time = datetime_obj.astimezone(user_tz)
        else:
            # إذا كان الوقت naive، اعتبره UTC أولاً
            utc_time = timezone.make_aware(datetime_obj, pytz.UTC)
            local_time = utc_time.astimezone(user_tz)

        return local_time
    except:
        return datetime_obj


@register.filter
def format_user_time(datetime_obj, user):
    """
    تنسيق الوقت حسب المنطقة الزمنية للمستخدم

    الاستخدام: {{ some_datetime|format_user_time:request.user }}
    """
    local_time = user_timezone(datetime_obj, user)
    if not local_time:
        return ''

    return local_time.strftime('%Y-%m-%d %H:%M')


@register.filter
def format_user_date(datetime_obj, user):
    """
    تنسيق التاريخ حسب المنطقة الزمنية للمستخدم

    الاستخدام: {{ some_datetime|format_user_date:request.user }}
    """
    local_time = user_timezone(datetime_obj, user)
    if not local_time:
        return ''

    return local_time.strftime('%Y-%m-%d')


@register.filter
def format_user_time_only(datetime_obj, user):
    """
    تنسيق الوقت فقط حسب المنطقة الزمنية للمستخدم

    الاستخدام: {{ some_datetime|format_user_time_only:request.user }}
    """
    local_time = user_timezone(datetime_obj, user)
    if not local_time:
        return ''

    return local_time.strftime('%H:%M')


@register.simple_tag
def user_current_time(user):
    """
    الحصول على الوقت الحالي حسب المنطقة الزمنية للمستخدم

    الاستخدام: {% user_current_time request.user %}
    """
    # الحصول على المنطقة الزمنية للمستخدم
    user_timezone_str = 'Asia/Riyadh'  # افتراضي
    try:
        if hasattr(user, 'profile'):
            profile = getattr(user, 'profile', None)
            if profile and hasattr(profile, 'timezone') and profile.timezone:
                user_timezone_str = profile.timezone
    except:
        pass

    try:
        user_tz = pytz.timezone(user_timezone_str)
        current_time = timezone.now().astimezone(user_tz)
        return current_time.strftime('%H:%M:%S')
    except:
        return timezone.now().strftime('%H:%M:%S')


@register.simple_tag
def user_current_date(user):
    """
    الحصول على التاريخ الحالي حسب المنطقة الزمنية للمستخدم

    الاستخدام: {% user_current_date request.user %}
    """
    # الحصول على المنطقة الزمنية للمستخدم
    user_timezone_str = 'Asia/Riyadh'  # افتراضي
    if hasattr(user, 'profile') and user.profile and user.profile.timezone:
        user_timezone_str = user.profile.timezone

    try:
        user_tz = pytz.timezone(user_timezone_str)
        current_time = timezone.now().astimezone(user_tz)
        return current_time.strftime('%Y-%m-%d')
    except:
        return timezone.now().strftime('%Y-%m-%d')


@register.simple_tag
def user_current_datetime(user):
    """
    الحصول على التاريخ والوقت الحالي حسب المنطقة الزمنية للمستخدم

    الاستخدام: {% user_current_datetime request.user %}
    """
    # الحصول على المنطقة الزمنية للمستخدم
    user_timezone_str = 'Asia/Riyadh'  # افتراضي
    if hasattr(user, 'profile') and user.profile and user.profile.timezone:
        user_timezone_str = user.profile.timezone

    try:
        user_tz = pytz.timezone(user_timezone_str)
        current_time = timezone.now().astimezone(user_tz)
        return current_time.strftime('%Y-%m-%d %H:%M:%S')
    except:
        return timezone.now().strftime('%Y-%m-%d %H:%M:%S')


@register.inclusion_tag('users/timezone_aware_time.html')
def timezone_aware_time(datetime_obj, user, format_type='full'):
    """
    عرض الوقت مع دعم المنطقة الزمنية

    الاستخدام: {% timezone_aware_time some_datetime request.user 'time' %}
    """
    local_time = user_timezone(datetime_obj, user)

    if format_type == 'time':
        formatted_time = local_time.strftime('%H:%M') if local_time else ''
    elif format_type == 'date':
        formatted_time = local_time.strftime('%Y-%m-%d') if local_time else ''
    elif format_type == 'short':
        formatted_time = local_time.strftime('%m-%d %H:%M') if local_time else ''
    else:  # full
        formatted_time = local_time.strftime('%Y-%m-%d %H:%M') if local_time else ''

    return {
        'datetime_obj': datetime_obj,
        'local_time': local_time,
        'formatted_time': formatted_time,
        'iso_time': local_time.isoformat() if local_time else '',
        'user_timezone': user.profile.timezone if hasattr(user, 'profile') and user.profile else 'Asia/Riyadh'
    }


@register.filter
def relative_time_user(datetime_obj, user):
    """
    عرض الوقت النسبي حسب المنطقة الزمنية للمستخدم

    الاستخدام: {{ some_datetime|relative_time_user:request.user }}
    """
    if not datetime_obj:
        return ''

    local_time = user_timezone(datetime_obj, user)
    if not local_time:
        return ''

    # الحصول على الوقت الحالي في نفس المنطقة الزمنية
    user_timezone_str = 'Asia/Riyadh'
    if hasattr(user, 'profile') and user.profile and user.profile.timezone:
        user_timezone_str = user.profile.timezone

    try:
        user_tz = pytz.timezone(user_timezone_str)
        now = timezone.now().astimezone(user_tz)

        diff = now - local_time

        if diff.days > 0:
            return f"منذ {diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"منذ {hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "الآن"
    except:
        return local_time.strftime('%Y-%m-%d %H:%M')


@register.simple_tag
def get_user_timezone_name(user):
    """
    الحصول على اسم المنطقة الزمنية للمستخدم

    الاستخدام: {% get_user_timezone_name request.user %}
    """
    try:
        if hasattr(user, 'profile'):
            profile = getattr(user, 'profile', None)
            if profile and hasattr(profile, 'timezone') and profile.timezone:
                return profile.timezone
    except:
        pass
    return 'Asia/Riyadh'


@register.simple_tag
def get_timezone_offset(user):
    """
    الحصول على إزاحة المنطقة الزمنية للمستخدم

    الاستخدام: {% get_timezone_offset request.user %}
    """
    user_timezone_str = 'Asia/Riyadh'
    if hasattr(user, 'profile') and user.profile and user.profile.timezone:
        user_timezone_str = user.profile.timezone

    try:
        user_tz = pytz.timezone(user_timezone_str)
        now = timezone.now().astimezone(user_tz)
        offset = now.strftime('%z')
        return f"GMT{offset[:3]}:{offset[3:]}"
    except:
        return "GMT+03:00"
