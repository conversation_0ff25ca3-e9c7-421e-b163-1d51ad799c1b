{% extends 'base.html' %}
{% load static %}

{% block title %}دفع عبر Stripe - {{ payment.subscription.plan.name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-8 text-white text-center">
                <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fab fa-stripe text-3xl"></i>
                </div>
                <h1 class="text-2xl font-bold mb-2">دفع آمن عبر Stripe</h1>
                <p class="text-purple-100">{{ payment.subscription.plan.name }}</p>
            </div>

            <!-- Payment Details -->
            <div class="px-6 py-4 bg-gray-50 border-b">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="font-semibold text-gray-900">{{ payment.subscription.plan.name }}</h3>
                        <p class="text-sm text-gray-600">{{ payment.subscription.plan.description }}</p>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-purple-600">${{ payment.amount }}</div>
                        <div class="text-sm text-gray-500">{{ payment.subscription.plan.currency|upper }}</div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="p-6">
                <form id="payment-form">
                    {% csrf_token %}
                    
                    <div class="space-y-6">
                        <h4 class="font-semibold text-gray-900 mb-4">
                            <i class="fas fa-credit-card ml-2 text-purple-600"></i>
                            معلومات البطاقة
                        </h4>

                        <!-- Card Element Container -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                                <i class="fas fa-credit-card ml-1"></i>
                                بيانات البطاقة
                            </label>
                            <div id="card-element"
                                 class="p-4 border border-gray-300 rounded-lg bg-white focus-within:border-purple-500 focus-within:ring-2 focus-within:ring-purple-200 transition-all min-h-[50px]"
                                 role="textbox"
                                 aria-label="معلومات بطاقة الائتمان"
                                 tabindex="0">
                                <!-- Stripe Elements will create the card input here -->
                            </div>
                            <p class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-info-circle ml-1"></i>
                                أدخل رقم البطاقة وتاريخ الانتهاء ورمز الأمان والرمز البريدي
                            </p>
                        </div>

                        <!-- Error Display -->
                        <div id="card-errors" class="hidden">
                            <div class="text-red-600 text-sm bg-red-50 border border-red-200 rounded-lg p-3">
                                <i class="fas fa-exclamation-triangle ml-2"></i>
                                <span id="card-errors-text"></span>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" id="submit-button" 
                                class="w-full bg-gray-400 text-white py-4 px-6 rounded-lg font-semibold text-lg transition-all duration-200 cursor-not-allowed"
                                disabled>
                            <span id="submit-text">أدخل بيانات البطاقة</span>
                        </button>

                        <!-- Security Notice -->
                        <div class="flex items-center justify-center text-sm text-gray-500 mt-4">
                            <i class="fas fa-shield-alt ml-2 text-green-500"></i>
                            <span>دفع آمن ومشفر بواسطة Stripe</span>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Test Card Info -->
            <div class="px-6 pb-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-medium text-blue-800 mb-2">🧪 بطاقة تجريبية للاختبار:</h4>
                    <div class="text-sm text-blue-700 space-y-1">
                        <p><strong>رقم البطاقة:</strong> 4242 4242 4242 4242</p>
                        <p><strong>تاريخ الانتهاء:</strong> أي تاريخ مستقبلي (مثل 12/25)</p>
                        <p><strong>CVC:</strong> أي 3 أرقام (مثل 123)</p>
                        <p><strong>الرمز البريدي:</strong> أي رمز (مثل 12345)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stripe.js -->
<script src="https://js.stripe.com/v3/"></script>

<script>
// متغيرات عامة
let stripe = null;
let cardElement = null;
let isProcessing = false;

// إخفاء تحذيرات المتصفح غير المهمة
const originalConsoleWarn = console.warn;
console.warn = function(...args) {
    const message = args.join(' ');
    // إخفاء تحذيرات Stripe المعروفة
    if (message.includes('Cross-Origin-Opener-Policy') ||
        message.includes('aria-hidden') ||
        message.includes('HTTPS')) {
        return;
    }
    originalConsoleWarn.apply(console, args);
};

// تهيئة Stripe عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تهيئة Stripe...');

    // انتظار قصير للتأكد من تحميل كل شيء
    setTimeout(function() {
        initializeStripe();
    }, 500);
});

function initializeStripe() {
    try {
        console.log('🔧 تهيئة Stripe...');
        
        // التحقق من تحميل Stripe
        if (typeof Stripe === 'undefined') {
            throw new Error('مكتبة Stripe غير محملة');
        }
        console.log('✅ مكتبة Stripe محملة');
        
        // إنشاء كائن Stripe
        const publishableKey = '{{ stripe_settings.stripe_publishable_key|default:"pk_test_TYooMQauvdEDq54NiTphI7jx" }}';
        console.log('🔑 استخدام مفتاح Stripe');
        
        stripe = Stripe(publishableKey);
        const elements = stripe.elements();
        console.log('✅ تم إنشاء كائن Stripe');
        
        // إنشاء عنصر البطاقة مع إعدادات إمكانية الوصول
        cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
                invalid: {
                    color: '#9e2146',
                },
            },
            hidePostalCode: false,
            iconStyle: 'solid',
            disabled: false
        });
        console.log('✅ تم إنشاء عنصر البطاقة');
        
        // mount العنصر
        cardElement.mount('#card-element');
        console.log('✅ تم mount عنصر البطاقة');
        
        // إضافة مستمع الأحداث
        cardElement.on('change', function(event) {
            console.log('🔄 تغيير في البطاقة:', event.complete, event.error);
            
            const displayError = document.getElementById('card-errors');
            const errorText = document.getElementById('card-errors-text');
            const submitButton = document.getElementById('submit-button');
            const submitText = document.getElementById('submit-text');
            
            // معالجة الأخطاء
            if (event.error) {
                errorText.textContent = event.error.message;
                displayError.classList.remove('hidden');
            } else {
                errorText.textContent = '';
                displayError.classList.add('hidden');
            }
            
            // تحديث زر الدفع
            if (event.complete && !event.error) {
                submitButton.disabled = false;
                submitButton.classList.remove('bg-gray-400', 'cursor-not-allowed');
                submitButton.classList.add('bg-purple-600', 'hover:bg-purple-700');
                submitText.textContent = 'ادفع الآن $' + '{{ payment.amount }}';
                console.log('✅ البطاقة مكتملة - تم تفعيل الزر');
            } else {
                submitButton.disabled = true;
                submitButton.classList.add('bg-gray-400', 'cursor-not-allowed');
                submitButton.classList.remove('bg-purple-600', 'hover:bg-purple-700');
                submitText.textContent = 'أدخل بيانات البطاقة';
            }
        });
        
        // معالجة إرسال النموذج
        document.getElementById('payment-form').addEventListener('submit', async function(event) {
            event.preventDefault();
            
            if (isProcessing) {
                console.log('⚠️ عملية دفع قيد التنفيذ بالفعل');
                return;
            }
            
            await processPayment();
        });
        
        console.log('🎉 تم تهيئة Stripe بنجاح!');
        
        // اختبار التفاعل مع معالجة محسنة
        setTimeout(function() {
            try {
                // التحقق من جاهزية العنصر
                if (cardElement && cardElement._element) {
                    cardElement.focus();
                    console.log('✅ عنصر البطاقة جاهز للتفاعل');
                } else {
                    console.log('⚠️ عنصر البطاقة لم يتم تحميله بعد');
                }
            } catch (error) {
                // تجاهل أخطاء التفاعل غير المهمة
                if (!error.message.includes('aria-hidden')) {
                    console.error('❌ خطأ في اختبار التفاعل:', error);
                }
            }
        }, 1000);
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة Stripe:', error);
        showError('خطأ في تهيئة نظام الدفع: ' + error.message);
    }
}

async function processPayment() {
    isProcessing = true;
    
    const submitButton = document.getElementById('submit-button');
    const submitText = document.getElementById('submit-text');
    
    try {
        console.log('🔄 بدء معالجة الدفع...');
        
        // تحديث واجهة المستخدم
        submitButton.disabled = true;
        submitText.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري معالجة الدفع...';
        
        // إنشاء طريقة الدفع
        const {error, paymentMethod} = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
                name: '{{ request.user.get_full_name }}',
                email: '{{ request.user.email }}',
            },
        });
        
        if (error) {
            throw new Error(error.message);
        }
        
        console.log('✅ تم إنشاء طريقة الدفع:', paymentMethod.id);
        
        // إرسال للخادم
        const response = await fetch('{% url "stripe_payment" payment.id %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                payment_method_id: paymentMethod.id,
                amount: parseFloat('{{ payment.amount }}'),
                currency: '{{ payment.subscription.plan.currency }}'
            })
        });
        
        const data = await response.json();
        console.log('📨 استجابة الخادم:', data);
        
        if (data.success) {
            console.log('🎉 نجح الدفع!');
            alert('تم الدفع بنجاح! سيتم توجيهك إلى صفحة الاشتراكات.');
            
            // إرسال رسالة للنافذة الأصلية إذا كانت منبثقة
            if (window.opener) {
                try {
                    window.opener.postMessage({
                        type: 'payment_result',
                        status: 'success',
                        data: {
                            payment_id: data.payment_id || parseInt('{{ payment.id }}'),
                            amount: parseFloat('{{ payment.amount }}'),
                            currency: '{{ payment.subscription.plan.currency }}',
                            payment_method: 'stripe'
                        }
                    }, '*');
                    window.close();
                } catch (error) {
                    console.error('خطأ في إرسال الرسالة:', error);
                    window.location.href = '{% url "student_subscriptions" %}';
                }
            } else {
                window.location.href = '{% url "student_subscriptions" %}';
            }
        } else {
            throw new Error(data.message || 'حدث خطأ أثناء معالجة الدفع');
        }
        
    } catch (error) {
        console.error('❌ خطأ في الدفع:', error);
        showError(error.message);
        
        // إعادة تفعيل الزر
        submitButton.disabled = false;
        submitButton.classList.remove('bg-gray-400', 'cursor-not-allowed');
        submitButton.classList.add('bg-purple-600', 'hover:bg-purple-700');
        submitText.textContent = 'ادفع الآن $' + '{{ payment.amount }}';
        
        // إرسال رسالة خطأ للنافذة الأصلية إذا كانت منبثقة
        if (window.opener) {
            try {
                window.opener.postMessage({
                    type: 'payment_result',
                    status: 'failed',
                    data: { message: error.message }
                }, '*');
            } catch (error) {
                console.error('خطأ في إرسال الرسالة:', error);
            }
        }
    } finally {
        isProcessing = false;
    }
}

function showError(message) {
    const errorDiv = document.getElementById('card-errors');
    const errorText = document.getElementById('card-errors-text');
    
    if (errorDiv && errorText) {
        errorText.textContent = message;
        errorDiv.classList.remove('hidden');
        
        // التمرير للخطأ
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}
</script>

{% endblock %}
