{% extends 'base.html' %}
{% load static %}

{% block title %}الإشعارات{% endblock %}

{% block extra_css %}
<style>
    .notification-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    .notification-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .notification-unread {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-left-color: #3b82f6;
    }
    .notification-urgent {
        border-left-color: #ef4444;
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    }
    .notification-high {
        border-left-color: #f59e0b;
    }
    .notification-medium {
        border-left-color: #10b981;
    }
    .notification-low {
        border-left-color: #6b7280;
    }
    .filter-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-white">
    <div class="container mx-auto px-4 py-8">

        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-islamic-primary mb-2">الإشعارات</h1>
                    <p class="text-gray-600">تابع جميع الإشعارات والتحديثات المهمة</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    {% if unread_notifications > 0 %}
                    <form method="post" action="{% url 'notifications:mark_all_read' %}" class="inline">
                        {% csrf_token %}
                        <button type="submit" class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors">
                            <i class="fas fa-check-double ml-2"></i>
                            تحديد الكل كمقروء
                        </button>
                    </form>
                    {% endif %}
                    <a href="{% url 'notifications:compose_message' %}" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        رسالة جديدة
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card rounded-xl shadow-lg p-6 text-white">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-bell text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-white text-opacity-80 text-sm">إجمالي الإشعارات</p>
                        <p class="text-2xl font-bold">{{ total_notifications }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-envelope-open text-blue-600 text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">غير مقروءة</p>
                        <p class="text-2xl font-bold text-blue-600">{{ unread_notifications }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">عاجلة</p>
                        <p class="text-2xl font-bold text-red-600">{{ urgent_notifications }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">مقروءة</p>
                        <p class="text-2xl font-bold text-green-600">{{ total_notifications|add:0|add:unread_notifications|add:"-"|add:unread_notifications|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card rounded-xl shadow-lg p-6 mb-8">
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع الإشعار</label>
                    <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="all" {% if current_type == 'all' %}selected{% endif %}>جميع الأنواع</option>
                        {% for type_key, type_name in notification_type_choices %}
                            <option value="{{ type_key }}" {% if current_type == type_key %}selected{% endif %}>{{ type_name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الأولوية</label>
                    <select name="priority" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="all" {% if current_priority == 'all' %}selected{% endif %}>جميع الأولويات</option>
                        {% for priority_key, priority_name in priority_choices %}
                            <option value="{{ priority_key }}" {% if current_priority == priority_key %}selected{% endif %}>{{ priority_name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">حالة القراءة</label>
                    <select name="read" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="all" {% if current_read_status == 'all' %}selected{% endif %}>الكل</option>
                        <option value="unread" {% if current_read_status == 'unread' %}selected{% endif %}>غير مقروءة</option>
                        <option value="read" {% if current_read_status == 'read' %}selected{% endif %}>مقروءة</option>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit" class="w-full bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-secondary transition-colors">
                        <i class="fas fa-filter ml-2"></i>
                        تطبيق الفلتر
                    </button>
                </div>
            </form>
        </div>

        <!-- Bulk Actions Toolbar -->
        {% if notifications %}
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- Select All Checkbox -->
                    <div class="flex items-center">
                        <input type="checkbox" id="select-all-notifications" class="w-5 h-5 text-islamic-primary border-gray-300 rounded focus:ring-islamic-primary">
                        <label for="select-all-notifications" class="mr-2 text-sm font-medium text-gray-700">تحديد الكل</label>
                    </div>

                    <!-- Selected Count -->
                    <span id="selected-count" class="text-sm text-gray-500 hidden">
                        <span id="selected-number">0</span> إشعار محدد
                    </span>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button id="delete-selected-btn" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-trash ml-2"></i>
                        حذف المختار
                    </button>

                    <div class="relative">
                        <button id="bulk-actions-dropdown" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                            <i class="fas fa-ellipsis-v ml-2"></i>
                            المزيد
                            <i class="fas fa-chevron-down mr-2"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="bulk-actions-menu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10 hidden">
                            <div class="py-1">
                                <button id="delete-all-btn" class="w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                    <i class="fas fa-trash-alt ml-2"></i>
                                    حذف جميع الإشعارات
                                </button>
                                <button id="delete-read-btn" class="w-full text-right px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 transition-colors">
                                    <i class="fas fa-check-circle ml-2"></i>
                                    حذف المقروءة فقط
                                </button>
                                <button id="delete-unread-btn" class="w-full text-right px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 transition-colors">
                                    <i class="fas fa-envelope ml-2"></i>
                                    حذف غير المقروءة فقط
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Notifications List -->
        {% if notifications %}
        <div class="space-y-4">
            {% for notification in notifications %}
            <div class="notification-card bg-white rounded-xl shadow-lg overflow-hidden
                        {% if not notification.is_read %}notification-unread{% endif %}
                        {% if notification.priority == 'urgent' %}notification-urgent{% endif %}
                        {% if notification.priority == 'high' %}notification-high{% endif %}
                        {% if notification.priority == 'medium' %}notification-medium{% endif %}
                        {% if notification.priority == 'low' %}notification-low{% endif %}">

                <div class="p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start flex-1">
                            <!-- Checkbox -->
                            <div class="flex-shrink-0 ml-4">
                                <input type="checkbox" class="notification-checkbox w-5 h-5 text-islamic-primary border-gray-300 rounded focus:ring-islamic-primary" value="{{ notification.id }}">
                            </div>

                            <!-- Icon -->
                            <div class="flex-shrink-0 ml-4">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center
                                            {% if notification.priority == 'urgent' %}bg-red-100{% endif %}
                                            {% if notification.priority == 'high' %}bg-orange-100{% endif %}
                                            {% if notification.priority == 'medium' %}bg-blue-100{% endif %}
                                            {% if notification.priority == 'low' %}bg-gray-100{% endif %}">
                                    {% if notification.notification_type == 'lesson_reminder' %}
                                        <i class="fas fa-clock text-blue-600"></i>
                                    {% elif notification.notification_type == 'payment_received' %}
                                        <i class="fas fa-money-bill-wave text-green-600"></i>
                                    {% elif notification.notification_type == 'new_message' %}
                                        <i class="fas fa-envelope text-purple-600"></i>
                                    {% elif notification.notification_type == 'support_ticket' %}
                                        <i class="fas fa-ticket-alt text-orange-600"></i>
                                    {% elif notification.notification_type == 'system_announcement' %}
                                        <i class="fas fa-bullhorn text-red-600"></i>
                                    {% else %}
                                        <i class="fas fa-bell text-gray-600"></i>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Content -->
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900">{{ notification.title }}</h3>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        {% if notification.priority == 'urgent' %}
                                            <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium animate-pulse">عاجل</span>
                                        {% elif notification.priority == 'high' %}
                                            <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium">عالي</span>
                                        {% elif notification.priority == 'medium' %}
                                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">متوسط</span>
                                        {% else %}
                                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full font-medium">منخفض</span>
                                        {% endif %}

                                        {% if not notification.is_read %}
                                            <span class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></span>
                                        {% endif %}
                                    </div>
                                </div>

                                <p class="text-gray-600 mb-3">{{ notification.message }}</p>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-sm text-gray-500">
                                        {% if notification.sender %}
                                            <span class="ml-4">من: {{ notification.sender.get_full_name }}</span>
                                        {% endif %}
                                        <span data-relative-time="{{ notification.created_at|date:'c' }}">{{ notification.created_at|timesince }} مضت</span>
                                    </div>

                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        {% if notification.action_url %}
                                            <a href="{{ notification.action_url }}" class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-secondary transition-colors text-sm">
                                                {{ notification.action_text|default:"عرض التفاصيل" }}
                                            </a>
                                        {% endif %}

                                        {% if not notification.is_read %}
                                            <form method="post" action="{% url 'notifications:mark_read' notification.id %}" class="inline">
                                                {% csrf_token %}
                                                <button type="submit" class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                                                    <i class="fas fa-check ml-1"></i>
                                                    تحديد كمقروء
                                                </button>
                                            </form>
                                        {% endif %}

                                        <!-- Delete Single Notification Button -->
                                        <form method="post" action="{% url 'notifications:delete_notification' notification.id %}" class="inline delete-single-form">
                                            {% csrf_token %}
                                            <button type="submit" class="bg-red-100 text-red-600 px-4 py-2 rounded-lg hover:bg-red-200 transition-colors text-sm delete-single-btn" data-notification-title="{{ notification.title }}">
                                                <i class="fas fa-trash ml-1"></i>
                                                حذف
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if notifications.has_other_pages %}
        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-center">
                <nav class="flex items-center space-x-2 space-x-reverse">
                    {% if notifications.has_previous %}
                        <a href="?page={{ notifications.previous_page_number }}&type={{ current_type }}&priority={{ current_priority }}&read={{ current_read_status }}"
                           class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}

                    <span class="px-4 py-2 bg-islamic-primary text-white rounded-lg">
                        {{ notifications.number }} من {{ notifications.paginator.num_pages }}
                    </span>

                    {% if notifications.has_next %}
                        <a href="?page={{ notifications.next_page_number }}&type={{ current_type }}&priority={{ current_priority }}&read={{ current_read_status }}"
                           class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
        </div>
        {% endif %}

        {% else %}
        <!-- Empty State -->
        <div class="bg-white rounded-xl shadow-lg p-12 text-center">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-bell-slash text-gray-400 text-4xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">لا توجد إشعارات</h3>
            <p class="text-gray-600 mb-6 max-w-md mx-auto">
                لم تتلق أي إشعارات حتى الآن. ستظهر هنا جميع الإشعارات والتحديثات المهمة.
            </p>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Auto-mark notifications as read when clicked
document.addEventListener('DOMContentLoaded', function() {
    const notificationCards = document.querySelectorAll('.notification-card');

    notificationCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking on buttons
            if (e.target.tagName === 'BUTTON' || e.target.tagName === 'A' || e.target.tagName === 'INPUT') {
                return;
            }

            // Add visual feedback
            this.style.opacity = '0.8';
            setTimeout(() => {
                this.style.opacity = '1';
            }, 200);
        });
    });

    // ==================== NOTIFICATION DELETION FUNCTIONALITY ====================

    // Get CSRF token
    function getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    }

    // Elements
    const selectAllCheckbox = document.getElementById('select-all-notifications');
    const notificationCheckboxes = document.querySelectorAll('.notification-checkbox');
    const selectedCount = document.getElementById('selected-count');
    const selectedNumber = document.getElementById('selected-number');
    const deleteSelectedBtn = document.getElementById('delete-selected-btn');
    const bulkActionsDropdown = document.getElementById('bulk-actions-dropdown');
    const bulkActionsMenu = document.getElementById('bulk-actions-menu');
    const deleteAllBtn = document.getElementById('delete-all-btn');
    const deleteReadBtn = document.getElementById('delete-read-btn');
    const deleteUnreadBtn = document.getElementById('delete-unread-btn');
    const deleteSingleBtns = document.querySelectorAll('.delete-single-btn');

    // Update selected count and button states
    function updateSelectionState() {
        const checkedBoxes = document.querySelectorAll('.notification-checkbox:checked');
        const count = checkedBoxes.length;

        selectedNumber.textContent = count;

        if (count > 0) {
            selectedCount.classList.remove('hidden');
            deleteSelectedBtn.disabled = false;
        } else {
            selectedCount.classList.add('hidden');
            deleteSelectedBtn.disabled = true;
        }

        // Update select all checkbox state
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === notificationCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }

    // Select/Deselect all notifications
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            notificationCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateSelectionState();
        });
    }

    // Individual checkbox change
    notificationCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectionState);
    });

    // Bulk actions dropdown toggle
    if (bulkActionsDropdown) {
        bulkActionsDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
            bulkActionsMenu.classList.toggle('hidden');
        });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        if (bulkActionsMenu) {
            bulkActionsMenu.classList.add('hidden');
        }
    });

    // Confirmation dialog
    function showConfirmDialog(message, onConfirm) {
        if (confirm(message)) {
            onConfirm();
        }
    }

    // Show loading state
    function showLoadingState(button, originalText) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحذف...';
        return originalText;
    }

    // Restore button state
    function restoreButtonState(button, originalText) {
        button.disabled = false;
        button.innerHTML = originalText;
    }

    // Show success message
    function showSuccessMessage(message) {
        // Create and show a temporary success message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
        alertDiv.textContent = message;
        document.body.appendChild(alertDiv);

        setTimeout(() => {
            alertDiv.remove();
        }, 3000);
    }

    // Delete selected notifications
    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', function() {
            const checkedBoxes = document.querySelectorAll('.notification-checkbox:checked');
            const notificationIds = Array.from(checkedBoxes).map(cb => cb.value);

            if (notificationIds.length === 0) {
                alert('يرجى تحديد إشعارات للحذف.');
                return;
            }

            showConfirmDialog(
                `هل أنت متأكد من حذف ${notificationIds.length} إشعار؟`,
                () => {
                    const originalText = this.innerHTML;
                    showLoadingState(this, originalText);

                    fetch('{% url "notifications:delete_selected" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCSRFToken(),
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            notification_ids: notificationIds
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccessMessage(data.message);
                            // Remove deleted notifications from DOM
                            checkedBoxes.forEach(cb => {
                                cb.closest('.notification-card').remove();
                            });
                            updateSelectionState();
                            // Reload page if no notifications left
                            if (document.querySelectorAll('.notification-card').length === 0) {
                                setTimeout(() => location.reload(), 1000);
                            }
                        } else {
                            alert('خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء الحذف.');
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        restoreButtonState(this, originalText);
                    });
                }
            );
        });
    }

    // Delete all notifications
    if (deleteAllBtn) {
        deleteAllBtn.addEventListener('click', function() {
            showConfirmDialog(
                'هل أنت متأكد من حذف جميع الإشعارات؟ هذا الإجراء لا يمكن التراجع عنه.',
                () => {
                    const originalText = this.innerHTML;
                    showLoadingState(this, originalText);

                    fetch('{% url "notifications:delete_all" %}', {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCSRFToken(),
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccessMessage(data.message);
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            alert('خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء الحذف.');
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        restoreButtonState(this, originalText);
                    });
                }
            );
        });
    }

    // Delete read notifications
    if (deleteReadBtn) {
        deleteReadBtn.addEventListener('click', function() {
            showConfirmDialog(
                'هل أنت متأكد من حذف جميع الإشعارات المقروءة؟',
                () => {
                    const originalText = this.innerHTML;
                    showLoadingState(this, originalText);

                    fetch('{% url "notifications:delete_read" %}', {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCSRFToken(),
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccessMessage(data.message);
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            alert('خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء الحذف.');
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        restoreButtonState(this, originalText);
                    });
                }
            );
        });
    }

    // Delete unread notifications
    if (deleteUnreadBtn) {
        deleteUnreadBtn.addEventListener('click', function() {
            showConfirmDialog(
                'هل أنت متأكد من حذف جميع الإشعارات غير المقروءة؟',
                () => {
                    const originalText = this.innerHTML;
                    showLoadingState(this, originalText);

                    fetch('{% url "notifications:delete_unread" %}', {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCSRFToken(),
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccessMessage(data.message);
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            alert('خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء الحذف.');
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        restoreButtonState(this, originalText);
                    });
                }
            );
        });
    }

    // Delete single notification
    deleteSingleBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const notificationTitle = this.getAttribute('data-notification-title');
            const form = this.closest('form');

            showConfirmDialog(
                `هل أنت متأكد من حذف الإشعار "${notificationTitle}"؟`,
                () => {
                    const originalText = this.innerHTML;
                    showLoadingState(this, originalText);

                    fetch(form.action, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCSRFToken(),
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccessMessage(data.message);
                            // Remove notification from DOM
                            this.closest('.notification-card').remove();
                            updateSelectionState();
                            // Reload page if no notifications left
                            if (document.querySelectorAll('.notification-card').length === 0) {
                                setTimeout(() => location.reload(), 1000);
                            }
                        } else {
                            alert('خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء الحذف.');
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        restoreButtonState(this, originalText);
                    });
                }
            );
        });
    });

    // Initialize selection state
    updateSelectionState();
});
</script>
{% endblock %}
