<!-- تفاصيل الطالب في النافذة المنبثقة -->
<div class="max-w-4xl mx-auto">
    <!-- معلومات الطالب الأساسية -->
    <div class="bg-gradient-to-r from-islamic-primary to-islamic-dark rounded-lg p-6 text-white mb-6">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center ml-4">
                <span class="text-2xl font-bold">{{ student.first_name|first }}{{ student.last_name|first }}</span>
            </div>
            <div>
                <h3 class="text-2xl font-bold">{{ student.get_full_name }}</h3>
                <p class="text-islamic-light-gold">{{ student.email }}</p>
                <p class="text-sm text-islamic-light-gold">عضو منذ: {{ student.date_joined|date:"d/m/Y" }}</p>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600">{{ stats.total_ratings }}</div>
            <div class="text-sm text-gray-600">إجمالي التقييمات</div>
        </div>
        <div class="bg-green-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600">{{ stats.overall_average|floatformat:1 }}</div>
            <div class="text-sm text-gray-600">المتوسط العام</div>
        </div>
        <div class="bg-purple-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-purple-600">{{ stats.scheduled_count }}</div>
            <div class="text-sm text-gray-600">حصص مجدولة</div>
        </div>
        <div class="bg-orange-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-orange-600">{{ stats.live_count }}</div>
            <div class="text-sm text-gray-600">حصص مباشرة</div>
        </div>
    </div>

    <!-- تفاصيل التقييمات -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- المعايير الأربعة -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-chart-bar text-islamic-primary ml-2"></i>
                تفاصيل المعايير
            </h4>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-gray-700">التقييم العام</span>
                    <div class="flex items-center">
                        <div class="w-24 bg-gray-200 rounded-full h-2 ml-2">
                            <div class="bg-islamic-primary h-2 rounded-full" style="width: {% widthratio stats.avg_overall 5 100 %}%"></div>
                        </div>
                        <span class="text-sm font-medium">{{ stats.avg_overall|floatformat:1 }}/5</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-700">جودة الحصة</span>
                    <div class="flex items-center">
                        <div class="w-24 bg-gray-200 rounded-full h-2 ml-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: {% widthratio stats.avg_quality 5 100 %}%"></div>
                        </div>
                        <span class="text-sm font-medium">{{ stats.avg_quality|floatformat:1 }}/5</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-700">تفاعل الطالب</span>
                    <div class="flex items-center">
                        <div class="w-24 bg-gray-200 rounded-full h-2 ml-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: {% widthratio stats.avg_interaction 5 100 %}%"></div>
                        </div>
                        <span class="text-sm font-medium">{{ stats.avg_interaction|floatformat:1 }}/5</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-700">الجودة التقنية</span>
                    <div class="flex items-center">
                        <div class="w-24 bg-gray-200 rounded-full h-2 ml-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: {% widthratio stats.avg_technical 5 100 %}%"></div>
                        </div>
                        <span class="text-sm font-medium">{{ stats.avg_technical|floatformat:1 }}/5</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- توزيع النجوم -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-star text-yellow-500 ml-2"></i>
                توزيع التقييمات
            </h4>
            <div class="space-y-3">
                {% for star, count in star_distribution.items %}
                <div class="flex items-center">
                    <span class="w-12 text-sm">{{ star }} نجوم</span>
                    <div class="flex-1 bg-gray-200 rounded-full h-2 mx-3">
                        <div class="bg-yellow-400 h-2 rounded-full" style="width: {% if stats.total_ratings > 0 %}{% widthratio count stats.total_ratings 100 %}{% else %}0{% endif %}%"></div>
                    </div>
                    <span class="w-8 text-sm text-right">{{ count }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- آخر التقييمات -->
    {% if recent_ratings %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-clock text-blue-500 ml-2"></i>
            آخر التقييمات
        </h4>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعلم</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التقييم</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">نوع الحصة</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التعليق</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for rating in recent_ratings|slice:":5" %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            {{ rating.created_at|date:"d/m/Y" }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            {{ rating.teacher.get_full_name|default:"غير محدد" }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-900">{{ rating.overall_rating }}/5</span>
                                <div class="flex mr-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= rating.overall_rating %}
                                            <span class="text-yellow-400">★</span>
                                        {% else %}
                                            <span class="text-gray-300">★</span>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            {% if rating.lesson_type == 'scheduled' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    مجدولة
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    مباشرة
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900 max-w-xs truncate">
                            {{ rating.comment|default:"لا يوجد تعليق" }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- أزرار الإجراءات -->
    <div class="flex justify-end space-x-3 space-x-reverse mt-6">
        <button onclick="generateStudentReport({{ student.id }})" class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors">
            <i class="fas fa-file-pdf ml-2"></i>
            تحميل تقرير PDF
        </button>
        <button onclick="closeModal()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
            إغلاق
        </button>
    </div>
</div>
