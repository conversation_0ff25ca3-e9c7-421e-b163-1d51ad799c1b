/**
 * Timezone Manager - إدارة المناطق الزمنية
 * يدير عرض الأوقات حسب المنطقة الزمنية للمستخدم
 */

class TimezoneManager {
    constructor() {
        this.userTimezone = this.getUserTimezone();
        this.defaultTimezone = 'Asia/Riyadh';
        this.init();
    }

    /**
     * تهيئة مدير المناطق الزمنية
     */
    init() {
        // تحديث جميع عناصر الوقت عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            this.updateAllTimeElements();
            this.startClockUpdate();
        });

        // تحديث الوقت كل دقيقة
        setInterval(() => {
            this.updateAllTimeElements();
        }, 60000);
    }

    /**
     * الحصول على المنطقة الزمنية للمستخدم
     */
    getUserTimezone() {
        // محاولة الحصول على المنطقة الزمنية من profile المستخدم
        const userTimezoneElement = document.getElementById('user-timezone');
        if (userTimezoneElement && userTimezoneElement.value) {
            return userTimezoneElement.value;
        }

        // محاولة الحصول من localStorage
        const savedTimezone = localStorage.getItem('userTimezone');
        if (savedTimezone) {
            return savedTimezone;
        }

        // استخدام المنطقة الزمنية للمتصفح
        try {
            return Intl.DateTimeFormat().resolvedOptions().timeZone;
        } catch (e) {
            return this.defaultTimezone;
        }
    }

    /**
     * تحديث جميع عناصر الوقت في الصفحة
     */
    updateAllTimeElements() {
        // تحديث العناصر التي تحتوي على data-utc-time
        const timeElements = document.querySelectorAll('[data-utc-time]');
        timeElements.forEach(element => {
            this.updateTimeElement(element);
        });

        // تحديث العناصر التي تحتوي على data-timestamp
        const timestampElements = document.querySelectorAll('[data-timestamp]');
        timestampElements.forEach(element => {
            this.updateTimestampElement(element);
        });

        // تحديث الساعة الرئيسية
        this.updateMainClock();
    }

    /**
     * تحديث عنصر وقت واحد
     */
    updateTimeElement(element) {
        const utcTime = element.getAttribute('data-utc-time');
        const format = element.getAttribute('data-format') || 'full';
        
        if (!utcTime) return;

        try {
            const date = new Date(utcTime);
            const localTime = this.formatTime(date, format);
            element.textContent = localTime;
            
            // إضافة tooltip مع الوقت الكامل
            element.title = this.formatTime(date, 'full');
        } catch (e) {
            console.warn('خطأ في تحويل الوقت:', e);
        }
    }

    /**
     * تحديث عنصر timestamp
     */
    updateTimestampElement(element) {
        const timestamp = element.getAttribute('data-timestamp');
        const format = element.getAttribute('data-format') || 'relative';
        
        if (!timestamp) return;

        try {
            const date = new Date(parseInt(timestamp) * 1000);
            
            if (format === 'relative') {
                element.textContent = this.getRelativeTime(date);
            } else {
                element.textContent = this.formatTime(date, format);
            }
        } catch (e) {
            console.warn('خطأ في تحويل timestamp:', e);
        }
    }

    /**
     * تحديث الساعة الرئيسية
     */
    updateMainClock() {
        const clockElement = document.getElementById('main-clock');
        if (!clockElement) return;

        const now = new Date();
        const timeString = this.formatTime(now, 'time');
        const dateString = this.formatTime(now, 'date');
        
        clockElement.innerHTML = `
            <div class="text-lg font-bold">${timeString}</div>
            <div class="text-sm opacity-75">${dateString}</div>
        `;
    }

    /**
     * تنسيق الوقت حسب النوع المطلوب
     */
    formatTime(date, format) {
        const options = {
            timeZone: this.userTimezone,
            locale: 'ar-SA'
        };

        switch (format) {
            case 'time':
                return date.toLocaleTimeString('ar-SA', {
                    ...options,
                    hour: '2-digit',
                    minute: '2-digit'
                });
            
            case 'date':
                return date.toLocaleDateString('ar-SA', {
                    ...options,
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            
            case 'short':
                return date.toLocaleString('ar-SA', {
                    ...options,
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            
            case 'full':
            default:
                return date.toLocaleString('ar-SA', {
                    ...options,
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
        }
    }

    /**
     * الحصول على الوقت النسبي (منذ كم من الوقت)
     */
    getRelativeTime(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) {
            return 'الآن';
        } else if (diffMins < 60) {
            return `منذ ${diffMins} دقيقة`;
        } else if (diffHours < 24) {
            return `منذ ${diffHours} ساعة`;
        } else if (diffDays < 7) {
            return `منذ ${diffDays} يوم`;
        } else {
            return this.formatTime(date, 'date');
        }
    }

    /**
     * بدء تحديث الساعة كل ثانية
     */
    startClockUpdate() {
        setInterval(() => {
            this.updateMainClock();
        }, 1000);
    }

    /**
     * تحديث المنطقة الزمنية للمستخدم
     */
    updateUserTimezone(newTimezone) {
        this.userTimezone = newTimezone;
        localStorage.setItem('userTimezone', newTimezone);
        this.updateAllTimeElements();
    }

    /**
     * الحصول على قائمة المناطق الزمنية الشائعة
     */
    getCommonTimezones() {
        return [
            { value: 'Asia/Riyadh', label: 'الرياض (السعودية)' },
            { value: 'Asia/Dubai', label: 'دبي (الإمارات)' },
            { value: 'Asia/Kuwait', label: 'الكويت' },
            { value: 'Asia/Qatar', label: 'قطر' },
            { value: 'Asia/Bahrain', label: 'البحرين' },
            { value: 'Asia/Muscat', label: 'مسقط (عمان)' },
            { value: 'Asia/Baghdad', label: 'بغداد (العراق)' },
            { value: 'Asia/Damascus', label: 'دمشق (سوريا)' },
            { value: 'Asia/Beirut', label: 'بيروت (لبنان)' },
            { value: 'Africa/Cairo', label: 'القاهرة (مصر)' },
            { value: 'Europe/London', label: 'لندن' },
            { value: 'America/New_York', label: 'نيويورك' }
        ];
    }

    /**
     * إنشاء عنصر اختيار المنطقة الزمنية
     */
    createTimezoneSelector(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const select = document.createElement('select');
        select.className = 'form-control';
        select.id = 'timezone-selector';

        const timezones = this.getCommonTimezones();
        timezones.forEach(tz => {
            const option = document.createElement('option');
            option.value = tz.value;
            option.textContent = tz.label;
            option.selected = tz.value === this.userTimezone;
            select.appendChild(option);
        });

        select.addEventListener('change', (e) => {
            this.updateUserTimezone(e.target.value);
        });

        container.appendChild(select);
    }
}

// إنشاء مثيل عام من مدير المناطق الزمنية
window.timezoneManager = new TimezoneManager();

// دوال مساعدة عامة
window.getUserProfileTimezone = function() {
    return window.timezoneManager.getUserTimezone();
};

window.formatUserTime = function(date, format = 'full') {
    return window.timezoneManager.formatTime(date, format);
};

window.updateUserTimezone = function(timezone) {
    window.timezoneManager.updateUserTimezone(timezone);
};
