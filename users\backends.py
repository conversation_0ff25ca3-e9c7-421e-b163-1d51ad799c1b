from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend
from django.db.models import Q

User = get_user_model()

class EmailOrUsernameModelBackend(ModelBackend):
    """
    Backend تسجيل دخول مخصص يسمح بتسجيل الدخول باستخدام البريد الإلكتروني أو اسم المستخدم
    ويسمح للمستخدمين المحظورين بتسجيل الدخول
    """
    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            # البحث عن المستخدم باسم المستخدم أو البريد الإلكتروني
            user = User.objects.get(Q(username=username) | Q(email=username))

            # التحقق من كلمة المرور
            if user.check_password(password):
                # السماح للمستخدمين المحظورين بتسجيل الدخول
                # سيتم التعامل مع توجيههم في middleware
                return user

        except User.DoesNotExist:
            # المستخدم غير موجود
            return None

        # كلمة المرور غير صحيحة
        return None

    def user_can_authenticate(self, user):
        """
        السماح للمستخدمين المحظورين بتسجيل الدخول
        """
        # المستخدمين المحظورين يمكنهم تسجيل الدخول
        if user.is_currently_banned():
            return True

        # بالنسبة للمستخدمين الآخرين، استخدم السلوك الافتراضي
        return super().user_can_authenticate(user)
