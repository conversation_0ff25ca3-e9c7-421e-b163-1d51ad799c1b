"""
أمر Django لإنشاء قوالب البريد الإلكتروني الافتراضية
يتم تشغيله تلقائياً عند النشر على الإنتاج
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from users.email_models import EmailTemplate, EmailEvent


class Command(BaseCommand):
    help = 'إنشاء قوالب البريد الإلكتروني الافتراضية'

    def handle(self, *args, **options):
        self.stdout.write('🚀 بدء إنشاء قوالب البريد الإلكتروني الافتراضية...')
        
        # قوالب البريد الإلكتروني الافتراضية
        default_templates = [
            {
                'name': 'تذكير بالحصة - 24 ساعة',
                'event_type': 'lesson_reminder_24h',
                'subject': '🕌 تذكير: لديك حصة غداً في {{ ACADEMY_NAME }}',
                'html_content': '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تذكير بالحصة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; direction: rtl; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #2D5016, #4A7C59); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .lesson-card { background: #f8f9fa; border-right: 4px solid #2D5016; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .button { display: inline-block; background-color: #2D5016; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕌 {{ ACADEMY_NAME }}</h1>
            <p>{{ ACADEMY_SLOGAN }}</p>
        </div>
        <div class="content">
            <h2>السلام عليكم {{ student_name }}</h2>
            <p>نذكركم بأن لديكم حصة مجدولة غداً:</p>
            
            <div class="lesson-card">
                <h3>📚 {{ lesson_title }}</h3>
                <p><strong>👨‍🏫 المعلم:</strong> {{ teacher_name }}</p>
                <p><strong>📅 التاريخ:</strong> {{ lesson_date }}</p>
                <p><strong>⏰ الوقت:</strong> {{ lesson_time }}</p>
                <p><strong>⏱️ المدة:</strong> {{ lesson_duration }} دقيقة</p>
            </div>
            
            <p>يرجى الاستعداد للحصة والدخول في الوقت المحدد.</p>
            
            <a href="{{ dashboard_url }}" class="button">دخول لوحة التحكم</a>
        </div>
        <div class="footer">
            <p>{{ ACADEMY_NAME }} - {{ ACADEMY_ADDRESS }}</p>
            <p>📞 {{ ACADEMY_PHONE }} | 📧 {{ ACADEMY_EMAIL }}</p>
        </div>
    </div>
</body>
</html>
                ''',
                'text_content': '''
السلام عليكم {{ student_name }}

نذكركم بأن لديكم حصة مجدولة غداً:

📚 الحصة: {{ lesson_title }}
👨‍🏫 المعلم: {{ teacher_name }}
📅 التاريخ: {{ lesson_date }}
⏰ الوقت: {{ lesson_time }}
⏱️ المدة: {{ lesson_duration }} دقيقة

يرجى الاستعداد للحصة والدخول في الوقت المحدد.

رابط لوحة التحكم: {{ dashboard_url }}

{{ ACADEMY_NAME }}
{{ ACADEMY_ADDRESS }}
📞 {{ ACADEMY_PHONE }} | 📧 {{ ACADEMY_EMAIL }}
                ''',
                'is_active': True
            },
            {
                'name': 'تذكير بالحصة - 30 دقيقة',
                'event_type': 'lesson_reminder_30min',
                'subject': '⏰ تذكير عاجل: حصتك تبدأ خلال 30 دقيقة',
                'html_content': '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تذكير عاجل بالحصة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; direction: rtl; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .urgent-card { background: #fff3cd; border: 2px solid #ffc107; padding: 20px; margin: 20px 0; border-radius: 8px; text-align: center; }
        .button { display: inline-block; background-color: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ تذكير عاجل</h1>
            <p>{{ ACADEMY_NAME }}</p>
        </div>
        <div class="content">
            <h2>السلام عليكم {{ student_name }}</h2>
            
            <div class="urgent-card">
                <h3>🚨 حصتك تبدأ خلال 30 دقيقة!</h3>
                <p><strong>📚 الحصة:</strong> {{ lesson_title }}</p>
                <p><strong>👨‍🏫 المعلم:</strong> {{ teacher_name }}</p>
                <p><strong>⏰ الوقت:</strong> {{ lesson_time }}</p>
            </div>
            
            <p>يرجى الاستعداد والدخول إلى لوحة التحكم الآن.</p>
            
            <a href="{{ dashboard_url }}" class="button">دخول الحصة الآن</a>
        </div>
        <div class="footer">
            <p>{{ ACADEMY_NAME }}</p>
        </div>
    </div>
</body>
</html>
                ''',
                'text_content': '''
السلام عليكم {{ student_name }}

🚨 تذكير عاجل: حصتك تبدأ خلال 30 دقيقة!

📚 الحصة: {{ lesson_title }}
👨‍🏫 المعلم: {{ teacher_name }}
⏰ الوقت: {{ lesson_time }}

يرجى الدخول إلى لوحة التحكم الآن.

رابط لوحة التحكم: {{ dashboard_url }}

{{ ACADEMY_NAME }}
                ''',
                'is_active': True
            },
            {
                'name': 'تذكير انتهاء الاشتراك - 7 أيام',
                'event_type': 'subscription_expiry_7days',
                'subject': '📋 تذكير: اشتراكك ينتهي خلال أسبوع',
                'html_content': '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تذكير انتهاء الاشتراك</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; direction: rtl; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #2D5016, #4A7C59); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .subscription-card { background: #e3f2fd; border-right: 4px solid #2196f3; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .button { display: inline-block; background-color: #2D5016; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕌 {{ ACADEMY_NAME }}</h1>
            <p>{{ ACADEMY_SLOGAN }}</p>
        </div>
        <div class="content">
            <h2>السلام عليكم {{ student_name }}</h2>
            <p>نود تذكيركم بأن اشتراككم سينتهي قريباً:</p>
            
            <div class="subscription-card">
                <h3>📋 {{ subscription_plan }}</h3>
                <p><strong>📅 تاريخ الانتهاء:</strong> {{ expiry_date }}</p>
                <p><strong>📚 الحصص المتبقية:</strong> {{ remaining_lessons }} حصة</p>
            </div>
            
            <p>لضمان استمرار تعليمكم، يرجى تجديد الاشتراك قبل انتهاء المدة.</p>
            
            <a href="{{ subscription_url }}" class="button">تجديد الاشتراك</a>
        </div>
        <div class="footer">
            <p>{{ ACADEMY_NAME }} - {{ ACADEMY_ADDRESS }}</p>
            <p>📞 {{ ACADEMY_PHONE }} | 📧 {{ ACADEMY_EMAIL }}</p>
        </div>
    </div>
</body>
</html>
                ''',
                'text_content': '''
السلام عليكم {{ student_name }}

نود تذكيركم بأن اشتراككم سينتهي قريباً:

📋 الباقة: {{ subscription_plan }}
📅 تاريخ الانتهاء: {{ expiry_date }}
📚 الحصص المتبقية: {{ remaining_lessons }} حصة

لضمان استمرار تعليمكم، يرجى تجديد الاشتراك قبل انتهاء المدة.

رابط التجديد: {{ subscription_url }}

{{ ACADEMY_NAME }}
{{ ACADEMY_ADDRESS }}
📞 {{ ACADEMY_PHONE }} | 📧 {{ ACADEMY_EMAIL }}
                ''',
                'is_active': True
            }
        ]

        # إنشاء الأحداث أولاً
        events_created = 0
        for template_data in default_templates:
            event, created = EmailEvent.objects.get_or_create(
                event_type=template_data['event_type'],
                defaults={
                    'is_active': True
                }
            )
            if created:
                events_created += 1
                self.stdout.write(f'✅ تم إنشاء الحدث: {event.get_event_type_display()}')

        # إنشاء القوالب
        templates_created = 0
        for template_data in default_templates:
            try:
                # تحديد نوع القالب بناءً على نوع الحدث
                template_type_mapping = {
                    'student_registration': 'welcome',
                    'lesson_reminder_24h': 'lesson_reminder',
                    'lesson_reminder_1h': 'lesson_reminder',
                    'lesson_completed': 'lesson_report',
                    'subscription_expiry_7d': 'subscription_expiry',
                    'subscription_expiry_3d': 'subscription_expiry',
                    'subscription_expiry_1d': 'subscription_expiry',
                    'subscription_expired': 'subscription_expiry',
                    'daily_summary': 'admin_report',
                    'weekly_report': 'admin_report',
                    'monthly_report': 'admin_report',
                    'payment_successful': 'payment',
                    'payment_failed': 'payment',
                }

                template_type = template_type_mapping.get(template_data['event_type'], 'general')

                template, created = EmailTemplate.objects.get_or_create(
                    name=template_data['name'],
                    defaults={
                        'template_type': template_type,
                        'subject': template_data['subject'],
                        'html_content': template_data['html_content'],
                        'text_content': template_data['text_content'],
                        'is_active': template_data['is_active'],
                        'is_default': False  # تجنب مشكلة unique_together
                    }
                )
                if created:
                    templates_created += 1
                    self.stdout.write(f'✅ تم إنشاء القالب: {template.name}')
                else:
                    self.stdout.write(f'ℹ️ القالب موجود مسبقاً: {template.name}')
            except Exception as e:
                self.stdout.write(f'❌ خطأ في إنشاء القالب {template_data["name"]}: {str(e)}')

        self.stdout.write(
            self.style.SUCCESS(
                f'🎉 تم الانتهاء! تم إنشاء {events_created} أحداث و {templates_created} قوالب جديدة.'
            )
        )
