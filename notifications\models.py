from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

User = get_user_model()


class Notification(models.Model):
    """نموذج الإشعارات"""

    NOTIFICATION_TYPES = (
        # إشعارات الحصص
        ('lesson_reminder', _('تذكير بالحصة')),
        ('lesson_cancelled', _('إلغاء الحصة')),
        ('lesson_rescheduled', _('إعادة جدولة الحصة')),
        ('lesson_completed', _('اكتمال حصة')),
        ('lesson_created', _('إنشاء حصة جديدة')),
        ('lesson_starting_soon', _('الحصة تبدأ قريباً')),

        # إشعارات المدفوعات
        ('payment_received', _('استلام دفعة')),
        ('payment_confirmed', _('تأكيد دفعة')),
        ('payment_reminder', _('تذكير بالدفع')),
        ('payment_failed', _('فشل في الدفع')),
        ('payment_status_changed', _('تغيير حالة الدفع')),

        # إشعارات الاشتراكات
        ('subscription_created', _('إنشاء اشتراك جديد')),
        ('subscription_payment_received', _('استلام دفعة اشتراك')),
        ('subscription_activated', _('تفعيل اشتراك')),
        ('subscription_cancelled', _('إلغاء اشتراك')),
        ('subscription_expiring_soon', _('اشتراك على وشك الانتهاء')),
        ('subscription_lessons_exhausted', _('انتهاء حصص الاشتراك')),
        ('subscription_needs_approval', _('اشتراك يحتاج موافقة')),

        # إشعارات التسجيل والدورات
        ('new_enrollment', _('تسجيل جديد')),
        ('enrollment_activated', _('تفعيل التسجيل')),
        ('course_completed', _('اكتمال الدورة')),

        # إشعارات التقييمات
        ('rating_received', _('استلام تقييم')),
        ('rating_request', _('طلب تقييم')),
        ('lesson_report', _('تقرير حصة')),

        # إشعارات المعلمين
        ('new_student_assigned', _('تعيين طالب جديد')),
        ('teacher_earnings_updated', _('تحديث أرباح المعلم')),
        ('teacher_rate_changed', _('تغيير أسعار المعلم')),

        # إشعارات الدعم والنظام
        ('new_message', _('رسالة جديدة')),
        ('support_ticket', _('تذكرة دعم')),
        ('support_ticket_updated', _('تحديث تذكرة دعم')),
        ('system_announcement', _('إعلان النظام')),

        # إشعارات المدير
        ('admin_new_user', _('مستخدم جديد')),
        ('admin_payment_pending', _('دفعة في الانتظار')),
        ('admin_low_rating', _('تقييم منخفض')),
    )

    PRIORITY_LEVELS = (
        ('low', _('منخفض')),
        ('medium', _('متوسط')),
        ('high', _('عالي')),
        ('urgent', _('عاجل')),
    )

    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name=_('المستقبل')
    )

    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_notifications',
        blank=True,
        null=True,
        verbose_name=_('المرسل')
    )

    notification_type = models.CharField(
        max_length=30,
        choices=NOTIFICATION_TYPES,
        verbose_name=_('نوع الإشعار')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('العنوان')
    )

    message = models.TextField(
        verbose_name=_('الرسالة')
    )

    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='medium',
        verbose_name=_('الأولوية')
    )

    is_read = models.BooleanField(
        default=False,
        verbose_name=_('مقروء')
    )

    read_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت القراءة')
    )

    action_url = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('رابط الإجراء')
    )

    action_text = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('نص الإجراء')
    )

    email_sent = models.BooleanField(
        default=False,
        verbose_name=_('تم إرسال إيميل')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('إشعار')
        verbose_name_plural = _('الإشعارات')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.recipient.get_full_name()}"

    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        from django.utils import timezone
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()


class Message(models.Model):
    """نموذج الرسائل الداخلية"""

    MESSAGE_TYPES = (
        ('private', _('رسالة خاصة')),
        ('support', _('رسالة دعم')),
        ('announcement', _('إعلان عام')),
        ('update', _('تحديث النظام')),
        ('maintenance', _('إشعار صيانة')),
        ('policy', _('تحديث السياسات')),
        ('training', _('دورة تدريبية')),
        ('event', _('فعالية أو مناسبة')),
        ('urgent', _('إشعار عاجل')),
        ('welcome', _('رسالة ترحيب')),
        ('reminder', _('تذكير')),
        ('congratulations', _('تهنئة')),
    )

    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        verbose_name=_('المرسل')
    )

    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_messages',
        verbose_name=_('المستقبل')
    )

    message_type = models.CharField(
        max_length=15,
        choices=MESSAGE_TYPES,
        default='private',
        verbose_name=_('نوع الرسالة')
    )

    subject = models.CharField(
        max_length=200,
        verbose_name=_('الموضوع')
    )

    content = models.TextField(
        verbose_name=_('المحتوى')
    )

    is_read = models.BooleanField(
        default=False,
        verbose_name=_('مقروءة')
    )

    read_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت القراءة')
    )

    parent_message = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='replies',
        verbose_name=_('الرسالة الأصلية')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإرسال')
    )

    class Meta:
        verbose_name = _('رسالة')
        verbose_name_plural = _('الرسائل')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.subject} - من {self.sender.get_full_name()} إلى {self.recipient.get_full_name()}"

    def mark_as_read(self):
        """تحديد الرسالة كمقروءة"""
        from django.utils import timezone
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()


class BulkMessage(models.Model):
    """نموذج الرسائل الجماعية للمدير"""

    MESSAGE_TYPES = (
        ('announcement', _('إعلان عام')),
        ('update', _('تحديث النظام')),
        ('maintenance', _('إشعار صيانة')),
        ('policy', _('تحديث السياسات')),
        ('training', _('دورة تدريبية')),
        ('event', _('فعالية أو مناسبة')),
        ('urgent', _('إشعار عاجل')),
        ('welcome', _('رسالة ترحيب')),
        ('reminder', _('تذكير')),
        ('congratulations', _('تهنئة')),
        ('newsletter', _('نشرة إخبارية')),
        ('survey', _('استطلاع رأي')),
    )

    RECIPIENT_TYPES = (
        ('all', _('جميع المستخدمين')),
        ('teachers', _('المعلمين فقط')),
        ('students', _('الطلاب فقط')),
        ('active_users', _('المستخدمين النشطين')),
        ('new_users', _('المستخدمين الجدد')),
        ('custom', _('مستخدمين محددين')),
    )

    PRIORITY_LEVELS = (
        ('low', _('منخفض')),
        ('medium', _('متوسط')),
        ('high', _('عالي')),
        ('urgent', _('عاجل')),
    )

    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_bulk_messages',
        limit_choices_to={'user_type': 'admin'},
        verbose_name=_('المرسل')
    )

    message_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPES,
        default='announcement',
        verbose_name=_('نوع الرسالة')
    )

    recipient_type = models.CharField(
        max_length=20,
        choices=RECIPIENT_TYPES,
        default='all',
        verbose_name=_('نوع المستقبلين')
    )

    custom_recipients = models.ManyToManyField(
        User,
        blank=True,
        related_name='received_bulk_messages',
        verbose_name=_('مستقبلين محددين')
    )

    subject = models.CharField(
        max_length=200,
        verbose_name=_('الموضوع')
    )

    content = models.TextField(
        verbose_name=_('المحتوى')
    )

    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='medium',
        verbose_name=_('الأولوية')
    )

    send_email = models.BooleanField(
        default=False,
        verbose_name=_('إرسال إيميل أيضاً')
    )

    schedule_send = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('جدولة الإرسال')
    )

    is_sent = models.BooleanField(
        default=False,
        verbose_name=_('تم الإرسال')
    )

    sent_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت الإرسال')
    )

    recipients_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('عدد المستقبلين')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('رسالة جماعية')
        verbose_name_plural = _('الرسائل الجماعية')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.subject} - {self.get_recipient_type_display()}"

    def get_recipients(self):
        """الحصول على قائمة المستقبلين"""
        if self.recipient_type == 'all':
            return User.objects.filter(is_active=True).exclude(id=self.sender.id)
        elif self.recipient_type == 'teachers':
            return User.objects.filter(user_type='teacher', is_active=True)
        elif self.recipient_type == 'students':
            return User.objects.filter(user_type='student', is_active=True)
        elif self.recipient_type == 'active_users':
            from django.utils import timezone
            from datetime import timedelta
            last_month = timezone.now() - timedelta(days=30)
            return User.objects.filter(
                is_active=True,
                last_login__gte=last_month
            ).exclude(id=self.sender.id)
        elif self.recipient_type == 'new_users':
            from django.utils import timezone
            from datetime import timedelta
            last_week = timezone.now() - timedelta(days=7)
            return User.objects.filter(
                is_active=True,
                date_joined__gte=last_week
            ).exclude(id=self.sender.id)
        elif self.recipient_type == 'custom':
            return self.custom_recipients.all()
        else:
            return User.objects.none()

    def send_to_recipients(self):
        """إرسال الرسالة للمستقبلين"""
        from django.utils import timezone
        from .utils import create_notification

        if self.is_sent:
            return False

        recipients = self.get_recipients()
        sent_count = 0

        for recipient in recipients:
            # إنشاء رسالة فردية
            individual_message = Message.objects.create(
                sender=self.sender,
                recipient=recipient,
                subject=self.subject,
                content=self.content,
                message_type=self.message_type
            )

            # إنشاء إشعار
            create_notification(
                recipient=recipient,
                notification_type='new_message',
                title=f'رسالة جديدة: {self.subject}',
                message=f'رسالة {self.get_message_type_display()} من {self.sender.get_full_name()}',
                sender=self.sender,
                priority=self.priority,
                action_url=f'/notifications/messages/{individual_message.id}/',
                action_text='عرض الرسالة'
            )

            sent_count += 1

        # تحديث حالة الرسالة الجماعية
        self.is_sent = True
        self.sent_at = timezone.now()
        self.recipients_count = sent_count
        self.save()

        return sent_count

