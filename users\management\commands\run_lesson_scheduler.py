"""
مجدول بسيط لتذكيرات الحصص - يعمل بدون Celery
"""

import time
import threading
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from users.tasks import send_lesson_reminder_30min, send_lesson_reminder_1day
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'مجدول بسيط لتذكيرات الحصص'

    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,  # كل دقيقة
            help='فترة الفحص بالثواني (افتراضي: 60 ثانية)'
        )

    def handle(self, *args, **options):
        interval = options['interval']
        
        self.stdout.write(
            self.style.SUCCESS(f'🚀 بدء مجدول تذكيرات الحصص - فحص كل {interval} ثانية')
        )
        
        # متغيرات لتتبع آخر تشغيل
        last_30min_check = None
        last_1day_check = None
        
        try:
            cycle_count = 0
            while True:
                now = timezone.now()
                current_time = now.strftime('%H:%M:%S')
                cycle_count += 1

                # عرض حالة كل دورة
                self.stdout.write(f'🔄 {current_time} - دورة فحص #{cycle_count}')

                # فحص تذكيرات 30 دقيقة باستمرار
                self.stdout.write(f'⏰ فحص تذكيرات 30 دقيقة...')
                try:
                    result = send_lesson_reminder_30min()
                    if "تم إرسال" in str(result) and "0" not in str(result):
                        self.stdout.write(f'   🎉 {result}')
                    else:
                        self.stdout.write(f'   ℹ️ {result}')
                except Exception as e:
                    self.stdout.write(f'   ❌ خطأ في تذكير 30 دقيقة: {e}')

                # فحص تذكيرات يوم واحد (فقط في الساعة 9)
                if now.hour == 9:
                    check_key_1day = now.strftime('%Y-%m-%d')
                    if last_1day_check != check_key_1day:
                        self.stdout.write(f'📅 فحص تذكيرات يوم واحد...')
                        try:
                            result = send_lesson_reminder_1day()
                            self.stdout.write(f'   ✅ {result}')
                            last_1day_check = check_key_1day
                        except Exception as e:
                            self.stdout.write(f'   ❌ خطأ في تذكير يوم واحد: {e}')

                # عرض حالة مفصلة
                self.show_detailed_status()

                self.stdout.write(f'💤 انتظار {interval} ثانية...\n')

                # انتظار قبل الفحص التالي
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.stdout.write(self.style.WARNING('\n🛑 تم إيقاف المجدول بواسطة المستخدم'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ خطأ في المجدول: {e}'))

    def show_detailed_status(self):
        """عرض حالة مفصلة للنظام"""
        from lessons.models import LiveLesson
        from users.models import WhatsAppMessage

        now = timezone.now()

        # حصص خلال 30 دقيقة (25-35 دقيقة من الآن)
        start_30min = now + timedelta(minutes=25)
        end_30min = now + timedelta(minutes=35)
        lessons_30min = LiveLesson.objects.filter(
            scheduled_date__range=(start_30min, end_30min),
            status='scheduled'
        )

        # حصص غداً
        tomorrow = now + timedelta(days=1)
        start_1day = tomorrow.replace(hour=0, minute=0, second=0, microsecond=0)
        end_1day = tomorrow.replace(hour=23, minute=59, second=59, microsecond=999999)
        lessons_1day = LiveLesson.objects.filter(
            scheduled_date__range=(start_1day, end_1day),
            status='scheduled'
        ).count()

        # إحصائيات رسائل WhatsApp
        pending = WhatsAppMessage.objects.filter(status='pending').count()
        sent_today = WhatsAppMessage.objects.filter(
            status='sent',
            sent_at__date=now.date()
        ).count()

        self.stdout.write(f'📊 إحصائيات:')
        self.stdout.write(f'   🎯 حصص تحتاج تذكير خلال 30 دقيقة: {lessons_30min.count()}')

        # عرض تفاصيل الحصص خلال 30 دقيقة
        for lesson in lessons_30min:
            time_diff = lesson.scheduled_date - now
            minutes = int(time_diff.total_seconds() / 60)
            self.stdout.write(
                f'      📖 {lesson.title} - {lesson.student.get_full_name()} - '
                f'خلال {minutes} دقيقة ({lesson.scheduled_date.strftime("%H:%M")})'
            )

        self.stdout.write(f'   📅 حصص غداً: {lessons_1day}')
        self.stdout.write(f'   ⏳ إشعارات معلقة: {pending}')
        self.stdout.write(f'   ✅ مرسلة اليوم: {sent_today}')
