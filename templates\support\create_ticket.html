{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء تذكرة دعم جديدة{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    .form-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .form-input {
        transition: all 0.3s ease;
        border: 2px solid #e2e8f0;
    }
    .form-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
    }
    .submit-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: all 0.3s ease;
    }
    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">إنشاء تذكرة دعم جديدة</h1>
            <p class="text-white text-opacity-90 text-lg">نحن هنا لمساعدتك - اكتب مشكلتك وسنرد عليك في أقرب وقت</p>
        </div>

        <!-- Form Card -->
        <div class="max-w-2xl mx-auto">
            <div class="form-card rounded-2xl shadow-2xl p-8">
                <form method="POST" class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Title Field -->
                    <div>
                        <label for="title" class="block text-sm font-bold text-gray-700 mb-2">
                            <i class="fas fa-heading text-blue-600 ml-2"></i>
                            عنوان التذكرة *
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               required
                               placeholder="اكتب عنواناً واضحاً ومختصراً للمشكلة"
                               class="form-input w-full px-4 py-3 rounded-lg focus:outline-none">
                    </div>

                    <!-- Category Field -->
                    <div>
                        <label for="category" class="block text-sm font-bold text-gray-700 mb-2">
                            <i class="fas fa-tags text-green-600 ml-2"></i>
                            فئة المشكلة *
                        </label>
                        <select id="category" 
                                name="category" 
                                required
                                class="form-input w-full px-4 py-3 rounded-lg focus:outline-none">
                            <option value="">اختر فئة المشكلة</option>
                            <option value="technical">مشكلة تقنية</option>
                            <option value="account">مشكلة في الحساب</option>
                            <option value="payment">مشكلة في الدفع</option>
                            <option value="lesson">مشكلة في الحصة</option>
                            <option value="general">استفسار عام</option>
                            <option value="complaint">شكوى</option>
                            <option value="suggestion">اقتراح</option>
                        </select>
                    </div>

                    <!-- Priority Field -->
                    <div>
                        <label for="priority" class="block text-sm font-bold text-gray-700 mb-2">
                            <i class="fas fa-exclamation-circle text-orange-600 ml-2"></i>
                            مستوى الأولوية
                        </label>
                        <select id="priority" 
                                name="priority" 
                                class="form-input w-full px-4 py-3 rounded-lg focus:outline-none">
                            <option value="low">منخفضة - يمكن الانتظار</option>
                            <option value="medium" selected>متوسطة - مشكلة عادية</option>
                            <option value="high">عالية - تحتاج حل سريع</option>
                            <option value="urgent">عاجلة - مشكلة طارئة</option>
                        </select>
                    </div>

                    <!-- Description Field -->
                    <div>
                        <label for="description" class="block text-sm font-bold text-gray-700 mb-2">
                            <i class="fas fa-align-left text-purple-600 ml-2"></i>
                            وصف المشكلة بالتفصيل *
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  required
                                  rows="6"
                                  placeholder="اشرح المشكلة بالتفصيل... متى حدثت؟ ما الخطوات التي قمت بها؟ ما النتيجة المتوقعة؟"
                                  class="form-input w-full px-4 py-3 rounded-lg focus:outline-none resize-none"></textarea>
                        <p class="text-sm text-gray-500 mt-2">
                            <i class="fas fa-info-circle ml-1"></i>
                            كلما كان الوصف أكثر تفصيلاً، كلما تمكنا من مساعدتك بشكل أفضل
                        </p>
                    </div>

                    <!-- Priority Info -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-bold text-blue-800 mb-2">
                            <i class="fas fa-clock text-blue-600 ml-2"></i>
                            أوقات الاستجابة المتوقعة:
                        </h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div class="flex items-center">
                                <span class="w-3 h-3 bg-green-400 rounded-full ml-2"></span>
                                <span class="text-gray-700">منخفضة: 24-48 ساعة</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 bg-yellow-400 rounded-full ml-2"></span>
                                <span class="text-gray-700">متوسطة: 12-24 ساعة</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 bg-orange-400 rounded-full ml-2"></span>
                                <span class="text-gray-700">عالية: 4-12 ساعة</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 bg-red-400 rounded-full ml-2"></span>
                                <span class="text-gray-700">عاجلة: 1-4 ساعات</span>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex items-center justify-between pt-6">
                        <a href="{% url 'ticket_list' %}" 
                           class="text-gray-600 hover:text-gray-800 font-medium transition-colors">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للتذاكر
                        </a>
                        
                        <button type="submit" 
                                class="submit-btn text-white font-bold py-3 px-8 rounded-lg focus:outline-none focus:ring-4 focus:ring-blue-300">
                            <i class="fas fa-paper-plane ml-2"></i>
                            إرسال التذكرة
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Help Section -->
        <div class="max-w-2xl mx-auto mt-8">
            <div class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl p-6 text-white">
                <h3 class="font-bold text-lg mb-4">
                    <i class="fas fa-question-circle ml-2"></i>
                    نصائح لكتابة تذكرة دعم فعالة:
                </h3>
                <ul class="space-y-2 text-sm">
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-400 ml-2 mt-1"></i>
                        <span>اكتب عنواناً واضحاً يلخص المشكلة</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-400 ml-2 mt-1"></i>
                        <span>اختر الفئة المناسبة لتسريع عملية التوجيه</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-400 ml-2 mt-1"></i>
                        <span>اشرح الخطوات التي قمت بها قبل حدوث المشكلة</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-400 ml-2 mt-1"></i>
                        <span>أذكر أي رسائل خطأ ظهرت لك</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-400 ml-2 mt-1"></i>
                        <span>حدد النتيجة المتوقعة والنتيجة الفعلية</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // تحديث لون الأولوية
    const prioritySelect = document.getElementById('priority');
    const updatePriorityColor = () => {
        const value = prioritySelect.value;
        prioritySelect.className = prioritySelect.className.replace(/border-\w+-500/g, '');
        
        switch(value) {
            case 'low':
                prioritySelect.classList.add('border-green-500');
                break;
            case 'medium':
                prioritySelect.classList.add('border-yellow-500');
                break;
            case 'high':
                prioritySelect.classList.add('border-orange-500');
                break;
            case 'urgent':
                prioritySelect.classList.add('border-red-500');
                break;
        }
    };
    
    prioritySelect.addEventListener('change', updatePriorityColor);
    updatePriorityColor(); // تطبيق اللون الأولي
    
    // عداد الأحرف للوصف
    const descriptionTextarea = document.getElementById('description');
    const createCharCounter = () => {
        const counter = document.createElement('div');
        counter.className = 'text-sm text-gray-500 text-left mt-1';
        counter.id = 'char-counter';
        descriptionTextarea.parentNode.appendChild(counter);
        
        const updateCounter = () => {
            const length = descriptionTextarea.value.length;
            counter.textContent = `${length} حرف`;
            
            if (length < 50) {
                counter.className = 'text-sm text-red-500 text-left mt-1';
            } else if (length < 100) {
                counter.className = 'text-sm text-yellow-500 text-left mt-1';
            } else {
                counter.className = 'text-sm text-green-500 text-left mt-1';
            }
        };
        
        descriptionTextarea.addEventListener('input', updateCounter);
        updateCounter();
    };
    
    createCharCounter();
});
</script>
{% endblock %}
