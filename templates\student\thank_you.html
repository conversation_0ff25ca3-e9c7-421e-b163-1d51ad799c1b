{% extends 'base.html' %}
{% load static %}

{% block title %}شكراً لك - أكاديمية ماركتيشن{% endblock %}

{% block extra_css %}
<style>
    .thank-you-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
    }

    .thank-you-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        max-width: 600px;
        width: 100%;
        animation: slideUp 0.8s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .success-icon {
        width: 80px;
        height: 80px;
        background: #10b981;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
        }
    }

    .thank-you-title {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 1rem;
    }

    .thank-you-message {
        font-size: 1.2rem;
        color: #6b7280;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .countdown-container {
        background: #f3f4f6;
        border-radius: 15px;
        padding: 2rem;
        margin: 2rem 0;
    }

    .countdown-title {
        font-size: 1.1rem;
        color: #374151;
        margin-bottom: 1rem;
    }

    .countdown-timer {
        font-size: 3rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .countdown-text {
        font-size: 0.9rem;
        color: #6b7280;
    }

    .payment-details {
        background: #f0f9ff;
        border: 1px solid #e0f2fe;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: right;
    }

    .payment-details h4 {
        color: #0369a1;
        margin-bottom: 1rem;
    }

    .payment-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .payment-info .label {
        color: #374151;
        font-weight: 500;
    }

    .payment-info .value {
        color: #6b7280;
    }

    .admin-note {
        background: #fef3c7;
        border: 1px solid #fbbf24;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: right;
    }

    .admin-note .icon {
        color: #f59e0b;
        font-size: 1.5rem;
        margin-left: 0.5rem;
    }

    .admin-note .text {
        color: #92400e;
        font-weight: 500;
    }

    .progress-bar {
        width: 100%;
        height: 6px;
        background: #e5e7eb;
        border-radius: 3px;
        overflow: hidden;
        margin-top: 1rem;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 3px;
        transition: width 1s ease-in-out;
    }

    @media (max-width: 768px) {
        .thank-you-card {
            padding: 2rem 1.5rem;
            margin: 1rem;
        }
        
        .thank-you-title {
            font-size: 2rem;
        }
        
        .countdown-timer {
            font-size: 2.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="thank-you-container">
    <div class="thank-you-card">
        <!-- أيقونة النجاح -->
        <div class="success-icon">
            <i class="fas fa-check text-white text-3xl"></i>
        </div>

        <!-- العنوان الرئيسي -->
        <h1 class="thank-you-title">شكراً لك على الاشتراك!</h1>
        
        <!-- الرسالة الرئيسية -->
        <p class="thank-you-message">
            تم استلام طلب اشتراكك بنجاح. نحن ممتنون لثقتك في أكاديمية ماركتيشن.
        </p>

        <!-- تفاصيل الدفع -->
        {% if payment_details %}
        <div class="payment-details">
            <h4><i class="fas fa-receipt ml-2"></i>تفاصيل الدفع</h4>
            <div class="payment-info">
                <span class="label">رقم العملية:</span>
                <span class="value">#{{ payment_details.id }}</span>
            </div>
            <div class="payment-info">
                <span class="label">المبلغ:</span>
                <span class="value">${{ payment_details.amount }}</span>
            </div>
            <div class="payment-info">
                <span class="label">طريقة الدفع:</span>
                <span class="value">{{ payment_details.payment_method }}</span>
            </div>
            <div class="payment-info">
                <span class="label">التاريخ:</span>
                <span class="value">{{ payment_details.created_at|date:"d/m/Y H:i" }}</span>
            </div>
        </div>
        {% endif %}

        <!-- ملاحظة الإدارة -->
        <div class="admin-note">
            <div class="flex items-center justify-center">
                <i class="fas fa-info-circle icon"></i>
                <span class="text">
                    سيتم مراجعة اشتراكك وتفعيله من قبل الإدارة خلال 24 ساعة
                </span>
            </div>
        </div>

        <!-- العداد التنازلي -->
        <div class="countdown-container">
            <div class="countdown-title">سيتم توجيهك إلى لوحة التحكم خلال:</div>
            <div class="countdown-timer" id="countdown">10</div>
            <div class="countdown-text">ثانية</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progress"></div>
            </div>
        </div>

        <!-- رسالة إضافية -->
        <p class="text-gray-600 text-sm">
            يمكنك متابعة حالة اشتراكك من لوحة التحكم في قسم "اشتراكاتي"
        </p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let seconds = 10;
    const countdownElement = document.getElementById('countdown');
    const progressElement = document.getElementById('progress');
    
    // تحديث شريط التقدم الأولي
    progressElement.style.width = '100%';
    
    const countdown = setInterval(() => {
        seconds--;
        countdownElement.textContent = seconds;
        
        // تحديث شريط التقدم
        const progressPercent = (seconds / 10) * 100;
        progressElement.style.width = progressPercent + '%';
        
        if (seconds <= 0) {
            clearInterval(countdown);
            
            // إضافة تأثير بصري قبل الانتقال
            countdownElement.textContent = '0';
            progressElement.style.width = '0%';
            
            // انتظار قصير ثم الانتقال
            setTimeout(() => {
                window.location.href = '/dashboard/student/';
            }, 500);
        }
    }, 1000);
    
    // منع إغلاق الصفحة عن طريق الخطأ
    window.addEventListener('beforeunload', function(e) {
        if (seconds > 0) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
    
    // إضافة إمكانية الانتقال الفوري عند الضغط على أي مكان
    document.addEventListener('click', function() {
        if (seconds > 3) { // السماح بالانتقال الفوري بعد 3 ثوانِ
            clearInterval(countdown);
            window.location.href = '/dashboard/student/';
        }
    });
});
</script>
{% endblock %}
