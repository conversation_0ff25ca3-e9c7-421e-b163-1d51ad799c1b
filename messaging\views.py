from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from django.contrib import messages as django_messages
from django.db.models import Q, Count, Max
from django.db import models
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.utils import timezone

from .models import Conversation, ChatMessage
# تم إزالة التبعية على courses.models.Enrollment
from users.middleware import UserOnlineStatusUpdater

User = get_user_model()


@login_required
def conversations_list(request):
    """قائمة المحادثات للمستخدم"""
    user = request.user

    if user.user_type == 'admin':
        # المدير يرى جميع المحادثات
        conversations = Conversation.objects.filter(
            is_active=True
        ).annotate(
            unread_count=Count('chat_messages', filter=Q(chat_messages__is_read=False)),
            last_message_time=Max('chat_messages__created_at')
        ).order_by('-updated_at')
    else:
        # المستخدمون العاديون يرون محادثاتهم فقط
        conversations = Conversation.objects.filter(
            models.Q(participant1=user) | models.Q(participant2=user),
            is_active=True
        ).annotate(
            unread_count=Count('chat_messages', filter=Q(chat_messages__is_read=False) & ~Q(chat_messages__sender=user)),
            last_message_time=Max('chat_messages__created_at')
        ).order_by('-updated_at')

    # حساب المحادثات غير المقروءة قبل التقسيم
    unread_conversations_count = conversations.filter(unread_count__gt=0).count()

    # تقسيم الصفحات
    paginator = Paginator(conversations, 20)
    page_number = request.GET.get('page')
    conversations_page = paginator.get_page(page_number)

    # إضافة معلومات حالة المستخدمين للمحادثات
    for conversation in conversations_page:
        other_participant = conversation.get_other_participant(user)
        conversation.other_participant_status = UserOnlineStatusUpdater.get_user_status_info(other_participant)

    context = {
        'conversations': conversations_page,
        'total_conversations': paginator.count,
        'unread_conversations': unread_conversations_count,
    }

    return render(request, 'messaging/conversations_list.html', context)


@login_required
def conversation_detail(request, conversation_id):
    """تفاصيل المحادثة"""
    conversation = get_object_or_404(Conversation, id=conversation_id)

    # التحقق من صلاحية الوصول
    if not conversation.can_user_access(request.user):
        django_messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه المحادثة.')
        return redirect('conversations_list')

    # تمييز الرسائل كمقروءة وإخفاء الإشعارات فوراً
    conversation.mark_as_read_for_user(request.user)

    # إخفاء إضافي للإشعارات للتأكد
    from notifications.models import Notification
    Notification.objects.filter(
        recipient=request.user,
        notification_type='new_message',
        action_url=f'/messages/conversation/{conversation.id}/',
        is_read=False
    ).update(is_read=True)

    # الحصول على الرسائل
    messages_list = conversation.chat_messages.all().order_by('created_at')

    # معالجة إرسال رسالة جديدة
    if request.method == 'POST':
        content = request.POST.get('content', '').strip()
        if content:
            # إنشاء الرسالة (الإشعار التلقائي معطل في النموذج)
            message = ChatMessage.objects.create(
                conversation=conversation,
                sender=request.user,
                content=content
            )
            # إرسال إشعار واحد فقط
            message.send_notification()
            django_messages.success(request, 'تم إرسال الرسالة بنجاح.')
            return redirect('conversation_detail', conversation_id=conversation.id)
        else:
            django_messages.error(request, 'لا يمكن إرسال رسالة فارغة.')

    context = {
        'conversation': conversation,
        'messages': messages_list,
        'other_participant': conversation.get_other_participant(request.user),
    }

    return render(request, 'messaging/conversation_detail.html', context)


@login_required
def start_conversation(request):
    """بدء محادثة جديدة"""
    user = request.user

    if request.method == 'POST':
        participant_id = request.POST.get('participant_id')
        content = request.POST.get('content', '').strip()

        if not participant_id or not content:
            django_messages.error(request, 'يجب اختيار المستقبل وكتابة الرسالة.')
            return redirect('start_conversation')

        try:
            participant = User.objects.get(id=participant_id)
        except User.DoesNotExist:
            django_messages.error(request, 'المستخدم المحدد غير موجود.')
            return redirect('start_conversation')

        # التحقق من إمكانية التراسل
        if not can_users_communicate(user, participant):
            django_messages.error(request, 'لا يمكنك التراسل مع هذا المستخدم.')
            return redirect('start_conversation')

        # إنشاء أو الحصول على المحادثة
        # ترتيب المشاركين حسب ID لضمان الاتساق
        participant1, participant2 = (user, participant) if user.id < participant.id else (participant, user)

        conversation, _ = Conversation.objects.get_or_create(
            participant1=participant1,
            participant2=participant2,
            defaults={'is_active': True}
        )

        # إنشاء الرسالة (الإشعار التلقائي معطل في النموذج)
        message = ChatMessage.objects.create(
            conversation=conversation,
            sender=user,
            content=content
        )
        # إرسال إشعار واحد فقط
        message.send_notification()

        django_messages.success(request, 'تم إرسال الرسالة بنجاح.')
        return redirect('conversation_detail', conversation_id=conversation.id)

    # الحصول على قائمة المستخدمين المتاحين للتراسل
    available_users = get_available_users_for_messaging(user)

    context = {
        'available_users': available_users,
    }

    return render(request, 'messaging/start_conversation.html', context)


def get_available_users_for_messaging(user):
    """الحصول على المستخدمين المتاحين للتراسل - تم تبسيطها بعد حذف courses app"""
    if user.user_type == 'student':
        # الطالب يمكنه مراسلة جميع المعلمين النشطين
        return User.objects.filter(
            user_type='teacher',
            is_active=True
        ).distinct()

    elif user.user_type == 'teacher':
        # المعلم يمكنه مراسلة جميع الطلاب النشطين
        return User.objects.filter(
            user_type='student',
            is_active=True
        ).distinct()

    else:
        # المدير يمكنه مراسلة الجميع
        return User.objects.filter(
            is_active=True,
            user_type__in=['student', 'teacher']
        )


def can_users_communicate(user1, user2):
    """التحقق من إمكانية التراسل بين مستخدمين - تم تبسيطها بعد حذف courses app"""
    # المدير يمكنه التراسل مع الجميع
    if user1.user_type == 'admin' or user2.user_type == 'admin':
        return True

    # طالب ومعلم - السماح للجميع بالتراسل
    if (user1.user_type == 'student' and user2.user_type == 'teacher') or \
       (user1.user_type == 'teacher' and user2.user_type == 'student'):
        return True

    return False


@login_required
@require_http_methods(["POST"])
def mark_conversation_read(request, conversation_id):
    """تمييز المحادثة كمقروءة"""
    conversation = get_object_or_404(Conversation, id=conversation_id)

    if not conversation.can_user_access(request.user):
        return JsonResponse({'success': False, 'error': 'ليس لديك صلاحية للوصول'})

    conversation.mark_as_read_for_user(request.user)

    return JsonResponse({'success': True})


@login_required
def conversations_update_api(request):
    """API لتحديث المحادثات بدون إعادة تحميل الصفحة"""
    if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'error': 'Invalid request'}, status=400)

    user = request.user

    # الحصول على المحادثات
    if user.user_type == 'admin':
        conversations = Conversation.objects.filter(is_active=True)
    else:
        conversations = Conversation.objects.filter(
            models.Q(participant1=user) | models.Q(participant2=user),
            is_active=True
        )

    # حساب الرسائل غير المقروءة
    unread_count = conversations.annotate(
        unread_count=Count('chat_messages', filter=Q(chat_messages__is_read=False))
    ).filter(unread_count__gt=0).count()

    # الحصول على حالات المستخدمين
    user_statuses = {}
    for conversation in conversations[:20]:  # أول 20 محادثة فقط
        other_participant = conversation.get_other_participant(user)
        user_statuses[str(other_participant.id)] = UserOnlineStatusUpdater.get_user_status_info(other_participant)

    return JsonResponse({
        'has_updates': True,
        'unread_count': unread_count,
        'user_statuses': user_statuses,
        'timestamp': timezone.now().isoformat()
    })


@login_required
def conversation_messages_api(request, conversation_id):
    """API للحصول على الرسائل الجديدة في المحادثة"""
    if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'error': 'Invalid request'}, status=400)

    conversation = get_object_or_404(Conversation, id=conversation_id)
    user = request.user

    # التحقق من صلاحية الوصول
    if not conversation.can_user_access(user):
        return JsonResponse({'error': 'Access denied'}, status=403)

    # الحصول على timestamp آخر رسالة معروفة
    last_message_time = request.GET.get('last_message_time')

    if last_message_time:
        try:
            from django.utils.dateparse import parse_datetime
            last_time = parse_datetime(last_message_time)
            # الحصول على الرسائل الجديدة فقط
            new_messages = conversation.chat_messages.filter(
                created_at__gt=last_time
            ).order_by('created_at')
        except:
            # إذا فشل parsing، أرجع جميع الرسائل
            new_messages = conversation.chat_messages.all().order_by('created_at')
    else:
        # إرجاع آخر 50 رسالة
        new_messages = conversation.chat_messages.all().order_by('created_at')[-50:]

    # تحويل الرسائل إلى JSON
    messages_data = []
    for message in new_messages:
        messages_data.append({
            'id': message.id,
            'content': message.get_display_content(),
            'sender_id': message.sender.id,
            'sender_name': message.sender.get_full_name(),
            'sender_avatar': message.sender.get_full_name()[0],
            'created_at': message.created_at.isoformat(),
            'created_at_display': message.get_created_at_display(),
            'is_read': message.is_read,
            'is_own_message': message.sender == user,
            'is_deleted': message.is_deleted(),
            'can_delete': message.can_delete(user)
        })

    # تمييز الرسائل كمقروءة للمستخدم الحالي وإخفاء الإشعارات فوراً
    unread_messages = conversation.chat_messages.filter(
        is_read=False
    ).exclude(sender=user)

    # تمييز الرسائل كمقروءة
    unread_count = unread_messages.update(is_read=True)

    # إخفاء جميع الإشعارات المرتبطة بهذه المحادثة فوراً
    from notifications.models import Notification
    notifications_hidden = Notification.objects.filter(
        recipient=user,
        notification_type='new_message',
        action_url=f'/messages/conversation/{conversation.id}/',
        is_read=False
    ).update(is_read=True)

    # طباعة للتتبع (يمكن إزالتها لاحقاً)
    if unread_count > 0 or notifications_hidden > 0:
        print(f"تم تمييز {unread_count} رسالة و {notifications_hidden} إشعار كمقروء للمستخدم {user.get_full_name()}")

    return JsonResponse({
        'messages': messages_data,
        'conversation_id': conversation.id,
        'total_messages': conversation.chat_messages.count(),
        'timestamp': timezone.now().isoformat()
    })


@login_required
def send_message_api(request, conversation_id):
    """API لإرسال رسالة جديدة"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'error': 'Invalid request'}, status=400)

    conversation = get_object_or_404(Conversation, id=conversation_id)
    user = request.user

    # التحقق من صلاحية الوصول
    if not conversation.can_user_access(user):
        return JsonResponse({'error': 'Access denied'}, status=403)

    # الحصول على محتوى الرسالة
    import json
    try:
        data = json.loads(request.body)
        content = data.get('content', '').strip()
    except:
        content = request.POST.get('content', '').strip()

    if not content:
        return JsonResponse({'error': 'Message content is required'}, status=400)

    if len(content) > 1000:
        return JsonResponse({'error': 'Message too long'}, status=400)

    # إنشاء الرسالة (الإشعار التلقائي معطل في النموذج)
    message = ChatMessage.objects.create(
        conversation=conversation,
        sender=user,
        content=content
    )

    # إرسال إشعار واحد فقط للطرف الآخر
    recipient = message.get_recipient()
    message.send_notification()

    # إرجاع بيانات الرسالة الجديدة مع معلومات الإشعار
    return JsonResponse({
        'success': True,
        'message': {
            'id': message.id,
            'content': message.get_display_content(),
            'sender_id': message.sender.id,
            'sender_name': message.sender.get_full_name(),
            'sender_avatar': message.sender.get_full_name()[0],
            'created_at': message.created_at.isoformat(),
            'created_at_display': message.get_created_at_display(),
            'is_read': message.is_read,
            'is_own_message': True,
            'is_deleted': message.is_deleted(),
            'can_delete': message.can_delete(user)
        },
        'notification_sent': True,
        'recipient_name': recipient.get_full_name() if recipient else None,
        'timestamp': timezone.now().isoformat()
    })


@login_required
def delete_message(request, message_id):
    """حذف رسالة"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    message = get_object_or_404(ChatMessage, id=message_id)
    user = request.user

    # التحقق من صلاحية الحذف
    if not message.can_delete(user):
        return JsonResponse({'error': 'Access denied'}, status=403)

    # التحقق من أن الرسالة لم تُحذف مسبقاً
    if message.is_deleted():
        return JsonResponse({'error': 'Message already deleted'}, status=400)

    # حذف الرسالة
    message.soft_delete(user)

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': 'تم حذف الرسالة بنجاح'
        })
    else:
        django_messages.success(request, 'تم حذف الرسالة بنجاح.')
        return redirect('conversation_detail', conversation_id=message.conversation.id)


@login_required
def restore_message(request, message_id):
    """استعادة رسالة محذوفة (للمديرين فقط)"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    if request.user.user_type != 'admin':
        return JsonResponse({'error': 'Access denied'}, status=403)

    message = get_object_or_404(ChatMessage, id=message_id)

    # التحقق من أن الرسالة محذوفة
    if not message.is_deleted():
        return JsonResponse({'error': 'Message is not deleted'}, status=400)

    # استعادة الرسالة
    message.deleted_at = None
    message.deleted_by = None
    message.save()

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': 'تم استعادة الرسالة بنجاح'
        })
    else:
        django_messages.success(request, 'تم استعادة الرسالة بنجاح.')
        return redirect('conversation_detail', conversation_id=message.conversation.id)