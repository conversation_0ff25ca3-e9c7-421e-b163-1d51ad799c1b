# Generated by Django 4.2.7 on 2025-06-01 18:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('messaging', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='conversation',
            name='participant1',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conversations_as_participant1', to=settings.AUTH_USER_MODEL, verbose_name='المشارك الأول'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='participant2',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conversations_as_participant2', to=settings.AUTH_USER_MODEL, verbose_name='المشارك الثاني'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='student',
            field=models.ForeignKey(blank=True, limit_choices_to={'user_type': 'student'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='student_conversations', to=settings.AUTH_USER_MODEL, verbose_name='الطالب'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='teacher',
            field=models.ForeignKey(blank=True, limit_choices_to={'user_type': 'teacher'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='teacher_conversations', to=settings.AUTH_USER_MODEL, verbose_name='المعلم'),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='conversation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chat_messages', to='messaging.conversation', verbose_name='المحادثة'),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='deleted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_messages', to=settings.AUTH_USER_MODEL, verbose_name='محذوف بواسطة'),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='sender',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_chat_messages', to=settings.AUTH_USER_MODEL, verbose_name='المرسل'),
        ),
    ]
