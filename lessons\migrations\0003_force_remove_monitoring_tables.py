# Generated by Django 4.2.7 on 2025-06-08 01:47

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0002_remove_monitoring_references'),
    ]

    operations = [
        # حذف قسري لجميع الجداول المرتبطة بنظام المراقبة
        migrations.RunSQL(
            """
            PRAGMA foreign_keys = OFF;
            DROP TABLE IF EXISTS lessons_universallessonmonitoring;
            DROP TABLE IF EXISTS lessons_lessonmonitoring;
            DROP TABLE IF EXISTS lessons_monitoringsession;
            DROP TABLE IF EXISTS lessons_attendancemonitoring;
            DROP TABLE IF EXISTS lessons_qualitymonitoring;
            PRAGMA foreign_keys = ON;
            """,
            reverse_sql="-- لا يمكن التراجع عن حذف الجداول"
        ),
    ]
