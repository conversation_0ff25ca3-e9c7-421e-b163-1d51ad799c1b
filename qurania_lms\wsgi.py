"""
WSGI config for qurania_lms project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

# Use production settings for Render deployment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania_lms.settings_render')

application = get_wsgi_application()

# Debug information for deployment
print(f"🚀 WSGI application loaded with settings: {os.environ.get('DJANGO_SETTINGS_MODULE')}")
