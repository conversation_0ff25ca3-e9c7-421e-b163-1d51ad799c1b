from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()


class Invoice(models.Model):
    """نموذج الفاتورة"""
    
    STATUS_CHOICES = (
        ('draft', _('مسودة')),
        ('sent', _('مرسلة')),
        ('paid', _('مدفوعة')),
        ('overdue', _('متأخرة')),
        ('cancelled', _('ملغية')),
    )

    # معرف فريد للفاتورة
    invoice_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('رقم الفاتورة')
    )
    
    # ربط بالاشتراك
    subscription = models.OneToOneField(
        'subscriptions.StudentSubscription',
        on_delete=models.CASCADE,
        related_name='invoice',
        verbose_name=_('الاشتراك')
    )
    
    # معلومات الطالب (نسخة للحفظ)
    student_name = models.CharField(
        max_length=200,
        verbose_name=_('اسم الطالب')
    )
    
    student_email = models.EmailField(
        verbose_name=_('بريد الطالب الإلكتروني')
    )
    
    student_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('هاتف الطالب')
    )
    
    # معلومات الباقة (نسخة للحفظ)
    plan_name = models.CharField(
        max_length=200,
        verbose_name=_('اسم الباقة')
    )
    
    plan_description = models.TextField(
        verbose_name=_('وصف الباقة')
    )
    
    # التفاصيل المالية
    subtotal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ الفرعي')
    )
    
    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('مبلغ الخصم')
    )
    
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('مبلغ الضريبة')
    )
    
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ الإجمالي')
    )
    
    currency = models.CharField(
        max_length=3,
        default='SAR',
        verbose_name=_('العملة')
    )
    
    # التواريخ
    issue_date = models.DateTimeField(
        default=timezone.now,
        verbose_name=_('تاريخ الإصدار')
    )
    
    due_date = models.DateTimeField(
        verbose_name=_('تاريخ الاستحقاق')
    )
    
    paid_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الدفع')
    )
    
    # الحالة
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('الحالة')
    )
    
    # ملاحظات
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )
    
    # معلومات إضافية
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('أنشئت بواسطة')
    )

    class Meta:
        verbose_name = _('فاتورة')
        verbose_name_plural = _('الفواتير')
        ordering = ['-created_at']

    def __str__(self):
        return f"فاتورة {self.invoice_number} - {self.student_name}"

    def save(self, *args, **kwargs):
        # إنشاء رقم فاتورة تلقائي إذا لم يكن موجوداً
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        
        # حساب المبلغ الإجمالي
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount
        
        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """إنشاء رقم فاتورة فريد"""
        from datetime import datetime
        year = datetime.now().year
        month = datetime.now().month
        
        # البحث عن آخر فاتورة في نفس الشهر
        last_invoice = Invoice.objects.filter(
            invoice_number__startswith=f"INV-{year}{month:02d}"
        ).order_by('-invoice_number').first()
        
        if last_invoice:
            # استخراج الرقم التسلسلي من آخر فاتورة
            last_number = int(last_invoice.invoice_number.split('-')[-1])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"INV-{year}{month:02d}-{new_number:04d}"

    def get_currency_symbol(self):
        """الحصول على رمز العملة"""
        currency_symbols = {
            'SAR': 'ر.س',
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق',
            'BHD': 'د.ب',
            'OMR': 'ر.ع',
            'JOD': 'د.أ',
            'EGP': 'ج.م',
            'JPY': '¥',
            'CNY': '¥',
            'INR': '₹',
            'KRW': '₩',
            'TRY': '₺',
            'RUB': '₽',
            'CAD': 'C$',
            'AUD': 'A$',
            'CHF': 'CHF',
            'BRL': 'R$',
            'ZAR': 'R',
        }
        return currency_symbols.get(self.currency, self.currency)

    def mark_as_paid(self):
        """تحديد الفاتورة كمدفوعة"""
        self.status = 'paid'
        self.paid_date = timezone.now()
        self.save()

    def is_overdue(self):
        """التحقق من تأخر الفاتورة"""
        if self.status in ['paid', 'cancelled']:
            return False
        return timezone.now() > self.due_date

    def get_status_color(self):
        """الحصول على لون الحالة"""
        colors = {
            'draft': 'gray',
            'sent': 'blue',
            'paid': 'green',
            'overdue': 'red',
            'cancelled': 'gray',
        }
        return colors.get(self.status, 'gray')


class InvoiceItem(models.Model):
    """عناصر الفاتورة"""
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('الفاتورة')
    )
    
    description = models.CharField(
        max_length=500,
        verbose_name=_('الوصف')
    )
    
    quantity = models.PositiveIntegerField(
        default=1,
        verbose_name=_('الكمية')
    )
    
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('سعر الوحدة')
    )
    
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('السعر الإجمالي')
    )

    class Meta:
        verbose_name = _('عنصر فاتورة')
        verbose_name_plural = _('عناصر الفاتورة')

    def __str__(self):
        return f"{self.description} - {self.total_price}"

    def save(self, *args, **kwargs):
        # حساب السعر الإجمالي
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)
