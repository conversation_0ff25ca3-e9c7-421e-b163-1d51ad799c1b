"""
Middleware لتتبع حالة المستخدمين ومعالجة مشاكل قاعدة البيانات
"""

import time
import logging
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.db import transaction, OperationalError
from django.http import HttpResponseServerError
from django.contrib import messages
from django.shortcuts import redirect
from datetime import timedelta

logger = logging.getLogger(__name__)


class UserStatusMiddleware(MiddlewareMixin):
    """Middleware لتتبع حالة المستخدمين (متصل/غير متصل)"""

    def process_request(self, request):
        """تحديث حالة المستخدم عند كل طلب"""
        # المسارات المسموح بها دائمًا
        ALWAYS_ALLOWED_PATHS = [
            '/logout/',
            '/static/',
            '/media/',
            '/status/banned/',
        ]

        # السماح بالوصول للمسارات المسموح بها دائمًا
        if any(request.path.startswith(path) for path in ALWAYS_ALLOWED_PATHS):
            # تحديث آخر ظهور للمستخدم إذا كان مسجل الدخول
            if request.user.is_authenticated:
                request.user.update_last_seen()
            return None

        # تحديث آخر ظهور للمستخدم إذا كان مسجل الدخول
        if request.user.is_authenticated:
            request.user.update_last_seen()

            # التحقق من حالة الحظر
            if request.user.is_currently_banned():
                # تخزين معلومات الحظر في الجلسة
                self._store_ban_info_in_session(request)

                # توجيه لصفحة الحظر العامة
                from django.shortcuts import redirect
                return redirect('public_banned_page')

    def _store_ban_info_in_session(self, request):
        """تخزين معلومات الحظر في الجلسة"""
        # حفظ معلومات المستخدم في الجلسة
        request.session['banned_username'] = request.user.username
        request.session['ban_reason'] = request.user.ban_reason
        request.session['ban_type'] = request.user.ban_type
        if request.user.banned_until:
            request.session['banned_until'] = request.user.banned_until.strftime('%Y-%m-%d %H:%M')


class UserVerificationMiddleware(MiddlewareMixin):
    """Middleware للتحقق من حالة تحقق المستخدم"""

    # المسارات المسموحة للمستخدمين غير المحققين
    ALLOWED_PATHS = [
        '/login/',
        '/logout/',
        '/register/',
        '/password-reset/',
        '/about/',
        '/privacy/',
        '/terms/',
        '/dashboard/verification-pending/',
        '/dashboard/verification-rejected/',
        '/dashboard/registration-success/',
        '/dashboard/banned/',
        '/static/',
        '/media/',
        '/status/banned/',
        '/status/pending/',
        '/status/rejected/',
    ]

    def process_request(self, request):
        """التحقق من حالة المستخدم قبل الوصول للوحات التحكم"""
        # إذا لم يكن المستخدم مسجل الدخول، لا داعي للتحقق
        if not request.user.is_authenticated:
            return None

        # المسارات المسموح بها دائمًا
        if any(request.path.startswith(path) for path in self.ALLOWED_PATHS):
            return None

        # المديرين يمكنهم الوصول دائماً
        if request.user.user_type == 'admin':
            return None

        # التحقق من حالة الحظر أولاً
        if request.user.is_currently_banned():
            # تخزين معلومات الحظر في الجلسة
            self._store_ban_info_in_session(request)

            # توجيه لصفحة الحظر العامة
            from django.shortcuts import redirect
            return redirect('public_banned_page')

        # التحقق من حالة التحقق للمستخدمين الآخرين
        if request.path.startswith('/dashboard/'):
            # السماح بالوصول لصفحات الحالة الخاصة
            special_status_paths = [
                '/dashboard/verification-pending/',
                '/dashboard/verification-rejected/',
                '/dashboard/registration-success/',
                '/dashboard/banned/',
            ]

            if any(request.path.startswith(path) for path in special_status_paths):
                return None

            # التحقق من إمكانية الوصول للوحة التحكم
            if not request.user.can_access_dashboard():
                if request.user.is_pending_verification():
                    # تخزين معلومات المستخدم في الجلسة
                    request.session['pending_username'] = request.user.username
                    request.session['pending_user_type'] = request.user.user_type

                    # توجيه لصفحة الانتظار العامة
                    from django.shortcuts import redirect
                    return redirect('public_pending_page')

                elif request.user.is_rejected():
                    # تخزين معلومات المستخدم في الجلسة
                    request.session['rejected_username'] = request.user.username
                    request.session['rejected_user_type'] = request.user.user_type
                    request.session['rejection_reason'] = request.user.rejection_reason

                    # توجيه لصفحة الرفض العامة
                    from django.shortcuts import redirect
                    return redirect('public_rejected_page')

                else:
                    # حساب غير نشط لأسباب أخرى
                    from django.contrib.auth import logout
                    logout(request)
                    # حفظ رسالة الخطأ في الجلسة
                    request.session['login_error'] = "حسابك غير نشط. يرجى التواصل مع الإدارة."
                    from django.shortcuts import redirect
                    return redirect('login')

        return None

    def _store_ban_info_in_session(self, request):
        """تخزين معلومات الحظر في الجلسة"""
        # حفظ معلومات المستخدم في الجلسة
        request.session['banned_username'] = request.user.username
        request.session['ban_reason'] = request.user.ban_reason
        request.session['ban_type'] = request.user.ban_type
        if request.user.banned_until:
            request.session['banned_until'] = request.user.banned_until.strftime('%Y-%m-%d %H:%M')

    def process_response(self, request, response):
        """معالجة الاستجابة"""
        return response


class UserOnlineStatusUpdater:
    """خدمة تحديث حالة المستخدمين"""

    @staticmethod
    def mark_user_offline(user):
        """تمييز المستخدم كغير متصل"""
        try:
            status = user.user_status
            status.is_online = False
            status.save()
        except:
            pass

    @staticmethod
    def mark_user_online(user):
        """تمييز المستخدم كمتصل"""
        try:
            status = user.user_status
            status.is_online = True
            status.last_seen = timezone.now()
            status.save()
        except:
            user.update_last_seen()

    @staticmethod
    def cleanup_offline_users():
        """تنظيف المستخدمين غير المتصلين"""
        from .models import UserStatus

        # المستخدمين الذين لم يظهروا منذ أكثر من 5 دقائق
        offline_threshold = timezone.now() - timedelta(minutes=5)

        UserStatus.objects.filter(
            last_seen__lt=offline_threshold,
            is_online=True
        ).update(is_online=False)

    @staticmethod
    def get_online_users():
        """الحصول على المستخدمين المتصلين"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        return User.objects.filter(
            user_status__is_online=True,
            is_active=True
        ).select_related('user_status')

    @staticmethod
    def get_user_status_info(user):
        """الحصول على معلومات حالة المستخدم"""
        try:
            status = user.user_status
            return {
                'is_online': status.is_online,
                'last_seen': status.last_seen,
                'status_display': status.get_status_display(),
                'status_color': status.get_status_color()
            }
        except:
            return {
                'is_online': False,
                'last_seen': None,
                'status_display': 'غير معروف',
                'status_color': 'gray'
            }


class DatabaseLockMiddleware(MiddlewareMixin):
    """
    Middleware لمعالجة مشاكل قفل قاعدة البيانات
    """

    def process_request(self, request):
        """معالجة الطلب مع إعادة المحاولة في حالة قفل قاعدة البيانات"""
        # تحديد ما إذا كان هذا طلب عملية جماعية
        is_bulk_operation = (
            request.method == 'POST' and
            'bulk_' in request.POST.get('action', '') and
            'user_ids' in request.POST
        )

        if is_bulk_operation:
            # تعيين إعدادات SQLite المحسنة للعمليات الجماعية
            try:
                from django.db import connection
                with connection.cursor() as cursor:
                    # تعيين timeout أطول
                    cursor.execute("PRAGMA busy_timeout = 30000")  # 30 ثانية
                    # تحسين الأداء
                    cursor.execute("PRAGMA journal_mode = WAL")
                    cursor.execute("PRAGMA synchronous = NORMAL")
                    cursor.execute("PRAGMA cache_size = 10000")
                    cursor.execute("PRAGMA temp_store = MEMORY")
            except Exception as e:
                logger.warning(f"Failed to set database optimizations: {str(e)}")

        return None

    def process_exception(self, request, exception):
        """معالجة الاستثناءات المتعلقة بقاعدة البيانات"""
        from django.db import IntegrityError

        if isinstance(exception, OperationalError):
            if "database is locked" in str(exception).lower():
                logger.error(f"Database lock error: {str(exception)}")
                if request.user.is_authenticated:
                    messages.error(request, "حدث خطأ مؤقت في قاعدة البيانات. يرجى المحاولة مرة أخرى.")
                    return redirect(request.META.get('HTTP_REFERER', '/dashboard/'))
                else:
                    return HttpResponseServerError("خطأ مؤقت في الخادم. يرجى المحاولة لاحقاً.")

        elif isinstance(exception, IntegrityError):
            if "FOREIGN KEY constraint failed" in str(exception):
                logger.error(f"Foreign key constraint error: {str(exception)}")
                if request.user.is_authenticated:
                    messages.error(request, "حدث خطأ في العلاقات بين البيانات. يرجى المحاولة مرة أخرى.")
                    return redirect(request.META.get('HTTP_REFERER', '/dashboard/'))
                else:
                    return HttpResponseServerError("خطأ في البيانات. يرجى المحاولة لاحقاً.")

        return None

