<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير شامل - تقييمات جميع المعلمين</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.4;
            font-size: 12px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2D5016;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .logo {
            color: #2D5016;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .report-title {
            font-size: 18px;
            font-weight: bold;
            color: #2D5016;
            margin-bottom: 5px;
        }
        
        .report-date {
            color: #666;
            font-size: 12px;
        }
        
        .summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-right: 4px solid #2D5016;
        }
        
        .summary-title {
            font-size: 16px;
            font-weight: bold;
            color: #2D5016;
            margin-bottom: 10px;
        }
        
        .teachers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            font-size: 11px;
        }
        
        .teachers-table th {
            background: #2D5016;
            color: white;
            padding: 8px 5px;
            text-align: center;
            border: 1px solid #ddd;
            font-weight: bold;
        }
        
        .teachers-table td {
            padding: 6px 5px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .teachers-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .teacher-name {
            text-align: right;
            font-weight: 500;
        }
        
        .performance-excellent {
            background: #d4edda;
            color: #155724;
            font-weight: bold;
        }
        
        .performance-very_good {
            background: #cce5ff;
            color: #004085;
            font-weight: bold;
        }
        
        .performance-good {
            background: #fff3cd;
            color: #856404;
            font-weight: bold;
        }
        
        .performance-fair {
            background: #f8d7da;
            color: #721c24;
            font-weight: bold;
        }
        
        .performance-poor {
            background: #e2e3e5;
            color: #383d41;
            font-weight: bold;
        }
        
        .trend-improving {
            color: #28a745;
            font-weight: bold;
        }
        
        .trend-declining {
            color: #dc3545;
            font-weight: bold;
        }
        
        .trend-stable {
            color: #6c757d;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #2D5016;
            margin-bottom: 3px;
        }
        
        .stat-label {
            font-size: 10px;
            color: #666;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 10px;
        }
        
        @media print {
            body {
                padding: 0;
            }
            .header {
                page-break-after: avoid;
            }
            .teachers-table {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <div class="logo">أكاديمية القرآنية</div>
        <div class="report-title">تقرير شامل - تقييمات جميع المعلمين</div>
        <div class="report-date">تاريخ التقرير: {{ report_date|date:"d/m/Y H:i" }}</div>
    </div>

    <!-- ملخص الإحصائيات -->
    <div class="summary">
        <div class="summary-title">ملخص عام</div>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ total_teachers }}</div>
                <div class="stat-label">إجمالي المعلمين</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">
                    {% if teacher_ratings %}
                        {{ teacher_ratings|length|add:0|floatformat:0 }}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="stat-label">معلمين لديهم تقييمات</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">
                    {% if teacher_ratings %}
                        {% for rating in teacher_ratings %}
                            {% if rating.performance_level == 'excellent' %}
                                {% if forloop.first %}1{% else %}{{ forloop.counter0|add:1 }}{% endif %}
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="stat-label">معلمين ممتازين</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">
                    {% if teacher_ratings %}
                        {% for rating in teacher_ratings %}
                            {% if rating.trend_direction == 'improving' %}
                                {% if forloop.first %}1{% else %}{{ forloop.counter0|add:1 }}{% endif %}
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="stat-label">معلمين متحسنين</div>
            </div>
        </div>
    </div>

    <!-- جدول المعلمين -->
    <table class="teachers-table">
        <thead>
            <tr>
                <th style="width: 20%;">اسم المعلم</th>
                <th style="width: 8%;">التقييمات</th>
                <th style="width: 8%;">التقييم العام</th>
                <th style="width: 8%;">جودة الحصة</th>
                <th style="width: 8%;">تفاعل المعلم</th>
                <th style="width: 8%;">الجودة التقنية</th>
                <th style="width: 8%;">المتوسط العام</th>
                <th style="width: 8%;">حصص مجدولة</th>
                <th style="width: 8%;">حصص مباشرة</th>
                <th style="width: 10%;">مستوى الأداء</th>
                <th style="width: 8%;">الاتجاه</th>
            </tr>
        </thead>
        <tbody>
            {% for teacher_rating in teacher_ratings %}
            <tr>
                <td class="teacher-name">{{ teacher_rating.teacher.get_full_name }}</td>
                <td>{{ teacher_rating.total_ratings }}</td>
                <td>{{ teacher_rating.avg_overall|floatformat:1 }}</td>
                <td>{{ teacher_rating.avg_quality|floatformat:1 }}</td>
                <td>{{ teacher_rating.avg_interaction|floatformat:1 }}</td>
                <td>{{ teacher_rating.avg_technical|floatformat:1 }}</td>
                <td><strong>{{ teacher_rating.overall_average|floatformat:1 }}</strong></td>
                <td>{{ teacher_rating.scheduled_count }}</td>
                <td>{{ teacher_rating.live_count }}</td>
                <td class="performance-{{ teacher_rating.performance_level }}">
                    {% if teacher_rating.performance_level == 'excellent' %}ممتاز
                    {% elif teacher_rating.performance_level == 'very_good' %}جيد جداً
                    {% elif teacher_rating.performance_level == 'good' %}جيد
                    {% elif teacher_rating.performance_level == 'fair' %}مقبول
                    {% else %}ضعيف{% endif %}
                </td>
                <td class="trend-{{ teacher_rating.trend_direction }}">
                    {% if teacher_rating.trend_direction == 'improving' %}متحسن
                    {% elif teacher_rating.trend_direction == 'declining' %}متراجع
                    {% else %}مستقر{% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11" style="text-align: center; padding: 20px; color: #666;">
                    لا توجد تقييمات للمعلمين حتى الآن
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- ملاحظات -->
    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h4 style="color: #2D5016; margin-bottom: 10px;">ملاحظات:</h4>
        <ul style="margin: 0; padding-right: 20px;">
            <li>التقييمات مبنية على نظام 5 نجوم</li>
            <li>المتوسط العام هو متوسط المعايير الأربعة</li>
            <li>مستوى الأداء: ممتاز (4.5+)، جيد جداً (4.0-4.4)، جيد (3.5-3.9)، مقبول (3.0-3.4)، ضعيف (أقل من 3.0)</li>
            <li>الاتجاه محسوب بناءً على آخر 10 تقييمات</li>
        </ul>
    </div>

    <!-- تذييل التقرير -->
    <div class="footer">
        <div>تم إنشاء هذا التقرير بواسطة نظام إدارة أكاديمية القرآنية</div>
        <div>{{ report_date|date:"d/m/Y H:i" }}</div>
    </div>
</body>
</html>
