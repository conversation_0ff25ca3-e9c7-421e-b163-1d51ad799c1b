from django import forms
from django.contrib.auth.forms import AuthenticationForm
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.db.models import Q
from .models import AcademySettings, User, UserProfile
import re


class CustomAuthenticationForm(AuthenticationForm):
    """نموذج تسجيل دخول مخصص يدعم البريد الإلكتروني واسم المستخدم"""

    username = forms.CharField(
        label='اسم المستخدم أو البريد الإلكتروني',
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent transition-all duration-200',
            'placeholder': 'أدخل اسم المستخدم أو البريد الإلكتروني',
            'autofocus': True
        })
    )

    password = forms.CharField(
        label='كلمة المرور',
        widget=forms.PasswordInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent transition-all duration-200 pl-12',
            'placeholder': 'أدخل كلمة المرور'
        })
    )

    def clean(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')

        if username is not None and password:
            # البحث عن المستخدم باسم المستخدم أو البريد الإلكتروني
            try:
                user = User.objects.get(Q(username=username) | Q(email=username))
                # تحديث اسم المستخدم في البيانات المنظفة ليكون اسم المستخدم الفعلي
                self.cleaned_data['username'] = user.username

                # التحقق من كلمة المرور
                if not user.check_password(password):
                    raise forms.ValidationError(
                        "اسم المستخدم أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.",
                        code='invalid_login'
                    )

                # حفظ المستخدم للاستخدام لاحقاً
                self.user_cache = user

            except User.DoesNotExist:
                raise forms.ValidationError(
                    "اسم المستخدم أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.",
                    code='invalid_login'
                )

        return self.cleaned_data


class AcademySettingsForm(forms.ModelForm):
    """نموذج تعديل إعدادات الأكاديمية"""

    class Meta:
        model = AcademySettings
        fields = [
            'academy_name',
            'academy_email',
            'academy_phone',
            'academy_whatsapp',
            'academy_support_email',
            'academy_website',
            'academy_logo',
            'academy_address',
            'academy_description',
            'academy_slogan',
            'academy_working_hours',
        ]

        widgets = {
            'academy_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل اسم الأكاديمية',
                'dir': 'rtl',
            }),
            'academy_email': forms.EmailInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل البريد الإلكتروني للأكاديمية',
                'dir': 'ltr',
            }),
            'academy_phone': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل رقم هاتف الأكاديمية',
                'dir': 'ltr',
            }),
            'academy_whatsapp': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل رقم واتساب الأكاديمية',
                'dir': 'ltr',
            }),
            'academy_support_email': forms.EmailInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل البريد الإلكتروني للدعم الفني',
                'dir': 'ltr',
            }),
            'academy_website': forms.URLInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل موقع الأكاديمية الإلكتروني',
                'dir': 'ltr',
            }),
            'academy_logo': forms.FileInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
            }),
            'academy_address': forms.Textarea(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل عنوان الأكاديمية',
                'rows': 3,
                'dir': 'rtl',
            }),
            'academy_description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل وصف الأكاديمية',
                'rows': 5,
                'dir': 'rtl',
            }),
            'academy_slogan': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل سلوجان الأكاديمية',
                'dir': 'rtl',
            }),
            'academy_working_hours': forms.Textarea(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل ساعات العمل',
                'rows': 3,
                'dir': 'rtl',
            }),
        }

        labels = {
            'academy_name': _('اسم الأكاديمية'),
            'academy_email': _('البريد الإلكتروني للأكاديمية'),
            'academy_phone': _('رقم هاتف الأكاديمية'),
            'academy_whatsapp': _('رقم واتساب الأكاديمية'),
            'academy_support_email': _('البريد الإلكتروني للدعم الفني'),
            'academy_website': _('موقع الأكاديمية الإلكتروني'),
            'academy_logo': _('شعار الأكاديمية'),
            'academy_address': _('عنوان الأكاديمية'),
            'academy_description': _('وصف الأكاديمية'),
            'academy_slogan': _('سلوجان الأكاديمية'),
            'academy_working_hours': _('ساعات العمل'),
        }

        help_texts = {
            'academy_logo': _('يفضل أن يكون الشعار بحجم 200×200 بكسل'),
            'academy_description': _('وصف مختصر للأكاديمية يظهر في الصفحة الرئيسية'),
            'academy_slogan': _('يظهر في عناوين الصفحات وفي أماكن متعددة في النظام (مثل: نظام قرآنيا التعليمي)'),
            'academy_working_hours': _('مثال: الأحد - الخميس: 8:00 ص - 10:00 م، الجمعة: 2:00 م - 10:00 م'),
            'academy_whatsapp': _('يستخدم للتواصل عبر واتساب في صفحة الدعم'),
            'academy_support_email': _('يستخدم للتواصل في صفحات الدعم والمساعدة'),
            'academy_website': _('الموقع الإلكتروني الرسمي للأكاديمية'),
        }


class ProfileUpdateForm(forms.ModelForm):
    """نموذج تحديث الملف الشخصي الأساسي"""

    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone',
            'date_of_birth', 'student_level',
            'profile_picture', 'bio'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'الاسم الأخير'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'البريد الإلكتروني'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'رقم الهاتف'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'type': 'date'
            }),
            'student_level': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent'
            }),
            'profile_picture': forms.FileInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'accept': 'image/*'
            }),
            'bio': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'rows': 4,
                'placeholder': 'اكتب نبذة مختصرة عن نفسك...'
            }),
        }
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'الاسم الأخير',
            'email': 'البريد الإلكتروني',
            'phone': 'رقم الهاتف',
            'date_of_birth': 'تاريخ الميلاد',
            'student_level': 'مستوى الطالب',
            'profile_picture': 'صورة الملف الشخصي',
            'bio': 'نبذة شخصية',
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # إخفاء حقل مستوى الطالب إذا لم يكن المستخدم طالباً
        if self.user and self.user.user_type != 'student':
            del self.fields['student_level']

    def clean_email(self):
        """التحقق من صحة البريد الإلكتروني وعدم تكراره"""
        email = self.cleaned_data.get('email')
        if email:
            # التحقق من عدم وجود بريد إلكتروني مشابه (باستثناء المستخدم الحالي)
            existing_user = User.objects.filter(email=email).exclude(id=self.instance.id).first()
            if existing_user:
                raise ValidationError('هذا البريد الإلكتروني مستخدم بالفعل.')
        return email

    def clean_phone(self):
        """التحقق من صحة رقم الهاتف"""
        phone = self.cleaned_data.get('phone')
        if phone:
            # إزالة المسافات والرموز غير المرغوبة (الاحتفاظ بالأرقام و + فقط)
            phone = re.sub(r'[^\d+]', '', phone)

            # إذا كان الحقل فارغاً بعد التنظيف، إرجاع فارغ
            if not phone:
                return ''

            # التحقق من الحد الأدنى والأقصى لطول الرقم
            if len(phone) < 8:
                raise ValidationError('رقم الهاتف قصير جداً. يجب أن يكون 8 أرقام على الأقل.')

            if len(phone) > 15:
                raise ValidationError('رقم الهاتف طويل جداً. يجب أن يكون 15 رقم كحد أقصى.')

            # التحقق من التنسيقات المختلفة
            # أرقام سعودية
            if re.match(r'^(05|5)\d{8}$', phone):
                # تحويل 05xxxxxxxx أو 5xxxxxxxx إلى +966xxxxxxxx
                if phone.startswith('05'):
                    phone = '+966' + phone[1:]
                elif phone.startswith('5'):
                    phone = '+966' + phone

            # أرقام تبدأ بـ 966 (سعودية بدون +)
            elif re.match(r'^966\d{9}$', phone):
                phone = '+' + phone

            # أرقام تبدأ بـ + (دولية)
            elif re.match(r'^\+\d{8,14}$', phone):
                # الرقم صحيح كما هو
                pass

            # أرقام محلية (بدون رمز دولة)
            elif re.match(r'^\d{8,10}$', phone):
                # قبول الأرقام المحلية كما هي
                pass

            # أرقام دولية أخرى
            elif re.match(r'^\d{10,15}$', phone):
                # إضافة + للأرقام الطويلة
                phone = '+' + phone

            else:
                raise ValidationError('تنسيق رقم الهاتف غير صحيح. أمثلة صحيحة: +966501234567، 0501234567، 501234567')

        return phone

    def clean_profile_picture(self):
        """التحقق من صحة صورة الملف الشخصي"""
        picture = self.cleaned_data.get('profile_picture')
        if picture:
            # التحقق من حجم الملف (أقل من 5 ميجابايت)
            if hasattr(picture, 'size') and picture.size > 5 * 1024 * 1024:
                raise ValidationError('حجم الصورة يجب أن يكون أقل من 5 ميجابايت.')

            # التحقق من نوع الملف
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if hasattr(picture, 'content_type') and picture.content_type:
                if picture.content_type not in allowed_types:
                    raise ValidationError(f'نوع الملف غير مدعوم ({picture.content_type}). يرجى استخدام JPG, PNG, GIF, أو WebP.')

            # التحقق من امتداد الملف كبديل
            if hasattr(picture, 'name') and picture.name:
                allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
                file_extension = picture.name.lower().split('.')[-1] if '.' in picture.name else ''
                if file_extension and f'.{file_extension}' not in allowed_extensions:
                    raise ValidationError(f'امتداد الملف غير مدعوم (.{file_extension}). يرجى استخدام .jpg, .png, .gif, أو .webp.')

        return picture


class UserProfileUpdateForm(forms.ModelForm):
    """نموذج تحديث الملف الشخصي الإضافي"""

    class Meta:
        model = UserProfile
        fields = [
            'emergency_contact', 'emergency_phone', 'address',
            'preferred_language', 'timezone'
        ]
        widgets = {
            'emergency_contact': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'اسم جهة الاتصال في حالات الطوارئ'
            }),
            'emergency_phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'رقم هاتف الطوارئ'
            }),
            'address': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'rows': 3,
                'placeholder': 'العنوان الكامل'
            }),
            'preferred_language': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent'
            }),
            'timezone': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent'
            }),
        }
        labels = {
            'emergency_contact': 'جهة الاتصال في حالات الطوارئ',
            'emergency_phone': 'هاتف الطوارئ',
            'address': 'العنوان',
            'preferred_language': 'اللغة المفضلة',
            'timezone': 'المنطقة الزمنية',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # إضافة جميع المناطق الزمنية
        timezone_choices = [
            # المناطق العربية والشرق الأوسط
            ('Asia/Riyadh', 'الرياض، السعودية (GMT+3)'),
            ('Asia/Dubai', 'دبي، الإمارات (GMT+4)'),
            ('Asia/Kuwait', 'الكويت (GMT+3)'),
            ('Asia/Qatar', 'قطر (GMT+3)'),
            ('Asia/Bahrain', 'البحرين (GMT+3)'),
            ('Asia/Muscat', 'مسقط، عمان (GMT+4)'),
            ('Africa/Cairo', 'القاهرة، مصر (GMT+2)'),
            ('Asia/Baghdad', 'بغداد، العراق (GMT+3)'),
            ('Asia/Damascus', 'دمشق، سوريا (GMT+3)'),
            ('Asia/Beirut', 'بيروت، لبنان (GMT+2)'),
            ('Asia/Amman', 'عمان، الأردن (GMT+3)'),
            ('Asia/Jerusalem', 'القدس، فلسطين (GMT+2)'),
            ('Africa/Tunis', 'تونس (GMT+1)'),
            ('Africa/Algiers', 'الجزائر (GMT+1)'),
            ('Africa/Casablanca', 'الدار البيضاء، المغرب (GMT+1)'),
            ('Africa/Tripoli', 'طرابلس، ليبيا (GMT+2)'),
            ('Africa/Khartoum', 'الخرطوم، السودان (GMT+2)'),
            ('Asia/Aden', 'عدن، اليمن (GMT+3)'),
            ('Indian/Comoro', 'جزر القمر (GMT+3)'),
            ('Indian/Mauritius', 'موريشيوس (GMT+4)'),

            # أوروبا
            ('Europe/London', 'لندن، المملكة المتحدة (GMT+0)'),
            ('Europe/Paris', 'باريس، فرنسا (GMT+1)'),
            ('Europe/Berlin', 'برلين، ألمانيا (GMT+1)'),
            ('Europe/Rome', 'روما، إيطاليا (GMT+1)'),
            ('Europe/Madrid', 'مدريد، إسبانيا (GMT+1)'),
            ('Europe/Amsterdam', 'أمستردام، هولندا (GMT+1)'),
            ('Europe/Brussels', 'بروكسل، بلجيكا (GMT+1)'),
            ('Europe/Vienna', 'فيينا، النمسا (GMT+1)'),
            ('Europe/Zurich', 'زيوريخ، سويسرا (GMT+1)'),
            ('Europe/Stockholm', 'ستوكهولم، السويد (GMT+1)'),
            ('Europe/Oslo', 'أوسلو، النرويج (GMT+1)'),
            ('Europe/Copenhagen', 'كوبنهاغن، الدنمارك (GMT+1)'),
            ('Europe/Helsinki', 'هلسنكي، فنلندا (GMT+2)'),
            ('Europe/Warsaw', 'وارسو، بولندا (GMT+1)'),
            ('Europe/Prague', 'براغ، التشيك (GMT+1)'),
            ('Europe/Budapest', 'بودابست، المجر (GMT+1)'),
            ('Europe/Bucharest', 'بوخارست، رومانيا (GMT+2)'),
            ('Europe/Athens', 'أثينا، اليونان (GMT+2)'),
            ('Europe/Istanbul', 'إسطنبول، تركيا (GMT+3)'),
            ('Europe/Moscow', 'موسكو، روسيا (GMT+3)'),

            # آسيا
            ('Asia/Tokyo', 'طوكيو، اليابان (GMT+9)'),
            ('Asia/Seoul', 'سيول، كوريا الجنوبية (GMT+9)'),
            ('Asia/Shanghai', 'شنغهاي، الصين (GMT+8)'),
            ('Asia/Hong_Kong', 'هونغ كونغ (GMT+8)'),
            ('Asia/Singapore', 'سنغافورة (GMT+8)'),
            ('Asia/Bangkok', 'بانكوك، تايلاند (GMT+7)'),
            ('Asia/Jakarta', 'جاكرتا، إندونيسيا (GMT+7)'),
            ('Asia/Manila', 'مانيلا، الفلبين (GMT+8)'),
            ('Asia/Kuala_Lumpur', 'كوالالمبور، ماليزيا (GMT+8)'),
            ('Asia/Dhaka', 'دكا، بنغلاديش (GMT+6)'),
            ('Asia/Kolkata', 'كولكاتا، الهند (GMT+5:30)'),
            ('Asia/Karachi', 'كراتشي، باكستان (GMT+5)'),
            ('Asia/Tashkent', 'طشقند، أوزبكستان (GMT+5)'),
            ('Asia/Almaty', 'ألماتي، كازاخستان (GMT+6)'),
            ('Asia/Yerevan', 'يريفان، أرمينيا (GMT+4)'),
            ('Asia/Baku', 'باكو، أذربيجان (GMT+4)'),
            ('Asia/Tbilisi', 'تبليسي، جورجيا (GMT+4)'),
            ('Asia/Tehran', 'طهران، إيران (GMT+3:30)'),
            ('Asia/Kabul', 'كابول، أفغانستان (GMT+4:30)'),

            # أمريكا الشمالية
            ('America/New_York', 'نيويورك، الولايات المتحدة (GMT-5)'),
            ('America/Los_Angeles', 'لوس أنجلوس، الولايات المتحدة (GMT-8)'),
            ('America/Chicago', 'شيكاغو، الولايات المتحدة (GMT-6)'),
            ('America/Denver', 'دنفر، الولايات المتحدة (GMT-7)'),
            ('America/Toronto', 'تورونتو، كندا (GMT-5)'),
            ('America/Vancouver', 'فانكوفر، كندا (GMT-8)'),
            ('America/Mexico_City', 'مكسيكو سيتي، المكسيك (GMT-6)'),

            # أمريكا الجنوبية
            ('America/Sao_Paulo', 'ساو باولو، البرازيل (GMT-3)'),
            ('America/Buenos_Aires', 'بوينس آيرس، الأرجنتين (GMT-3)'),
            ('America/Lima', 'ليما، بيرو (GMT-5)'),
            ('America/Bogota', 'بوغوتا، كولومبيا (GMT-5)'),
            ('America/Caracas', 'كاراكاس، فنزويلا (GMT-4)'),
            ('America/Santiago', 'سانتياغو، تشيلي (GMT-3)'),

            # أفريقيا
            ('Africa/Johannesburg', 'جوهانسبرغ، جنوب أفريقيا (GMT+2)'),
            ('Africa/Lagos', 'لاغوس، نيجيريا (GMT+1)'),
            ('Africa/Nairobi', 'نيروبي، كينيا (GMT+3)'),
            ('Africa/Addis_Ababa', 'أديس أبابا، إثيوبيا (GMT+3)'),
            ('Africa/Accra', 'أكرا، غانا (GMT+0)'),
            ('Africa/Dakar', 'داكار، السنغال (GMT+0)'),

            # أوقيانوسيا
            ('Australia/Sydney', 'سيدني، أستراليا (GMT+10)'),
            ('Australia/Melbourne', 'ملبورن، أستراليا (GMT+10)'),
            ('Australia/Perth', 'بيرث، أستراليا (GMT+8)'),
            ('Pacific/Auckland', 'أوكلاند، نيوزيلندا (GMT+12)'),
            ('Pacific/Fiji', 'فيجي (GMT+12)'),
            ('Pacific/Honolulu', 'هونولولو، هاواي (GMT-10)'),
        ]
        self.fields['timezone'].widget = forms.Select(
            choices=timezone_choices,
            attrs=self.fields['timezone'].widget.attrs
        )

        # جعل جميع الحقول اختيارية
        for field_name in self.fields:
            self.fields[field_name].required = False

    def clean_emergency_phone(self):
        """التحقق من صحة رقم هاتف الطوارئ"""
        phone = self.cleaned_data.get('emergency_phone')
        if phone:
            # إزالة المسافات والرموز غير المرغوبة (الاحتفاظ بالأرقام و + فقط)
            phone = re.sub(r'[^\d+]', '', phone)

            # إذا كان الحقل فارغاً بعد التنظيف، إرجاع فارغ
            if not phone:
                return ''

            # التحقق من الحد الأدنى والأقصى لطول الرقم
            if len(phone) < 8:
                raise ValidationError('رقم هاتف الطوارئ قصير جداً. يجب أن يكون 8 أرقام على الأقل.')

            if len(phone) > 15:
                raise ValidationError('رقم هاتف الطوارئ طويل جداً. يجب أن يكون 15 رقم كحد أقصى.')

            # التحقق من التنسيقات المختلفة (نفس منطق الهاتف الأساسي)
            # أرقام سعودية
            if re.match(r'^(05|5)\d{8}$', phone):
                if phone.startswith('05'):
                    phone = '+966' + phone[1:]
                elif phone.startswith('5'):
                    phone = '+966' + phone

            # أرقام تبدأ بـ 966 (سعودية بدون +)
            elif re.match(r'^966\d{9}$', phone):
                phone = '+' + phone

            # أرقام تبدأ بـ + (دولية)
            elif re.match(r'^\+\d{8,14}$', phone):
                pass

            # أرقام محلية (بدون رمز دولة)
            elif re.match(r'^\d{8,10}$', phone):
                pass

            # أرقام دولية أخرى
            elif re.match(r'^\d{10,15}$', phone):
                phone = '+' + phone

            else:
                raise ValidationError('تنسيق رقم هاتف الطوارئ غير صحيح. أمثلة صحيحة: +966501234567، 0501234567، 501234567')

        return phone


class PasswordChangeForm(forms.Form):
    """نموذج تغيير كلمة المرور"""

    current_password = forms.CharField(
        label='كلمة المرور الحالية',
        widget=forms.PasswordInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
            'placeholder': 'كلمة المرور الحالية'
        })
    )

    new_password = forms.CharField(
        label='كلمة المرور الجديدة',
        widget=forms.PasswordInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
            'placeholder': 'كلمة المرور الجديدة'
        })
    )

    confirm_password = forms.CharField(
        label='تأكيد كلمة المرور الجديدة',
        widget=forms.PasswordInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
            'placeholder': 'تأكيد كلمة المرور الجديدة'
        })
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_current_password(self):
        """التحقق من كلمة المرور الحالية"""
        current_password = self.cleaned_data.get('current_password')
        if not self.user.check_password(current_password):
            raise ValidationError('كلمة المرور الحالية غير صحيحة.')
        return current_password

    def clean_new_password(self):
        """التحقق من قوة كلمة المرور الجديدة"""
        password = self.cleaned_data.get('new_password')
        if password:
            if len(password) < 8:
                raise ValidationError('كلمة المرور يجب أن تكون 8 أحرف على الأقل.')

            # التحقق من وجود أرقام وحروف
            if not re.search(r'\d', password):
                raise ValidationError('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل.')

            if not re.search(r'[a-zA-Z]', password):
                raise ValidationError('كلمة المرور يجب أن تحتوي على حرف واحد على الأقل.')

        return password

    def clean(self):
        """التحقق من تطابق كلمات المرور"""
        cleaned_data = super().clean()
        new_password = cleaned_data.get('new_password')
        confirm_password = cleaned_data.get('confirm_password')

        if new_password and confirm_password:
            if new_password != confirm_password:
                raise ValidationError('كلمات المرور الجديدة غير متطابقة.')

        return cleaned_data

    def save(self):
        """حفظ كلمة المرور الجديدة"""
        password = self.cleaned_data['new_password']
        self.user.set_password(password)
        self.user.save()
        return self.user
