{% extends 'base.html' %}

{% block title %}حصصي - {{ ACADEMY_SLOGAN }}{% endblock %}

{% block extra_css %}
<!-- FullCalendar CSS -->
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-6">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-islamic-dark">حصصي</h1>
                    <p class="text-gray-600 mt-1">عرض وإدارة حصصك التعليمية والتقويم</p>
                    {% if active_subscription %}
                    <div class="mt-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle ml-1"></i>
                            اشتراك نشط: {{ active_subscription.plan.name }}
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mr-2">
                            <i class="fas fa-clock ml-1"></i>
                            {{ subscription_stats.remaining_lessons }} حصة متبقية
                        </span>
                    </div>
                    {% endif %}
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- تم حذف زر إظهار/إخفاء التقويم - التقويم ظاهر دائماً الآن -->
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="fas fa-calendar-day text-blue-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">إجمالي الحصص</p>
                    <p class="text-2xl font-bold text-gray-900">{{ general_stats.total_lessons }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">الحصص المكتملة</p>
                    <p class="text-2xl font-bold text-gray-900">{{ general_stats.completed_lessons }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                    <i class="fas fa-hourglass-half text-yellow-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">الحصص القادمة</p>
                    <p class="text-2xl font-bold text-gray-900">{{ general_stats.upcoming_lessons }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <i class="fas fa-star text-purple-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">متوسط التقييم</p>
                    <p class="text-2xl font-bold text-gray-900">{{ avg_rating|floatformat:1 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Section (Always Visible) -->
    <div id="calendarSection" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-calendar-alt text-islamic-primary ml-2"></i>
                تقويم الحصص
                <span id="syncStatus" class="mr-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    <i class="fas fa-sync-alt mr-1"></i>
                    متزامن في الوقت الفعلي
                </span>
            </h3>
            <div class="flex items-center space-x-2 space-x-reverse">
                <button id="refreshCalendar" class="bg-islamic-primary hover:bg-islamic-light text-white px-4 py-2 rounded-lg text-sm transition-colors">
                    <i class="fas fa-sync-alt ml-1"></i>
                    تحديث
                </button>
            </div>
        </div>
        <div id="calendarContainer">
            <div id="calendar"></div>
            <div id="noEventsMessage" class="text-center py-8 hidden">
                <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600 text-lg">لا توجد حصص مجدولة حالياً</p>
                <p class="text-gray-500 text-sm mt-2">سيتم عرض الحصص هنا بعد جدولتها من قبل الإدارة</p>
            </div>
        </div>
    </div>

    {% if active_subscription %}
    <!-- Subscription Lessons Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-calendar-check text-islamic-primary ml-2"></i>
            حصص الاشتراك - {{ active_subscription.plan.name }}
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-blue-50 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-list-ol text-blue-600 text-xl ml-3"></i>
                    <div>
                        <p class="text-sm font-medium text-blue-600">إجمالي الحصص</p>
                        <p class="text-xl font-bold text-blue-900">{{ subscription_stats.total_lessons }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-green-50 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-600 text-xl ml-3"></i>
                    <div>
                        <p class="text-sm font-medium text-green-600">المكتملة</p>
                        <p class="text-xl font-bold text-green-900">{{ subscription_stats.completed_lessons }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-yellow-50 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-clock text-yellow-600 text-xl ml-3"></i>
                    <div>
                        <p class="text-sm font-medium text-yellow-600">القادمة</p>
                        <p class="text-xl font-bold text-yellow-900">{{ subscription_stats.upcoming_lessons }}</p>
                    </div>
                </div>
            </div>
        </div>

        {% if upcoming_scheduled %}
        <h4 class="text-md font-semibold text-gray-800 mb-3">الحصص القادمة من الاشتراك</h4>
        <div class="space-y-3">
            {% for lesson in upcoming_scheduled %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-islamic-primary rounded-full flex items-center justify-center">
                            <span class="text-white font-bold">{{ lesson.lesson_number }}</span>
                        </div>
                        <div>
                            <h5 class="font-medium text-gray-900">حصة رقم {{ lesson.lesson_number }}</h5>
                            <p class="text-sm text-gray-600">
                                {% if lesson.teacher %}
                                    مع {{ lesson.teacher.get_full_name }}
                                {% else %}
                                    <span class="text-orange-600">لم يتم تعيين معلم بعد</span>
                                {% endif %}
                            </p>
                            <p class="text-sm text-gray-500">
                                <i class="fas fa-calendar ml-1"></i>
                                {{ lesson.scheduled_date|date:"Y/m/d" }} - {{ lesson.scheduled_date|time:"H:i" }}
                                ({{ lesson.duration_minutes }} دقيقة)
                            </p>
                            {% if lesson.scheduled_date %}
                            <p class="text-xs text-blue-600 mt-1">
                                <i class="fas fa-clock ml-1"></i>
                                {% now "Y-m-d H:i" as current_time %}
                                {% if lesson.scheduled_date|date:"Y-m-d H:i" > current_time %}
                                    {% if lesson.scheduled_date|timeuntil %}
                                        متبقي {{ lesson.scheduled_date|timeuntil }}
                                    {% endif %}
                                {% endif %}
                            </p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        {% if lesson.status == 'scheduled' %}
                            <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                <i class="fas fa-clock ml-1"></i>
                                مجدولة
                            </span>
                        {% elif lesson.status == 'completed' %}
                            <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                <i class="fas fa-check ml-1"></i>
                                مكتملة
                            </span>
                        {% elif lesson.status == 'cancelled' %}
                            <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                <i class="fas fa-times ml-1"></i>
                                ملغية
                            </span>
                        {% else %}
                            <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                {{ lesson.get_status_display }}
                            </span>
                        {% endif %}

                        {% if lesson.teacher and lesson.status == 'scheduled' %}
                        <button class="bg-islamic-primary text-white px-3 py-1 rounded-lg text-xs hover:bg-islamic-light transition-colors">
                            <i class="fas fa-video ml-1"></i>
                            دخول الحصة
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8">
            <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
            <p class="text-gray-600">لا توجد حصص قادمة من الاشتراك</p>
            <p class="text-sm text-gray-500 mt-2">سيتم إضافة الحصص بعد موافقة الإدارة على الجدولة</p>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Today's Lessons - Dynamic Content -->
    {% if today_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-calendar-day text-islamic-primary ml-2"></i>
            حصص اليوم
        </h3>

        <div class="space-y-4">
            {% for lesson in today_lessons %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-islamic-primary rounded-full flex items-center justify-center">
                            <span class="text-white font-bold">{{ lesson.lesson_number }}</span>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">حصة رقم {{ lesson.lesson_number }}</h4>
                            <p class="text-sm text-gray-600">
                                {% if lesson.teacher %}
                                    مع {{ lesson.teacher.get_full_name }}
                                {% else %}
                                    <span class="text-orange-600">لم يتم تعيين معلم بعد</span>
                                {% endif %}
                            </p>
                            <p class="text-sm text-gray-500">
                                <i class="fas fa-clock ml-1"></i>
                                {{ lesson.scheduled_date|time:"H:i" }} ({{ lesson.duration_minutes }} دقيقة)
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        {% if lesson.status == 'scheduled' %}
                            <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                <i class="fas fa-clock ml-1"></i>
                                مجدولة
                            </span>
                        {% elif lesson.status == 'completed' %}
                            <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                <i class="fas fa-check ml-1"></i>
                                مكتملة
                            </span>
                        {% endif %}

                        {% if lesson.teacher and lesson.status == 'scheduled' %}
                        <button class="bg-islamic-primary text-white px-3 py-1 rounded-lg text-xs hover:bg-islamic-light transition-colors">
                            <i class="fas fa-video ml-1"></i>
                            دخول الحصة
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Upcoming Lessons - Dynamic Content -->
    {% if upcoming_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-calendar-alt text-islamic-primary ml-2"></i>
            الحصص القادمة
        </h3>

        <div class="space-y-4">
            {% for lesson in upcoming_lessons %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 font-bold">{{ lesson.lesson_number }}</span>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">حصة رقم {{ lesson.lesson_number }}</h4>
                            <p class="text-sm text-gray-600">
                                {% if lesson.teacher %}
                                    مع {{ lesson.teacher.get_full_name }}
                                {% else %}
                                    <span class="text-orange-600">لم يتم تعيين معلم بعد</span>
                                {% endif %}
                            </p>
                            <p class="text-sm text-gray-500">
                                <i class="fas fa-calendar ml-1"></i>
                                {{ lesson.scheduled_date|date:"Y/m/d" }} - {{ lesson.scheduled_date|time:"H:i" }} ({{ lesson.duration_minutes }} دقيقة)
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            <i class="fas fa-clock ml-1"></i>
                            مجدولة
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Recent Completed Lessons with Rating System -->
    {% if completed_lessons %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-check-circle text-islamic-primary ml-2"></i>
            الحصص المكتملة مؤخراً - تقييم الحصص
        </h3>

        <div class="space-y-4">
            {% for lesson in completed_lessons %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <span class="text-green-600 font-bold">{{ lesson.lesson_number }}</span>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">حصة رقم {{ lesson.lesson_number }}</h4>
                            <p class="text-sm text-gray-600">
                                {% if lesson.teacher %}
                                    مع {{ lesson.teacher.get_full_name }}
                                {% else %}
                                    معلم غير محدد
                                {% endif %}
                            </p>
                            <p class="text-sm text-gray-500">
                                <i class="fas fa-calendar ml-1"></i>
                                {{ lesson.scheduled_date|date:"Y/m/d" }} - {{ lesson.scheduled_date|time:"H:i" }}
                                <span class="mr-2">
                                    <i class="fas fa-clock ml-1"></i>
                                    {{ lesson.duration_minutes }} دقيقة
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            <i class="fas fa-check ml-1"></i>
                            مكتملة
                        </span>

                        <!-- Rating Section - النظام الموحد -->
                        {% if lesson.unified_rating %}
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 ml-2" id="rating-display-{{ lesson.id }}">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= lesson.unified_rating.overall_rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="text-sm text-gray-600 mr-2">تم التقييم ({{ lesson.unified_rating.average_rating }})</span>
                            <button onclick="viewUnifiedRating({{ lesson.id }}, {{ lesson.unified_rating.id }})"
                                    class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-xs hover:bg-gray-200 transition-colors duration-200">
                                <i class="fas fa-eye ml-1"></i>
                                عرض التقييم
                            </button>
                            <button onclick="editUnifiedRating({{ lesson.id }}, {{ lesson.unified_rating.id }})"
                                    class="bg-blue-100 text-blue-600 px-3 py-1 rounded-lg text-xs hover:bg-blue-200 transition-colors duration-200 mr-2">
                                <i class="fas fa-edit ml-1"></i>
                                تعديل
                            </button>
                        </div>
                        {% else %}
                        <button onclick="openUnifiedRatingModal({{ lesson.id }}, '{{ lesson.teacher.get_full_name|default:"معلم غير محدد" }}', {{ lesson.lesson_number }}, 'scheduled')"
                                class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200">
                            <i class="fas fa-star ml-2"></i>
                            تقييم الحصة
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Unified Rating Modal -->
<div id="unifiedRatingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="unifiedModalTitle">تقييم الحصة</h3>
                <button onclick="closeUnifiedRatingModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4">
                <p class="text-sm text-gray-600 mb-4" id="unifiedModalLessonInfo">حصة رقم 1 مع الأستاذ محمد</p>

                <!-- التقييم العام -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">التقييم العام</label>
                    <div class="flex items-center space-x-1 space-x-reverse" id="overallRating">
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="1" data-field="overall_rating"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="2" data-field="overall_rating"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="3" data-field="overall_rating"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="4" data-field="overall_rating"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="5" data-field="overall_rating"></i>
                    </div>
                </div>

                <!-- جودة الحصة -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">جودة الحصة</label>
                    <div class="flex items-center space-x-1 space-x-reverse" id="lessonQuality">
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="1" data-field="lesson_quality"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="2" data-field="lesson_quality"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="3" data-field="lesson_quality"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="4" data-field="lesson_quality"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="5" data-field="lesson_quality"></i>
                    </div>
                </div>

                <!-- تفاعل المعلم -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">تفاعل المعلم</label>
                    <div class="flex items-center space-x-1 space-x-reverse" id="teacherInteraction">
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="1" data-field="teacher_interaction"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="2" data-field="teacher_interaction"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="3" data-field="teacher_interaction"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="4" data-field="teacher_interaction"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="5" data-field="teacher_interaction"></i>
                    </div>
                </div>

                <!-- الجودة التقنية -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الجودة التقنية</label>
                    <div class="flex items-center space-x-1 space-x-reverse" id="technicalQuality">
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="1" data-field="technical_quality"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="2" data-field="technical_quality"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="3" data-field="technical_quality"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="4" data-field="technical_quality"></i>
                        <i class="fas fa-star text-gray-300 hover:text-yellow-400 cursor-pointer text-xl" data-rating="5" data-field="technical_quality"></i>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="unifiedRatingComment" class="block text-sm font-medium text-gray-700 mb-2">تعليق على الحصة (اختياري)</label>
                    <textarea id="unifiedRatingComment" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                              placeholder="شاركنا رأيك في الحصة..."></textarea>
                </div>
            </div>

            <div class="flex items-center justify-end space-x-2 space-x-reverse">
                <button onclick="closeUnifiedRatingModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    إلغاء
                </button>
                <button onclick="submitUnifiedRating()"
                        class="px-4 py-2 bg-islamic-primary text-white rounded-lg hover:bg-islamic-light transition-colors">
                    <i class="fas fa-star ml-1"></i>
                    إرسال التقييم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Rating Modal -->
<div id="viewRatingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">عرض التقييم</h3>
                <button onclick="closeViewRatingModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4" id="viewRatingContent">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>

            <div class="flex items-center justify-end">
                <button onclick="closeViewRatingModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- FullCalendar JS -->
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let calendar;
    let calendarInitialized = false;

    // Auto-refresh calendar every 30 seconds to sync with admin changes
    let autoRefreshInterval;
    let isAutoRefreshing = false;

    function startAutoRefresh() {
        autoRefreshInterval = setInterval(() => {
            if (calendar && calendarInitialized) {
                console.log('تحديث تلقائي للتقويم...');
                isAutoRefreshing = true;
                calendar.refetchEvents();
            }
        }, 30000); // كل 30 ثانية
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }

    // Initialize calendar immediately since it's always visible
    console.log('تهيئة التقويم - التقويم ظاهر دائماً');
    initializeCalendar();
    calendarInitialized = true;

    // بدء التحديث التلقائي فوراً
    startAutoRefresh();
    console.log('تم بدء التحديث التلقائي للتقويم');

    function updateEventsDisplay(eventsCount) {
        const noEventsMessage = document.getElementById('noEventsMessage');
        const calendarEl = document.getElementById('calendar');

        if (eventsCount === 0) {
            console.log('إظهار رسالة عدم وجود أحداث');
            if (noEventsMessage) {
                noEventsMessage.classList.remove('hidden');
            }
            if (calendarEl) {
                calendarEl.style.display = 'none';
            }
        } else {
            console.log(`إخفاء رسالة عدم وجود أحداث - يوجد ${eventsCount} حدث`);
            if (noEventsMessage) {
                noEventsMessage.classList.add('hidden');
            }
            if (calendarEl) {
                calendarEl.style.display = 'block';
            }
        }
    }

    function showUpdateNotification(message) {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-islamic-primary text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-sync-alt mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        // إضافة الإشعار للصفحة
        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    function initializeCalendar() {
        const calendarEl = document.getElementById('calendar');

        if (!calendarEl) {
            console.error('لم يتم العثور على عنصر التقويم');
            return;
        }

        console.log('بدء تهيئة التقويم...');

        calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'ar',
            direction: 'rtl',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            buttonText: {
                today: 'اليوم',
                month: 'شهر',
                week: 'أسبوع',
                day: 'يوم'
            },
            events: function(fetchInfo, successCallback, failureCallback) {
                fetch('{% url "student_lessons_calendar_data" %}')
                    .then(response => {
                        console.log('استجابة API:', response.status, response.statusText);
                        return response.json();
                    })
                    .then(data => {
                        console.log('بيانات API الكاملة:', data);

                        if (data.events && Array.isArray(data.events)) {
                            console.log('عدد الأحداث المستلمة:', data.events.length);
                            console.log('الأحداث:', data.events);

                            // Update UI based on events count
                            setTimeout(() => {
                                updateEventsDisplay(data.events.length);

                                // إظهار رسالة تحديث فقط عند التحديث التلقائي
                                if (isAutoRefreshing) {
                                    if (data.events.length > 0) {
                                        showUpdateNotification(`تم تحديث التقويم - يوجد ${data.events.length} حصة مجدولة`);
                                    } else {
                                        showUpdateNotification('تم تحديث التقويم - لا توجد حصص مجدولة حالياً');
                                    }
                                    isAutoRefreshing = false;
                                }
                            }, 500);

                            successCallback(data.events);
                        } else {
                            console.warn('لا توجد أحداث في البيانات المستلمة');

                            // Update UI for no events
                            setTimeout(() => {
                                updateEventsDisplay(0);

                                // إظهار رسالة تحديث فقط عند التحديث التلقائي
                                if (isAutoRefreshing) {
                                    showUpdateNotification('تم تحديث التقويم - لا توجد حصص مجدولة حالياً');
                                    isAutoRefreshing = false;
                                }
                            }, 500);

                            successCallback([]);
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في جلب بيانات التقويم:', error);
                        failureCallback(error);
                    });
            },
            eventClick: function(info) {
                showLessonDetails(info.event);
            },
            loading: function(bool) {
                if (bool) {
                    console.log('جاري تحميل بيانات التقويم...');
                } else {
                    console.log('تم تحميل بيانات التقويم');

                    // Check if there are any events and show/hide message
                    setTimeout(() => {
                        const events = calendar.getEvents();
                        const noEventsMessage = document.getElementById('noEventsMessage');
                        const calendarEl = document.getElementById('calendar');

                        if (events.length === 0) {
                            console.log('لا توجد حصص مجدولة');
                            if (noEventsMessage) {
                                noEventsMessage.classList.remove('hidden');
                                calendarEl.style.display = 'none';
                            }
                        } else {
                            console.log(`تم العثور على ${events.length} حصة مجدولة`);
                            if (noEventsMessage) {
                                noEventsMessage.classList.add('hidden');
                                calendarEl.style.display = 'block';
                            }
                        }
                    }, 100); // Small delay to ensure events are loaded
                }
            },
            eventDidMount: function(info) {
                console.log('عرض حدث:', info.event.title, info.event);

                // Add tooltip
                const props = info.event.extendedProps;
                let tooltipText = info.event.title + '\n' +
                    'المعلم: ' + (props.teacher_name || 'غير محدد') + '\n' +
                    'المدة: ' + props.duration + ' دقيقة' + '\n' +
                    'الحالة: ' + getStatusText(props.status);

                if (props.type === 'scheduled_lesson') {
                    tooltipText += '\n' + 'من الاشتراك: ' + props.subscription_plan;
                }

                info.el.setAttribute('title', tooltipText);

                // Add status icon
                const statusIcon = getStatusIcon(props.status);
                if (statusIcon) {
                    const iconElement = document.createElement('i');
                    iconElement.className = statusIcon + ' mr-1';
                    const titleElement = info.el.querySelector('.fc-event-title');
                    if (titleElement) {
                        titleElement.prepend(iconElement);
                    }
                }

                // Add custom styling based on status
                if (props.status === 'scheduled') {
                    info.el.style.backgroundColor = '#3498db';
                    info.el.style.borderColor = '#2980b9';
                } else if (props.status === 'completed') {
                    info.el.style.backgroundColor = '#27ae60';
                    info.el.style.borderColor = '#229954';
                } else if (props.status === 'cancelled') {
                    info.el.style.backgroundColor = '#e74c3c';
                    info.el.style.borderColor = '#c0392b';
                }
            },
            height: 'auto',
            aspectRatio: 1.8
        });

        calendar.render();
        console.log('تم عرض التقويم بنجاح');

        // Add refresh button functionality
        const refreshButton = document.getElementById('refreshCalendar');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                console.log('تحديث يدوي للتقويم...');

                // Visual feedback
                const icon = refreshButton.querySelector('i');
                icon.classList.add('fa-spin');

                // تعيين علامة التحديث اليدوي
                isAutoRefreshing = false;

                // Refetch events
                calendar.refetchEvents();

                setTimeout(() => {
                    icon.classList.remove('fa-spin');

                    // Check events after refresh
                    const events = calendar.getEvents();
                    updateEventsDisplay(events.length);

                    // إظهار رسالة التحديث اليدوي
                    showUpdateNotification(`تم تحديث التقويم يدوياً - يوجد ${events.length} حصة مجدولة`);
                }, 1000);
            });
        }
    }

    function showLessonDetails(event) {
        const props = event.extendedProps;
        const statusText = getStatusText(props.status);
        const statusIcon = getStatusIcon(props.status);

        let detailsText = `📚 ${event.title}\n\n`;
        detailsText += `📅 التاريخ: ${event.start.toLocaleDateString('ar-SA')}\n`;
        detailsText += `🕐 الوقت: ${event.start.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}\n`;
        detailsText += `⏱️ المدة: ${props.duration} دقيقة\n`;
        detailsText += `👨‍🏫 المعلم: ${props.teacher_name || 'لم يتم تعيين معلم'}\n`;
        detailsText += `📊 الحالة: ${statusText}\n`;

        if (props.type === 'scheduled_lesson') {
            detailsText += `🔢 رقم الحصة: ${props.lesson_number}\n`;
            detailsText += `📦 الباقة: ${props.subscription_plan}\n`;
        }

        // Add action buttons based on status
        if (props.status === 'scheduled' && props.teacher_name && props.teacher_name !== 'لم يتم تعيين معلم') {
            const now = new Date();
            const lessonTime = new Date(event.start);
            const timeDiff = lessonTime.getTime() - now.getTime();
            const minutesDiff = Math.floor(timeDiff / (1000 * 60));

            if (minutesDiff <= 15 && minutesDiff >= -30) { // Can join 15 minutes before to 30 minutes after
                detailsText += `\n🎥 يمكنك دخول الحصة الآن!`;
            } else if (minutesDiff > 15) {
                detailsText += `\n⏰ متبقي ${Math.floor(minutesDiff / 60)} ساعة و ${minutesDiff % 60} دقيقة للحصة`;
            }
        }

        alert(detailsText);
    }

    function getStatusText(status) {
        const statusMap = {
            'scheduled': 'مجدولة',
            'completed': 'مكتملة',
            'cancelled': 'ملغية',
            'rescheduled': 'معاد جدولتها',
            'no_show': 'غياب',
            'in_progress': 'جارية'
        };
        return statusMap[status] || status;
    }

    function getStatusIcon(status) {
        const iconMap = {
            'scheduled': 'fas fa-clock',
            'completed': 'fas fa-check',
            'cancelled': 'fas fa-times',
            'rescheduled': 'fas fa-redo',
            'no_show': 'fas fa-user-times',
            'in_progress': 'fas fa-play'
        };
        return iconMap[status] || null;
    }

    // Unified Rating System JavaScript
    let currentLessonId = null;
    let currentLessonType = null;
    let currentRatingId = null;
    let currentRatings = {
        overall_rating: 0,
        lesson_quality: 0,
        teacher_interaction: 0,
        technical_quality: 0
    };

    function openUnifiedRatingModal(lessonId, teacherName, lessonNumber, lessonType) {
        currentLessonId = lessonId;
        currentLessonType = lessonType;
        currentRatingId = null;

        // Reset ratings
        resetUnifiedRatings();

        document.getElementById('unifiedModalLessonInfo').textContent = `حصة رقم ${lessonNumber} مع ${teacherName}`;
        document.getElementById('unifiedRatingComment').value = '';

        // Show modal
        document.getElementById('unifiedRatingModal').classList.remove('hidden');
    }

    function editUnifiedRating(lessonId, ratingId) {
        // جلب بيانات التقييم الحالي وفتح النافذة للتعديل
        currentLessonId = lessonId;
        currentRatingId = ratingId;

        // يمكن إضافة API لجلب بيانات التقييم الحالي
        // لكن الآن سنفتح النافذة فارغة للتعديل
        document.getElementById('unifiedRatingModal').classList.remove('hidden');
    }

    function viewUnifiedRating(lessonId, ratingId) {
        // عرض التقييم الموجود (للقراءة فقط)
        // يمكن إضافة API لجلب بيانات التقييم وعرضها
        alert('سيتم تطوير عرض التقييم قريباً');
    }

    function closeUnifiedRatingModal() {
        document.getElementById('unifiedRatingModal').classList.add('hidden');
        currentLessonId = null;
        currentLessonType = null;
        currentRatingId = null;
        resetUnifiedRatings();
    }

    function closeViewRatingModal() {
        document.getElementById('viewRatingModal').classList.add('hidden');
    }

    function resetUnifiedRatings() {
        // Reset all rating sections
        ['overallRating', 'lessonQuality', 'teacherInteraction', 'technicalQuality'].forEach(sectionId => {
            const stars = document.querySelectorAll(`#${sectionId} i`);
            stars.forEach(star => {
                star.classList.remove('text-yellow-400');
                star.classList.add('text-gray-300');
            });
        });

        // Reset ratings object
        currentRatings = {
            overall_rating: 0,
            lesson_quality: 0,
            teacher_interaction: 0,
            technical_quality: 0
        };
    }

    function setUnifiedRating(field, rating) {
        currentRatings[field] = rating;

        // Update visual stars for this field
        const sectionMap = {
            'overall_rating': 'overallRating',
            'lesson_quality': 'lessonQuality',
            'teacher_interaction': 'teacherInteraction',
            'technical_quality': 'technicalQuality'
        };

        const sectionId = sectionMap[field];
        const stars = document.querySelectorAll(`#${sectionId} i`);

        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('text-gray-300');
                star.classList.add('text-yellow-400');
            } else {
                star.classList.remove('text-yellow-400');
                star.classList.add('text-gray-300');
            }
        });
    }

    function submitUnifiedRating() {
        if (!currentLessonId || !currentLessonType) {
            alert('خطأ: لم يتم تحديد الحصة');
            return;
        }

        // التحقق من أن جميع التقييمات تم إدخالها
        const requiredRatings = ['overall_rating', 'lesson_quality', 'teacher_interaction', 'technical_quality'];
        for (let field of requiredRatings) {
            if (currentRatings[field] === 0) {
                alert(`يرجى إدخال تقييم ${field}`);
                return;
            }
        }

        const comment = document.getElementById('unifiedRatingComment').value.trim();

        // Show loading state
        const submitButton = document.querySelector('button[onclick="submitUnifiedRating()"]');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> جاري الإرسال...';
        submitButton.disabled = true;

        // Submit unified rating
        fetch('{% url "submit_unified_rating" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                lesson_id: currentLessonId,
                lesson_type: currentLessonType,
                overall_rating: currentRatings.overall_rating,
                lesson_quality: currentRatings.lesson_quality,
                teacher_interaction: currentRatings.teacher_interaction,
                technical_quality: currentRatings.technical_quality,
                comment: comment
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال التقييم بنجاح!');
                closeUnifiedRatingModal();

                // Reload the page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إرسال التقييم');
        })
        .finally(() => {
            // Reset button state
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        });
    }

    function updateLessonRatingDisplay(lessonId, rating) {
        const ratingDisplay = document.getElementById(`rating-display-${lessonId}`);
        if (ratingDisplay) {
            // Update stars display
            const stars = ratingDisplay.querySelectorAll('i');
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.remove('far');
                    star.classList.add('fas');
                } else {
                    star.classList.remove('fas');
                    star.classList.add('far');
                }
            });
        }
    }

    // Unified Star rating click handlers
    const unifiedStars = document.querySelectorAll('#unifiedRatingModal i[data-rating]');

    unifiedStars.forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.getAttribute('data-rating'));
            const field = this.getAttribute('data-field');
            setUnifiedRating(field, rating);
        });

        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.getAttribute('data-rating'));
            const field = this.getAttribute('data-field');
            // Temporary highlight on hover
            highlightUnifiedStars(field, rating);
        });
    });

    // Reset to actual rating on mouse leave
    ['overallRating', 'lessonQuality', 'teacherInteraction', 'technicalQuality'].forEach(sectionId => {
        document.getElementById(sectionId).addEventListener('mouseleave', function() {
            const fieldMap = {
                'overallRating': 'overall_rating',
                'lessonQuality': 'lesson_quality',
                'teacherInteraction': 'teacher_interaction',
                'technicalQuality': 'technical_quality'
            };
            const field = fieldMap[sectionId];
            setUnifiedRating(field, currentRatings[field]);
        });
    });

    function highlightUnifiedStars(field, rating) {
        const sectionMap = {
            'overall_rating': 'overallRating',
            'lesson_quality': 'lessonQuality',
            'teacher_interaction': 'teacherInteraction',
            'technical_quality': 'technicalQuality'
        };

        const sectionId = sectionMap[field];
        const stars = document.querySelectorAll(`#${sectionId} i`);

        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('text-gray-300');
                star.classList.add('text-yellow-400');
            } else {
                star.classList.remove('text-yellow-400');
                star.classList.add('text-gray-300');
            }
        });
    }
});
</script>
{% endblock %}
