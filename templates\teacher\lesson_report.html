{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تقرير الحصة - {{ live_lesson.title }}{% endblock %}

{% block extra_css %}
<style>
.report-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.report-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

.report-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.rating-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-right: 4px solid #007bff;
}

.star-rating {
    display: flex;
    gap: 0.5rem;
}

.star {
    font-size: 1.5rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s;
}

.star.active,
.star:hover {
    color: #ffc107;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.btn-submit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50px;
    padding: 1rem 3rem;
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
    transition: transform 0.3s;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.lesson-info {
    background: #e3f2fd;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-right: 4px solid #2196f3;
}

.completion-status {
    background: #e8f5e8;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-right: 4px solid #4caf50;
}
</style>
{% endblock %}

{% block content %}
<div class="report-form">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="report-card">
                    <!-- Header -->
                    <div class="report-header">
                        <h2><i class="fas fa-clipboard-list ml-2"></i>تقرير الحصة</h2>
                        <p class="mb-0">{{ live_lesson.title }}</p>
                        <small>{{ live_lesson.scheduled_date|date:"Y/m/d H:i" }}</small>
                    </div>

                    <div class="p-4">
                        <!-- معلومات الحصة -->
                        <div class="lesson-info">
                            <h5><i class="fas fa-info-circle text-primary ml-2"></i>معلومات الحصة</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الطالب:</strong> {{ live_lesson.student.get_full_name }}</p>
                                    <p><strong>المدة:</strong> {{ live_lesson.duration_minutes }} دقيقة</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>الحالة:</strong> 
                                        <span class="badge badge-success">مكتملة</span>
                                    </p>
                                    <p><strong>تاريخ الانتهاء:</strong> {{ live_lesson.ended_at|date:"Y/m/d H:i" }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- حالة الاكتمال -->
                        <div class="completion-status">
                            <h6><i class="fas fa-check-circle text-success ml-2"></i>حالة احتساب الحصة</h6>
                            <p class="mb-0">
                                <i class="fas fa-exclamation-triangle text-warning ml-1"></i>
                                <strong>مطلوب:</strong> يجب إرسال هذا التقرير لاحتساب الحصة للطالب
                            </p>
                        </div>

                        <!-- نموذج التقرير -->
                        <form id="teacher-report-form" method="post">
                            {% csrf_token %}
                            
                            <!-- تقييم أداء الطالب -->
                            <div class="form-group">
                                <h5><i class="fas fa-user-graduate text-primary ml-2"></i>تقييم أداء الطالب</h5>
                                
                                <div class="rating-group">
                                    <label>أداء الطالب العام:</label>
                                    <div class="star-rating" data-field="student_performance">
                                        <span class="star" data-value="1">★</span>
                                        <span class="star" data-value="2">★</span>
                                        <span class="star" data-value="3">★</span>
                                        <span class="star" data-value="4">★</span>
                                        <span class="star" data-value="5">★</span>
                                    </div>
                                </div>

                                <div class="rating-group">
                                    <label>مستوى التفاعل والمشاركة:</label>
                                    <div class="star-rating" data-field="student_participation">
                                        <span class="star" data-value="1">★</span>
                                        <span class="star" data-value="2">★</span>
                                        <span class="star" data-value="3">★</span>
                                        <span class="star" data-value="4">★</span>
                                        <span class="star" data-value="5">★</span>
                                    </div>
                                </div>

                                <div class="rating-group">
                                    <label>مستوى الفهم والاستيعاب:</label>
                                    <div class="star-rating" data-field="student_understanding">
                                        <span class="star" data-value="1">★</span>
                                        <span class="star" data-value="2">★</span>
                                        <span class="star" data-value="3">★</span>
                                        <span class="star" data-value="4">★</span>
                                        <span class="star" data-value="5">★</span>
                                    </div>
                                </div>

                                <div class="rating-group">
                                    <label>تقييم عام للحصة:</label>
                                    <div class="star-rating" data-field="overall_lesson_rating">
                                        <span class="star" data-value="1">★</span>
                                        <span class="star" data-value="2">★</span>
                                        <span class="star" data-value="3">★</span>
                                        <span class="star" data-value="4">★</span>
                                        <span class="star" data-value="5">★</span>
                                    </div>
                                </div>
                            </div>

                            <!-- النصوص المطلوبة -->
                            <div class="form-group">
                                <label for="strengths"><i class="fas fa-thumbs-up text-success ml-2"></i>نقاط القوة</label>
                                <textarea class="form-control" id="strengths" name="strengths" rows="3" 
                                    placeholder="اذكر نقاط القوة التي لاحظتها على الطالب..." required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="areas_for_improvement"><i class="fas fa-chart-line text-warning ml-2"></i>نقاط تحتاج تحسين</label>
                                <textarea class="form-control" id="areas_for_improvement" name="areas_for_improvement" rows="3" 
                                    placeholder="اذكر النقاط التي يحتاج الطالب لتحسينها..." required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="lesson_summary"><i class="fas fa-book text-info ml-2"></i>ملخص الحصة</label>
                                <textarea class="form-control" id="lesson_summary" name="lesson_summary" rows="4" 
                                    placeholder="ملخص مختصر عما تم تدريسه في الحصة..." required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="homework_assigned"><i class="fas fa-tasks text-secondary ml-2"></i>الواجبات المطلوبة (اختياري)</label>
                                <textarea class="form-control" id="homework_assigned" name="homework_assigned" rows="3" 
                                    placeholder="الواجبات أو المهام المطلوبة من الطالب..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="recommendations"><i class="fas fa-lightbulb text-primary ml-2"></i>توصيات للحصص القادمة (اختياري)</label>
                                <textarea class="form-control" id="recommendations" name="recommendations" rows="3" 
                                    placeholder="توصيات لتحسين الحصص القادمة..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="additional_notes"><i class="fas fa-sticky-note text-muted ml-2"></i>ملاحظات إضافية (اختياري)</label>
                                <textarea class="form-control" id="additional_notes" name="additional_notes" rows="2" 
                                    placeholder="أي ملاحظات إضافية..."></textarea>
                            </div>

                            <!-- أزرار الإرسال -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-submit">
                                    <i class="fas fa-paper-plane ml-2"></i>
                                    إرسال التقرير واحتساب الحصة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden inputs for ratings -->
<input type="hidden" id="student_performance" name="student_performance" value="">
<input type="hidden" id="student_participation" name="student_participation" value="">
<input type="hidden" id="student_understanding" name="student_understanding" value="">
<input type="hidden" id="overall_lesson_rating" name="overall_lesson_rating" value="">
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // نظام التقييم بالنجوم
    const starRatings = document.querySelectorAll('.star-rating');
    
    starRatings.forEach(rating => {
        const stars = rating.querySelectorAll('.star');
        const fieldName = rating.getAttribute('data-field');
        
        stars.forEach((star, index) => {
            star.addEventListener('click', () => {
                const value = parseInt(star.getAttribute('data-value'));
                
                // تحديث النجوم
                stars.forEach((s, i) => {
                    if (i < value) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
                
                // تحديث الحقل المخفي
                const hiddenInput = document.getElementById(fieldName);
                if (hiddenInput) {
                    hiddenInput.value = value;
                }
            });
            
            star.addEventListener('mouseover', () => {
                const value = parseInt(star.getAttribute('data-value'));
                stars.forEach((s, i) => {
                    if (i < value) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
        
        rating.addEventListener('mouseleave', () => {
            const hiddenInput = document.getElementById(fieldName);
            const currentValue = hiddenInput ? parseInt(hiddenInput.value) : 0;
            
            stars.forEach((s, i) => {
                if (i < currentValue) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
    });
    
    // إرسال النموذج
    const form = document.getElementById('teacher-report-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // التحقق من التقييمات المطلوبة
        const requiredRatings = ['student_performance', 'student_participation', 'student_understanding', 'overall_lesson_rating'];
        let allRated = true;
        
        requiredRatings.forEach(field => {
            const input = document.getElementById(field);
            if (!input.value) {
                allRated = false;
            }
        });
        
        if (!allRated) {
            alert('يرجى تقييم جميع النقاط المطلوبة');
            return;
        }
        
        // إضافة التقييمات للنموذج
        requiredRatings.forEach(field => {
            const input = document.getElementById(field);
            const formInput = document.createElement('input');
            formInput.type = 'hidden';
            formInput.name = field;
            formInput.value = input.value;
            form.appendChild(formInput);
        });
        
        // إرسال النموذج
        form.submit();
    });
});
</script>
{% endblock %}
