{% extends 'base.html' %}
{% load static %}

{% block title %}تقارير الحصص الشاملة - قرآنيا{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 p-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold text-islamic-dark flex items-center">
                        <i class="fas fa-clipboard-list text-purple-600 ml-4"></i>
                        تقارير الحصص الشاملة
                    </h1>
                    <p class="mt-3 text-lg text-gray-600">عرض وتحليل تقارير جميع أنواع الحصص في الأكاديمية</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="{% url 'admin_ratings_view' %}" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة لمركز التقييمات
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-clipboard-list text-white"></i>
                </div>
                <div class="text-2xl font-bold text-purple-600 mb-1">{{ stats.total_reports }}</div>
                <div class="text-sm text-gray-600">إجمالي التقارير</div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
                <div class="text-2xl font-bold text-green-600 mb-1">{{ stats.complete_reports }}</div>
                <div class="text-sm text-gray-600">تقارير مكتملة</div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-vial text-white"></i>
                </div>
                <div class="text-2xl font-bold text-blue-600 mb-1">{{ stats.trial_lessons }}</div>
                <div class="text-sm text-gray-600">حصص تجريبية</div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-calendar-check text-white"></i>
                </div>
                <div class="text-2xl font-bold text-orange-600 mb-1">{{ stats.subscription_lessons }}</div>
                <div class="text-sm text-gray-600">حصص اشتراك</div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <div class="w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-calendar-alt text-white"></i>
                </div>
                <div class="text-2xl font-bold text-teal-600 mb-1">{{ stats.scheduled_lessons }}</div>
                <div class="text-sm text-gray-600">حصص مجدولة</div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 p-6">
            <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-filter text-purple-600 ml-2"></i>
                فلاتر متقدمة
            </h3>
            
            <form method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع الحصة</label>
                    <select name="lesson_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <option value="">جميع الأنواع</option>
                        <option value="trial" {% if filters.lesson_type == 'trial' %}selected{% endif %}>تجريبية</option>
                        <option value="subscription" {% if filters.lesson_type == 'subscription' %}selected{% endif %}>اشتراك</option>
                        <option value="scheduled" {% if filters.lesson_type == 'scheduled' %}selected{% endif %}>مجدولة</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">المعلم</label>
                    <select name="teacher" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <option value="">جميع المعلمين</option>
                        {% for teacher in teachers %}
                        <option value="{{ teacher.id }}" {% if filters.teacher == teacher.id|stringformat:"s" %}selected{% endif %}>
                            {{ teacher.get_full_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الطالب</label>
                    <select name="student" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <option value="">جميع الطلاب</option>
                        {% for student in students %}
                        <option value="{{ student.id }}" {% if filters.student == student.id|stringformat:"s" %}selected{% endif %}>
                            {{ student.get_full_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">حالة التقرير</label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <option value="">جميع الحالات</option>
                        <option value="complete" {% if filters.status == 'complete' %}selected{% endif %}>مكتمل</option>
                        <option value="incomplete" {% if filters.status == 'incomplete' %}selected{% endif %}>غير مكتمل</option>
                        <option value="teacher_only" {% if filters.status == 'teacher_only' %}selected{% endif %}>تقرير المعلم فقط</option>
                        <option value="student_only" {% if filters.status == 'student_only' %}selected{% endif %}>تقييم الطالب فقط</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
                    <input type="date" name="date_from" value="{{ filters.date_from }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
                    <input type="date" name="date_to" value="{{ filters.date_to }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>

                <div class="md:col-span-6 flex justify-end space-x-4 space-x-reverse">
                    <button type="submit" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center">
                        <i class="fas fa-search ml-2"></i>
                        تطبيق الفلاتر
                    </button>
                    <a href="{% url 'admin_comprehensive_lesson_reports' %}" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center">
                        <i class="fas fa-times ml-2"></i>
                        إزالة الفلاتر
                    </a>
                </div>
            </form>
        </div>

        <!-- Reports Table -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-table text-purple-600 ml-2"></i>
                    جدول التقارير
                </h3>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">نوع الحصة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعلم</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">حالة التقرير</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تقييم المعلم</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تقييم الطالب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for report in reports %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if report.lesson_type == 'trial' %}bg-blue-100 text-blue-800
                                    {% elif report.lesson_type == 'subscription' %}bg-orange-100 text-orange-800
                                    {% else %}bg-teal-100 text-teal-800{% endif %}">
                                    {% if report.lesson_type == 'trial' %}تجريبية
                                    {% elif report.lesson_type == 'subscription' %}اشتراك
                                    {% else %}مجدولة{% endif %}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ report.teacher.get_full_name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ report.student.get_full_name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ report.created_at|date:"Y/m/d" }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if report.is_complete %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        مكتمل
                                    </span>
                                {% elif report.teacher_report_submitted %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-user-tie ml-1"></i>
                                        تقرير المعلم فقط
                                    </span>
                                {% elif report.student_evaluation_submitted %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-user-graduate ml-1"></i>
                                        تقييم الطالب فقط
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle ml-1"></i>
                                        غير مكتمل
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% if report.teacher_report_submitted %}
                                    <div class="flex items-center">
                                        <div class="flex text-yellow-400">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= report.get_teacher_average_rating %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        <span class="mr-2 text-sm text-gray-600">{{ report.get_teacher_average_rating|floatformat:1 }}/5</span>
                                    </div>
                                {% else %}
                                    <span class="text-gray-400">لا يوجد</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% if report.student_evaluation_submitted %}
                                    <div class="flex items-center">
                                        <div class="flex text-yellow-400">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= report.get_student_average_rating %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        <span class="mr-2 text-sm text-gray-600">{{ report.get_student_average_rating|floatformat:1 }}/5</span>
                                    </div>
                                {% else %}
                                    <span class="text-gray-400">لا يوجد</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="{% url 'admin_lesson_report_detail' report.id %}" 
                                       class="text-purple-600 hover:text-purple-900 flex items-center">
                                        <i class="fas fa-eye ml-1"></i>
                                        عرض
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-inbox text-4xl mb-4"></i>
                                <p class="text-lg">لا توجد تقارير متاحة</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
