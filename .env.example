# ==================== إعدادات قاعدة البيانات ====================
DATABASE_URL=postgresql://username:password@localhost:5432/qurania_lms

# ==================== إعدادات Django ====================
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,yourdomain.com

# ==================== إعدادات WhatsApp Business API ====================
# للحصول على هذه البيانات: https://developers.facebook.com
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_from_facebook
WHATSAPP_ACCESS_TOKEN=your_permanent_access_token_from_facebook

# ==================== إعدادات Twilio WhatsApp ====================
# للحصول على هذه البيانات: https://www.twilio.com
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# ==================== إعدادات WhatsApp العامة ====================
WHATSAPP_DEFAULT_PROVIDER=business_api
WHATSAPP_TIMEOUT=30

# ==================== إعدادات Celery (للمهام المجدولة) ====================
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# ==================== إعدادات Redis ====================
REDIS_URL=redis://localhost:6379/0

# ==================== إعدادات البريد الإلكتروني ====================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# ==================== إعدادات الأمان ====================
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False

# ==================== إعدادات التخزين ====================
STATIC_ROOT=/path/to/static/files
MEDIA_ROOT=/path/to/media/files

# ==================== إعدادات التطوير ====================
# فقط للتطوير - لا تستخدم في الإنتاج
DEVELOPMENT_MODE=False
