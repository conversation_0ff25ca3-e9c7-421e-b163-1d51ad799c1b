{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/enhanced-breadcrumb.css' %}">
<style>
    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .form-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #2D5016;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #11998e;
        box-shadow: 0 0 0 0.2rem rgba(17, 153, 142, 0.25);
    }

    .btn-save {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-preview {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-preview:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }

    .variables-help {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .variable-tag {
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-family: monospace;
        font-size: 0.9rem;
        margin: 0.25rem;
        display: inline-block;
        cursor: pointer;
    }

    .variable-tag:hover {
        background: #bbdefb;
    }

    .code-editor {
        font-family: 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.5;
    }

    /* تحسين مسارات التوجيه */
    .breadcrumb {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    .breadcrumb-item {
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #11998e;
        text-decoration: none;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: inline-block;
    }

    .breadcrumb-item a:hover {
        background: rgba(17, 153, 142, 0.1);
        color: #0d7377;
        transform: translateY(-1px);
    }

    .breadcrumb-item.active {
        color: #2D5016;
        font-weight: 600;
        background: rgba(45, 80, 22, 0.1);
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
        color: #11998e;
        font-weight: bold;
        margin: 0 0.5rem;
    }

    .page-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    @media (max-width: 768px) {
        .form-header {
            padding: 1rem;
        }
        
        .btn-save, .btn-preview {
            width: 100%;
            margin-bottom: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'admin_dashboard' %}">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'technical_settings' %}">
                    <i class="fas fa-cogs me-2"></i>الإعدادات التقنية
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'email_settings_dashboard' %}">
                    <i class="fas fa-envelope-open-text me-2"></i>إعدادات البريد
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'email_templates_list' %}">
                    <i class="fas fa-file-alt me-2"></i>القوالب
                </a>
            </li>
            <li class="breadcrumb-item active">
                <i class="fas fa-{% if is_create %}plus{% else %}edit{% endif %} me-2"></i>
                {% if is_create %}إنشاء قالب جديد{% else %}تعديل القالب{% endif %}
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="page-header text-center">
        <div class="container">
            <h1 class="mb-3">
                <i class="fas fa-{% if is_create %}plus{% else %}edit{% endif %} me-3"></i>
                {% if is_create %}إنشاء قالب جديد{% else %}تعديل القالب{% endif %}
            </h1>
            <p class="lead mb-0">
                {% if is_create %}
                    إنشاء قالب بريد إلكتروني جديد مع المتغيرات الديناميكية
                {% else %}
                    تعديل قالب "{{ template.name }}"
                {% endif %}
            </p>
        </div>
    </div>

    <div class="row">
        <!-- Form Column -->
        <div class="col-lg-8">
            <div class="form-card">
                <div class="form-header">
                    <h4 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        معلومات القالب
                    </h4>
                </div>
                
                <div class="card-body p-4">
                    <form method="post" id="templateForm">
                        {% csrf_token %}
                        
                        <!-- اسم القالب -->
                        <div class="form-group">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <i class="fas fa-tag me-2"></i>
                                {{ form.name.label }}
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger mt-1">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- نوع القالب -->
                        <div class="form-group">
                            <label for="{{ form.template_type.id_for_label }}" class="form-label">
                                <i class="fas fa-tag me-2"></i>
                                {{ form.template_type.label }}
                            </label>
                            {{ form.template_type }}
                            {% if form.template_type.errors %}
                                <div class="text-danger mt-1">{{ form.template_type.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                اختر نوع القالب المناسب
                            </small>
                        </div>

                        <!-- موضوع الرسالة -->
                        <div class="form-group">
                            <label for="{{ form.subject.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope me-2"></i>
                                {{ form.subject.label }}
                            </label>
                            {{ form.subject }}
                            {% if form.subject.errors %}
                                <div class="text-danger mt-1">{{ form.subject.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                يمكنك استخدام المتغيرات مثل {{ "{{ academy_name }}" }} في الموضوع
                            </small>
                        </div>

                        <!-- المحتوى HTML -->
                        <div class="form-group">
                            <label for="{{ form.html_content.id_for_label }}" class="form-label">
                                <i class="fas fa-code me-2"></i>
                                {{ form.html_content.label }}
                            </label>
                            {{ form.html_content }}
                            {% if form.html_content.errors %}
                                <div class="text-danger mt-1">{{ form.html_content.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- المحتوى النصي -->
                        <div class="form-group">
                            <label for="{{ form.text_content.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-2"></i>
                                {{ form.text_content.label }}
                            </label>
                            {{ form.text_content }}
                            {% if form.text_content.errors %}
                                <div class="text-danger mt-1">{{ form.text_content.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                نسخة نصية احتياطية للبريد الإلكتروني (اختياري)
                            </small>
                        </div>

                        <!-- إعدادات القالب -->
                        <div class="form-group">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    <i class="fas fa-toggle-on me-2"></i>
                                    {{ form.is_active.label }}
                                </label>
                                <small class="form-text text-muted d-block mt-1">
                                    إذا كان غير نشط، لن يتم استخدام هذا القالب في الإشعارات
                                </small>
                            </div>
                        </div>

                        <!-- أزرار العمل -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <button type="button" class="btn btn-preview" onclick="previewTemplate()">
                                <i class="fas fa-eye me-2"></i>
                                معاينة
                            </button>
                            
                            <div>
                                <a href="{% url 'email_templates_list' %}" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-save">
                                    <i class="fas fa-save me-2"></i>
                                    {% if is_create %}إنشاء القالب{% else %}حفظ التغييرات{% endif %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Variables Help Column -->
        <div class="col-lg-4">
            <div class="form-card">
                <div class="form-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        المتغيرات المتاحة
                    </h5>
                </div>
                
                <div class="card-body p-3">
                    <h6 class="text-muted mb-3">متغيرات الأكاديمية:</h6>
                    <div class="variables-help">
                        <span class="variable-tag" onclick="insertVariable('academy_name')">{{ "{{ academy_name }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('academy_slogan')">{{ "{{ academy_slogan }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('academy_email')">{{ "{{ academy_email }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('academy_phone')">{{ "{{ academy_phone }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('academy_address')">{{ "{{ academy_address }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('academy_website')">{{ "{{ academy_website }}" }}</span>
                    </div>

                    <h6 class="text-muted mb-3 mt-4">متغيرات المستخدم:</h6>
                    <div class="variables-help">
                        <span class="variable-tag" onclick="insertVariable('user_name')">{{ "{{ user_name }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('user_email')">{{ "{{ user_email }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('user_phone')">{{ "{{ user_phone }}" }}</span>
                    </div>

                    <h6 class="text-muted mb-3 mt-4">متغيرات الحصص:</h6>
                    <div class="variables-help">
                        <span class="variable-tag" onclick="insertVariable('lesson_subject')">{{ "{{ lesson_subject }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('lesson_date')">{{ "{{ lesson_date }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('lesson_time')">{{ "{{ lesson_time }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('teacher_name')">{{ "{{ teacher_name }}" }}</span>
                    </div>

                    <h6 class="text-muted mb-3 mt-4">متغيرات النظام:</h6>
                    <div class="variables-help">
                        <span class="variable-tag" onclick="insertVariable('current_date')">{{ "{{ current_date }}" }}</span>
                        <span class="variable-tag" onclick="insertVariable('current_year')">{{ "{{ current_year }}" }}</span>
                    </div>

                    <div class="alert alert-info mt-3">
                        <small>
                            <i class="fas fa-lightbulb me-1"></i>
                            انقر على أي متغير لإدراجه في المحتوى
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة القالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function insertVariable(variableName) {
    const htmlContent = document.getElementById('{{ form.html_content.id_for_label }}');
    const variable = '{{ ' + variableName + ' }}';
    
    // إدراج المتغير في موضع المؤشر
    const start = htmlContent.selectionStart;
    const end = htmlContent.selectionEnd;
    const text = htmlContent.value;
    
    htmlContent.value = text.substring(0, start) + variable + text.substring(end);
    htmlContent.focus();
    htmlContent.setSelectionRange(start + variable.length, start + variable.length);
}

function previewTemplate() {
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    const content = document.getElementById('previewContent');
    
    // جمع بيانات النموذج
    const formData = new FormData(document.getElementById('templateForm'));
    
    // إظهار تحميل
    content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">جاري إنشاء المعاينة...</p></div>';
    modal.show();
    
    // معاينة بسيطة (سيتم تحسينها لاحقاً)
    setTimeout(() => {
        const subject = formData.get('subject') || 'موضوع الرسالة';
        const htmlContent = formData.get('html_content') || '<p>محتوى الرسالة</p>';
        
        // استبدال بعض المتغيرات للمعاينة
        let previewContent = htmlContent
            .replace(/\{\{\s*academy_name\s*\}\}/g, 'أكاديمية القرآنية')
            .replace(/\{\{\s*academy_slogan\s*\}\}/g, 'نحو تعليم قرآني متميز')
            .replace(/\{\{\s*user_name\s*\}\}/g, 'أحمد محمد')
            .replace(/\{\{\s*academy_email\s*\}\}/g, '<EMAIL>')
            .replace(/\{\{\s*academy_phone\s*\}\}/g, '+966501234567')
            .replace(/\{\{\s*current_date\s*\}\}/g, new Date().toLocaleDateString('ar-SA'))
            .replace(/\{\{\s*current_year\s*\}\}/g, new Date().getFullYear());
        
        content.innerHTML = `
            <div class="border rounded p-3">
                <h6 class="text-muted">الموضوع:</h6>
                <p class="fw-bold">${subject.replace(/\{\{\s*academy_name\s*\}\}/g, 'أكاديمية القرآنية')}</p>
                <hr>
                <h6 class="text-muted">المحتوى:</h6>
                <div class="border rounded p-3" style="background: #f8f9fa;">
                    ${previewContent}
                </div>
            </div>
        `;
    }, 1000);
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة syntax highlighting بسيط للمحتوى HTML
    const htmlTextarea = document.getElementById('{{ form.html_content.id_for_label }}');
    if (htmlTextarea) {
        htmlTextarea.classList.add('code-editor');
    }
});
</script>
{% endblock %}
