"""
إعدادات الإنتاج للاستضافة المشتركة - Namecheap
"""

from .settings import *
import os

# إعدادات الإنتاج
DEBUG = False
ALLOWED_HOSTS = ['marketation.online', 'www.marketation.online', '*.marketation.online']

# إعدادات قاعدة البيانات للإنتاج - MySQL مع PyMySQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'markoivw_qurania',
        'USER': 'markoivw_elasapni',
        'PASSWORD': '0122931008aAs',  # استبدل بكلمة المرور الفعلية
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# تكوين PyMySQL لاستخدامه مع Django
import pymysql
pymysql.install_as_MySQLdb()

# إعدادات الملفات الثابتة للإنتاج
STATIC_URL = '/static/'
STATIC_ROOT = '/home/<USER>/application/staticfiles'
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# إعدادات الملفات المرفوعة
MEDIA_URL = '/media/'
MEDIA_ROOT = '/home/<USER>/application/media'

# إعدادات الأمان
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'SAMEORIGIN'
SECURE_REFERRER_POLICY = 'same-origin'

# إعدادات الجلسات (SSL متاح على Namecheap)
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# إعدادات البريد الإلكتروني للإنتاج - استخدام SMTP البسيط
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
DEFAULT_FROM_EMAIL = 'Qurania Academy <<EMAIL>>'

# إعدادات احتياطية للنظام القديم (في حالة الحاجة)
# EMAIL_HOST = 'mail.marketation.online'
# EMAIL_PORT = 587
# EMAIL_USE_TLS = True
# EMAIL_HOST_USER = '<EMAIL>'
# EMAIL_HOST_PASSWORD = 'your_email_password'

# إعدادات التسجيل للإنتاج
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': '/home/<USER>/application/logs/django_errors.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'qurania_lms': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# إعدادات الكاش للإنتاج
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': '/home/<USER>/application/cache',
    }
}

# إعدادات إضافية للأداء
USE_TZ = True
TIME_ZONE = 'Africa/Cairo'

# إعدادات الضغط للملفات الثابتة
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.ManifestStaticFilesStorage'


