{% extends 'base.html' %}
{% load static %}

{% block title %}إرسال رسالة جماعية{% endblock %}

{% block extra_css %}
<style>
    .compose-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    .form-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .stats-card {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .user-checkbox {
        transition: all 0.3s ease;
    }
    .user-checkbox:hover {
        background: #f8fafc;
    }
    .message-type-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }
    .message-type-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .message-type-card.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .recipient-type-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid #e2e8f0;
    }
    .recipient-type-card:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }
    .recipient-type-card.selected {
        border-color: #667eea;
        background: #f0f4ff;
    }
</style>
{% endblock %}

{% block content %}
<div class="compose-form">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">إرسال رسالة جماعية</h1>
            <p class="text-white text-opacity-90 text-lg">تواصل مع جميع المستخدمين أو مجموعة محددة</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
            <div class="stats-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-blue-600">{{ stats.total_users }}</div>
                <div class="text-sm text-gray-600">إجمالي المستخدمين</div>
            </div>
            <div class="stats-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-green-600">{{ stats.teachers_count }}</div>
                <div class="text-sm text-gray-600">المعلمين</div>
            </div>
            <div class="stats-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-purple-600">{{ stats.students_count }}</div>
                <div class="text-sm text-gray-600">الطلاب</div>
            </div>
            <div class="stats-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-orange-600">{{ stats.active_users_count }}</div>
                <div class="text-sm text-gray-600">نشطين (30 يوم)</div>
            </div>
            <div class="stats-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-red-600">{{ stats.new_users_count }}</div>
                <div class="text-sm text-gray-600">جدد (7 أيام)</div>
            </div>
        </div>

        <!-- Form Card -->
        <div class="max-w-6xl mx-auto">
            <div class="form-card rounded-2xl shadow-2xl p-8">
                <form method="POST" id="bulk-message-form" class="space-y-8">
                    {% csrf_token %}
                    
                    <!-- Message Type Selection -->
                    <div>
                        <label class="block text-xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-tag text-purple-600 ml-2"></i>
                            نوع الرسالة
                        </label>
                        
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {% for type_key, type_name in message_types %}
                            <div class="message-type-card bg-white rounded-xl p-4 text-center" 
                                 onclick="selectMessageType('{{ type_key }}', this)">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center
                                            {% if type_key == 'announcement' %}bg-blue-100{% endif %}
                                            {% if type_key == 'update' %}bg-green-100{% endif %}
                                            {% if type_key == 'maintenance' %}bg-yellow-100{% endif %}
                                            {% if type_key == 'urgent' %}bg-red-100{% endif %}
                                            {% if type_key == 'training' %}bg-purple-100{% endif %}
                                            {% if type_key == 'event' %}bg-indigo-100{% endif %}">
                                    {% if type_key == 'announcement' %}
                                        <i class="fas fa-bullhorn text-blue-600"></i>
                                    {% elif type_key == 'update' %}
                                        <i class="fas fa-sync text-green-600"></i>
                                    {% elif type_key == 'maintenance' %}
                                        <i class="fas fa-tools text-yellow-600"></i>
                                    {% elif type_key == 'urgent' %}
                                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                                    {% elif type_key == 'training' %}
                                        <i class="fas fa-graduation-cap text-purple-600"></i>
                                    {% elif type_key == 'event' %}
                                        <i class="fas fa-calendar-star text-indigo-600"></i>
                                    {% else %}
                                        <i class="fas fa-envelope text-gray-600"></i>
                                    {% endif %}
                                </div>
                                <h4 class="font-bold text-sm">{{ type_name }}</h4>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <input type="hidden" name="message_type" id="message_type" value="announcement" required>
                    </div>

                    <!-- Recipient Type Selection -->
                    <div>
                        <label class="block text-xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-users text-green-600 ml-2"></i>
                            المستقبلين
                        </label>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            {% for type_key, type_name in recipient_types %}
                            <div class="recipient-type-card bg-white rounded-xl p-4" 
                                 onclick="selectRecipientType('{{ type_key }}', this)">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full flex items-center justify-center ml-3
                                                {% if type_key == 'all' %}bg-blue-100{% endif %}
                                                {% if type_key == 'teachers' %}bg-green-100{% endif %}
                                                {% if type_key == 'students' %}bg-purple-100{% endif %}
                                                {% if type_key == 'active_users' %}bg-orange-100{% endif %}
                                                {% if type_key == 'new_users' %}bg-red-100{% endif %}
                                                {% if type_key == 'custom' %}bg-gray-100{% endif %}">
                                        {% if type_key == 'all' %}
                                            <i class="fas fa-globe text-blue-600"></i>
                                        {% elif type_key == 'teachers' %}
                                            <i class="fas fa-chalkboard-teacher text-green-600"></i>
                                        {% elif type_key == 'students' %}
                                            <i class="fas fa-user-graduate text-purple-600"></i>
                                        {% elif type_key == 'active_users' %}
                                            <i class="fas fa-user-check text-orange-600"></i>
                                        {% elif type_key == 'new_users' %}
                                            <i class="fas fa-user-plus text-red-600"></i>
                                        {% else %}
                                            <i class="fas fa-user-friends text-gray-600"></i>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-gray-800">{{ type_name }}</h4>
                                        <p class="text-sm text-gray-600">
                                            {% if type_key == 'all' %}{{ stats.total_users }} مستخدم{% endif %}
                                            {% if type_key == 'teachers' %}{{ stats.teachers_count }} معلم{% endif %}
                                            {% if type_key == 'students' %}{{ stats.students_count }} طالب{% endif %}
                                            {% if type_key == 'active_users' %}{{ stats.active_users_count }} مستخدم{% endif %}
                                            {% if type_key == 'new_users' %}{{ stats.new_users_count }} مستخدم{% endif %}
                                            {% if type_key == 'custom' %}حسب الاختيار{% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <input type="hidden" name="recipient_type" id="recipient_type" value="all" required>
                    </div>

                    <!-- Custom Recipients (Hidden by default) -->
                    <div id="custom-recipients" class="hidden">
                        <label class="block text-lg font-bold text-gray-800 mb-4">
                            <i class="fas fa-user-check text-blue-600 ml-2"></i>
                            اختيار مستخدمين محددين
                        </label>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Teachers -->
                            <div class="bg-gray-50 rounded-xl p-4">
                                <h4 class="font-bold text-gray-800 mb-3">
                                    <i class="fas fa-chalkboard-teacher text-green-600 ml-2"></i>
                                    المعلمين ({{ teachers.count }})
                                </h4>
                                <div class="max-h-48 overflow-y-auto space-y-2">
                                    {% for teacher in teachers %}
                                    <label class="user-checkbox flex items-center p-2 rounded-lg cursor-pointer">
                                        <input type="checkbox" name="custom_recipients" value="{{ teacher.id }}" 
                                               class="ml-3 text-green-600 focus:ring-green-500">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center ml-2">
                                                <i class="fas fa-chalkboard-teacher text-green-600 text-sm"></i>
                                            </div>
                                            <span class="font-medium text-gray-800">{{ teacher.get_full_name }}</span>
                                        </div>
                                    </label>
                                    {% endfor %}
                                </div>
                            </div>
                            
                            <!-- Students -->
                            <div class="bg-gray-50 rounded-xl p-4">
                                <h4 class="font-bold text-gray-800 mb-3">
                                    <i class="fas fa-user-graduate text-purple-600 ml-2"></i>
                                    الطلاب ({{ students.count }})
                                </h4>
                                <div class="max-h-48 overflow-y-auto space-y-2">
                                    {% for student in students %}
                                    <label class="user-checkbox flex items-center p-2 rounded-lg cursor-pointer">
                                        <input type="checkbox" name="custom_recipients" value="{{ student.id }}" 
                                               class="ml-3 text-purple-600 focus:ring-purple-500">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center ml-2">
                                                <i class="fas fa-user-graduate text-purple-600 text-sm"></i>
                                            </div>
                                            <span class="font-medium text-gray-800">{{ student.get_full_name }}</span>
                                        </div>
                                    </label>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Subject -->
                    <div>
                        <label for="subject" class="block text-lg font-bold text-gray-800 mb-2">
                            <i class="fas fa-heading text-blue-600 ml-2"></i>
                            موضوع الرسالة *
                        </label>
                        <input type="text" 
                               id="subject" 
                               name="subject" 
                               required
                               placeholder="اكتب موضوع الرسالة..."
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors">
                    </div>

                    <!-- Content -->
                    <div>
                        <label for="content" class="block text-lg font-bold text-gray-800 mb-2">
                            <i class="fas fa-align-left text-orange-600 ml-2"></i>
                            محتوى الرسالة *
                        </label>
                        <textarea id="content" 
                                  name="content" 
                                  required
                                  rows="8"
                                  placeholder="اكتب محتوى الرسالة هنا..."
                                  class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none transition-colors resize-none"></textarea>
                        <div class="flex justify-between items-center mt-2">
                            <p class="text-sm text-gray-500">
                                <i class="fas fa-info-circle ml-1"></i>
                                سيتم إرسال إشعار لجميع المستقبلين
                            </p>
                            <span id="char-count" class="text-sm text-gray-500">0 حرف</span>
                        </div>
                    </div>

                    <!-- Priority and Options -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Priority -->
                        <div>
                            <label for="priority" class="block text-lg font-bold text-gray-800 mb-2">
                                <i class="fas fa-exclamation-circle text-red-600 ml-2"></i>
                                الأولوية
                            </label>
                            <select id="priority" name="priority" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-red-500 focus:outline-none transition-colors">
                                {% for priority_key, priority_name in priority_levels %}
                                    <option value="{{ priority_key }}" {% if priority_key == 'medium' %}selected{% endif %}>{{ priority_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Email Option -->
                        <div class="flex items-center">
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" name="send_email" class="ml-3 text-blue-600 focus:ring-blue-500">
                                <div>
                                    <span class="font-bold text-gray-800">إرسال إيميل أيضاً</span>
                                    <p class="text-sm text-gray-600">سيتم إرسال الرسالة عبر البريد الإلكتروني</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <a href="{% url 'notifications:message_list' %}" 
                               class="bg-gray-100 text-gray-600 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors">
                                <i class="fas fa-arrow-right ml-2"></i>
                                إلغاء
                            </a>
                            
                            <a href="{% url 'notifications:bulk_message_list' %}" 
                               class="bg-blue-100 text-blue-600 px-6 py-3 rounded-lg hover:bg-blue-200 transition-colors">
                                <i class="fas fa-list ml-2"></i>
                                الرسائل المرسلة
                            </a>
                        </div>
                        
                        <button type="submit"
                                class="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                                id="send-btn" disabled>
                            <i class="fas fa-paper-plane ml-2"></i>
                            إرسال الرسالة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let selectedMessageType = 'announcement';
let selectedRecipientType = 'all';

function selectMessageType(type, element) {
    // Remove selection from all cards
    document.querySelectorAll('.message-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Add selection to clicked card
    element.classList.add('selected');
    
    // Set the value
    selectedMessageType = type;
    document.getElementById('message_type').value = type;
    
    checkFormValidity();
}

function selectRecipientType(type, element) {
    // Remove selection from all cards
    document.querySelectorAll('.recipient-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Add selection to clicked card
    element.classList.add('selected');
    
    // Set the value
    selectedRecipientType = type;
    document.getElementById('recipient_type').value = type;
    
    // Show/hide custom recipients
    const customRecipientsDiv = document.getElementById('custom-recipients');
    if (type === 'custom') {
        customRecipientsDiv.classList.remove('hidden');
    } else {
        customRecipientsDiv.classList.add('hidden');
        // Uncheck all custom recipients
        document.querySelectorAll('input[name="custom_recipients"]').forEach(checkbox => {
            checkbox.checked = false;
        });
    }
    
    checkFormValidity();
}

function checkFormValidity() {
    const subject = document.getElementById('subject').value;
    const content = document.getElementById('content').value;
    const sendBtn = document.getElementById('send-btn');
    
    let isValid = subject.trim() && content.trim() && selectedMessageType && selectedRecipientType;
    
    // If custom recipients, check if at least one is selected
    if (selectedRecipientType === 'custom') {
        const checkedRecipients = document.querySelectorAll('input[name="custom_recipients"]:checked');
        isValid = isValid && checkedRecipients.length > 0;
    }
    
    sendBtn.disabled = !isValid;
}

// Character counter
const contentTextarea = document.getElementById('content');
const charCount = document.getElementById('char-count');

contentTextarea.addEventListener('input', function() {
    const length = this.value.length;
    charCount.textContent = `${length} حرف`;
    
    if (length > 1000) {
        charCount.classList.add('text-red-500');
    } else if (length > 500) {
        charCount.classList.add('text-yellow-500');
        charCount.classList.remove('text-red-500');
    } else {
        charCount.classList.remove('text-red-500', 'text-yellow-500');
        charCount.classList.add('text-gray-500');
    }
    
    checkFormValidity();
});

// Check form validity on input changes
document.getElementById('subject').addEventListener('input', checkFormValidity);

// Check custom recipients
document.addEventListener('change', function(e) {
    if (e.target.name === 'custom_recipients') {
        checkFormValidity();
    }
});

// Initialize first selections
document.addEventListener('DOMContentLoaded', function() {
    // Select first message type
    const firstMessageType = document.querySelector('.message-type-card');
    if (firstMessageType) {
        firstMessageType.click();
    }
    
    // Select first recipient type
    const firstRecipientType = document.querySelector('.recipient-type-card');
    if (firstRecipientType) {
        firstRecipientType.click();
    }
});
</script>
{% endblock %}
