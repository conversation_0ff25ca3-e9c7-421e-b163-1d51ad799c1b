{% extends 'base.html' %}

{% block title %}حساب محظور - {{ SITE_NAME }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-red-50 to-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-20 w-20 bg-red-100 rounded-full flex items-center justify-center mb-6">
                <i class="fas fa-ban text-red-600 text-3xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">
                حساب محظور
            </h2>
            <p class="text-gray-600">
                تم حظر هذا الحساب من الوصول للنظام
            </p>
        </div>

        <!-- Status Card -->
        <div class="bg-white rounded-lg shadow-lg p-8 border border-gray-200">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-user-slash text-red-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">
                    حساب محظور
                </h3>
                {% if username %}
                <p class="text-gray-600 mb-2">
                    <span class="font-semibold">{{ username }}</span>
                </p>
                {% endif %}
                <p class="text-gray-600 mb-4">
                    هذا الحساب محظور من استخدام النظام
                </p>
            </div>

            <!-- Ban Info -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-600 text-lg mt-1"></i>
                    </div>
                    <div class="mr-3 w-full">
                        <h4 class="text-sm font-medium text-red-800 mb-3">
                            معلومات الحظر
                        </h4>

                        <!-- حالة الحظر -->
                        <div class="flex items-center justify-between mb-3 pb-2 border-b border-red-200">
                            <span class="text-sm font-medium text-red-700">حالة الحظر:</span>
                            <span class="text-sm bg-red-100 text-red-800 px-2 py-1 rounded-full">
                                {% if is_permanent %}
                                    <i class="fas fa-ban ml-1"></i> حظر دائم
                                {% elif is_temporary %}
                                    <i class="fas fa-clock ml-1"></i> حظر مؤقت
                                {% else %}
                                    <i class="fas fa-question-circle ml-1"></i> غير محدد
                                {% endif %}
                            </span>
                        </div>

                        <!-- سبب الحظر -->
                        {% if ban_reason %}
                        <div class="mb-3 pb-2 border-b border-red-200">
                            <div class="text-sm font-medium text-red-700 mb-1">سبب الحظر:</div>
                            <div class="text-sm bg-white p-2 rounded border border-red-200">
                                {{ ban_reason }}
                            </div>
                        </div>
                        {% endif %}

                        <!-- تاريخ انتهاء الحظر -->
                        {% if is_temporary and banned_until %}
                        <div class="mb-3 pb-2 border-b border-red-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-red-700">تاريخ انتهاء الحظر:</span>
                                <span class="text-sm font-bold text-red-800">{{ banned_until }}</span>
                            </div>
                            <div class="mt-2 h-2 w-full bg-red-200 rounded-full overflow-hidden">
                                <div id="ban-progress" class="h-full bg-red-600" style="width: 0%"></div>
                            </div>
                            <div class="text-xs text-red-600 mt-1 text-center" id="ban-remaining"></div>
                        </div>
                        {% endif %}

                        <!-- تنبيه -->
                        <div class="text-sm text-red-700 bg-red-100 p-2 rounded-lg border border-red-300">
                            <i class="fas fa-exclamation-circle ml-1"></i>
                            لا يمكنك الوصول للوحة التحكم أو استخدام خدمات المنصة خلال فترة الحظر.
                        </div>
                    </div>
                </div>
            </div>

            {% if is_temporary and banned_until %}
            <script>
                // حساب الوقت المتبقي للحظر
                function updateBanTimer() {
                    const banEndDate = new Date("{{ banned_until }}");
                    const now = new Date();

                    // إذا انتهى الحظر
                    if (now >= banEndDate) {
                        document.getElementById('ban-progress').style.width = '100%';
                        document.getElementById('ban-remaining').innerHTML = 'انتهى الحظر! يمكنك <a href="{% url "login" %}" class="text-red-800 underline">تسجيل الدخول</a> مرة أخرى.';
                        return;
                    }

                    // حساب الوقت المتبقي
                    const totalDuration = banEndDate - now;
                    const days = Math.floor(totalDuration / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((totalDuration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((totalDuration % (1000 * 60 * 60)) / (1000 * 60));

                    // تحديث شريط التقدم
                    const banStartDate = new Date(banEndDate);
                    banStartDate.setDate(banStartDate.getDate() - 30); // افتراض أن مدة الحظر 30 يوم
                    const progress = ((now - banStartDate) / (banEndDate - banStartDate)) * 100;
                    document.getElementById('ban-progress').style.width = Math.min(progress, 100) + '%';

                    // تحديث النص
                    let remainingText = 'متبقي: ';
                    if (days > 0) remainingText += days + ' يوم ';
                    if (hours > 0) remainingText += hours + ' ساعة ';
                    if (minutes > 0) remainingText += minutes + ' دقيقة';

                    document.getElementById('ban-remaining').textContent = remainingText;
                }

                // تحديث الوقت كل دقيقة
                updateBanTimer();
                setInterval(updateBanTimer, 60000);
            </script>
            {% endif %}

            <!-- Contact Info -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-headset text-blue-600 text-lg mt-1"></i>
                    </div>
                    <div class="mr-3">
                        <h4 class="text-sm font-medium text-blue-800 mb-2">
                            تواصل مع الدعم
                        </h4>
                        <p class="text-sm text-blue-700 mb-3">
                            إذا كنت تعتقد أن هذا الحظر خطأ أو تريد الاستفسار عن حالة حسابك، يمكنك التواصل مع فريق الدعم.
                        </p>
                        <div class="space-y-2 text-sm text-blue-700">
                            <p><i class="fas fa-envelope ml-1"></i> البريد الإلكتروني: {% if ACADEMY_SUPPORT_EMAIL %}{{ ACADEMY_SUPPORT_EMAIL }}{% else %}<EMAIL>{% endif %}</p>
                            <p><i class="fas fa-phone ml-1"></i> الهاتف: {% if ACADEMY_PHONE %}{{ ACADEMY_PHONE }}{% else %}+966 50 123 4567{% endif %}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex flex-col space-y-3">
                <a href="{% url 'login' %}"
                   class="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition duration-200 text-center">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </a>

                <a href="/"
                   class="w-full bg-islamic-primary text-white py-2 px-4 rounded-lg hover:bg-islamic-light transition duration-200 text-center">
                    <i class="fas fa-home ml-2"></i>
                    العودة للصفحة الرئيسية
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center">
            <p class="text-sm text-gray-500">
                © {{ CURRENT_YEAR }} {{ ACADEMY_SLOGAN }}. جميع الحقوق محفوظة.
            </p>
        </div>
    </div>
</div>

<style>
/* تحسينات إضافية للتصميم */
.bg-islamic-primary {
    background-color: #2d5a27;
}

.hover\:bg-islamic-light:hover {
    background-color: #3d7a37;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.space-y-8 > * {
    animation: fadeIn 0.6s ease-out;
}

.space-y-8 > *:nth-child(2) {
    animation-delay: 0.2s;
}

.space-y-8 > *:nth-child(3) {
    animation-delay: 0.4s;
}

/* تحسين الاستجابة */
@media (max-width: 640px) {
    .max-w-md {
        max-width: 95%;
    }

    .px-8 {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
</style>
{% endblock %}
