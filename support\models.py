from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

User = get_user_model()


class SupportTicket(models.Model):
    """نموذج تذاكر الدعم الفني"""

    PRIORITY_CHOICES = (
        ('low', _('منخفضة')),
        ('medium', _('متوسطة')),
        ('high', _('عالية')),
        ('urgent', _('عاجلة')),
    )

    STATUS_CHOICES = (
        ('open', _('مفتوحة')),
        ('in_progress', _('قيد المعالجة')),
        ('waiting_response', _('في انتظار الرد')),
        ('resolved', _('محلولة')),
        ('closed', _('مغلقة')),
    )

    CATEGORY_CHOICES = (
        ('technical', _('مشكلة تقنية')),
        ('account', _('مشكلة في الحساب')),
        ('payment', _('مشكلة في الدفع')),
        ('lesson', _('مشكلة في الحصة')),
        ('general', _('استفسار عام')),
        ('complaint', _('شكوى')),
        ('suggestion', _('اقتراح')),
    )

    # معلومات التذكرة الأساسية
    ticket_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رقم التذكرة')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان التذكرة')
    )

    description = models.TextField(
        verbose_name=_('وصف المشكلة')
    )

    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='general',
        verbose_name=_('فئة التذكرة')
    )

    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium',
        verbose_name=_('الأولوية')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='open',
        verbose_name=_('حالة التذكرة')
    )

    # معلومات المستخدمين
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_tickets',
        verbose_name=_('منشئ التذكرة')
    )

    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_tickets',
        limit_choices_to={'user_type': 'admin'},
        verbose_name=_('مُعيّن إلى')
    )

    # معلومات التوقيت
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    resolved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الحل')
    )

    closed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الإغلاق')
    )

    # معلومات إضافية
    is_read_by_admin = models.BooleanField(
        default=False,
        verbose_name=_('مقروءة من المدير')
    )

    is_read_by_user = models.BooleanField(
        default=True,  # المستخدم قرأها عند إنشائها
        verbose_name=_('مقروءة من المستخدم')
    )

    admin_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات المدير')
    )

    class Meta:
        verbose_name = _('تذكرة دعم')
        verbose_name_plural = _('تذاكر الدعم')
        ordering = ['-created_at']

    def __str__(self):
        return f"#{self.ticket_number} - {self.title}"

    def save(self, *args, **kwargs):
        """إنشاء رقم تذكرة تلقائياً وإدارة التواريخ"""
        is_new = not self.pk
        old_status = None

        if not is_new:
            # الحصول على الحالة القديمة للمقارنة
            try:
                old_ticket = SupportTicket.objects.get(pk=self.pk)
                old_status = old_ticket.status
            except SupportTicket.DoesNotExist:
                pass

        if not self.ticket_number:
            import uuid
            self.ticket_number = f"TK{timezone.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6].upper()}"

        # إدارة تواريخ الحل والإغلاق بناءً على الحالة
        if self.status == 'resolved':
            if not self.resolved_at:
                self.resolved_at = timezone.now()
        elif self.status == 'closed':
            if not self.closed_at:
                self.closed_at = timezone.now()
        else:
            # إذا لم تكن التذكرة محلولة أو مغلقة، إزالة التواريخ
            if old_status == 'closed' and self.status != 'closed':
                self.closed_at = None
            if old_status == 'resolved' and self.status != 'resolved':
                self.resolved_at = None

        # حفظ التذكرة
        super().save(*args, **kwargs)

        # إرسال إشعارات فقط عند التحديث (ليس الإنشاء)
        if not is_new and old_status and old_status != self.status:
            from notifications.utils import NotificationService
            NotificationService.notify_support_ticket_updated(self)

    def get_priority_color(self):
        """إرجاع لون الأولوية"""
        colors = {
            'low': 'text-green-600',
            'medium': 'text-yellow-600',
            'high': 'text-orange-600',
            'urgent': 'text-red-600'
        }
        return colors.get(self.priority, 'text-gray-600')

    def get_status_color(self):
        """إرجاع لون الحالة"""
        colors = {
            'open': 'text-blue-600',
            'in_progress': 'text-yellow-600',
            'waiting_response': 'text-purple-600',
            'resolved': 'text-green-600',
            'closed': 'text-gray-600'
        }
        return colors.get(self.status, 'text-gray-600')

    def can_be_closed_by_user(self):
        """التحقق من إمكانية إغلاق التذكرة من المستخدم"""
        return self.status in ['resolved', 'waiting_response']

    def can_add_response(self):
        """تحديد ما إذا كان بإمكان إضافة ردود على التذكرة"""
        # يمكن إضافة ردود فقط إذا لم تكن التذكرة مغلقة أو محلولة
        return self.status not in ['closed', 'resolved']

    def get_response_time(self):
        """حساب وقت الاستجابة"""
        if self.responses.exists():
            first_response = self.responses.filter(is_admin_response=True).first()
            if first_response:
                delta = first_response.created_at - self.created_at
                return delta
        return None


class SupportTicketResponse(models.Model):
    """نموذج ردود تذاكر الدعم"""

    ticket = models.ForeignKey(
        SupportTicket,
        on_delete=models.CASCADE,
        related_name='responses',
        verbose_name=_('التذكرة')
    )

    message = models.TextField(
        verbose_name=_('الرسالة')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='support_responses',
        verbose_name=_('كاتب الرد')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الرد')
    )

    is_admin_response = models.BooleanField(
        default=False,
        verbose_name=_('رد من المدير')
    )

    is_read = models.BooleanField(
        default=False,
        verbose_name=_('مقروء')
    )

    attachment = models.FileField(
        upload_to='support_attachments/',
        blank=True,
        null=True,
        verbose_name=_('مرفق')
    )

    class Meta:
        verbose_name = _('رد تذكرة دعم')
        verbose_name_plural = _('ردود تذاكر الدعم')
        ordering = ['created_at']

    def __str__(self):
        return f"رد على {self.ticket.ticket_number} - {self.created_by.get_full_name()}"

    def save(self, *args, **kwargs):
        """حفظ الرد مع تحديث حالة التذكرة"""
        # تحديد نوع الرد
        self.is_admin_response = self.created_by.is_admin()

        super().save(*args, **kwargs)

        # تحديث حالة التذكرة
        if self.is_admin_response:
            self.ticket.status = 'waiting_response'
            self.ticket.is_read_by_user = False
        else:
            if self.ticket.status == 'waiting_response':
                self.ticket.status = 'in_progress'
            self.ticket.is_read_by_admin = False

        self.ticket.save()

        # إرسال إشعارات
        from notifications.utils import NotificationService
        NotificationService.notify_support_response_added(self)


# تم حذف نموذج SupportMessage - استخدم SystemMessage بدلاً منه


class SystemMessage(models.Model):
    """نموذج رسائل النظام المخصصة للمعلمين والطلاب"""

    MESSAGE_TYPES = (
        ('announcement', _('إعلان عام')),
        ('warning', _('تحذير مهم')),
        ('reminder', _('تذكير')),
        ('update', _('تحديث النظام')),
        ('maintenance', _('إشعار صيانة')),
        ('policy', _('سياسة جديدة')),
        ('feature', _('ميزة جديدة')),
        ('urgent', _('عاجل')),
    )

    PRIORITY_LEVELS = (
        ('low', _('منخفض')),
        ('medium', _('متوسط')),
        ('high', _('عالي')),
        ('urgent', _('عاجل')),
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الرسالة')
    )

    content = models.TextField(
        verbose_name=_('محتوى الرسالة')
    )

    message_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPES,
        default='announcement',
        verbose_name=_('نوع الرسالة')
    )

    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='medium',
        verbose_name=_('الأولوية')
    )

    sent_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_system_messages',
        limit_choices_to={'user_type': 'admin'},
        verbose_name=_('مُرسل بواسطة')
    )

    recipients = models.ManyToManyField(
        User,
        through='SystemMessageRecipient',
        related_name='received_system_messages',
        verbose_name=_('المستقبلون')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإرسال')
    )

    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ انتهاء الصلاحية')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    class Meta:
        verbose_name = _('رسالة النظام')
        verbose_name_plural = _('رسائل النظام')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.sent_by.get_full_name()}"

    def get_type_color(self):
        """إرجاع لون نوع الرسالة"""
        colors = {
            'announcement': 'text-blue-600 bg-blue-50',
            'warning': 'text-red-600 bg-red-50',
            'reminder': 'text-yellow-600 bg-yellow-50',
            'update': 'text-green-600 bg-green-50',
            'maintenance': 'text-purple-600 bg-purple-50',
            'policy': 'text-indigo-600 bg-indigo-50',
            'feature': 'text-teal-600 bg-teal-50',
            'urgent': 'text-red-800 bg-red-100',
        }
        return colors.get(self.message_type, 'text-gray-600 bg-gray-50')

    def get_priority_color(self):
        """إرجاع لون الأولوية"""
        colors = {
            'low': 'text-gray-500',
            'medium': 'text-blue-500',
            'high': 'text-orange-500',
            'urgent': 'text-red-600',
        }
        return colors.get(self.priority, 'text-gray-500')

    def is_expired(self):
        """التحقق من انتهاء صلاحية الرسالة"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def get_recipients_count(self):
        """عدد المستقبلين"""
        return self.recipients.count()

    def get_read_count(self):
        """عدد الذين قرأوا الرسالة"""
        return SystemMessageRecipient.objects.filter(
            system_message=self,
            is_read=True
        ).count()

    def get_unread_count(self):
        """عدد الذين لم يقرأوا الرسالة"""
        return SystemMessageRecipient.objects.filter(
            system_message=self,
            is_read=False
        ).count()


class SystemMessageRecipient(models.Model):
    """نموذج وسطي لتتبع حالة قراءة رسائل النظام"""

    system_message = models.ForeignKey(
        SystemMessage,
        on_delete=models.CASCADE,
        related_name='message_recipients',
        verbose_name=_('رسالة النظام')
    )

    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='system_message_receipts',
        verbose_name=_('المستقبل')
    )

    is_read = models.BooleanField(
        default=False,
        verbose_name=_('مقروءة')
    )

    read_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ القراءة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الاستلام')
    )

    class Meta:
        verbose_name = _('مستقبل رسالة النظام')
        verbose_name_plural = _('مستقبلو رسائل النظام')
        unique_together = ['system_message', 'recipient']

    def __str__(self):
        return f"{self.system_message.title} → {self.recipient.get_full_name()}"

    def mark_as_read(self):
        """تمييز الرسالة كمقروءة"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()
