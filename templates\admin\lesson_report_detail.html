{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل تقرير الحصة - قرآنيا{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 p-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold text-islamic-dark flex items-center">
                        <i class="fas fa-file-alt text-purple-600 ml-4"></i>
                        تفاصيل تقرير الحصة
                    </h1>
                    <p class="mt-3 text-lg text-gray-600">عرض تفصيلي لتقرير المعلم وتقييم الطالب</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="{% url 'admin_comprehensive_lesson_reports' %}" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>

        <!-- Lesson Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-info-circle text-blue-600 ml-2"></i>
                معلومات الحصة
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-1">نوع الحصة</div>
                    <div class="font-semibold">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium
                            {% if report.lesson_type == 'trial' %}bg-blue-100 text-blue-800
                            {% elif report.lesson_type == 'subscription' %}bg-orange-100 text-orange-800
                            {% else %}bg-teal-100 text-teal-800{% endif %}">
                            {% if report.lesson_type == 'trial' %}حصة تجريبية
                            {% elif report.lesson_type == 'subscription' %}حصة اشتراك
                            {% else %}حصة مجدولة{% endif %}
                        </span>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-1">المعلم</div>
                    <div class="font-semibold text-gray-900">{{ report.teacher.get_full_name }}</div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-1">الطالب</div>
                    <div class="font-semibold text-gray-900">{{ report.student.get_full_name }}</div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-1">تاريخ الحصة</div>
                    <div class="font-semibold text-gray-900">{{ report.get_lesson_date|date:"Y/m/d H:i" }}</div>
                </div>
            </div>

            {% if lesson %}
            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-1">عنوان الحصة</div>
                    <div class="font-semibold text-gray-900">{{ lesson.title|default:"غير محدد" }}</div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-1">مدة الحصة</div>
                    <div class="font-semibold text-gray-900">{{ lesson.duration_minutes|default:"غير محدد" }} دقيقة</div>
                </div>
            </div>
            {% endif %}
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Teacher Report -->
            {% if report.teacher_report_submitted %}
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-user-tie text-green-600 ml-2"></i>
                    تقرير المعلم
                </h3>

                <div class="space-y-6">
                    <!-- Ratings -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-green-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600 mb-2">أداء الطالب</div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 ml-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= report.student_performance %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-lg font-semibold">{{ report.student_performance }}/5</span>
                            </div>
                        </div>

                        <div class="bg-green-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600 mb-2">مشاركة الطالب</div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 ml-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= report.student_participation %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-lg font-semibold">{{ report.student_participation }}/5</span>
                            </div>
                        </div>

                        <div class="bg-green-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600 mb-2">فهم الطالب</div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 ml-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= report.student_understanding %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-lg font-semibold">{{ report.student_understanding }}/5</span>
                            </div>
                        </div>

                        <div class="bg-green-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600 mb-2">تقييم الحصة العام</div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 ml-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= report.overall_lesson_rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-lg font-semibold">{{ report.overall_lesson_rating }}/5</span>
                            </div>
                        </div>
                    </div>

                    <!-- Text Fields -->
                    {% if report.lesson_summary %}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">ملخص الحصة:</h4>
                        <p class="text-gray-700">{{ report.lesson_summary }}</p>
                    </div>
                    {% endif %}

                    {% if report.strengths %}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">نقاط القوة:</h4>
                        <p class="text-gray-700">{{ report.strengths }}</p>
                    </div>
                    {% endif %}

                    {% if report.areas_for_improvement %}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">نقاط التحسين:</h4>
                        <p class="text-gray-700">{{ report.areas_for_improvement }}</p>
                    </div>
                    {% endif %}

                    {% if report.additional_notes %}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">ملاحظات إضافية:</h4>
                        <p class="text-gray-700">{{ report.additional_notes }}</p>
                    </div>
                    {% endif %}

                    <div class="text-sm text-gray-500 text-center">
                        تم إرسال التقرير في: {{ report.teacher_report_date|date:"Y/m/d H:i" }}
                    </div>
                </div>
            </div>
            {% else %}
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-user-tie text-gray-400 ml-2"></i>
                    تقرير المعلم
                </h3>
                <div class="text-center py-12">
                    <i class="fas fa-clipboard text-gray-300 text-6xl mb-4"></i>
                    <p class="text-gray-500 text-lg">لم يتم إرسال تقرير المعلم بعد</p>
                </div>
            </div>
            {% endif %}

            <!-- Student Evaluation -->
            {% if report.student_evaluation_submitted %}
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-user-graduate text-blue-600 ml-2"></i>
                    تقييم الطالب
                </h3>

                <div class="space-y-6">
                    <!-- Ratings -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600 mb-2">التقييم العام</div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 ml-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= report.overall_rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-lg font-semibold">{{ report.overall_rating }}/5</span>
                            </div>
                        </div>

                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600 mb-2">جودة التدريس</div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 ml-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= report.teaching_quality %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-lg font-semibold">{{ report.teaching_quality }}/5</span>
                            </div>
                        </div>

                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600 mb-2">محتوى الحصة</div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 ml-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= report.lesson_content %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-lg font-semibold">{{ report.lesson_content }}/5</span>
                            </div>
                        </div>

                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600 mb-2">جودة التفاعل</div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 ml-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= report.interaction_quality %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-lg font-semibold">{{ report.interaction_quality }}/5</span>
                            </div>
                        </div>

                        <div class="bg-blue-50 rounded-lg p-4 md:col-span-2">
                            <div class="text-sm text-gray-600 mb-2">الالتزام بالوقت</div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 ml-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= report.punctuality %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-lg font-semibold">{{ report.punctuality }}/5</span>
                            </div>
                        </div>
                    </div>

                    {% if report.student_comment %}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">تعليق الطالب:</h4>
                        <p class="text-gray-700">{{ report.student_comment }}</p>
                    </div>
                    {% endif %}

                    {% if report.lesson_type == 'trial' %}
                    <div class="bg-yellow-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-3">أسئلة خاصة بالحصة التجريبية:</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700">هل ينصح بالمعلم؟</span>
                                <span class="font-semibold {% if report.would_recommend %}text-green-600{% else %}text-red-600{% endif %}">
                                    {% if report.would_recommend %}نعم{% else %}لا{% endif %}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700">مهتم بالاشتراك؟</span>
                                <span class="font-semibold {% if report.interested_in_subscription %}text-green-600{% else %}text-red-600{% endif %}">
                                    {% if report.interested_in_subscription %}نعم{% else %}لا{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="text-sm text-gray-500 text-center">
                        تم إرسال التقييم في: {{ report.student_evaluation_date|date:"Y/m/d H:i" }}
                    </div>
                </div>
            </div>
            {% else %}
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-user-graduate text-gray-400 ml-2"></i>
                    تقييم الطالب
                </h3>
                <div class="text-center py-12">
                    <i class="fas fa-star text-gray-300 text-6xl mb-4"></i>
                    <p class="text-gray-500 text-lg">لم يتم إرسال تقييم الطالب بعد</p>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Analysis and Comparison -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-chart-bar text-purple-600 ml-2"></i>
                التحليل والمقارنة
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-purple-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-2">متوسط تقييم المعلم لهذا الطالب</div>
                    <div class="text-2xl font-bold text-purple-600">{{ teacher_avg_for_student|floatformat:1 }}/5</div>
                </div>

                <div class="bg-blue-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-2">متوسط تقييم الطالب لهذا المعلم</div>
                    <div class="text-2xl font-bold text-blue-600">{{ student_avg_for_teacher|floatformat:1 }}/5</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
