/* 
Enhanced Breadcrumb Styles for Email Settings
تحسينات مسارات التوجيه لقسم إعدادات البريد الإلكتروني
*/

/* تحسين مسارات التوجيه */
.breadcrumb {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 1rem 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
    border: 1px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

.breadcrumb::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(17, 153, 142, 0.1), transparent);
    transition: left 0.5s ease;
}

.breadcrumb:hover::before {
    left: 100%;
}

.breadcrumb-item {
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
}

.breadcrumb-item a {
    color: #11998e;
    text-decoration: none;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
}

.breadcrumb-item a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(17, 153, 142, 0.1);
    border-radius: 8px;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
    z-index: -1;
}

.breadcrumb-item a:hover::before {
    transform: scaleX(1);
}

.breadcrumb-item a:hover {
    color: #0d7377;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(17, 153, 142, 0.2);
}

.breadcrumb-item.active {
    color: #2D5016;
    font-weight: 600;
    background: rgba(45, 80, 22, 0.1);
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    position: relative;
}

.breadcrumb-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(45, 80, 22, 0.1), rgba(45, 80, 22, 0.05));
    border-radius: 8px;
    z-index: -1;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: #11998e;
    font-weight: bold;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    display: inline-block;
}

.breadcrumb-item:hover + .breadcrumb-item::before {
    color: #0d7377;
    transform: translateX(-2px);
}

/* تحسين الأيقونات */
.breadcrumb-item i {
    transition: all 0.3s ease;
    margin-left: 0.25rem;
}

.breadcrumb-item a:hover i {
    transform: scale(1.1) rotate(5deg);
    color: #0d7377;
}

.breadcrumb-item.active i {
    color: #2D5016;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { 
        transform: scale(1); 
        opacity: 1;
    }
    50% { 
        transform: scale(1.05); 
        opacity: 0.8;
    }
    100% { 
        transform: scale(1); 
        opacity: 1;
    }
}

/* تأثيرات متقدمة للتفاعل */
.breadcrumb-item a {
    position: relative;
    overflow: hidden;
}

.breadcrumb-item a::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(17, 153, 142, 0.3) 0%, transparent 70%);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.breadcrumb-item a:hover::after {
    width: 100px;
    height: 100px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .breadcrumb {
        padding: 0.75rem 1rem;
        margin-bottom: 1.5rem;
    }
    
    .breadcrumb-item {
        font-size: 0.9rem;
    }
    
    .breadcrumb-item a,
    .breadcrumb-item.active {
        padding: 0.4rem 0.6rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        margin: 0 0.3rem;
        font-size: 1rem;
    }
    
    .breadcrumb-item i {
        margin-left: 0.15rem;
    }
}

@media (max-width: 576px) {
    .breadcrumb {
        padding: 0.5rem 0.75rem;
        border-radius: 10px;
    }
    
    .breadcrumb-item {
        font-size: 0.85rem;
    }
    
    .breadcrumb-item a,
    .breadcrumb-item.active {
        padding: 0.3rem 0.5rem;
    }
    
    /* إخفاء النصوص الطويلة في الشاشات الصغيرة جداً */
    .breadcrumb-item:not(.active):not(:first-child) {
        display: none;
    }
    
    .breadcrumb-item:nth-last-child(2) {
        display: list-item;
    }
    
    .breadcrumb-item:nth-last-child(2)::before {
        content: "... ←";
    }
}

/* تأثيرات خاصة للتركيز */
.breadcrumb-item a:focus {
    outline: 2px solid #11998e;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(17, 153, 142, 0.2);
}

/* تحسين الألوان للوضع المظلم (إذا كان مطلوباً لاحقاً) */
@media (prefers-color-scheme: dark) {
    .breadcrumb {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        border-color: #4a5568;
    }
    
    .breadcrumb-item {
        color: #a0aec0;
    }
    
    .breadcrumb-item a {
        color: #4fd1c7;
    }
    
    .breadcrumb-item a:hover {
        color: #38b2ac;
    }
    
    .breadcrumb-item.active {
        color: #68d391;
        background: rgba(104, 211, 145, 0.1);
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        color: #4fd1c7;
    }
}
