{% extends 'base.html' %}
{% load static %}

{% block title %}فاتورة {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-4xl font-bold text-islamic-primary mb-2 flex items-center">
                    <i class="fas fa-file-invoice text-islamic-gold ml-3"></i>
                    فاتورة {{ invoice.invoice_number }}
                </h1>
                <p class="text-gray-600">تفاصيل الفاتورة وإجراءات الطباعة والتحميل</p>
            </div>
            <div class="flex space-x-4 space-x-reverse">
                <a href="{% url 'admin_invoices' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للفواتير
                </a>
                <a href="{% url 'admin_invoice_print' invoice.id %}" target="_blank" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-print ml-2"></i>
                    طباعة
                </a>
                <a href="{% url 'admin_invoice_pdf' invoice.id %}" class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-file-pdf ml-2"></i>
                    تحميل PDF
                </a>
            </div>
        </div>

        <!-- Invoice Status Alert -->
        <div class="mb-8">
            {% if invoice.status == 'paid' %}
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 text-xl ml-3"></i>
                    <div>
                        <h3 class="text-lg font-semibold text-green-800">فاتورة مدفوعة</h3>
                        <p class="text-green-700">تم دفع هذه الفاتورة في {{ invoice.paid_date|date:"Y-m-d H:i" }}</p>
                    </div>
                </div>
            </div>
            {% elif invoice.is_overdue %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-500 text-xl ml-3"></i>
                    <div>
                        <h3 class="text-lg font-semibold text-red-800">فاتورة متأخرة</h3>
                        <p class="text-red-700">تجاوزت هذه الفاتورة تاريخ الاستحقاق في {{ invoice.due_date|date:"Y-m-d" }}</p>
                    </div>
                </div>
            </div>
            {% elif invoice.status == 'sent' %}
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-clock text-yellow-500 text-xl ml-3"></i>
                    <div>
                        <h3 class="text-lg font-semibold text-yellow-800">فاتورة معلقة</h3>
                        <p class="text-yellow-700">في انتظار الدفع - تاريخ الاستحقاق: {{ invoice.due_date|date:"Y-m-d" }}</p>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Invoice Details -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- Company Header -->
            <div class="bg-islamic-primary text-white p-8">
                <div class="flex justify-between items-start">
                    <div class="flex items-start space-x-4 space-x-reverse">
                        {% if company_info.logo %}
                        <div class="flex-shrink-0">
                            <img src="{{ company_info.logo }}" alt="{{ company_info.name }}" class="w-16 h-16 rounded-lg bg-white p-2">
                        </div>
                        {% endif %}
                        <div>
                            <h2 class="text-3xl font-bold mb-2">{{ company_info.name }}</h2>
                            {% if company_info.description %}
                            <p class="text-islamic-light text-sm mb-2">{{ company_info.description }}</p>
                            {% endif %}
                            <p class="text-islamic-light">{{ company_info.address }}</p>
                            <p class="text-islamic-light">{{ company_info.phone }}</p>
                            <p class="text-islamic-light">{{ company_info.email }}</p>
                            {% if company_info.website %}
                            <p class="text-islamic-light">{{ company_info.website }}</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="text-left">
                        <h3 class="text-2xl font-bold mb-2">فاتورة</h3>
                        <p class="text-islamic-light">رقم: {{ invoice.invoice_number }}</p>
                        <p class="text-islamic-light">التاريخ: {{ invoice.issue_date|date:"Y-m-d" }}</p>
                        <p class="text-islamic-light">الاستحقاق: {{ invoice.due_date|date:"Y-m-d" }}</p>
                    </div>
                </div>
            </div>

            <!-- Customer and Invoice Info -->
            <div class="p-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <!-- Customer Info -->
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">معلومات العميل</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="font-semibold text-gray-900">{{ invoice.student_name }}</p>
                            <p class="text-gray-600">{{ invoice.student_email }}</p>
                            {% if invoice.student_phone %}
                            <p class="text-gray-600">{{ invoice.student_phone }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Invoice Info -->
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">معلومات الفاتورة</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <span class="text-sm font-medium text-gray-600">رقم الفاتورة:</span>
                                    <p class="font-semibold">{{ invoice.invoice_number }}</p>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-600">تاريخ الإصدار:</span>
                                    <p class="font-semibold">{{ invoice.issue_date|date:"Y-m-d" }}</p>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-600">تاريخ الاستحقاق:</span>
                                    <p class="font-semibold">{{ invoice.due_date|date:"Y-m-d" }}</p>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-600">الحالة:</span>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-{{ invoice.get_status_color }}-100 text-{{ invoice.get_status_color }}-800">
                                        {{ invoice.get_status_display }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">تفاصيل الفاتورة</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">سعر الوحدة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المجموع</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for item in items %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.description }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.quantity }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {% if item.unit_price > 0 %}
                                            {{ item.unit_price }} {{ invoice.get_currency_symbol }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {% if item.total_price > 0 %}
                                            {{ item.total_price }} {{ invoice.get_currency_symbol }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Invoice Totals -->
                <div class="flex justify-end">
                    <div class="w-full md:w-1/2">
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">المبلغ الفرعي:</span>
                                    <span class="font-semibold">{{ invoice.subtotal }} {{ invoice.get_currency_symbol }}</span>
                                </div>
                                {% if invoice.discount_amount > 0 %}
                                <div class="flex justify-between text-green-600">
                                    <span>الخصم:</span>
                                    <span class="font-semibold">-{{ invoice.discount_amount }} {{ invoice.get_currency_symbol }}</span>
                                </div>
                                {% endif %}
                                {% if invoice.tax_amount > 0 %}
                                <div class="flex justify-between">
                                    <span class="text-gray-600">الضريبة:</span>
                                    <span class="font-semibold">{{ invoice.tax_amount }} {{ invoice.get_currency_symbol }}</span>
                                </div>
                                {% endif %}
                                <hr class="border-gray-300">
                                <div class="flex justify-between text-lg font-bold">
                                    <span>المجموع الكلي:</span>
                                    <span class="text-islamic-primary">{{ invoice.total_amount }} {{ invoice.get_currency_symbol }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                {% if invoice.notes %}
                <div class="mt-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">ملاحظات</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700">{{ invoice.notes }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- Subscription Details -->
                <div class="mt-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">تفاصيل الاشتراك</h4>
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <span class="text-sm font-medium text-gray-600">الباقة:</span>
                                <p class="font-semibold">{{ invoice.plan_name }}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-600">تاريخ البداية:</span>
                                <p class="font-semibold">{{ invoice.subscription.start_date|date:"Y-m-d" }}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-600">تاريخ النهاية:</span>
                                <p class="font-semibold">{{ invoice.subscription.end_date|date:"Y-m-d" }}</p>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
</div>
{% endblock %}
