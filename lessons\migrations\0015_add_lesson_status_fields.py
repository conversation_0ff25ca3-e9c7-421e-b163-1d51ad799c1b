# Generated manually

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0013_comprehensive_lesson_reports'),
    ]

    operations = [
        migrations.AddField(
            model_name='livelesson',
            name='student_evaluation_submitted',
            field=models.BooleanField(default=False, verbose_name='تم إرسال تقييم الطالب'),
        ),
        migrations.AddField(
            model_name='livelesson',
            name='teacher_report_submitted',
            field=models.BooleanField(default=False, verbose_name='تم إرسال تقرير المعلم'),
        ),
        migrations.AddField(
            model_name='livelesson',
            name='time_up_notified',
            field=models.BooleanField(default=False, verbose_name='تم إشعار انتهاء الوقت'),
        ),
    ]
