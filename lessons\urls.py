from django.urls import path, include
from . import views, views_unified, api_views

app_name = 'lessons'

urlpatterns = [
    # ===== النظام الجديد الموحد =====

    # لوحات التحكم الجديدة - المسارات الصحيحة
    path('', views_unified.admin_lessons_dashboard, name='admin_lessons_unified'),
    path('', views_unified.teacher_lessons_dashboard, name='teacher_lessons_unified'),
    path('', views_unified.student_lessons_dashboard, name='student_lessons_unified'),

    # API الجديد
    path('api/lessons/', views_unified.lessons_api, name='lessons_api'),
    path('api/lessons/<int:lesson_id>/', views_unified.lesson_detail_api, name='lesson_detail_api'),

    # صفحات إنشاء الحصص
    path('admin/create-trial/', views_unified.admin_create_trial_lesson, name='admin_create_trial_lesson'),
    path('admin/create-subscription/', views_unified.admin_create_subscription_lesson, name='admin_create_subscription_lesson'),
    path('student/book-trial/', views_unified.student_trial_lesson, name='student_trial_lesson'),
    path('student/book-lesson/', views_unified.student_book_lesson, name='student_book_lesson'),

    # غرفة الحصة
    path('room/<int:lesson_id>/', views_unified.lesson_room, name='lesson_room'),

    # إدارة الحصص
    path('teacher/availability/', views_unified.teacher_availability, name='teacher_availability'),
    path('admin/bulk-reschedule/', views_unified.admin_bulk_reschedule, name='admin_bulk_reschedule'),
    path('admin/export/', views_unified.admin_export_lessons, name='admin_export_lessons'),
    path('admin/reports/', views_unified.admin_lesson_reports, name='admin_lesson_reports'),

    # تقارير وإحصائيات
    path('teacher/reports/', views_unified.teacher_reports, name='teacher_reports'),
    path('teacher/performance/', views_unified.teacher_performance, name='teacher_performance'),
    path('teacher/students/', views_unified.teacher_students, name='teacher_students'),
    path('student/progress/', views_unified.student_progress, name='student_progress'),
    path('student/certificates/', views_unified.student_certificates, name='student_certificates'),
    path('student/profile/', views_unified.student_profile, name='student_profile'),

    # ===== النظام القديم (للتوافق المؤقت) =====

    # Lesson Management
    path('old/', views.lesson_list, name='list'),
    path('create/', views.lesson_create, name='create'),
    path('<int:lesson_id>/', views.lesson_detail, name='detail'),
    path('<int:lesson_id>/edit/', views.lesson_edit, name='edit'),
    path('<int:lesson_id>/delete/', views.lesson_delete, name='delete'),
    
    # Lesson Actions
    path('<int:lesson_id>/join/', views.join_lesson, name='join'),
    path('<int:lesson_id>/start/', views.start_lesson, name='start'),
    path('<int:lesson_id>/end/', views.end_lesson, name='end'),
    path('<int:lesson_id>/cancel/', views.cancel_lesson, name='cancel'),
    
    # Lesson Content
    path('<int:lesson_id>/content/', views.lesson_content, name='content'),
    path('<int:lesson_id>/content/add/', views.add_lesson_content, name='add_content'),
    
    # Lesson Rating
    path('<int:lesson_id>/rate/', views.rate_lesson, name='rate'),
    
    # Calendar
    path('calendar/', views.lesson_calendar, name='calendar'),
    path('calendar/data/', views.calendar_data, name='calendar_data'),
    
    # Teacher specific
    path('teacher/schedule/', views.teacher_schedule, name='teacher_schedule'),
    path('teacher/students/', views.teacher_students, name='teacher_students'),
    path('teacher/earnings/', views.teacher_earnings, name='teacher_earnings'),
    path('teacher/ratings/', views.teacher_ratings, name='teacher_ratings'),
    
    # Student specific
    path('student/lessons/', views.student_lessons, name='student_lessons'),
    path('student/progress/', views.student_progress, name='student_progress'),
    path('student/archive/', views.student_archive, name='student_archive'),

    # APIs للحصص المباشرة المحدثة
    path('api/live-lessons/<int:lesson_id>/start/', api_views.start_live_lesson_api, name='start_live_lesson_api'),
    path('api/live-lessons/<int:lesson_id>/end/', api_views.end_live_lesson_api, name='end_live_lesson_api'),
    path('api/live-lessons/<int:lesson_id>/time-up/', api_views.time_up_lesson_api, name='time_up_lesson_api'),
    path('api/live-lessons/<int:lesson_id>/status/', api_views.lesson_status_api, name='lesson_status_api'),
    path('api/live-lessons/<int:lesson_id>/teacher-report/', api_views.submit_teacher_report_api, name='submit_teacher_report_api'),
    path('api/live-lessons/<int:lesson_id>/student-rating/', api_views.submit_student_rating_api, name='submit_student_rating_api'),
]
