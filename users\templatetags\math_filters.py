from django import template
from django.utils import timezone
import pytz

register = template.Library()

@register.filter
def mul(value, arg):
    """Multiplies the value by the argument."""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def div(value, arg):
    """Divides the value by the argument."""
    try:
        if float(arg) == 0:
            return 0
        return float(value) / float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def percentage(value, total):
    """Calculates percentage of value from total."""
    try:
        if float(total) == 0:
            return 0
        return round((float(value) * 100) / float(total), 1)
    except (ValueError, TypeError):
        return 0

@register.filter
def subtract(value, arg):
    """Subtracts the argument from the value."""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def add_class(field, css_class):
    """Adds CSS class to form field."""
    return field.as_widget(attrs={"class": css_class})


# ===== دوال المنطقة الزمنية =====

@register.filter
def user_timezone(datetime_obj, user):
    """
    تحويل التاريخ والوقت إلى المنطقة الزمنية للمستخدم

    الاستخدام: {{ some_datetime|user_timezone:request.user }}
    """
    if not datetime_obj:
        return ''

    # الحصول على المنطقة الزمنية للمستخدم
    user_timezone_str = 'Asia/Riyadh'  # افتراضي
    try:
        if hasattr(user, 'profile'):
            profile = getattr(user, 'profile', None)
            if profile and hasattr(profile, 'timezone') and profile.timezone:
                user_timezone_str = profile.timezone
    except:
        pass

    try:
        user_tz = pytz.timezone(user_timezone_str)

        # تحويل الوقت إذا كان UTC
        if timezone.is_aware(datetime_obj):
            local_time = datetime_obj.astimezone(user_tz)
        else:
            # إذا كان الوقت naive، اعتبره UTC أولاً
            utc_time = timezone.make_aware(datetime_obj, pytz.UTC)
            local_time = utc_time.astimezone(user_tz)

        return local_time
    except:
        return datetime_obj


@register.filter
def format_user_time(datetime_obj, user):
    """
    تنسيق الوقت حسب المنطقة الزمنية للمستخدم

    الاستخدام: {{ some_datetime|format_user_time:request.user }}
    """
    local_time = user_timezone(datetime_obj, user)
    if not local_time:
        return ''

    return local_time.strftime('%Y-%m-%d %H:%M')


@register.simple_tag
def user_current_time(user):
    """
    الحصول على الوقت الحالي حسب المنطقة الزمنية للمستخدم

    الاستخدام: {% user_current_time request.user %}
    """
    # الحصول على المنطقة الزمنية للمستخدم
    user_timezone_str = 'Asia/Riyadh'  # افتراضي
    try:
        if hasattr(user, 'profile'):
            profile = getattr(user, 'profile', None)
            if profile and hasattr(profile, 'timezone') and profile.timezone:
                user_timezone_str = profile.timezone
    except:
        pass

    try:
        user_tz = pytz.timezone(user_timezone_str)
        current_time = timezone.now().astimezone(user_tz)
        return current_time.strftime('%H:%M:%S')
    except:
        return timezone.now().strftime('%H:%M:%S')


@register.simple_tag
def user_current_date(user):
    """
    الحصول على التاريخ الحالي حسب المنطقة الزمنية للمستخدم

    الاستخدام: {% user_current_date request.user %}
    """
    # الحصول على المنطقة الزمنية للمستخدم
    user_timezone_str = 'Asia/Riyadh'  # افتراضي
    try:
        if hasattr(user, 'profile'):
            profile = getattr(user, 'profile', None)
            if profile and hasattr(profile, 'timezone') and profile.timezone:
                user_timezone_str = profile.timezone
    except:
        pass

    try:
        user_tz = pytz.timezone(user_timezone_str)
        current_time = timezone.now().astimezone(user_tz)
        return current_time.strftime('%Y-%m-%d')
    except:
        return timezone.now().strftime('%Y-%m-%d')


@register.simple_tag
def get_user_timezone_name(user):
    """
    الحصول على اسم المنطقة الزمنية للمستخدم

    الاستخدام: {% get_user_timezone_name request.user %}
    """
    try:
        if hasattr(user, 'profile'):
            profile = getattr(user, 'profile', None)
            if profile and hasattr(profile, 'timezone') and profile.timezone:
                return profile.timezone
    except:
        pass
    return 'Asia/Riyadh'


@register.filter
def time_until_minutes(value):
    """
    حساب الوقت المتبقي بالدقائق حتى التاريخ المحدد
    إرجاع قيمة سالبة إذا كان التاريخ في الماضي
    """
    if not value:
        return float('inf')

    now = timezone.now()
    time_diff = (value - now).total_seconds() / 60  # بالدقائق

    return time_diff
