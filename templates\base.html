<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ SITE_NAME }} - {{ ACADEMY_SLOGAN }}{% endblock %}</title>

    <!-- Tailwind CSS - Production Build -->
    {% load static %}
    <link href="{% static 'css/tailwind-production.css' %}" rel="stylesheet">

    <!-- تحسينات إضافية للأداء -->
    <link rel="preload" href="{% static 'css/tailwind-production.css' %}" as="style">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">

    <!-- تحسين الخطوط العربية -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Enhanced Styles -->
    {% load static %}
    <link href="{% static 'css/enhanced-style.css' %}" rel="stylesheet">
    <!-- Sidebar Fix CSS -->
    <link href="{% static 'css/sidebar-fix.css' %}" rel="stylesheet">
    <!-- New Sidebar CSS -->
    <link href="{% static 'css/new-sidebar.css' %}" rel="stylesheet">

    <!-- Admin Specific Styles -->
    {% if user.is_authenticated and user.is_admin %}
    <link href="{% static 'css/admin-sidebar.css' %}" rel="stylesheet">
    {% endif %}

    <!-- Student Specific Styles -->
    {% if user.is_authenticated and user.is_student %}
    <link href="{% static 'css/student-sidebar.css' %}" rel="stylesheet">
    {% endif %}

    <!-- Timezone Manager -->
    <script src="{% static 'js/timezone-manager.js' %}"></script>

    <!-- عنصر مخفي لتخزين المنطقة الزمنية للمستخدم -->
    {% if user.is_authenticated %}
        <input type="hidden" id="user-timezone" value="{% if user.profile.timezone %}{{ user.profile.timezone }}{% else %}Asia/Riyadh{% endif %}">
    {% endif %}

    <style>
        /* تطبيق نظام الخطوط الموحد */
        body {
            font-family: 'Cairo', 'Inter', 'Segoe UI', sans-serif !important;
            direction: rtl;
            text-align: right;
        }

        /* إعادة تعريف الخطوط للعناصر المختلفة */
        .arabic-text {
            font-family: 'Amiri', 'Scheherazade', 'Traditional Arabic', serif !important;
            direction: rtl;
            text-align: right;
            line-height: 1.8;
        }

        /* تطبيق الخطوط على العناصر المختلفة */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', sans-serif !important;
            font-weight: 700;
        }

        p, span, div {
            font-family: 'Cairo', 'Inter', sans-serif !important;
        }

        /* الخطوط الإنجليزية */
        .english-text {
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif !important;
            direction: ltr;
            text-align: left;
        }

        /* النصوص القرآنية */
        .quran-text {
            font-family: 'Amiri', serif !important;
            direction: rtl;
            text-align: center;
            line-height: 2;
            font-size: 1.25rem;
        }

        /* إصلاح الخطوط للعناصر المختلفة */
        input, textarea, select, button {
            font-family: 'Cairo', sans-serif !important;
        }

        /* الجداول */
        table, th, td {
            font-family: 'Cairo', sans-serif !important;
        }

        /* القوائم */
        ul, ol, li {
            font-family: 'Cairo', sans-serif !important;
        }

        /* الروابط */
        a {
            font-family: 'Cairo', sans-serif !important;
        }

        /* الأزرار */
        .btn, .button {
            font-family: 'Cairo', sans-serif !important;
            font-weight: 600;
        }

        /* البطاقات */
        .card, .card-body, .card-header {
            font-family: 'Cairo', sans-serif !important;
        }

        /* القائمة الجانبية */
        .sidebar, .sidebar * {
            font-family: 'Cairo', sans-serif !important;
        }

        /* النماذج */
        .form-control, .form-label, .form-text {
            font-family: 'Cairo', sans-serif !important;
        }

        /* التنبيهات */
        .alert, .notification {
            font-family: 'Cairo', sans-serif !important;
        }

        /* الشريط العلوي */
        .navbar, .nav-link {
            font-family: 'Cairo', sans-serif !important;
        }

        /* Islamic Design Colors */
        :root {
            --primary-green: #2D5016;
            --light-green: #4A7C59;
            --gold: #D4AF37;
            --light-gold: #F4E4BC;
            --dark-blue: #1B365D;
            --light-blue: #E8F4FD;
        }

        .bg-islamic-primary { background-color: var(--primary-green); }
        .bg-islamic-light { background-color: var(--light-green); }
        .bg-islamic-gold { background-color: var(--gold); }
        .bg-islamic-light-gold { background-color: var(--light-gold); }
        .bg-islamic-dark { background-color: var(--dark-blue); }
        .bg-islamic-light-blue { background-color: var(--light-blue); }

        .text-islamic-primary { color: var(--primary-green); }
        .text-islamic-gold { color: var(--gold); }
        .text-islamic-dark { color: var(--dark-blue); }

        .border-islamic-gold { border-color: var(--gold); }

        /* Islamic Patterns */
        .islamic-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-green);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--light-green);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen" {% if user.is_authenticated %}{% if user.is_admin %}data-user-type="admin"{% elif user.is_student %}data-user-type="student"{% elif user.is_teacher %}data-user-type="teacher"{% endif %}{% endif %}>
    <!-- Navigation -->
    {% if user.is_authenticated %}
        <!-- 🚀 القائمة الجانبية الجديدة الموحدة والمتجاوبة -->
        {% include 'partials/new_sidebar.html' %}
    {% endif %}

    <!-- Main Content -->
    <main class="main-content {% if user.is_authenticated %}lg:mr-64{% endif %}" id="mainContent">
        <!-- Messages -->
        {% if messages %}
            <div class="fixed top-4 left-4 right-4 z-50">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} mb-2 p-4 rounded-lg shadow-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                        <div class="flex justify-between items-center">
                            <span>{{ message }}</span>
                            <button onclick="this.parentElement.parentElement.remove()" class="text-lg font-bold">&times;</button>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Page Content -->
        <div class="p-6">
            {% block content %}{% endblock %}
        </div>
    </main>



    <!-- Scripts -->
    <script>
        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);

        // تطبيق ألوان المدير تلقائياً على جميع الصفحات
        {% if user.is_authenticated and user.is_admin %}
        function applyAdminColors() {
            // تطبيق اللون على القائمة الجانبية
            const sidebar = document.querySelector('.new-sidebar');
            if (sidebar) {
                sidebar.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
                sidebar.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
            }

            // تطبيق اللون على header البطاقة
            const cardHeaders = document.querySelectorAll('.enhanced-card-header');
            cardHeaders.forEach(cardHeader => {
                cardHeader.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
                cardHeader.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
                cardHeader.style.color = 'white';
            });

            // تطبيق اللون على البطاقات الرئيسية
            const enhancedCards = document.querySelectorAll('.enhanced-card');
            enhancedCards.forEach(enhancedCard => {
                enhancedCard.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
                enhancedCard.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
            });

            // تطبيق اللون على زر القائمة الجانبية
            const sidebarToggle = document.querySelector('.new-sidebar-toggle');
            if (sidebarToggle) {
                sidebarToggle.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
                sidebarToggle.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
            }
        }

        // تطبيق الألوان عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', applyAdminColors);

        // تطبيق الألوان عند تغيير المحتوى (للتنقل بين الصفحات)
        const adminObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    applyAdminColors();
                }
            });
        });

        // مراقبة تغييرات DOM
        adminObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
        {% endif %}

        // تطبيق ألوان الطالب تلقائياً على جميع الصفحات
        {% if user.is_authenticated and user.is_student %}
        function applyStudentColors() {
            // تطبيق اللون على القائمة الجانبية
            const sidebar = document.querySelector('.new-sidebar');
            if (sidebar) {
                sidebar.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
                sidebar.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
            }

            // تطبيق اللون على header البطاقة
            const cardHeaders = document.querySelectorAll('.enhanced-card-header');
            cardHeaders.forEach(cardHeader => {
                cardHeader.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
                cardHeader.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
                cardHeader.style.color = 'white';
            });

            // تطبيق اللون على البطاقات الرئيسية
            const enhancedCards = document.querySelectorAll('.enhanced-card');
            enhancedCards.forEach(enhancedCard => {
                enhancedCard.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
                enhancedCard.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
            });

            // تطبيق اللون على زر القائمة الجانبية
            const sidebarToggle = document.querySelector('.new-sidebar-toggle');
            if (sidebarToggle) {
                sidebarToggle.style.background = 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)';
                sidebarToggle.style.setProperty('background', 'linear-gradient(135deg, #2D5016 0%, #1A3009 100%)', 'important');
            }
        }

        // تطبيق الألوان عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', applyStudentColors);

        // تطبيق الألوان عند تغيير المحتوى (للتنقل بين الصفحات)
        const studentObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    applyStudentColors();
                }
            });
        });

        // مراقبة تغييرات DOM
        studentObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
        {% endif %}

        // متغيرات لتتبع الإشعارات المعروضة
        let shownNotifications = new Set();
        let shownMessages = new Set();

        // مسح جميع الإشعارات المنبثقة الموجودة عند تحميل الصفحة
        function clearAllExistingNotifications() {
            // مسح الإشعارات المنبثقة الموجودة
            const existingNotifications = document.querySelectorAll('.fixed.top-4.left-4');
            existingNotifications.forEach(notification => {
                if (notification.innerHTML.includes('رسالة جديدة') ||
                    notification.innerHTML.includes('رسالة من') ||
                    notification.classList.contains('bg-green-600') ||
                    notification.classList.contains('bg-blue-600')) {
                    notification.remove();
                }
            });

            // مسح البيانات المحفوظة
            shownNotifications.clear();
            shownMessages.clear();

            // مسح Local Storage إذا كان يحتوي على إشعارات
            try {
                localStorage.removeItem('shownNotifications');
                localStorage.removeItem('shownMessages');
                sessionStorage.removeItem('shownNotifications');
                sessionStorage.removeItem('shownMessages');
            } catch (e) {}
        }

        // Real-time notifications and messages update
        function updateNotificationsAndMessages() {
            fetch('/api/notifications-count/', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                },
            })
            .then(response => response.json())
            .then(data => {
                // تحديث عداد الإشعارات فقط
                const notificationCount = document.getElementById('notification-count');
                if (notificationCount) {
                    if (data.unread_notifications > 0) {
                        notificationCount.textContent = data.unread_notifications;
                        notificationCount.classList.remove('hidden');
                    } else {
                        notificationCount.classList.add('hidden');
                    }
                }

                // تحديث عداد الرسائل في القائمة الجانبية فقط
                const messagesBadges = document.querySelectorAll('.messages-count');
                messagesBadges.forEach(badge => {
                    if (data.unread_messages > 0) {
                        badge.textContent = data.unread_messages;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                });

                // لا نعرض إشعارات منبثقة هنا لمنع التكرار
                // الإشعارات المنبثقة تظهر فقط عند إنشاء رسالة جديدة فعلياً
            })
            .catch(error => console.log('تحديث الإشعارات:', error));
        }

        // إظهار إشعار رسالة جديدة - مع منع التكرار والتحقق من الصفحة الحالية
        function showNewMessageNotification(message) {
            // منع الإشعارات إذا كان المستخدم في صفحة المحادثات
            if (window.location.pathname.includes('/messages/')) {
                console.log('تم منع الإشعار - المستخدم في صفحة المحادثات');
                return;
            }

            // التحقق من عدم عرض هذه الرسالة من قبل
            const messageKey = `msg_${message.conversation_id}_${message.sender_name}`;
            if (shownMessages.has(messageKey)) {
                return; // لا نعرض الإشعار مرة أخرى
            }

            // إضافة الرسالة للقائمة المعروضة
            shownMessages.add(messageKey);

            const notification = document.createElement('div');
            notification.className = 'fixed top-4 left-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm animate-bounce';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-envelope text-xl ml-3"></i>
                    <div>
                        <p class="font-bold">رسالة جديدة</p>
                        <p class="text-sm">من: ${message.sender_name}</p>
                        <p class="text-xs mt-1">${message.preview}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
            playNotificationSound();
        }

        // إظهار إشعار عام جديد - مع منع التكرار والتحقق من الصفحة
        function showNewNotification(notification) {
            // منع الإشعارات إذا كان المستخدم في صفحة المحادثات
            if (window.location.pathname.includes('/messages/')) {
                console.log('تم منع الإشعار العام - المستخدم في صفحة المحادثات');
                return;
            }

            // التحقق من عدم عرض هذا الإشعار من قبل
            const notificationKey = `notif_${notification.id}_${notification.title}`;
            if (shownNotifications.has(notificationKey)) {
                return; // لا نعرض الإشعار مرة أخرى
            }

            // إضافة الإشعار للقائمة المعروضة
            shownNotifications.add(notificationKey);

            const notificationElement = document.createElement('div');
            notificationElement.className = 'fixed top-4 left-4 bg-green-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm animate-bounce';
            notificationElement.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-bell text-xl ml-3"></i>
                    <div>
                        <p class="font-bold">${notification.title}</p>
                        <p class="text-sm">${notification.message}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notificationElement);
            setTimeout(() => {
                if (notificationElement.parentElement) {
                    notificationElement.remove();
                }
            }, 5000);
            playNotificationSound();
        }

        // تشغيل صوت إشعار
        function playNotificationSound() {
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.play().catch(() => {});
            } catch (e) {}
        }

        // دالة لمسح الإشعارات المعروضة للمحادثة المحددة
        function clearShownMessagesForConversation(conversationId) {
            const keysToRemove = [];
            shownMessages.forEach(key => {
                if (key.includes(`msg_${conversationId}_`)) {
                    keysToRemove.push(key);
                }
            });
            keysToRemove.forEach(key => shownMessages.delete(key));
        }

        // مسح جميع الإشعارات المنبثقة عند تحميل الصفحة
        clearAllExistingNotifications();

        // تشغيل التحديث فقط خارج صفحات المحادثات
        if (!window.location.pathname.includes('/messages/')) {
            // تحديث كل 30 ثانية (تقليل التكرار)
            setInterval(updateNotificationsAndMessages, 30000);

            // تحديث عند تحميل الصفحة
            if (document.getElementById('notification-count')) {
                updateNotificationsAndMessages();
            }
        } else {
            // في صفحات المحادثات: مسح الإشعارات المعروضة فقط
            shownMessages.clear();
            console.log('تم إيقاف التحديث التلقائي - صفحة المحادثات');
        }

        // Get user's timezone from profile (global function)
        function getUserProfileTimezone() {
            // Try to get from TimezoneManager static method first
            if (typeof TimezoneManager !== 'undefined' && TimezoneManager.getUserProfileTimezone) {
                return TimezoneManager.getUserProfileTimezone();
            }

            // Try to get from hidden input element
            const timezoneElement = document.getElementById('user-timezone');
            if (timezoneElement && timezoneElement.value) {
                return timezoneElement.value;
            }

            // Try to get from localStorage
            const storedTimezone = localStorage.getItem('userTimezone');
            if (storedTimezone) {
                return storedTimezone;
            }

            // Fallback to browser timezone
            return Intl.DateTimeFormat().resolvedOptions().timeZone;
        }

        // Enhanced live clock update with user's profile timezone support
        function updateClock() {
            const now = new Date();

            // Get user's timezone from profile or fallback to browser timezone
            let userTimezone = getUserProfileTimezone();

            // Format time in user's timezone
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: userTimezone
            });

            // Format date in user's timezone
            const dateString = now.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                timeZone: userTimezone
            });

            // Update time-only elements
            const clockElement = document.getElementById('current-time');
            if (clockElement) {
                clockElement.textContent = timeString;
            }

            // Update time and date elements
            const clockDateElement = document.getElementById('current-time-date');
            if (clockDateElement) {
                clockDateElement.textContent = `${timeString} ${dateString}`;
            }
        }

        // Update all time elements with user's profile timezone
        function updateAllTimeElements() {
            // Update main clock
            updateClock();

            // Get user's timezone
            let userTimezone = getUserProfileTimezone();

            // Update all elements with data-utc-time attribute
            const timeElements = document.querySelectorAll('[data-utc-time]');
            timeElements.forEach(element => {
                const utcTime = element.getAttribute('data-utc-time');
                if (utcTime) {
                    const date = new Date(utcTime);
                    const localTime = date.toLocaleString('ar-SA', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        timeZone: userTimezone
                    });
                    element.textContent = localTime;
                }
            });

            // Update relative time elements
            const relativeTimeElements = document.querySelectorAll('[data-relative-time]');
            relativeTimeElements.forEach(element => {
                const utcTime = element.getAttribute('data-relative-time');
                if (utcTime) {
                    const date = new Date(utcTime);
                    const now = new Date();
                    const diffMs = now - date;
                    const diffMins = Math.floor(diffMs / 60000);
                    const diffHours = Math.floor(diffMs / 3600000);
                    const diffDays = Math.floor(diffMs / 86400000);

                    let relativeText = '';
                    if (diffMins < 1) {
                        relativeText = 'الآن';
                    } else if (diffMins < 60) {
                        relativeText = `منذ ${diffMins} دقيقة`;
                    } else if (diffHours < 24) {
                        relativeText = `منذ ${diffHours} ساعة`;
                    } else {
                        relativeText = `منذ ${diffDays} يوم`;
                    }
                    element.textContent = relativeText;
                }
            });
        }

        // Update clock every second
        if (document.getElementById('current-time') || document.getElementById('current-time-date')) {
            setInterval(updateAllTimeElements, 1000);
            updateAllTimeElements(); // Initial call
        }

        // Enhanced animations on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const animationObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.addEventListener('DOMContentLoaded', () => {
            const animatedElements = document.querySelectorAll('.animate-fade-in-up, .animate-slide-in-right');
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                animationObserver.observe(el);
            });

            // Enhanced hover effects for cards
            const cards = document.querySelectorAll('.enhanced-card, .stats-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-5px) scale(1.02)';
                });
                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Mobile sidebar functionality
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarClose = document.getElementById('sidebarClose');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const mobileSidebar = document.getElementById('mobileSidebar');

            function openSidebar() {
                if (mobileSidebar && sidebarOverlay) {
                    mobileSidebar.classList.add('active');
                    sidebarOverlay.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    console.log('Sidebar opened');
                }
            }

            function closeSidebar() {
                if (mobileSidebar && sidebarOverlay) {
                    mobileSidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                    console.log('Sidebar closed');
                }
            }

            // Event listeners
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', openSidebar);
            }

            if (sidebarClose) {
                sidebarClose.addEventListener('click', closeSidebar);
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }

            // Close sidebar on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeSidebar();
                }
            });

            // Close sidebar on window resize if screen becomes large
            window.addEventListener('resize', () => {
                if (window.innerWidth > 1024) {
                    closeSidebar();
                }
            });

            // Close sidebar when clicking on sidebar links (mobile)
            setTimeout(() => {
                const sidebarLinks = document.querySelectorAll('.mobile-sidebar a');
                sidebarLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        if (window.innerWidth <= 1024) {
                            setTimeout(closeSidebar, 100);
                        }
                    });
                });
            }, 100);
        });

        // Functions for starting subscription lessons (for teachers)
        window.startSubscriptionLesson = function(lessonId, studentName, lessonNumber) {
            if (confirm(`هل تريد بدء الحصة رقم ${lessonNumber} للطالب ${studentName}؟`)) {
                // Find the button that was clicked
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري البدء...';
                button.disabled = true;

                // إرسال طلب لبدء الحصة
                fetch(`/api/teacher/start-scheduled-lesson/${lessonId}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        button.innerHTML = '<i class="fas fa-check ml-1"></i>تم البدء!';
                        button.className = 'bg-green-600 text-white px-3 py-1 rounded-lg text-xs font-medium';

                        // إعادة توجيه إلى صفحة الحصة المباشرة
                        setTimeout(() => {
                            window.location.href = data.live_lesson_url;
                        }, 1000);
                    } else {
                        // Restore button state
                        button.innerHTML = originalText;
                        button.disabled = false;

                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;

                    alert('حدث خطأ في الاتصال');
                });
            }
        };

        // Function for students to join lessons
        window.joinSubscriptionLesson = function(lessonId, lessonNumber) {
            if (confirm(`هل تريد الانضمام للحصة رقم ${lessonNumber}؟`)) {
                // Find the button that was clicked
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري الانضمام...';
                button.disabled = true;

                // إرسال طلب للانضمام للحصة
                fetch(`/api/student/join-scheduled-lesson/${lessonId}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        button.innerHTML = '<i class="fas fa-check ml-1"></i>تم الانضمام!';
                        button.className = 'bg-green-600 text-white px-3 py-1 rounded-lg text-xs font-medium';

                        // إعادة توجيه إلى صفحة الحصة المباشرة
                        setTimeout(() => {
                            window.location.href = data.live_lesson_url;
                        }, 1000);
                    } else {
                        // Restore button state
                        button.innerHTML = originalText;
                        button.disabled = false;

                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;

                    alert('حدث خطأ في الاتصال');
                });
            }
        };
    </script>

    <!-- jQuery - مطلوب لبعض الصفحات -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- New Sidebar JavaScript -->
    <script src="{% static 'js/new-sidebar.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
