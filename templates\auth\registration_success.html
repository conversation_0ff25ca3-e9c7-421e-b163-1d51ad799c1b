{% extends 'base.html' %}

{% block title %}تم إنشاء الحساب بنجاح - نظام قرآنيا التعليمي{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-green-50 to-islamic-light-blue flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-lg w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-24 w-24 bg-green-100 rounded-full flex items-center justify-center mb-6 animate-pulse">
                <i class="fas fa-check-circle text-green-600 text-4xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">
                تم إنشاء حسابك بنجاح! 🎉
            </h2>
            <p class="text-gray-600">
                مرحباً بك في منصة قرآنيا التعليمية
            </p>
        </div>

        <!-- Success Card -->
        <div class="bg-white rounded-lg shadow-lg p-8 border border-gray-200">
            <div class="text-center mb-6">
                {% if user.user_type == 'teacher' %}
                    <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-chalkboard-teacher text-purple-600 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-2">
                        أهلاً وسهلاً {{ user.get_full_name|default:user.username }}
                    </h3>
                    <p class="text-lg text-gray-600 mb-4">
                        شكراً لانضمامك إلى فريق المعلمين في منصة قرآنيا التعليمية
                    </p>
                {% else %}
                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user-graduate text-green-600 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-2">
                        أهلاً وسهلاً {{ user.get_full_name|default:user.username }}
                    </h3>
                    <p class="text-lg text-gray-600 mb-4">
                        مرحباً بك في منصة قرآنيا التعليمية
                    </p>
                {% endif %}
            </div>

            <!-- Success Message -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-600 text-2xl mt-1"></i>
                    </div>
                    <div class="mr-4">
                        <h4 class="text-lg font-medium text-green-800 mb-3">
                            تم إنشاء حسابك بنجاح!
                        </h4>
                        <div class="text-green-700 space-y-2">
                            {% if user.user_type == 'teacher' %}
                                <p class="text-base leading-relaxed">
                                    حسابك الآن قيد المراجعة من قبل الإدارة. سيتم إشعارك عبر البريد الإلكتروني عند الموافقة على طلبك.
                                </p>
                            {% else %}
                                <p class="text-base leading-relaxed">
                                    حسابك الآن قيد المراجعة من قبل الإدارة. سيتم تفعيل حسابك وإشعارك عبر البريد الإلكتروني قريباً.
                                </p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Steps -->
            <div class="mb-6">
                <h4 class="font-semibold text-gray-900 mb-4">حالة طلبك:</h4>

                <div class="space-y-4">
                    <!-- Step 1: Completed -->
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-900">تم إنشاء الحساب</p>
                            <p class="text-xs text-gray-500">{{ user.created_at|date:"Y-m-d H:i" }}</p>
                        </div>
                    </div>

                    <!-- Step 2: Current -->
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center animate-pulse">
                            <i class="fas fa-clock text-white text-sm"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-900">قيد المراجعة من الإدارة</p>
                            <p class="text-xs text-gray-500">جاري المراجعة...</p>
                        </div>
                    </div>

                    <!-- Step 3: Pending -->
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-user-check text-gray-500 text-sm"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-500">تفعيل الحساب</p>
                            <p class="text-xs text-gray-400">في انتظار الموافقة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- What's Next -->
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900">ماذا بعد؟</h4>

                {% if user.user_type == 'teacher' %}
                    <div class="space-y-3 text-sm text-gray-600">
                        <div class="flex items-start">
                            <i class="fas fa-search text-blue-500 mt-1 ml-3"></i>
                            <span>ستقوم الإدارة بمراجعة بياناتك وخبراتك التعليمية</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-certificate text-blue-500 mt-1 ml-3"></i>
                            <span>سيتم التحقق من مؤهلاتك في تدريس القرآن الكريم</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-envelope text-blue-500 mt-1 ml-3"></i>
                            <span>ستصلك رسالة بريد إلكتروني عند الموافقة على طلبك</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-chalkboard-teacher text-blue-500 mt-1 ml-3"></i>
                            <span>بعد الموافقة، ستتمكن من الوصول للوحة تحكم المعلم وبدء التدريس</span>
                        </div>
                    </div>
                {% else %}
                    <div class="space-y-3 text-sm text-gray-600">
                        <div class="flex items-start">
                            <i class="fas fa-user-check text-blue-500 mt-1 ml-3"></i>
                            <span>ستقوم الإدارة بمراجعة بياناتك الشخصية</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-shield-check text-blue-500 mt-1 ml-3"></i>
                            <span>سيتم التحقق من صحة المعلومات المقدمة</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-envelope text-blue-500 mt-1 ml-3"></i>
                            <span>ستصلك رسالة بريد إلكتروني عند تفعيل حسابك</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-graduation-cap text-blue-500 mt-1 ml-3"></i>
                            <span>بعد التفعيل، ستتمكن من التسجيل في الدورات وبدء التعلم</span>
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- Contact Info -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-headset text-blue-600 text-lg mt-1"></i>
                        </div>
                        <div class="mr-3">
                            <h4 class="text-sm font-medium text-blue-800 mb-1">
                                تحتاج مساعدة؟
                            </h4>
                            <p class="text-sm text-blue-700 mb-2">
                                يمكنك التواصل مع فريق الدعم:
                            </p>
                            <div class="space-y-1">
                                <p class="text-sm text-blue-700">
                                    📧 البريد الإلكتروني:
                                    <a href="mailto:{% if ACADEMY_SUPPORT_EMAIL %}{{ ACADEMY_SUPPORT_EMAIL }}{% else %}<EMAIL>{% endif %}" class="font-medium underline">
                                        {% if ACADEMY_SUPPORT_EMAIL %}{{ ACADEMY_SUPPORT_EMAIL }}{% else %}<EMAIL>{% endif %}
                                    </a>
                                </p>
                                <p class="text-sm text-blue-700">
                                    📞 الهاتف: {% if ACADEMY_PHONE %}{{ ACADEMY_PHONE }}{% else %}+966 XX XXX XXXX{% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Info -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="text-sm text-gray-500 space-y-1">
                    <p><strong>اسم المستخدم:</strong> {{ user.username }}</p>
                    <p><strong>البريد الإلكتروني:</strong> {{ user.email }}</p>
                    <p><strong>نوع الحساب:</strong> {{ user_type_display }}</p>
                    <p><strong>تاريخ التسجيل:</strong> {{ user.created_at|date:"Y-m-d H:i" }}</p>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="text-center space-y-4">
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <a href="{% url 'login' %}" class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-islamic-primary hover:bg-islamic-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-islamic-primary transition-colors duration-200">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </a>

                <a href="{% url 'home' %}" class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-islamic-primary transition-colors duration-200">
                    <i class="fas fa-home ml-2"></i>
                    العودة للرئيسية
                </a>
            </div>

            <p class="text-xs text-gray-500">
                سيتم إشعارك عبر البريد الإلكتروني عند تحديث حالة حسابك
            </p>
        </div>
    </div>
</div>

<!-- Auto redirect to login after 10 seconds -->
<script>
let countdown = 10;
const countdownElement = document.createElement('div');
countdownElement.className = 'fixed bottom-4 right-4 bg-islamic-primary text-white px-4 py-2 rounded-lg shadow-lg';
countdownElement.innerHTML = `سيتم توجيهك لصفحة تسجيل الدخول خلال <span id="countdown">${countdown}</span> ثانية`;
document.body.appendChild(countdownElement);

const timer = setInterval(() => {
    countdown--;
    document.getElementById('countdown').textContent = countdown;

    if (countdown <= 0) {
        clearInterval(timer);
        window.location.href = "{% url 'login' %}";
    }
}, 1000);

// Allow user to cancel auto redirect
countdownElement.addEventListener('click', () => {
    clearInterval(timer);
    countdownElement.remove();
});
</script>
{% endblock %}
