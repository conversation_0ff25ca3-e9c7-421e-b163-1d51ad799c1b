{% extends 'base.html' %}
{% load static %}

{% block title %}تذكرة #{{ ticket.ticket_number }}{% endblock %}

{% block extra_css %}
<style>
    .ticket-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .response-bubble {
        transition: all 0.3s ease;
    }
    .response-bubble:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .admin-response {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .user-response {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    }
    .status-badge {
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="container mx-auto px-4 py-8">

        <!-- Ticket Header -->
        <div class="ticket-header rounded-xl shadow-lg p-6 mb-8 text-white">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <a href="{% url 'ticket_list' %}" class="text-white hover:text-blue-200 transition-colors ml-4">
                        <i class="fas fa-arrow-right text-2xl"></i>
                    </a>
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-ticket-alt text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">تذكرة #{{ ticket.ticket_number }}</h1>
                        <p class="text-white text-opacity-80">{{ ticket.title }}</p>
                    </div>
                </div>

                <div class="text-left">
                    <span class="status-badge inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-white bg-opacity-20">
                        {{ ticket.get_status_display }}
                    </span>
                </div>
            </div>

            <!-- Ticket Info -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div class="bg-white bg-opacity-10 rounded-lg p-3">
                    <p class="text-white text-opacity-70">الفئة</p>
                    <p class="font-semibold">{{ ticket.get_category_display }}</p>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-3">
                    <p class="text-white text-opacity-70">الأولوية</p>
                    <p class="font-semibold {{ ticket.get_priority_color }}">{{ ticket.get_priority_display }}</p>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-3">
                    <p class="text-white text-opacity-70">تاريخ الإنشاء</p>
                    <p class="font-semibold">{{ ticket.created_at|date:"Y/m/d H:i" }}</p>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-3">
                    <p class="text-white text-opacity-70">آخر تحديث</p>
                    <p class="font-semibold">{{ ticket.updated_at|timesince }} مضت</p>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

            <!-- Main Content -->
            <div class="lg:col-span-2">

                <!-- Original Ticket -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">{{ ticket.created_by.get_full_name }}</h3>
                            <p class="text-sm text-gray-500">{{ ticket.created_at|date:"Y/m/d H:i" }}</p>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">{{ ticket.title }}</h4>
                        <p class="text-gray-700 whitespace-pre-line">{{ ticket.description }}</p>
                    </div>
                </div>

                <!-- Responses -->
                {% for response in responses %}
                <div class="response-bubble bg-white rounded-xl shadow-lg p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 {% if response.is_admin_response %}bg-purple-100{% else %}bg-green-100{% endif %} rounded-full flex items-center justify-center ml-3">
                                <i class="fas {% if response.is_admin_response %}fa-user-shield text-purple-600{% else %}fa-user text-green-600{% endif %}"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-gray-900">{{ response.created_by.get_full_name }}</h4>
                                <p class="text-sm text-gray-500">
                                    {% if response.is_admin_response %}
                                        <span class="text-purple-600 font-medium">فريق الدعم</span> •
                                    {% endif %}
                                    <span data-datetime="{{ response.created_at|date:'c' }}" data-format="full">{{ response.created_at|date:"Y/m/d H:i" }}</span>
                                </p>
                            </div>
                        </div>

                        {% if response.is_admin_response %}
                        <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                            رد رسمي
                        </span>
                        {% endif %}
                    </div>

                    <div class="{% if response.is_admin_response %}bg-purple-50 border border-purple-200{% else %}bg-green-50 border border-green-200{% endif %} rounded-lg p-4">
                        <p class="text-gray-700 whitespace-pre-line">{{ response.message }}</p>

                        {% if response.attachment %}
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <a href="{{ response.attachment.url }}" target="_blank"
                               class="inline-flex items-center text-blue-600 hover:text-blue-800">
                                <i class="fas fa-paperclip ml-2"></i>
                                مرفق
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}

                <!-- Add Response Form -->
                {% if ticket.status != 'closed' and ticket.status != 'resolved' %}
                <div class="response-form bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-reply text-blue-600 ml-2"></i>
                        إضافة رد
                    </h3>

                    <form method="POST" class="space-y-4">
                        {% csrf_token %}
                        <div>
                            <textarea name="message"
                                      required
                                      rows="4"
                                      placeholder="اكتب ردك هنا..."
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
                        </div>

                        <div class="flex items-center justify-between">
                            <p class="text-sm text-gray-500">
                                <i class="fas fa-info-circle ml-1"></i>
                                سيتم إرسال إشعار للطرف الآخر عند إضافة الرد
                            </p>

                            <button type="submit"
                                    class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-paper-plane ml-2"></i>
                                إرسال الرد
                            </button>
                        </div>
                    </form>
                </div>
                {% elif ticket.status == 'closed' %}
                <div class="closed-message bg-gray-100 rounded-xl p-6 text-center">
                    <i class="fas fa-lock text-gray-400 text-3xl mb-3"></i>
                    <h3 class="text-lg font-semibold text-gray-600 mb-2">التذكرة مغلقة</h3>
                    <p class="text-gray-500">لا يمكن إضافة ردود جديدة على هذه التذكرة</p>
                </div>
                {% elif ticket.status == 'resolved' %}
                <div class="resolved-message bg-purple-100 rounded-xl p-6 text-center">
                    <i class="fas fa-check-circle text-purple-400 text-3xl mb-3"></i>
                    <h3 class="text-lg font-semibold text-purple-600 mb-2">التذكرة محلولة</h3>
                    <p class="text-purple-500">تم حل هذه التذكرة ولا يمكن إضافة ردود جديدة</p>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">

                <!-- Ticket Actions -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-cogs text-gray-600 ml-2"></i>
                        إجراءات التذكرة
                    </h3>

                    <div class="space-y-3">
                        {% if user.is_admin %}
                        <!-- Admin Actions -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تحديث الحالة</label>
                            {% if ticket.status == 'closed' or ticket.status == 'resolved' %}
                            <!-- التذكرة مغلقة أو محلولة - لا يمكن تغيير الحالة -->
                            <div class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-600">
                                {{ ticket.get_status_display }} (نهائية)
                            </div>
                            <p class="text-sm text-gray-500 mt-1">
                                <i class="fas fa-info-circle ml-1"></i>
                                لا يمكن تغيير حالة التذاكر المغلقة أو المحلولة
                            </p>
                            {% else %}
                            <!-- التذكرة مفتوحة - يمكن تغيير الحالة -->
                            <select id="status-select"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="open" {% if ticket.status == 'open' %}selected{% endif %}>مفتوحة</option>
                                <option value="in_progress" {% if ticket.status == 'in_progress' %}selected{% endif %}>قيد المعالجة</option>
                                <option value="waiting_response" {% if ticket.status == 'waiting_response' %}selected{% endif %}>في انتظار الرد</option>
                                <option value="resolved" {% if ticket.status == 'resolved' %}selected{% endif %}>محلولة</option>
                                <option value="closed" {% if ticket.status == 'closed' %}selected{% endif %}>مغلقة</option>
                            </select>
                            {% endif %}
                        </div>

                        {% if ticket.status != 'closed' and ticket.status != 'resolved' %}
                        <button onclick="updateTicketStatus()"
                                class="w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-save ml-2"></i>
                            حفظ التحديث
                        </button>
                        {% endif %}



                        <hr class="my-4">
                        {% endif %}

                        {% if ticket.can_be_closed_by_user or user.is_admin %}
                        {% if ticket.status != 'closed' and ticket.status != 'resolved' %}
                        <button onclick="closeTicket()"
                                class="w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-times ml-2"></i>
                            إغلاق التذكرة
                        </button>
                        {% endif %}
                        {% endif %}

                        <a href="{% url 'ticket_list' %}"
                           class="block w-full bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700 transition-colors text-center">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>

                <!-- Ticket Info -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-info-circle text-blue-600 ml-2"></i>
                        معلومات التذكرة
                    </h3>

                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">منشئ التذكرة:</span>
                            <span class="font-medium">{{ ticket.created_by.get_full_name }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">نوع المستخدم:</span>
                            <span class="font-medium">{{ ticket.created_by.get_user_type_display }}</span>
                        </div>

                        {% if ticket.assigned_to %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">مخصصة لـ:</span>
                            <span class="font-medium">{{ ticket.assigned_to.get_full_name }}</span>
                        </div>
                        {% endif %}

                        <div class="flex justify-between">
                            <span class="text-gray-600">عدد الردود:</span>
                            <span class="font-medium">{{ responses|length }}</span>
                        </div>

                        {% if ticket.resolved_at %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">تاريخ الحل:</span>
                            <span class="font-medium" data-datetime="{{ ticket.resolved_at|date:'c' }}" data-format="full">{{ ticket.resolved_at|date:"Y/m/d H:i" }}</span>
                        </div>
                        {% endif %}

                        {% if ticket.closed_at %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">تاريخ الإغلاق:</span>
                            <span class="font-medium" data-datetime="{{ ticket.closed_at|date:'c' }}" data-format="full">{{ ticket.closed_at|date:"Y/m/d H:i" }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
{% if user.is_admin %}
function updateTicketStatus() {
    const status = document.getElementById('status-select').value;
    const adminNotes = prompt('ملاحظات إضافية (اختيارية):');

    fetch(`/support/tickets/{{ ticket.id }}/update-status/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `status=${status}&admin_notes=${adminNotes || ''}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إعادة تحميل الصفحة فوراً لإظهار التحديثات
            location.reload();
        } else {
            alert(data.error || 'حدث خطأ أثناء تحديث الحالة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث الحالة');
    });
}


{% endif %}

function closeTicket() {
    if (confirm('هل أنت متأكد من إغلاق هذه التذكرة؟ لن تتمكن من إضافة ردود جديدة بعد الإغلاق.')) {
        fetch(`/support/tickets/{{ ticket.id }}/close/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إعادة تحميل الصفحة فوراً لإظهار التحديثات
                location.reload();
            } else {
                alert(data.error || 'حدث خطأ أثناء إغلاق التذكرة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إغلاق التذكرة');
        });
    }
}



// Auto-refresh every 30 seconds for new responses
setInterval(() => {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 30000);
</script>
{% endblock %}
