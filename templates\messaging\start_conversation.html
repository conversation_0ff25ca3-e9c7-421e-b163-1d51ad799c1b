{% extends 'base.html' %}
{% load static %}

{% block title %}بدء محادثة جديدة{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-islamic-light-blue to-islamic-light-green py-8">
    <div class="container mx-auto px-4 max-w-4xl">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-islamic-primary rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">بدء محادثة جديدة</h1>
                        <p class="text-gray-600">ابدأ محادثة مع
                            {% if user.user_type == 'student' %}معلميك{% elif user.user_type == 'teacher' %}طلابك{% else %}المستخدمين{% endif %}
                        </p>
                    </div>
                </div>

                <a href="{% url 'conversations_list' %}"
                   class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للرسائل
                </a>
            </div>
        </div>

        {% if available_users %}
        <!-- Start Conversation Form -->
        <div class="bg-white rounded-xl shadow-lg p-8">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Recipient Selection -->
                <div>
                    <label for="participant_id" class="block text-sm font-bold text-gray-900 mb-3">
                        <i class="fas fa-user text-islamic-primary ml-2"></i>
                        اختر المستقبل
                    </label>
                    <select name="participant_id" id="participant_id" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">-- اختر المستقبل --</option>
                        {% for user_option in available_users %}
                        <option value="{{ user_option.id }}">
                            {{ user_option.get_full_name }} - {{ user_option.get_user_type_display }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Message Content -->
                <div>
                    <label for="content" class="block text-sm font-bold text-gray-900 mb-3">
                        <i class="fas fa-comment text-islamic-primary ml-2"></i>
                        الرسالة الأولى
                    </label>
                    <textarea name="content" id="content" rows="6" required
                              placeholder="اكتب رسالتك هنا..."
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent resize-none"></textarea>
                    <div class="flex items-center justify-between mt-2">
                        <p class="text-sm text-gray-500">
                            <i class="fas fa-info-circle ml-1"></i>
                            اكتب رسالة واضحة ومهذبة
                        </p>
                        <span id="char-count" class="text-sm text-gray-500">0 حرف</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <button type="button" onclick="clearForm()"
                            class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-eraser ml-2"></i>
                        مسح النموذج
                    </button>

                    <button type="submit"
                            class="bg-islamic-primary text-white px-8 py-3 rounded-lg hover:bg-islamic-secondary transition-colors">
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال الرسالة
                    </button>
                </div>
            </form>
        </div>

        <!-- Available Users Info -->
        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">
                <i class="fas fa-users text-islamic-primary ml-2"></i>
                المستخدمون المتاحون للتراسل
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for user_option in available_users %}
                <div class="border border-gray-200 rounded-lg p-4 hover:border-islamic-primary transition-colors cursor-pointer user-card"
                     data-user-id="{{ user_option.id }}">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-full flex items-center justify-center ml-3">
                            <span class="text-white font-bold">{{ user_option.get_full_name|first }}</span>
                        </div>
                        <div>
                            <h4 class="font-bold text-gray-900">{{ user_option.get_full_name }}</h4>
                            <p class="text-sm text-gray-600">{{ user_option.get_user_type_display }}</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        {% else %}
        <!-- No Available Users -->
        <div class="bg-white rounded-xl shadow-lg p-12 text-center">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user-slash text-gray-400 text-4xl"></i>
            </div>

            <h3 class="text-xl font-bold text-gray-900 mb-4">لا يوجد مستخدمون متاحون</h3>
            <p class="text-gray-600 mb-6 max-w-md mx-auto">
                {% if user.user_type == 'student' %}
                    لا يوجد معلمون متاحون للتراسل. يجب أن تكون مسجلاً في دورة لتتمكن من مراسلة المعلم.
                {% elif user.user_type == 'teacher' %}
                    لا يوجد طلاب متاحون للتراسل. يجب أن يكون لديك طلاب مسجلون في دوراتك.
                {% else %}
                    لا يوجد مستخدمون نشطون في النظام حالياً.
                {% endif %}
            </p>

            <div class="flex items-center justify-center space-x-4 space-x-reverse">
                <a href="{% url 'conversations_list' %}"
                   class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للرسائل
                </a>

                {% if user.user_type == 'student' %}
                <a href="{% url 'courses:list' %}"
                   class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-book ml-2"></i>
                    تصفح الدورات
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Character counter
document.getElementById('content').addEventListener('input', function() {
    const charCount = this.value.length;
    document.getElementById('char-count').textContent = charCount + ' حرف';

    // Change color based on length
    const counter = document.getElementById('char-count');
    if (charCount > 500) {
        counter.className = 'text-sm text-red-500';
    } else if (charCount > 300) {
        counter.className = 'text-sm text-yellow-500';
    } else {
        counter.className = 'text-sm text-gray-500';
    }
});

// User card selection
document.querySelectorAll('.user-card').forEach(card => {
    card.addEventListener('click', function() {
        const userId = this.dataset.userId;
        const select = document.getElementById('participant_id');
        select.value = userId;

        // Visual feedback
        document.querySelectorAll('.user-card').forEach(c => c.classList.remove('border-islamic-primary', 'bg-islamic-light-blue'));
        this.classList.add('border-islamic-primary', 'bg-islamic-light-blue');

        // Focus on message textarea
        document.getElementById('content').focus();
    });
});

// Clear form function
function clearForm() {
    if (confirm('هل أنت متأكد من مسح النموذج؟')) {
        document.getElementById('participant_id').value = '';
        document.getElementById('content').value = '';
        document.getElementById('char-count').textContent = '0 حرف';
        document.getElementById('char-count').className = 'text-sm text-gray-500';

        // Remove selection from user cards
        document.querySelectorAll('.user-card').forEach(c => {
            c.classList.remove('border-islamic-primary', 'bg-islamic-light-blue');
        });
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const participantId = document.getElementById('participant_id').value;
    const content = document.getElementById('content').value.trim();

    if (!participantId) {
        e.preventDefault();
        alert('يرجى اختيار المستقبل');
        document.getElementById('participant_id').focus();
        return;
    }

    if (!content) {
        e.preventDefault();
        alert('يرجى كتابة الرسالة');
        document.getElementById('content').focus();
        return;
    }

    if (content.length < 5) {
        e.preventDefault();
        alert('الرسالة قصيرة جداً. يرجى كتابة رسالة أطول');
        document.getElementById('content').focus();
        return;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الإرسال...';
    submitBtn.disabled = true;
});

// Auto-resize textarea
document.getElementById('content').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
</script>
{% endblock %}
