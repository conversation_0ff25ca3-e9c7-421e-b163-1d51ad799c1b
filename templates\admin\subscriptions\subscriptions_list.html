{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الاشتراكات{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        {% csrf_token %}
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-4xl font-bold text-islamic-primary mb-2 flex items-center">
                    <i class="fas fa-users text-islamic-gold ml-3"></i>
                    إدارة الاشتراكات
                </h1>
                <p class="text-gray-600">إدارة ومراقبة جميع اشتراكات الطلاب</p>
            </div>
            <a href="{% url 'admin_subscriptions' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للوحة الرئيسية
            </a>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-filter ml-2"></i>
                فلترة الاشتراكات
            </h3>
            <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select name="status" id="status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if value == status_filter %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <input type="text" name="search" id="search" value="{{ search_query }}" 
                           placeholder="اسم الطالب أو الباقة..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-2 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-search ml-2"></i>
                        بحث
                    </button>
                    <a href="{% url 'admin_subscriptions_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg mr-2 transition-colors">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>

        <!-- Subscriptions Table -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-list ml-2"></i>
                    قائمة الاشتراكات
                    <span class="text-sm font-normal text-gray-500">({{ subscriptions.count }} اشتراك)</span>
                </h3>
            </div>
            
            {% if subscriptions %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباقة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ المدفوع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ البداية</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ النهاية</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for subscription in subscriptions %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-islamic-primary rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ subscription.student.get_full_name }}</div>
                                        <div class="text-sm text-gray-500">{{ subscription.student.email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ subscription.plan.name }}</div>
                                <div class="text-sm text-gray-500">{{ subscription.plan.get_plan_type_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ subscription.amount_paid }} {{ subscription.plan.get_currency_symbol }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ subscription.start_date|date:"Y-m-d" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ subscription.end_date|date:"Y-m-d" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if subscription.status == 'active' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        نشط
                                    </span>
                                {% elif subscription.status == 'expired' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle ml-1"></i>
                                        منتهي
                                    </span>
                                {% elif subscription.status == 'pending' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock ml-1"></i>
                                        في الانتظار
                                    </span>
                                {% elif subscription.status == 'cancelled' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        <i class="fas fa-ban ml-1"></i>
                                        ملغي
                                    </span>
                                {% else %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        {{ subscription.get_status_display }}
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button onclick="viewSubscription({{ subscription.id }})"
                                            class="text-blue-600 hover:text-blue-900 transition-colors"
                                            title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if subscription.status == 'active' %}
                                    <a href="{% url 'admin_manual_schedule_lessons' subscription.id %}"
                                       class="text-islamic-primary hover:text-islamic-light transition-colors"
                                       title="جدولة الحصص يدوياً">
                                        <i class="fas fa-calendar-plus"></i>
                                    </a>
                                    <button onclick="cancelSubscription({{ subscription.id }})"
                                            class="text-red-600 hover:text-red-900 transition-colors"
                                            title="إلغاء الاشتراك">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                    {% endif %}
                                    {% if subscription.status == 'pending' %}
                                    <button onclick="activateSubscription({{ subscription.id }})"
                                            class="text-green-600 hover:text-green-900 transition-colors"
                                            title="تفعيل الاشتراك">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد اشتراكات</h3>
                {% if status_filter or search_query %}
                <p class="text-gray-500 mb-6">لم يتم العثور على اشتراكات تطابق معايير البحث</p>
                <a href="{% url 'admin_subscriptions_list' %}" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors">
                    <i class="fas fa-times ml-2"></i>
                    إزالة الفلاتر
                </a>
                {% else %}
                <p class="text-gray-500 mb-6">لم يتم إنشاء أي اشتراكات بعد</p>
                <a href="{% url 'admin_plans_list' %}" class="bg-islamic-primary hover:bg-islamic-light text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    إدارة الباقات
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Statistics Summary -->
        {% if subscriptions %}
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-green-600">
                    {{ subscriptions|length }}
                </div>
                <div class="text-sm text-gray-600">إجمالي الاشتراكات</div>
            </div>
            
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-blue-600">
                    {% for subscription in subscriptions %}{% if subscription.status == 'active' %}{{ forloop.counter0|add:1 }}{% endif %}{% endfor %}
                </div>
                <div class="text-sm text-gray-600">اشتراكات نشطة</div>
            </div>
            
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-yellow-600">
                    {% for subscription in subscriptions %}{% if subscription.status == 'pending' %}{{ forloop.counter0|add:1 }}{% endif %}{% endfor %}
                </div>
                <div class="text-sm text-gray-600">في الانتظار</div>
            </div>
            
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-red-600">
                    {% for subscription in subscriptions %}{% if subscription.status == 'expired' %}{{ forloop.counter0|add:1 }}{% endif %}{% endfor %}
                </div>
                <div class="text-sm text-gray-600">منتهية</div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Subscription Details Modal -->
<div id="subscriptionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 relative">
                <h3 class="text-lg font-semibold text-gray-900">تفاصيل الاشتراك</h3>
                <button onclick="closeModal()" class="absolute top-4 left-4 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-colors">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
            <div id="modalContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function viewSubscription(subscriptionId) {
    // إظهار شاشة التحميل
    document.getElementById('modalContent').innerHTML = `
        <div class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
            <p class="text-gray-600 mt-2">جاري تحميل التفاصيل...</p>
        </div>
    `;
    document.getElementById('subscriptionModal').classList.remove('hidden');

    // جلب تفاصيل الاشتراك من الخادم
    fetch(`/dashboard/admin/subscriptions/subscriptions/${subscriptionId}/detail/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const subscription = data.subscription;

                // تحديد لون الحالة
                let statusColor = 'gray';
                if (subscription.status_class === 'active') statusColor = 'green';
                else if (subscription.status_class === 'pending') statusColor = 'yellow';
                else if (subscription.status_class === 'cancelled') statusColor = 'red';
                else if (subscription.status_class === 'expired') statusColor = 'gray';

                document.getElementById('modalContent').innerHTML = `
                    <div class="space-y-6">
                        <!-- معلومات الطالب -->
                        <div class="bg-blue-50 rounded-lg p-4">
                            <h4 class="font-semibold text-blue-800 mb-3 flex items-center">
                                <i class="fas fa-user ml-2"></i>
                                معلومات الطالب
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">الاسم:</span>
                                    <span class="text-gray-900">${subscription.student_name}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">البريد الإلكتروني:</span>
                                    <span class="text-gray-900">${subscription.student_email}</span>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الباقة -->
                        <div class="bg-purple-50 rounded-lg p-4">
                            <h4 class="font-semibold text-purple-800 mb-3 flex items-center">
                                <i class="fas fa-box ml-2"></i>
                                معلومات الباقة
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">اسم الباقة:</span>
                                    <span class="text-gray-900">${subscription.plan_name}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">الوصف:</span>
                                    <span class="text-gray-900">${subscription.plan_description}</span>
                                </div>
                            </div>
                        </div>

                        <!-- حالة الاشتراك -->
                        <div class="bg-${statusColor}-50 rounded-lg p-4">
                            <h4 class="font-semibold text-${statusColor}-800 mb-3 flex items-center">
                                <i class="fas fa-info-circle ml-2"></i>
                                حالة الاشتراك
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">الحالة:</span>
                                    <span class="px-2 py-1 rounded-full text-xs bg-${statusColor}-100 text-${statusColor}-800">
                                        ${subscription.status}
                                    </span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">تاريخ الإنشاء:</span>
                                    <span class="text-gray-900">${subscription.created_at}</span>
                                </div>
                                ${subscription.start_date ? `
                                <div>
                                    <span class="font-medium text-gray-700">تاريخ البداية:</span>
                                    <span class="text-gray-900">${subscription.start_date}</span>
                                </div>
                                ` : ''}
                                ${subscription.end_date ? `
                                <div>
                                    <span class="font-medium text-gray-700">تاريخ الانتهاء:</span>
                                    <span class="text-gray-900">${subscription.end_date}</span>
                                </div>
                                ` : ''}
                                ${subscription.remaining_days > 0 ? `
                                <div>
                                    <span class="font-medium text-gray-700">الأيام المتبقية:</span>
                                    <span class="text-gray-900">${subscription.remaining_days} يوم</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>

                        <!-- إحصائيات الحصص -->
                        <div class="bg-green-50 rounded-lg p-4">
                            <h4 class="font-semibold text-green-800 mb-3 flex items-center">
                                <i class="fas fa-chart-bar ml-2"></i>
                                إحصائيات الحصص
                            </h4>
                            <div class="grid grid-cols-3 gap-4 text-center mb-4">
                                <div>
                                    <div class="text-2xl font-bold text-blue-600">${subscription.total_lessons}</div>
                                    <div class="text-xs text-gray-600">إجمالي الحصص</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-green-600">${subscription.completed_lessons}</div>
                                    <div class="text-xs text-gray-600">حصص مكتملة</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-orange-600">${subscription.remaining_lessons}</div>
                                    <div class="text-xs text-gray-600">حصص متبقية</div>
                                </div>
                            </div>
                            <!-- شريط التقدم -->
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full transition-all duration-300"
                                     style="width: ${subscription.progress_percentage}%"></div>
                            </div>
                            <div class="text-center text-sm text-gray-600 mt-2">
                                ${subscription.progress_percentage}% مكتمل
                            </div>
                        </div>

                        ${subscription.payment ? `
                        <!-- معلومات الدفع -->
                        <div class="bg-yellow-50 rounded-lg p-4">
                            <h4 class="font-semibold text-yellow-800 mb-3 flex items-center">
                                <i class="fas fa-credit-card ml-2"></i>
                                معلومات الدفع
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">المبلغ:</span>
                                    <span class="text-gray-900">${subscription.payment.amount} ${subscription.payment.currency}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">طريقة الدفع:</span>
                                    <span class="text-gray-900">${subscription.payment.method}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">حالة الدفع:</span>
                                    <span class="text-gray-900">${subscription.payment.status}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">تاريخ الدفع:</span>
                                    <span class="text-gray-900">${subscription.payment.created_at}</span>
                                </div>
                                ${subscription.payment.processed_at ? `
                                <div>
                                    <span class="font-medium text-gray-700">تاريخ المعالجة:</span>
                                    <span class="text-gray-900">${subscription.payment.processed_at}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                        ` : ''}

                        <!-- الحصص القادمة -->
                        ${subscription.upcoming_lessons && subscription.upcoming_lessons.length > 0 ? `
                        <div class="bg-blue-50 rounded-lg p-4">
                            <h4 class="font-semibold text-blue-800 mb-3 flex items-center">
                                <i class="fas fa-clock ml-2"></i>
                                الحصص القادمة (${subscription.upcoming_lessons.length})
                            </h4>
                            <div class="space-y-2 max-h-32 overflow-y-auto">
                                ${subscription.upcoming_lessons.map(lesson => `
                                    <div class="bg-white rounded p-3 border border-blue-200">
                                        <div class="flex justify-between items-center">
                                            <span class="font-medium text-sm">حصة رقم ${lesson.lesson_number}</span>
                                            <span class="text-xs text-blue-600">${lesson.scheduled_date}</span>
                                        </div>
                                        <div class="text-xs text-gray-600 mt-1">
                                            المعلم: ${lesson.teacher_name}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}

                        <!-- آخر الحصص المكتملة -->
                        ${subscription.recent_completed_lessons && subscription.recent_completed_lessons.length > 0 ? `
                        <div class="bg-green-50 rounded-lg p-4">
                            <h4 class="font-semibold text-green-800 mb-3 flex items-center">
                                <i class="fas fa-check-circle ml-2"></i>
                                آخر الحصص المكتملة (${subscription.recent_completed_lessons.length})
                            </h4>
                            <div class="space-y-2 max-h-32 overflow-y-auto">
                                ${subscription.recent_completed_lessons.map(lesson => `
                                    <div class="bg-white rounded p-3 border border-green-200">
                                        <div class="flex justify-between items-center">
                                            <span class="font-medium text-sm">حصة رقم ${lesson.lesson_number}</span>
                                            <span class="text-xs text-green-600">${lesson.scheduled_date}</span>
                                        </div>
                                        <div class="text-xs text-gray-600 mt-1">
                                            المعلم: ${lesson.teacher_name}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}

                        <!-- أزرار الإجراءات -->
                        <div class="flex flex-wrap justify-center gap-3 pt-4 border-t border-gray-200">
                            <a href="/dashboard/admin/subscriptions/${subscriptionId}/manual-schedule/"
                               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm flex items-center">
                                <i class="fas fa-calendar-plus ml-2"></i>
                                جدولة الحصص
                            </a>

                            <button onclick="printSubscriptionDetails(${subscriptionId})"
                                    class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors text-sm flex items-center">
                                <i class="fas fa-print ml-2"></i>
                                طباعة التفاصيل
                            </button>

                            ${subscription.status_class === 'active' ? `
                            <button onclick="cancelSubscriptionConfirm(${subscriptionId})"
                                    class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors text-sm flex items-center">
                                <i class="fas fa-times ml-2"></i>
                                إلغاء الاشتراك
                            </button>
                            ` : ''}

                            <button onclick="closeModal()"
                                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors text-sm flex items-center">
                                <i class="fas fa-times ml-2"></i>
                                إغلاق
                            </button>
                        </div>
                    </div>
                `;
            } else {
                document.getElementById('modalContent').innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-400"></i>
                        <p class="text-red-600 mt-2">${data.error}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('modalContent').innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-2xl text-red-400"></i>
                    <p class="text-red-600 mt-2">حدث خطأ في تحميل التفاصيل</p>
                </div>
            `;
        });
}

function cancelSubscription(subscriptionId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الاشتراك؟\n\nسيتم إلغاء جميع الحصص المجدولة المستقبلية.')) {
        // إرسال طلب إلغاء الاشتراك
        fetch(`/dashboard/admin/subscriptions/subscriptions/${subscriptionId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم إلغاء الاشتراك بنجاح!\n\n${data.message}`);
                // إعادة تحميل الصفحة لإظهار التحديثات
                location.reload();
            } else {
                alert(`خطأ: ${data.error}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إلغاء الاشتراك');
        });
    }
}

function cancelSubscriptionConfirm(subscriptionId) {
    // إغلاق النافذة المنبثقة أولاً
    closeModal();

    // انتظار قصير ثم إظهار تأكيد الإلغاء
    setTimeout(() => {
        cancelSubscription(subscriptionId);
    }, 300);
}

function activateSubscription(subscriptionId) {
    if (confirm('هل أنت متأكد من تفعيل هذا الاشتراك؟')) {
        // This would send a request to activate the subscription
        alert('سيتم تطوير هذه الميزة قريباً');
    }
}

function printSubscriptionDetails(subscriptionId) {
    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank');
    const modalContent = document.getElementById('modalContent').innerHTML;

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تفاصيل الاشتراك #${subscriptionId}</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                    text-align: right;
                }
                .space-y-6 > * + * { margin-top: 1.5rem; }
                .rounded-lg { border-radius: 0.5rem; }
                .p-4 { padding: 1rem; }
                .mb-3 { margin-bottom: 0.75rem; }
                .grid { display: grid; }
                .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
                .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
                .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
                .gap-4 { gap: 1rem; }
                .text-center { text-align: center; }
                .font-semibold { font-weight: 600; }
                .font-bold { font-weight: 700; }
                .text-sm { font-size: 0.875rem; }
                .text-xs { font-size: 0.75rem; }
                .text-2xl { font-size: 1.5rem; }
                .bg-blue-50 { background-color: #eff6ff; }
                .bg-purple-50 { background-color: #faf5ff; }
                .bg-green-50 { background-color: #f0fdf4; }
                .bg-yellow-50 { background-color: #fefce8; }
                .bg-gray-50 { background-color: #f9fafb; }
                .text-blue-800 { color: #1e40af; }
                .text-purple-800 { color: #6b21a8; }
                .text-green-800 { color: #166534; }
                .text-yellow-800 { color: #92400e; }
                .text-gray-700 { color: #374151; }
                .text-gray-900 { color: #111827; }
                .text-blue-600 { color: #2563eb; }
                .text-green-600 { color: #16a34a; }
                .text-orange-600 { color: #ea580c; }
                .border-t { border-top: 1px solid #e5e7eb; }
                .pt-4 { padding-top: 1rem; }
                .hidden { display: none; }
                @media print {
                    .hidden { display: none !important; }
                    body { margin: 0; }
                }
            </style>
        </head>
        <body>
            <h1 style="text-align: center; color: #1e40af; margin-bottom: 2rem;">
                تفاصيل الاشتراك #${subscriptionId}
            </h1>
            ${modalContent.replace(/onclick="[^"]*"/g, '').replace(/class="[^"]*hidden[^"]*"/g, 'class="hidden"')}
            <div style="text-align: center; margin-top: 2rem; font-size: 0.875rem; color: #6b7280;">
                تم طباعة هذا التقرير في: ${new Date().toLocaleString('ar-SA')}
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    // انتظار تحميل المحتوى ثم الطباعة
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);
}

function closeModal() {
    document.getElementById('subscriptionModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('subscriptionModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Close modal when pressing Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modal = document.getElementById('subscriptionModal');
        if (!modal.classList.contains('hidden')) {
            closeModal();
        }
    }
});
</script>
{% endblock %}
