/**
 * إعدادات خوادم Jitsi المحسنة
 * خوادم مجانية عالية الجودة بدون قيود وقت
 */

class JitsiServerManager {
    constructor() {
        this.servers = [
            {
                name: 'FFMUC (ألمانيا)',
                domain: 'meet.ffmuc.net',
                quality: 'عالية جداً',
                timeLimit: 'بدون قيود',
                features: ['HD 1080p', 'تسجيل', 'مشاركة شاشة', 'دردشة'],
                primary: true
            },
            {
                name: '8x8 Official',
                domain: '8x8.vc',
                quality: 'عالية',
                timeLimit: 'بدون قيود',
                features: ['HD 720p', 'تسجيل', 'مشاركة شاشة'],
                primary: false
            },
            {
                name: 'Element.io',
                domain: 'meet.element.io',
                quality: 'عالية',
                timeLimit: 'بدون قيود',
                features: ['HD 720p', 'تشفير قوي', 'مشاركة شاشة'],
                primary: false
            },
            {
                name: 'Jitsi Official (احتياطي)',
                domain: 'meet.jit.si',
                quality: 'متوسطة',
                timeLimit: '40 دقيقة',
                features: ['HD 720p', 'مشاركة شاشة'],
                primary: false
            }
        ];
        
        this.currentServerIndex = 0;
        this.maxRetries = 3;
    }
    
    /**
     * الحصول على الخادم الأساسي
     */
    getPrimaryServer() {
        return this.servers.find(server => server.primary) || this.servers[0];
    }
    
    /**
     * الحصول على الخادم الحالي
     */
    getCurrentServer() {
        return this.servers[this.currentServerIndex];
    }
    
    /**
     * الانتقال للخادم التالي
     */
    switchToNextServer() {
        this.currentServerIndex = (this.currentServerIndex + 1) % this.servers.length;
        return this.getCurrentServer();
    }
    
    /**
     * إعادة تعيين للخادم الأساسي
     */
    resetToPrimary() {
        this.currentServerIndex = 0;
    }
    
    /**
     * تحميل API من الخادم الحالي
     */
    async loadJitsiAPI() {
        const server = this.getCurrentServer();
        
        return new Promise((resolve, reject) => {
            // التحقق من وجود API محمل مسبقاً
            if (typeof JitsiMeetExternalAPI !== 'undefined') {
                console.log(`✅ Jitsi API محمل مسبقاً من: ${server.domain}`);
                resolve(server);
                return;
            }
            
            console.log(`🔄 تحميل Jitsi API من: ${server.domain}`);
            
            const script = document.createElement('script');
            script.src = `https://${server.domain}/external_api.js`;
            
            script.onload = () => {
                console.log(`✅ تم تحميل Jitsi API بنجاح من: ${server.domain}`);
                console.log(`📊 جودة الخادم: ${server.quality}`);
                console.log(`⏰ قيود الوقت: ${server.timeLimit}`);
                console.log(`🎯 الميزات: ${server.features.join(', ')}`);
                resolve(server);
            };
            
            script.onerror = () => {
                console.warn(`⚠️ فشل تحميل من: ${server.domain}`);
                reject(new Error(`فشل تحميل من ${server.domain}`));
            };
            
            // إزالة أي script سابق
            const existingScript = document.querySelector(`script[src*="${server.domain}"]`);
            if (existingScript) {
                existingScript.remove();
            }
            
            document.head.appendChild(script);
        });
    }
    
    /**
     * تحميل API مع إعادة المحاولة التلقائية
     */
    async loadWithFallback() {
        let attempts = 0;
        
        while (attempts < this.maxRetries) {
            try {
                const server = await this.loadJitsiAPI();
                return server;
            } catch (error) {
                attempts++;
                console.warn(`❌ المحاولة ${attempts} فشلت: ${error.message}`);
                
                if (attempts < this.maxRetries) {
                    const nextServer = this.switchToNextServer();
                    console.log(`🔄 التبديل للخادم التالي: ${nextServer.domain}`);
                } else {
                    console.error('❌ فشل تحميل Jitsi API من جميع الخوادم');
                    throw new Error('فشل تحميل Jitsi API من جميع الخوادم المتاحة');
                }
            }
        }
    }
    
    /**
     * الحصول على معلومات جميع الخوادم
     */
    getServersList() {
        return this.servers.map(server => ({
            name: server.name,
            domain: server.domain,
            quality: server.quality,
            timeLimit: server.timeLimit,
            features: server.features,
            status: 'غير محدد'
        }));
    }
    
    /**
     * اختبار حالة الخوادم
     */
    async testServersStatus() {
        const results = [];
        
        for (const server of this.servers) {
            try {
                const response = await fetch(`https://${server.domain}/`, {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                
                results.push({
                    ...server,
                    status: 'متاح',
                    responseTime: Date.now()
                });
            } catch (error) {
                results.push({
                    ...server,
                    status: 'غير متاح',
                    error: error.message
                });
            }
        }
        
        return results;
    }
}

// إنشاء مثيل عام
window.jitsiServerManager = new JitsiServerManager();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = JitsiServerManager;
}
