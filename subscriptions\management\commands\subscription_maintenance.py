"""
أمر إدارة لتشغيل مهام صيانة الاشتراكات
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from subscriptions.tasks import (
    run_subscription_maintenance_task,
    convert_scheduled_lessons_task,
    send_lesson_reminders_task
)


class Command(BaseCommand):
    help = 'تشغيل مهام صيانة الاشتراكات والحصص'

    def add_arguments(self, parser):
        parser.add_argument(
            '--task',
            type=str,
            choices=['all', 'subscriptions', 'lessons', 'reminders'],
            default='all',
            help='نوع المهمة المراد تشغيلها'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='عرض تفاصيل أكثر'
        )

    def handle(self, *args, **options):
        task_type = options['task']
        verbose = options['verbose']
        
        self.stdout.write(
            self.style.SUCCESS(
                f'[{timezone.now()}] بدء تشغيل مهام صيانة الاشتراكات - النوع: {task_type}'
            )
        )
        
        results = {}
        
        try:
            if task_type in ['all', 'subscriptions']:
                if verbose:
                    self.stdout.write('تشغيل مهام صيانة الاشتراكات...')
                
                subscription_results = run_subscription_maintenance_task()
                results['subscriptions'] = subscription_results
                
                if verbose:
                    self.stdout.write(
                        f"  - اشتراكات على وشك الانتهاء: {subscription_results.get('expiring_count', 0)}"
                    )
                    self.stdout.write(
                        f"  - اشتراكات انتهت حصصها: {subscription_results.get('exhausted_count', 0)}"
                    )
                    self.stdout.write(
                        f"  - اشتراكات منتهية: {subscription_results.get('expired_count', 0)}"
                    )
            
            if task_type in ['all', 'lessons']:
                if verbose:
                    self.stdout.write('تشغيل مهام تحويل الحصص...')
                
                converted_count = convert_scheduled_lessons_task()
                results['converted_lessons'] = converted_count
                
                if verbose:
                    self.stdout.write(f"  - حصص تم تحويلها: {converted_count}")
            
            if task_type in ['all', 'reminders']:
                if verbose:
                    self.stdout.write('تشغيل مهام تذكيرات الحصص...')
                
                reminder_count = send_lesson_reminders_task()
                results['reminders_sent'] = reminder_count
                
                if verbose:
                    self.stdout.write(f"  - تذكيرات تم إرسالها: {reminder_count}")
            
            # عرض النتائج النهائية
            self.stdout.write(
                self.style.SUCCESS(
                    f'[{timezone.now()}] انتهاء المهام بنجاح'
                )
            )
            
            if verbose:
                self.stdout.write('النتائج النهائية:')
                for key, value in results.items():
                    self.stdout.write(f"  {key}: {value}")
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f'[{timezone.now()}] خطأ في تشغيل المهام: {str(e)}'
                )
            )
            raise e
