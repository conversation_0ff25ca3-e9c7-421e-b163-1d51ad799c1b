"""
خدمات الإشعارات
"""

def get_dashboard_url(user_type, page):
    """توليد مسار صحيح للوحة التحكم"""
    base_urls = {
        'admin': '/dashboard/admin/',
        'teacher': '/dashboard/teacher/',
        'student': '/dashboard/student/'
    }

    page_mappings = {
        # صفحات المدير
        'users': 'users/',
        'payment-management': 'payment-management/',
        'ratings': 'ratings/',
        'teacher-payouts': 'teacher-payouts/',

        # صفحات المعلم
        'students': 'students/',
        'earnings': 'earnings/',
        'schedule': 'schedule/',
        'teacher_ratings': 'ratings/',
        'payouts': 'payouts/',

        # صفحات الطالب
        'lessons': 'lessons/',
        'courses': 'courses/',
        'payments': 'payments/',
        'progress': 'progress/',
        'subscriptions': 'subscriptions/',
        'archive': 'archive/',

        # صفحات مشتركة
        'dashboard': '',
    }

    base_url = base_urls.get(user_type, '/dashboard/')
    page_path = page_mappings.get(page, page + '/')

    return base_url + page_path


def create_notification(recipient, notification_type, title, message, sender=None, priority='medium', action_url=None, action_text=None):
    """إنشاء إشعار جديد"""
    from .models import Notification

    notification = Notification.objects.create(
        recipient=recipient,
        sender=sender,
        notification_type=notification_type,
        title=title,
        message=message,
        priority=priority,
        action_url=action_url,
        action_text=action_text
    )

    return notification


class NotificationService:
    """خدمة الإشعارات الشاملة"""

    @staticmethod
    def notify_lesson_created(lesson):
        """إشعار بإنشاء حصة جديدة"""
        # إشعار الطالب
        create_notification(
            recipient=lesson.student,
            notification_type='lesson_created',
            title='حصة جديدة مجدولة',
            message=f'تم جدولة حصة جديدة "{lesson.title}" يوم {lesson.scheduled_date.strftime("%Y-%m-%d")} الساعة {lesson.scheduled_date.strftime("%H:%M")}',
            sender=lesson.teacher,
            priority='medium',
            action_url=get_dashboard_url('student', 'lessons'),
            action_text='عرض الحصص'
        )

        # إشعار المعلم
        create_notification(
            recipient=lesson.teacher,
            notification_type='lesson_created',
            title='حصة جديدة مجدولة',
            message=f'تم جدولة حصة جديدة "{lesson.title}" مع الطالب {lesson.student.get_full_name()} يوم {lesson.scheduled_date.strftime("%Y-%m-%d")} الساعة {lesson.scheduled_date.strftime("%H:%M")}',
            priority='medium',
            action_url=get_dashboard_url('teacher', 'schedule'),
            action_text='عرض الجدول'
        )

    @staticmethod
    def notify_live_lesson_created(live_lesson):
        """إشعار بإنشاء حصة مباشرة جديدة"""
        # إشعار المعلم
        create_notification(
            recipient=live_lesson.teacher,
            notification_type='live_lesson_created',
            title='حصة مباشرة جديدة',
            message=f'تم إنشاء حصة مباشرة جديدة "{live_lesson.title}" مع الطالب {live_lesson.student.get_full_name()} في {live_lesson.scheduled_date.strftime("%Y-%m-%d %H:%M")}',
            sender=live_lesson.created_by,
            priority='high',
            action_url=live_lesson.get_teacher_jitsi_url(),
            action_text='الانضمام كمشرف'
        )

        # إشعار الطالب
        create_notification(
            recipient=live_lesson.student,
            notification_type='live_lesson_created',
            title='حصة مباشرة جديدة',
            message=f'تم دعوتك لحصة مباشرة "{live_lesson.title}" مع المعلم {live_lesson.teacher.get_full_name()} في {live_lesson.scheduled_date.strftime("%Y-%m-%d %H:%M")}',
            sender=live_lesson.created_by,
            priority='high',
            action_url=live_lesson.get_student_jitsi_url(),
            action_text='الانضمام للحصة'
        )

    @staticmethod
    def notify_payment_status_changed(payment, changed_by):
        """إشعار بتغيير حالة الدفع"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار الطالب
        create_notification(
            recipient=payment.enrollment.student,
            notification_type='payment_status_changed',
            title='تحديث حالة الدفع',
            message=f'تم تحديث حالة دفعتك إلى "{payment.get_status_display()}" للدورة {payment.enrollment.course.title}',
            sender=changed_by,
            priority='high' if payment.status == 'completed' else 'medium',
            action_url=get_dashboard_url('student', 'payments'),
            action_text='عرض المدفوعات'
        )

        # إشعار المعلم
        create_notification(
            recipient=payment.enrollment.teacher,
            notification_type='payment_status_changed',
            title='تحديث حالة دفع طالب',
            message=f'تم تحديث حالة دفع الطالب {payment.enrollment.student.get_full_name()} إلى "{payment.get_status_display()}"',
            sender=changed_by,
            priority='medium',
            action_url=get_dashboard_url('teacher', 'students'),
            action_text='عرض الطلاب'
        )

        # إشعار المدير إذا كانت الحالة فاشلة
        if payment.status == 'failed':
            admins = User.objects.filter(user_type='admin', is_active=True)
            for admin in admins:
                create_notification(
                    recipient=admin,
                    notification_type='admin_payment_pending',
                    title='فشل في دفعة',
                    message=f'فشلت دفعة الطالب {payment.enrollment.student.get_full_name()} بمبلغ {payment.amount} ر.س',
                    priority='high',
                    action_url=get_dashboard_url('admin', 'payment-management'),
                    action_text='إدارة المدفوعات'
                )

    @staticmethod
    def notify_new_enrollment(enrollment):
        """إشعار بتسجيل جديد"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار الطالب
        create_notification(
            recipient=enrollment.student,
            notification_type='new_enrollment',
            title='تسجيل جديد',
            message=f'تم تسجيلك في دورة {enrollment.course.title} مع المعلم {enrollment.teacher.get_full_name()}',
            priority='high',
            action_url=get_dashboard_url('student', 'courses'),
            action_text='عرض دوراتي'
        )

        # إشعار المعلم
        create_notification(
            recipient=enrollment.teacher,
            notification_type='new_student_assigned',
            title='طالب جديد',
            message=f'تم تعيين الطالب {enrollment.student.get_full_name()} لك في دورة {enrollment.course.title}',
            priority='high',
            action_url=get_dashboard_url('teacher', 'students'),
            action_text='عرض الطلاب'
        )

        # إشعار المدير
        admins = User.objects.filter(user_type='admin', is_active=True)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='admin_new_user',
                title='تسجيل جديد',
                message=f'تم تسجيل الطالب {enrollment.student.get_full_name()} في دورة {enrollment.course.title}',
                priority='medium',
                action_url=get_dashboard_url('admin', 'users'),
                action_text='إدارة المستخدمين'
            )

    @staticmethod
    def notify_lesson_rating(rating):
        """إشعار بتقييم حصة"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار المعلم
        create_notification(
            recipient=rating.lesson.enrollment.teacher,
            notification_type='rating_received',
            title='تقييم جديد',
            message=f'تلقيت تقييم {rating.overall_satisfaction}/5 من الطالب {rating.lesson.enrollment.student.get_full_name()}',
            sender=rating.lesson.enrollment.student,
            priority='medium',
            action_url=get_dashboard_url('teacher', 'teacher_ratings'),
            action_text='عرض التقييمات'
        )

        # إشعار المدير إذا كان التقييم منخفض
        if rating.overall_satisfaction <= 2:
            admins = User.objects.filter(user_type='admin', is_active=True)
            for admin in admins:
                create_notification(
                    recipient=admin,
                    notification_type='admin_low_rating',
                    title='تقييم منخفض',
                    message=f'تلقى المعلم {rating.lesson.enrollment.teacher.get_full_name()} تقييم منخفض ({rating.overall_satisfaction}/5)',
                    priority='high',
                    action_url=get_dashboard_url('admin', 'ratings'),
                    action_text='عرض التقييمات'
                )

    @staticmethod
    def notify_live_lesson_rating(rating):
        """إشعار بتقييم حصة مباشرة"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار المعلم
        create_notification(
            recipient=rating.teacher,
            notification_type='live_lesson_rating_received',
            title='تقييم جديد للحصة المباشرة',
            message=f'تلقيت تقييم {rating.overall_rating}/5 من الطالب {rating.student.get_full_name()} للحصة "{rating.live_lesson.title}"',
            sender=rating.student,
            priority='medium',
            action_url=get_dashboard_url('teacher', 'teacher_ratings'),
            action_text='عرض التقييمات'
        )

        # إشعار المدير إذا كان التقييم منخفض
        if rating.overall_rating <= 2:
            admins = User.objects.filter(user_type='admin', is_active=True)
            for admin in admins:
                create_notification(
                    recipient=admin,
                    notification_type='admin_low_live_rating',
                    title='تقييم منخفض للحصة المباشرة',
                    message=f'تلقى المعلم {rating.teacher.get_full_name()} تقييم منخفض ({rating.overall_rating}/5) للحصة المباشرة "{rating.live_lesson.title}"',
                    priority='high',
                    action_url=get_dashboard_url('admin', 'ratings'),
                    action_text='عرض التقييمات'
                )

    @staticmethod
    def notify_unified_lesson_rating(rating):
        """إشعار بتقييم موحد للحصة (مجدولة أو مباشرة)"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # تحديد نوع الحصة والعنوان
        if rating.scheduled_lesson:
            lesson_title = f"حصة رقم {rating.scheduled_lesson.lesson_number}"
            lesson_type_text = "المجدولة"
        elif rating.live_lesson:
            lesson_title = rating.live_lesson.title
            lesson_type_text = "المباشرة"
        else:
            lesson_title = "حصة غير محددة"
            lesson_type_text = ""

        # إشعار المعلم
        create_notification(
            recipient=rating.teacher,
            notification_type='rating_received',
            title='تقييم جديد',
            message=f'تلقيت تقييم {rating.overall_rating}/5 من الطالب {rating.student.get_full_name()} للحصة {lesson_type_text} "{lesson_title}"',
            sender=rating.student,
            priority='medium',
            action_url=get_dashboard_url('teacher', 'teacher_ratings'),
            action_text='عرض التقييمات'
        )

        # إشعار المدير إذا كان التقييم منخفض
        if rating.overall_rating <= 2:
            admins = User.objects.filter(user_type='admin', is_active=True)
            for admin in admins:
                create_notification(
                    recipient=admin,
                    notification_type='admin_low_rating',
                    title='تقييم منخفض',
                    message=f'تلقى المعلم {rating.teacher.get_full_name()} تقييم منخفض ({rating.overall_rating}/5) للحصة {lesson_type_text} "{lesson_title}"',
                    priority='high',
                    action_url=get_dashboard_url('admin', 'ratings'),
                    action_text='عرض التقييمات'
                )

    @staticmethod
    def notify_live_lesson_started(live_lesson):
        """إشعار ببدء حصة مباشرة"""
        # إشعار الطالب
        create_notification(
            recipient=live_lesson.student,
            notification_type='live_lesson_started',
            title='بدأت الحصة المباشرة',
            message=f'بدأت الحصة المباشرة "{live_lesson.title}" مع المعلم {live_lesson.teacher.get_full_name()}',
            sender=live_lesson.teacher,
            priority='urgent',
            action_url=live_lesson.get_student_jitsi_url(),
            action_text='الانضمام للحصة'
        )

        # إشعار المعلم
        create_notification(
            recipient=live_lesson.teacher,
            notification_type='live_lesson_started',
            title='بدأت الحصة المباشرة',
            message=f'بدأت الحصة المباشرة "{live_lesson.title}" مع الطالب {live_lesson.student.get_full_name()}',
            priority='urgent',
            action_url=live_lesson.get_teacher_jitsi_url(),
            action_text='الانضمام كمشرف'
        )

    @staticmethod
    def notify_support_ticket(ticket):
        """إشعار بتذكرة دعم جديدة"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار جميع المديرين
        admins = User.objects.filter(user_type='admin', is_active=True)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='support_ticket',
                title='تذكرة دعم جديدة',
                message=f'تذكرة دعم جديدة #{ticket.ticket_number} من {ticket.user.get_full_name()}: {ticket.subject}',
                sender=ticket.user,
                priority='high' if ticket.priority in ['high', 'urgent'] else 'medium',
                action_url=get_dashboard_url('admin', 'support'),
                action_text='عرض التذاكر'
            )

    @staticmethod
    def notify_teacher_rates_changed(teacher, changed_by):
        """إشعار بتغيير أسعار المعلم"""
        create_notification(
            recipient=teacher,
            notification_type='teacher_rate_changed',
            title='تحديث أسعارك',
            message='تم تحديث أسعار حصصك ونسب العمولة الخاصة بك',
            sender=changed_by,
            priority='medium',
            action_url=get_dashboard_url('teacher', 'earnings'),
            action_text='عرض الأرباح'
        )

    @staticmethod
    def notify_lesson_reminder(lesson, hours_before=2):
        """تذكير بالحصة"""
        # إشعار الطالب
        create_notification(
            recipient=lesson.enrollment.student,
            notification_type='lesson_reminder',
            title='تذكير بالحصة',
            message=f'لديك حصة في دورة {lesson.enrollment.course.title} خلال {hours_before} ساعة',
            priority='high',
            action_url=get_dashboard_url('student', 'lessons'),
            action_text='الانضمام للحصة'
        )

        # إشعار المعلم
        create_notification(
            recipient=lesson.enrollment.teacher,
            notification_type='lesson_reminder',
            title='تذكير بالحصة',
            message=f'لديك حصة مع الطالب {lesson.enrollment.student.get_full_name()} خلال {hours_before} ساعة',
            priority='high',
            action_url=get_dashboard_url('teacher', 'schedule'),
            action_text='بدء الحصة'
        )

    @staticmethod
    def notify_new_support_ticket(ticket):
        """إشعار بتذكرة دعم جديدة"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار جميع المديرين
        admins = User.objects.filter(user_type='admin', is_active=True)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='new_support_ticket',
                title='تذكرة دعم جديدة',
                message=f'تذكرة دعم جديدة #{ticket.ticket_number} من {ticket.created_by.get_full_name()}: {ticket.title}',
                sender=ticket.created_by,
                priority='high' if ticket.priority in ['high', 'urgent'] else 'medium',
                action_url=f'/support/tickets/{ticket.id}/',
                action_text='عرض التذكرة'
            )

    @staticmethod
    def notify_support_ticket_updated(ticket):
        """إشعار بتحديث تذكرة دعم"""
        # إشعار منشئ التذكرة إذا لم يكن مدير
        if not ticket.created_by.is_admin():
            create_notification(
                recipient=ticket.created_by,
                notification_type='support_ticket_updated',
                title='تحديث تذكرة الدعم',
                message=f'تم تحديث تذكرة الدعم #{ticket.ticket_number} - الحالة: {ticket.get_status_display()}',
                priority='medium',
                action_url=f'/support/tickets/{ticket.id}/',
                action_text='عرض التذكرة'
            )

    @staticmethod
    def notify_support_response_added(response):
        """إشعار بإضافة رد على تذكرة دعم"""
        ticket = response.ticket

        if response.is_admin_response:
            # رد من المدير - إشعار المستخدم
            create_notification(
                recipient=ticket.created_by,
                notification_type='support_response_added',
                title='رد جديد على تذكرة الدعم',
                message=f'تلقيت رد جديد على تذكرة الدعم #{ticket.ticket_number}',
                sender=response.created_by,
                priority='medium',
                action_url=f'/support/tickets/{ticket.id}/',
                action_text='عرض الرد'
            )
        else:
            # رد من المستخدم - إشعار المديرين
            from django.contrib.auth import get_user_model
            User = get_user_model()

            admins = User.objects.filter(user_type='admin', is_active=True)
            for admin in admins:
                create_notification(
                    recipient=admin,
                    notification_type='support_response_added',
                    title='رد جديد على تذكرة دعم',
                    message=f'رد جديد من {response.created_by.get_full_name()} على التذكرة #{ticket.ticket_number}',
                    sender=response.created_by,
                    priority='medium',
                    action_url=f'/support/tickets/{ticket.id}/',
                    action_text='عرض الرد'
                )

    @staticmethod
    def notify_ticket_status_changed(ticket, old_status):
        """إشعار بتغيير حالة التذكرة"""
        # تحديد أولوية الإشعار حسب نوع التغيير
        priority = 'medium'
        if ticket.status == 'closed':
            priority = 'low'
        elif ticket.status == 'resolved':
            priority = 'high'
        elif old_status == 'closed' and ticket.status != 'closed':
            priority = 'high'  # إعادة فتح التذكرة

        # رسالة مخصصة حسب نوع التغيير
        if old_status == 'closed' and ticket.status != 'closed':
            message = f'تم إعادة فتح تذكرة الدعم #{ticket.ticket_number} - الحالة الجديدة: {ticket.get_status_display()}'
        else:
            message = f'تم تغيير حالة تذكرة الدعم #{ticket.ticket_number} من {old_status} إلى {ticket.get_status_display()}'

        create_notification(
            recipient=ticket.created_by,
            notification_type='ticket_status_changed',
            title='تحديث حالة التذكرة',
            message=message,
            sender=ticket.assigned_to,
            priority=priority,
            action_url=f'/support/tickets/{ticket.id}/',
            action_text='عرض التذكرة'
        )

    # تم حذف notify_support_message_received - استخدم notify_system_message_received بدلاً منه

    @staticmethod
    def notify_new_message(message, recipient):
        """إشعار برسالة جديدة في المحادثة - إشعار واحد فقط لكل رسالة"""
        from .models import Notification

        # فحص وجود إشعار لنفس الرسالة مسبقاً
        existing_notification = Notification.objects.filter(
            recipient=recipient,
            sender=message.sender,
            notification_type='new_message',
            action_url=f'/messages/conversation/{message.conversation.id}/',
            is_read=False
        ).first()

        # إذا وُجد إشعار غير مقروء لنفس المحادثة، لا نرسل إشعار جديد
        if existing_notification:
            return existing_notification

        # إنشاء إشعار جديد فقط إذا لم يوجد إشعار غير مقروء
        notification = create_notification(
            recipient=recipient,
            notification_type='new_message',
            title=f'رسالة جديدة من {message.sender.get_full_name()}',
            message=message.content[:100] + '...' if len(message.content) > 100 else message.content,
            sender=message.sender,
            priority='medium',
            action_url=f'/messages/conversation/{message.conversation.id}/',
            action_text='عرض المحادثة'
        )

        return notification


class SubscriptionNotificationService:
    """خدمة إشعارات الاشتراكات"""

    @staticmethod
    def notify_subscription_created(subscription):
        """إشعار الطالب بإنشاء اشتراك جديد (payment_pending)"""
        create_notification(
            recipient=subscription.student,
            notification_type='subscription_created',
            title='تم إنشاء اشتراك جديد',
            message=f'تم إنشاء اشتراكك في باقة "{subscription.plan.name}" بنجاح. يرجى إكمال عملية الدفع لتفعيل الاشتراك.',
            priority='medium',
            action_url='/dashboard/student/subscriptions/',
            action_text='عرض الاشتراكات'
        )

    @staticmethod
    def notify_payment_received(subscription):
        """إشعار الطالب باستلام الدفعة والمدير بالحاجة للموافقة"""
        # إشعار الطالب
        create_notification(
            recipient=subscription.student,
            notification_type='subscription_payment_received',
            title='تم استلام دفعتك بنجاح',
            message=f'تم استلام دفعة اشتراكك في باقة "{subscription.plan.name}" بمبلغ {subscription.amount_paid} {subscription.plan.get_currency_symbol()}. سيتم مراجعة اشتراكك وتفعيله خلال 24 ساعة.',
            priority='high',
            action_url='/dashboard/student/subscriptions/',
            action_text='عرض الاشتراكات'
        )

        # إشعار المدير
        from django.contrib.auth import get_user_model
        User = get_user_model()
        admins = User.objects.filter(user_type='admin')

        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='subscription_needs_approval',
                title='دفعة جديدة تحتاج موافقة',
                message=f'الطالب {subscription.student.get_full_name()} دفع مبلغ {subscription.amount_paid} {subscription.plan.get_currency_symbol()} لباقة "{subscription.plan.name}". يرجى مراجعة الدفعة وتفعيل الاشتراك.',
                priority='high',
                action_url='/dashboard/admin/subscriptions/',
                action_text='مراجعة الاشتراكات'
            )

    @staticmethod
    def notify_subscription_activated(subscription):
        """إشعار الطالب بتفعيل الاشتراك"""
        create_notification(
            recipient=subscription.student,
            notification_type='subscription_activated',
            title='تم تفعيل اشتراكك!',
            message=f'مبروك! تم تفعيل اشتراكك في باقة "{subscription.plan.name}" بنجاح. يمكنك الآن حجز حصصك والاستفادة من جميع الخدمات.',
            priority='high',
            action_url='/dashboard/student/lessons/',
            action_text='عرض الحصص'
        )

    @staticmethod
    def notify_subscription_cancelled(subscription, cancellation_reason=None):
        """إشعار الطالب بإلغاء الاشتراك"""
        reason_text = f" السبب: {cancellation_reason}" if cancellation_reason else ""

        create_notification(
            recipient=subscription.student,
            notification_type='subscription_cancelled',
            title='تم إلغاء اشتراكك',
            message=f'تم إلغاء اشتراكك في باقة "{subscription.plan.name}" من قبل الإدارة.{reason_text} يمكنك التواصل مع الدعم الفني أو الاشتراك في باقة جديدة.',
            priority='high',
            action_url='/dashboard/student/subscriptions/',
            action_text='عرض الاشتراكات'
        )

    @staticmethod
    def notify_subscription_expiring_soon(subscription, days_remaining):
        """إشعار الطالب والمدير باقتراب انتهاء الاشتراك"""
        # إشعار الطالب
        create_notification(
            recipient=subscription.student,
            notification_type='subscription_expiring_soon',
            title='اشتراكك على وشك الانتهاء',
            message=f'سينتهي اشتراكك في باقة "{subscription.plan.name}" خلال {days_remaining} أيام. يرجى تجديد اشتراكك لمواصلة الاستفادة من الخدمات.',
            priority='medium',
            action_url='/dashboard/student/subscriptions/',
            action_text='تجديد الاشتراك'
        )

        # إشعار المدير
        from django.contrib.auth import get_user_model
        User = get_user_model()
        admins = User.objects.filter(user_type='admin')

        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='subscription_expiring_soon',
                title='اشتراك طالب على وشك الانتهاء',
                message=f'اشتراك الطالب {subscription.student.get_full_name()} في باقة "{subscription.plan.name}" سينتهي خلال {days_remaining} أيام.',
                priority='low',
                action_url='/dashboard/admin/subscriptions/',
                action_text='عرض الاشتراكات'
            )

    @staticmethod
    def notify_lessons_exhausted(subscription):
        """إشعار الطالب والمدير بانتهاء حصص الاشتراك"""
        # إشعار الطالب
        create_notification(
            recipient=subscription.student,
            notification_type='subscription_lessons_exhausted',
            title='انتهت حصص اشتراكك',
            message=f'لقد استنفدت جميع حصص اشتراكك في باقة "{subscription.plan.name}". يمكنك تجديد الباقة أو الاشتراك في باقة جديدة.',
            priority='medium',
            action_url='/dashboard/student/subscriptions/',
            action_text='تجديد الاشتراك'
        )

        # إشعار المدير
        from django.contrib.auth import get_user_model
        User = get_user_model()
        admins = User.objects.filter(user_type='admin')

        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='subscription_lessons_exhausted',
                title='انتهت حصص طالب',
                message=f'الطالب {subscription.student.get_full_name()} استنفد جميع حصص اشتراكه في باقة "{subscription.plan.name}".',
                priority='low',
                action_url='/dashboard/admin/subscriptions/',
                action_text='عرض الاشتراكات'
            )

    @staticmethod
    def notify_bank_transfer_submitted(subscription):
        """إشعار المدير بإرسال إثبات حوالة بنكية"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        admins = User.objects.filter(user_type='admin')

        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='subscription_needs_approval',
                title='طلب اشتراك جديد بحوالة بنكية',
                message=f'الطالب {subscription.student.get_full_name()} أرسل إثبات حوالة بنكية لباقة "{subscription.plan.name}". يرجى مراجعة الإثبات والموافقة على الاشتراك.',
                priority='high',
                action_url='/dashboard/admin/subscriptions/',
                action_text='مراجعة الاشتراكات'
            )

    @staticmethod
    def notify_system_message_received(system_message, recipient):
        """إشعار باستلام رسالة نظام جديدة"""
        # تحديد الأولوية بناءً على نوع الرسالة وأولويتها
        priority_map = {
            'urgent': 'urgent',
            'high': 'high',
            'medium': 'medium',
            'low': 'low'
        }

        # تحديد الأولوية بناءً على نوع الرسالة
        if system_message.message_type in ['warning', 'urgent']:
            priority = 'urgent'
        elif system_message.message_type in ['maintenance', 'policy']:
            priority = 'high'
        else:
            priority = priority_map.get(system_message.priority, 'medium')

        # تحديد العنوان بناءً على نوع الرسالة
        title_map = {
            'announcement': 'إعلان جديد من النظام',
            'warning': 'تحذير مهم من النظام',
            'reminder': 'تذكير من النظام',
            'update': 'تحديث النظام',
            'maintenance': 'إشعار صيانة',
            'policy': 'سياسة جديدة',
            'feature': 'ميزة جديدة',
            'urgent': 'رسالة عاجلة من النظام',
        }

        title = title_map.get(system_message.message_type, 'رسالة جديدة من النظام')

        create_notification(
            recipient=recipient,
            notification_type='system_announcement',
            title=title,
            message=f'{system_message.title}',
            sender=system_message.sent_by,
            priority=priority,
            action_url=f'/notifications/messages/{system_message.id}/',
            action_text='عرض الرسالة'
        )

    @staticmethod
    def notify_user_approved(user):
        """إشعار بالموافقة على طلب التسجيل"""
        # إشعار المستخدم بالموافقة
        create_notification(
            recipient=user,
            notification_type='account_approved',
            title='تمت الموافقة على حسابك',
            message='تمت الموافقة على حسابك بنجاح. يمكنك الآن استخدام جميع ميزات النظام.',
            priority='high',
            action_url='/dashboard/',
            action_text='الذهاب للوحة التحكم'
        )

        # إشعار المديرين
        from django.contrib.auth import get_user_model
        User = get_user_model()

        admins = User.objects.filter(user_type='admin', is_active=True).exclude(id=user.verified_by_id if user.verified_by_id else 0)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='admin_user_approved',
                title='تمت الموافقة على مستخدم',
                message=f'تمت الموافقة على حساب {user.get_full_name()} ({user.get_user_type_display()}) بواسطة {user.verified_by.get_full_name() if user.verified_by else "النظام"}',
                sender=user.verified_by,
                priority='medium',
                action_url='/dashboard/admin/users/',
                action_text='إدارة المستخدمين'
            )

    @staticmethod
    def notify_user_rejected(user):
        """إشعار برفض طلب التسجيل"""
        # إشعار المستخدم بالرفض
        rejection_reason = f" السبب: {user.rejection_reason}" if user.rejection_reason else ""

        create_notification(
            recipient=user,
            notification_type='account_rejected',
            title='تم رفض طلب التسجيل',
            message=f'نأسف، تم رفض طلب التسجيل الخاص بك.{rejection_reason}',
            priority='high',
            action_url='/dashboard/',
            action_text='عرض التفاصيل'
        )

        # إشعار المديرين
        from django.contrib.auth import get_user_model
        User = get_user_model()

        admins = User.objects.filter(user_type='admin', is_active=True).exclude(id=user.verified_by_id if user.verified_by_id else 0)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='admin_user_rejected',
                title='تم رفض طلب تسجيل',
                message=f'تم رفض طلب تسجيل {user.get_full_name()} ({user.get_user_type_display()}) بواسطة {user.verified_by.get_full_name() if user.verified_by else "النظام"}',
                sender=user.verified_by,
                priority='medium',
                action_url='/dashboard/admin/users/',
                action_text='إدارة المستخدمين'
            )


class LessonNotificationService:
    """خدمة إشعارات الحصص"""

    @staticmethod
    def notify_lesson_starting_soon(lesson, recipient):
        """إشعار ببدء الحصة قريباً (15 دقيقة)"""

        # تحديد نوع الحصة والمسار المناسب
        if hasattr(lesson, 'subscription') and lesson.subscription:
            # حصة مجدولة من الاشتراك
            lesson_type = "حصة مجدولة"
            if recipient.user_type == 'student':
                action_url = f'/dashboard/student/lessons/'
            else:  # teacher
                action_url = f'/dashboard/teacher/scheduled-lesson/{lesson.id}/'
        else:
            # حصة مباشرة
            lesson_type = "حصة مباشرة"
            if recipient.user_type == 'student':
                action_url = f'/dashboard/student/live-lesson/{lesson.id}/'
            else:  # teacher
                action_url = f'/dashboard/teacher/live-lesson/{lesson.id}/'

        create_notification(
            recipient=recipient,
            notification_type='lesson_reminder',
            title=f'{lesson_type} ستبدأ خلال 15 دقيقة',
            message=f'تذكير: {lesson_type} ستبدأ خلال 15 دقيقة. يرجى الاستعداد والدخول في الوقت المحدد.',
            priority='high',
            action_url=action_url,
            action_text='دخول الحصة'
        )

    @staticmethod
    def notify_lesson_completed(lesson, student, teacher):
        """إشعار بانتهاء الحصة"""

        # إشعار الطالب
        create_notification(
            recipient=student,
            notification_type='lesson_completed',
            title='تم إنهاء الحصة',
            message=f'تم إنهاء حصتك مع المعلم {teacher.get_full_name()} بنجاح. يمكنك الآن تقييم الحصة.',
            priority='medium',
            action_url='/dashboard/student/lessons/',
            action_text='عرض الحصص'
        )

        # إشعار المعلم
        create_notification(
            recipient=teacher,
            notification_type='lesson_completed',
            title='تم إنهاء الحصة',
            message=f'تم إنهاء حصتك مع الطالب {student.get_full_name()} بنجاح. يرجى كتابة تقرير الحصة.',
            priority='medium',
            action_url='/dashboard/teacher/lessons/',
            action_text='كتابة التقرير'
        )

    @staticmethod
    def notify_lesson_cancelled(lesson, recipient, cancelled_by):
        """إشعار بإلغاء الحصة"""

        # تحديد المسار حسب نوع المستخدم
        if recipient.user_type == 'student':
            action_url = '/dashboard/student/lessons/'
        elif recipient.user_type == 'teacher':
            action_url = '/dashboard/teacher/lessons/'
        else:  # admin
            action_url = '/dashboard/admin/lessons-monitoring/'

        create_notification(
            recipient=recipient,
            notification_type='lesson_cancelled',
            title='تم إلغاء الحصة',
            message=f'تم إلغاء الحصة من قبل {cancelled_by.get_full_name()}. يرجى التواصل مع الإدارة لإعادة الجدولة.',
            sender=cancelled_by,
            priority='high',
            action_url=action_url,
            action_text='عرض الحصص'
        )

    @staticmethod
    def notify_lesson_rescheduled(lesson, recipient, new_datetime):
        """إشعار بإعادة جدولة الحصة"""

        # تحديد المسار حسب نوع المستخدم
        if recipient.user_type == 'student':
            action_url = '/dashboard/student/lessons/'
        elif recipient.user_type == 'teacher':
            action_url = '/dashboard/teacher/lessons/'
        else:  # admin
            action_url = '/dashboard/admin/lessons-monitoring/'

        create_notification(
            recipient=recipient,
            notification_type='lesson_rescheduled',
            title='تم تغيير موعد الحصة',
            message=f'تم تغيير موعد حصتك إلى {new_datetime.strftime("%Y-%m-%d %H:%M")}. يرجى تحديث جدولك الشخصي.',
            priority='high',
            action_url=action_url,
            action_text='عرض الجدول'
        )


def send_bulk_notifications(recipients, notification_type, title, message, sender=None, priority='medium'):
    """إرسال إشعارات جماعية"""
    from .models import Notification

    notifications = []
    for recipient in recipients:
        notifications.append(
            Notification(
                recipient=recipient,
                sender=sender,
                notification_type=notification_type,
                title=title,
                message=message,
                priority=priority
            )
        )

    Notification.objects.bulk_create(notifications)
    return len(notifications)
