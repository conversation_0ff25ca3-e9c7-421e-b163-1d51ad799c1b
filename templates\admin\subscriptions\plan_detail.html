{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الباقة - {{ plan.name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-4xl font-bold text-islamic-primary mb-2 flex items-center">
                    <i class="fas fa-box text-islamic-gold ml-3"></i>
                    {{ plan.name }}
                </h1>
                <p class="text-gray-600">تفاصيل وإحصائيات الباقة</p>
            </div>
            <div class="flex space-x-4 space-x-reverse">
                <a href="{% url 'admin_plans_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للقائمة
                </a>
                <a href="{% url 'admin_plan_edit' plan.id %}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-edit ml-2"></i>
                    تعديل الباقة
                </a>
                <a href="{% url 'admin_plan_delete' plan.id %}" class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                    <i class="fas fa-trash ml-2"></i>
                    حذف الباقة
                </a>
            </div>
        </div>

        <!-- Plan Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Plan Details Card -->
            <div class="lg:col-span-2 bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-islamic-primary mb-6">
                    <i class="fas fa-info-circle ml-2"></i>
                    تفاصيل الباقة
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">اسم الباقة</label>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نوع الباقة</label>
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-semibold rounded-full">
                            {{ plan.get_plan_type_display }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">السعر</label>
                        <div class="text-lg font-semibold text-gray-900">
                            {% if plan.discount_percentage > 0 %}
                                <span class="line-through text-gray-500">{{ plan.get_price_with_currency }}</span>
                                <br>
                                <span class="text-green-600">{{ plan.get_discounted_price_with_currency }}</span>
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2">
                                    خصم {{ plan.discount_percentage }}%
                                </span>
                            {% else %}
                                {{ plan.get_price_with_currency }}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">العملة</label>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.get_currency_display }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">المدة</label>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.duration_days }} يوم ({{ plan.get_duration_type_display }})</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">عدد الحصص</label>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.lessons_count }} حصة</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">مدة الحصة</label>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.lesson_duration }} دقيقة</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">سعر الحصة الواحدة</label>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.get_price_per_lesson|floatformat:2 }} {{ plan.get_currency_symbol }}</p>
                    </div>
                </div>
                
                <!-- Description -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف الباقة</label>
                    <p class="text-gray-700 bg-gray-50 p-4 rounded-lg">{{ plan.description }}</p>
                </div>
                
                <!-- Features -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">مميزات الباقة</label>
                    <ul class="space-y-2">
                        {% for feature in plan.features %}
                        <li class="flex items-center text-gray-700">
                            <i class="fas fa-check text-green-500 ml-2"></i>
                            {{ feature }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                
                <!-- Status -->
                <div class="mt-6 flex space-x-4 space-x-reverse">
                    {% if plan.is_active %}
                        <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-semibold rounded-full">
                            <i class="fas fa-check-circle ml-1"></i>
                            نشطة
                        </span>
                    {% else %}
                        <span class="px-3 py-1 bg-red-100 text-red-800 text-sm font-semibold rounded-full">
                            <i class="fas fa-times-circle ml-1"></i>
                            معطلة
                        </span>
                    {% endif %}
                    
                    {% if plan.is_featured %}
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-semibold rounded-full">
                            <i class="fas fa-star ml-1"></i>
                            مميزة
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-islamic-primary mb-6">
                    <i class="fas fa-chart-bar ml-2"></i>
                    إحصائيات الباقة
                </h3>
                
                <div class="space-y-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">{{ plan_stats.total_subscriptions }}</div>
                        <div class="text-sm text-gray-600">إجمالي الاشتراكات</div>
                    </div>
                    
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">{{ plan_stats.active_subscriptions }}</div>
                        <div class="text-sm text-gray-600">اشتراكات نشطة</div>
                    </div>
                    
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-yellow-600">{{ plan_stats.expired_subscriptions }}</div>
                        <div class="text-sm text-gray-600">اشتراكات منتهية</div>
                    </div>
                    
                    <div class="bg-red-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-red-600">{{ plan_stats.cancelled_subscriptions }}</div>
                        <div class="text-sm text-gray-600">اشتراكات ملغية</div>
                    </div>
                    
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">{{ plan_stats.total_revenue|floatformat:0 }}</div>
                        <div class="text-sm text-gray-600">إجمالي الإيرادات ({{ plan.get_currency_symbol }})</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Subscriptions -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-islamic-primary mb-6">
                <i class="fas fa-users ml-2"></i>
                أحدث الاشتراكات
            </h3>
            
            {% if recent_subscriptions %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ المدفوع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الاشتراك</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الانتهاء</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for subscription in recent_subscriptions %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-islamic-primary rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                    <div class="text-sm font-medium text-gray-900">{{ subscription.student.get_full_name }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ subscription.amount_paid }} {{ plan.get_currency_symbol }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ subscription.created_at|date:"Y-m-d" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ subscription.end_date|date:"Y-m-d" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if subscription.status == 'active' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        نشط
                                    </span>
                                {% elif subscription.status == 'expired' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        منتهي
                                    </span>
                                {% elif subscription.status == 'cancelled' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        ملغي
                                    </span>
                                {% else %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        {{ subscription.get_status_display }}
                                    </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-8">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-600 mb-2">لا توجد اشتراكات</h3>
                <p class="text-gray-500">لم يشترك أي طالب في هذه الباقة بعد</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
