/*! Tailwind CSS Production Build للمشروع الإسلامي */
/*! تم إنشاؤه خصيصاً للمشروع مع جميع الفئات المستخدمة */

/* ========================================
   🎨 متغيرات الألوان الإسلامية
======================================== */
:root {
  /* الألوان الأساسية */
  --islamic-primary: #2D5016;
  --islamic-light: #4A7C59;
  --islamic-dark: #1A3009;
  --islamic-gold: #D4AF37;
  --islamic-light-gold: #F4E4BC;
  --islamic-light-blue: #E8F4FD;
  --islamic-cream: #FDF6E3;
  --islamic-emerald: #50C878;
  --islamic-sage: #9CAF88;
  --islamic-mint: #F0F8F0;

  /* ألوان إضافية من الملف المخصص */
  --primary-green: #2D5016;
  --light-green: #4A7C59;
  --gold: #D4AF37;
  --light-gold: #F4E4BC;
  --dark-blue: #1B365D;
  --light-blue: #E8F4FD;
  --islamic-secondary: #2d3748;
  
  /* الظلال */
  --shadow-light: rgba(45, 80, 22, 0.1);
  --shadow-medium: rgba(45, 80, 22, 0.15);
  --shadow-strong: rgba(45, 80, 22, 0.25);
  
  /* التدرجات */
  --gradient-primary: linear-gradient(135deg, var(--islamic-primary) 0%, var(--islamic-light) 100%);
  --gradient-gold: linear-gradient(135deg, var(--islamic-gold) 0%, var(--islamic-light-gold) 100%);
  
  /* الحدود والانتقالات */
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --transition-fast: all 0.2s ease;
  --transition-medium: all 0.3s ease;
}

/* ========================================
   🔧 إعدادات أساسية
======================================== */
*,::before,::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

::before,::after {
  --tw-content: '';
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
}

body {
  margin: 0;
  line-height: inherit;
}

/* ========================================
   📐 التخطيط (Layout)
======================================== */
.container { width: 100%; }
@media (min-width: 640px) { .container { max-width: 640px; } }
@media (min-width: 768px) { .container { max-width: 768px; } }
@media (min-width: 1024px) { .container { max-width: 1024px; } }
@media (min-width: 1280px) { .container { max-width: 1280px; } }
@media (min-width: 1536px) { .container { max-width: 1536px; } }

.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

.inset-0 { top: 0px; right: 0px; bottom: 0px; left: 0px; }
.top-0 { top: 0px; }
.top-1 { top: 0.25rem; }
.top-2 { top: 0.5rem; }
.top-3 { top: 0.75rem; }
.top-4 { top: 1rem; }
.top-6 { top: 1.5rem; }
.top-12 { top: 3rem; }
.top-1\/2 { top: 50%; }
.top-1\/3 { top: 33.333333%; }
.top-1\/4 { top: 25%; }
.top-3\/4 { top: 75%; }

.right-0 { right: 0px; }
.right-2 { right: 0.5rem; }
.right-4 { right: 1rem; }
.right-6 { right: 1.5rem; }
.right-12 { right: 3rem; }
.right-20 { right: 5rem; }

.bottom-0 { bottom: 0px; }
.bottom-6 { bottom: 1.5rem; }
.bottom-1\/3 { bottom: 33.333333%; }

.left-0 { left: 0px; }
.left-4 { left: 1rem; }
.left-6 { left: 1.5rem; }
.left-12 { left: 3rem; }
.left-20 { left: 5rem; }

.z-10 { z-index: 10; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* ========================================
   📱 العرض (Display)
======================================== */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.table { display: table; }
.grid { display: grid; }
.hidden { display: none; }

/* ========================================
   🔄 Flexbox
======================================== */
.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

.flex-wrap { flex-wrap: wrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

.grow { flex-grow: 1; }
.grow-0 { flex-grow: 0; }
.shrink { flex-shrink: 1; }
.shrink-0 { flex-shrink: 0; }

/* ========================================
   📏 Grid
======================================== */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-12 { grid-column: span 12 / span 12; }

/* ========================================
   📐 الأبعاد (Sizing)
======================================== */
.w-0 { width: 0px; }
.w-1 { width: 0.25rem; }
.w-2 { width: 0.5rem; }
.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-14 { width: 3.5rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-24 { width: 6rem; }
.w-32 { width: 8rem; }
.w-40 { width: 10rem; }
.w-48 { width: 12rem; }
.w-56 { width: 14rem; }
.w-64 { width: 16rem; }
.w-auto { width: auto; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-3\/4 { width: 75%; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }

.min-w-0 { min-width: 0px; }
.min-w-full { min-width: 100%; }

.max-w-none { max-width: none; }
.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-full { max-width: 100%; }

.h-0 { height: 0px; }
.h-1 { height: 0.25rem; }
.h-2 { height: 0.5rem; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-14 { height: 3.5rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-24 { height: 6rem; }
.h-32 { height: 8rem; }
.h-40 { height: 10rem; }
.h-48 { height: 12rem; }
.h-56 { height: 14rem; }
.h-64 { height: 16rem; }
.h-auto { height: auto; }
.h-1\/2 { height: 50%; }
.h-1\/3 { height: 33.333333%; }
.h-2\/3 { height: 66.666667%; }
.h-1\/4 { height: 25%; }
.h-3\/4 { height: 75%; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }

.min-h-0 { min-height: 0px; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }

.max-h-full { max-height: 100%; }
.max-h-screen { max-height: 100vh; }

/* ========================================
   📏 المسافات (Spacing)
======================================== */
.p-0 { padding: 0px; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-10 { padding: 2.5rem; }
.p-12 { padding: 3rem; }
.p-16 { padding: 4rem; }
.p-20 { padding: 5rem; }
.p-24 { padding: 6rem; }

.px-0 { padding-left: 0px; padding-right: 0px; }
.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }

.py-0 { padding-top: 0px; padding-bottom: 0px; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }

.pt-0 { padding-top: 0px; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 0.75rem; }
.pt-4 { padding-top: 1rem; }
.pt-6 { padding-top: 1.5rem; }
.pt-8 { padding-top: 2rem; }

.pr-0 { padding-right: 0px; }
.pr-1 { padding-right: 0.25rem; }
.pr-2 { padding-right: 0.5rem; }
.pr-3 { padding-right: 0.75rem; }
.pr-4 { padding-right: 1rem; }
.pr-6 { padding-right: 1.5rem; }

.pb-0 { padding-bottom: 0px; }
.pb-1 { padding-bottom: 0.25rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-3 { padding-bottom: 0.75rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-6 { padding-bottom: 1.5rem; }

.pl-0 { padding-left: 0px; }
.pl-1 { padding-left: 0.25rem; }
.pl-2 { padding-left: 0.5rem; }
.pl-3 { padding-left: 0.75rem; }
.pl-4 { padding-left: 1rem; }
.pl-6 { padding-left: 1.5rem; }

.m-0 { margin: 0px; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }
.m-auto { margin: auto; }

.mx-0 { margin-left: 0px; margin-right: 0px; }
.mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }
.mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }
.mx-3 { margin-left: 0.75rem; margin-right: 0.75rem; }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }
.mx-6 { margin-left: 1.5rem; margin-right: 1.5rem; }
.mx-8 { margin-left: 2rem; margin-right: 2rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.my-0 { margin-top: 0px; margin-bottom: 0px; }
.my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }
.my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }
.my-3 { margin-top: 0.75rem; margin-bottom: 0.75rem; }
.my-4 { margin-top: 1rem; margin-bottom: 1rem; }
.my-6 { margin-top: 1.5rem; margin-bottom: 1.5rem; }
.my-8 { margin-top: 2rem; margin-bottom: 2rem; }

.mt-0 { margin-top: 0px; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-10 { margin-top: 2.5rem; }
.mt-12 { margin-top: 3rem; }

.mr-0 { margin-right: 0px; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }
.mr-6 { margin-right: 1.5rem; }
.mr-8 { margin-right: 2rem; }
.mr-64 { margin-right: 16rem; }

.mb-0 { margin-bottom: 0px; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mb-12 { margin-bottom: 3rem; }

.ml-0 { margin-left: 0px; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.ml-6 { margin-left: 1.5rem; }
.ml-8 { margin-left: 2rem; }

.space-x-0 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0px * var(--tw-space-x-reverse)); margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse))); }
.space-x-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0.25rem * var(--tw-space-x-reverse)); margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0.5rem * var(--tw-space-x-reverse)); margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-3 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0.75rem * var(--tw-space-x-reverse)); margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(1rem * var(--tw-space-x-reverse)); margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-6 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(1.5rem * var(--tw-space-x-reverse)); margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-8 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(2rem * var(--tw-space-x-reverse)); margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse))); }

.space-x-reverse > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 1; }

.space-y-0 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0px * var(--tw-space-y-reverse)); }
.space-y-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.25rem * var(--tw-space-y-reverse)); }
.space-y-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.5rem * var(--tw-space-y-reverse)); }
.space-y-3 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.75rem * var(--tw-space-y-reverse)); }
.space-y-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(1rem * var(--tw-space-y-reverse)); }
.space-y-6 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(1.5rem * var(--tw-space-y-reverse)); }

.gap-0 { gap: 0px; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-5 { gap: 1.25rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* ========================================
   🎨 الألوان (Colors)
======================================== */
.text-transparent { color: transparent; }
.text-current { color: currentColor; }
.text-black { --tw-text-opacity: 1; color: rgb(0 0 0 / var(--tw-text-opacity)); }
.text-white { --tw-text-opacity: 1; color: rgb(255 255 255 / var(--tw-text-opacity)); }

/* Gray Colors */
.text-gray-50 { --tw-text-opacity: 1; color: rgb(249 250 251 / var(--tw-text-opacity)); }
.text-gray-100 { --tw-text-opacity: 1; color: rgb(243 244 246 / var(--tw-text-opacity)); }
.text-gray-200 { --tw-text-opacity: 1; color: rgb(229 231 235 / var(--tw-text-opacity)); }
.text-gray-300 { --tw-text-opacity: 1; color: rgb(209 213 219 / var(--tw-text-opacity)); }
.text-gray-400 { --tw-text-opacity: 1; color: rgb(156 163 175 / var(--tw-text-opacity)); }
.text-gray-500 { --tw-text-opacity: 1; color: rgb(107 114 128 / var(--tw-text-opacity)); }
.text-gray-600 { --tw-text-opacity: 1; color: rgb(75 85 99 / var(--tw-text-opacity)); }
.text-gray-700 { --tw-text-opacity: 1; color: rgb(55 65 81 / var(--tw-text-opacity)); }
.text-gray-800 { --tw-text-opacity: 1; color: rgb(31 41 55 / var(--tw-text-opacity)); }
.text-gray-900 { --tw-text-opacity: 1; color: rgb(17 24 39 / var(--tw-text-opacity)); }

/* Red Colors */
.text-red-50 { --tw-text-opacity: 1; color: rgb(254 242 242 / var(--tw-text-opacity)); }
.text-red-100 { --tw-text-opacity: 1; color: rgb(254 226 226 / var(--tw-text-opacity)); }
.text-red-200 { --tw-text-opacity: 1; color: rgb(252 165 165 / var(--tw-text-opacity)); }
.text-red-300 { --tw-text-opacity: 1; color: rgb(248 113 113 / var(--tw-text-opacity)); }
.text-red-400 { --tw-text-opacity: 1; color: rgb(248 113 113 / var(--tw-text-opacity)); }
.text-red-500 { --tw-text-opacity: 1; color: rgb(239 68 68 / var(--tw-text-opacity)); }
.text-red-600 { --tw-text-opacity: 1; color: rgb(220 38 38 / var(--tw-text-opacity)); }
.text-red-700 { --tw-text-opacity: 1; color: rgb(185 28 28 / var(--tw-text-opacity)); }
.text-red-800 { --tw-text-opacity: 1; color: rgb(153 27 27 / var(--tw-text-opacity)); }
.text-red-900 { --tw-text-opacity: 1; color: rgb(127 29 29 / var(--tw-text-opacity)); }

/* Orange Colors */
.text-orange-50 { --tw-text-opacity: 1; color: rgb(255 247 237 / var(--tw-text-opacity)); }
.text-orange-100 { --tw-text-opacity: 1; color: rgb(254 215 170 / var(--tw-text-opacity)); }
.text-orange-200 { --tw-text-opacity: 1; color: rgb(253 186 116 / var(--tw-text-opacity)); }
.text-orange-300 { --tw-text-opacity: 1; color: rgb(251 146 60 / var(--tw-text-opacity)); }
.text-orange-400 { --tw-text-opacity: 1; color: rgb(251 146 60 / var(--tw-text-opacity)); }
.text-orange-500 { --tw-text-opacity: 1; color: rgb(249 115 22 / var(--tw-text-opacity)); }
.text-orange-600 { --tw-text-opacity: 1; color: rgb(234 88 12 / var(--tw-text-opacity)); }
.text-orange-700 { --tw-text-opacity: 1; color: rgb(194 65 12 / var(--tw-text-opacity)); }
.text-orange-800 { --tw-text-opacity: 1; color: rgb(154 52 18 / var(--tw-text-opacity)); }
.text-orange-900 { --tw-text-opacity: 1; color: rgb(124 45 18 / var(--tw-text-opacity)); }

/* Amber Colors */
.text-amber-50 { --tw-text-opacity: 1; color: rgb(255 251 235 / var(--tw-text-opacity)); }
.text-amber-100 { --tw-text-opacity: 1; color: rgb(254 243 199 / var(--tw-text-opacity)); }
.text-amber-200 { --tw-text-opacity: 1; color: rgb(253 230 138 / var(--tw-text-opacity)); }
.text-amber-300 { --tw-text-opacity: 1; color: rgb(252 211 77 / var(--tw-text-opacity)); }
.text-amber-400 { --tw-text-opacity: 1; color: rgb(251 191 36 / var(--tw-text-opacity)); }
.text-amber-500 { --tw-text-opacity: 1; color: rgb(245 158 11 / var(--tw-text-opacity)); }
.text-amber-600 { --tw-text-opacity: 1; color: rgb(217 119 6 / var(--tw-text-opacity)); }
.text-amber-700 { --tw-text-opacity: 1; color: rgb(180 83 9 / var(--tw-text-opacity)); }
.text-amber-800 { --tw-text-opacity: 1; color: rgb(146 64 14 / var(--tw-text-opacity)); }
.text-amber-900 { --tw-text-opacity: 1; color: rgb(120 53 15 / var(--tw-text-opacity)); }

/* Yellow Colors */
.text-yellow-50 { --tw-text-opacity: 1; color: rgb(254 252 232 / var(--tw-text-opacity)); }
.text-yellow-100 { --tw-text-opacity: 1; color: rgb(254 249 195 / var(--tw-text-opacity)); }
.text-yellow-200 { --tw-text-opacity: 1; color: rgb(254 240 138 / var(--tw-text-opacity)); }
.text-yellow-300 { --tw-text-opacity: 1; color: rgb(253 224 71 / var(--tw-text-opacity)); }
.text-yellow-400 { --tw-text-opacity: 1; color: rgb(250 204 21 / var(--tw-text-opacity)); }
.text-yellow-500 { --tw-text-opacity: 1; color: rgb(234 179 8 / var(--tw-text-opacity)); }
.text-yellow-600 { --tw-text-opacity: 1; color: rgb(202 138 4 / var(--tw-text-opacity)); }
.text-yellow-700 { --tw-text-opacity: 1; color: rgb(161 98 7 / var(--tw-text-opacity)); }
.text-yellow-800 { --tw-text-opacity: 1; color: rgb(133 77 14 / var(--tw-text-opacity)); }
.text-yellow-900 { --tw-text-opacity: 1; color: rgb(113 63 18 / var(--tw-text-opacity)); }

/* Lime Colors */
.text-lime-50 { --tw-text-opacity: 1; color: rgb(247 254 231 / var(--tw-text-opacity)); }
.text-lime-100 { --tw-text-opacity: 1; color: rgb(236 252 203 / var(--tw-text-opacity)); }
.text-lime-200 { --tw-text-opacity: 1; color: rgb(217 249 157 / var(--tw-text-opacity)); }
.text-lime-300 { --tw-text-opacity: 1; color: rgb(190 242 100 / var(--tw-text-opacity)); }
.text-lime-400 { --tw-text-opacity: 1; color: rgb(163 230 53 / var(--tw-text-opacity)); }
.text-lime-500 { --tw-text-opacity: 1; color: rgb(132 204 22 / var(--tw-text-opacity)); }
.text-lime-600 { --tw-text-opacity: 1; color: rgb(101 163 13 / var(--tw-text-opacity)); }
.text-lime-700 { --tw-text-opacity: 1; color: rgb(77 124 15 / var(--tw-text-opacity)); }
.text-lime-800 { --tw-text-opacity: 1; color: rgb(63 98 18 / var(--tw-text-opacity)); }
.text-lime-900 { --tw-text-opacity: 1; color: rgb(54 83 20 / var(--tw-text-opacity)); }

/* Green Colors */
.text-green-50 { --tw-text-opacity: 1; color: rgb(240 253 244 / var(--tw-text-opacity)); }
.text-green-100 { --tw-text-opacity: 1; color: rgb(220 252 231 / var(--tw-text-opacity)); }
.text-green-200 { --tw-text-opacity: 1; color: rgb(187 247 208 / var(--tw-text-opacity)); }
.text-green-300 { --tw-text-opacity: 1; color: rgb(134 239 172 / var(--tw-text-opacity)); }
.text-green-400 { --tw-text-opacity: 1; color: rgb(74 222 128 / var(--tw-text-opacity)); }
.text-green-500 { --tw-text-opacity: 1; color: rgb(34 197 94 / var(--tw-text-opacity)); }
.text-green-600 { --tw-text-opacity: 1; color: rgb(22 163 74 / var(--tw-text-opacity)); }
.text-green-700 { --tw-text-opacity: 1; color: rgb(21 128 61 / var(--tw-text-opacity)); }
.text-green-800 { --tw-text-opacity: 1; color: rgb(22 101 52 / var(--tw-text-opacity)); }
.text-green-900 { --tw-text-opacity: 1; color: rgb(20 83 45 / var(--tw-text-opacity)); }

/* Emerald Colors */
.text-emerald-50 { --tw-text-opacity: 1; color: rgb(236 253 245 / var(--tw-text-opacity)); }
.text-emerald-100 { --tw-text-opacity: 1; color: rgb(209 250 229 / var(--tw-text-opacity)); }
.text-emerald-200 { --tw-text-opacity: 1; color: rgb(167 243 208 / var(--tw-text-opacity)); }
.text-emerald-300 { --tw-text-opacity: 1; color: rgb(110 231 183 / var(--tw-text-opacity)); }
.text-emerald-400 { --tw-text-opacity: 1; color: rgb(52 211 153 / var(--tw-text-opacity)); }
.text-emerald-500 { --tw-text-opacity: 1; color: rgb(16 185 129 / var(--tw-text-opacity)); }
.text-emerald-600 { --tw-text-opacity: 1; color: rgb(5 150 105 / var(--tw-text-opacity)); }
.text-emerald-700 { --tw-text-opacity: 1; color: rgb(4 120 87 / var(--tw-text-opacity)); }
.text-emerald-800 { --tw-text-opacity: 1; color: rgb(6 95 70 / var(--tw-text-opacity)); }
.text-emerald-900 { --tw-text-opacity: 1; color: rgb(6 78 59 / var(--tw-text-opacity)); }

/* Teal Colors */
.text-teal-50 { --tw-text-opacity: 1; color: rgb(240 253 250 / var(--tw-text-opacity)); }
.text-teal-100 { --tw-text-opacity: 1; color: rgb(204 251 241 / var(--tw-text-opacity)); }
.text-teal-200 { --tw-text-opacity: 1; color: rgb(153 246 228 / var(--tw-text-opacity)); }
.text-teal-300 { --tw-text-opacity: 1; color: rgb(94 234 212 / var(--tw-text-opacity)); }
.text-teal-400 { --tw-text-opacity: 1; color: rgb(45 212 191 / var(--tw-text-opacity)); }
.text-teal-500 { --tw-text-opacity: 1; color: rgb(20 184 166 / var(--tw-text-opacity)); }
.text-teal-600 { --tw-text-opacity: 1; color: rgb(13 148 136 / var(--tw-text-opacity)); }
.text-teal-700 { --tw-text-opacity: 1; color: rgb(15 118 110 / var(--tw-text-opacity)); }
.text-teal-800 { --tw-text-opacity: 1; color: rgb(17 94 89 / var(--tw-text-opacity)); }
.text-teal-900 { --tw-text-opacity: 1; color: rgb(19 78 74 / var(--tw-text-opacity)); }

/* Cyan Colors */
.text-cyan-50 { --tw-text-opacity: 1; color: rgb(236 254 255 / var(--tw-text-opacity)); }
.text-cyan-100 { --tw-text-opacity: 1; color: rgb(207 250 254 / var(--tw-text-opacity)); }
.text-cyan-200 { --tw-text-opacity: 1; color: rgb(165 243 252 / var(--tw-text-opacity)); }
.text-cyan-300 { --tw-text-opacity: 1; color: rgb(103 232 249 / var(--tw-text-opacity)); }
.text-cyan-400 { --tw-text-opacity: 1; color: rgb(34 211 238 / var(--tw-text-opacity)); }
.text-cyan-500 { --tw-text-opacity: 1; color: rgb(6 182 212 / var(--tw-text-opacity)); }
.text-cyan-600 { --tw-text-opacity: 1; color: rgb(8 145 178 / var(--tw-text-opacity)); }
.text-cyan-700 { --tw-text-opacity: 1; color: rgb(14 116 144 / var(--tw-text-opacity)); }
.text-cyan-800 { --tw-text-opacity: 1; color: rgb(21 94 117 / var(--tw-text-opacity)); }
.text-cyan-900 { --tw-text-opacity: 1; color: rgb(22 78 99 / var(--tw-text-opacity)); }

/* Blue Colors */
.text-blue-50 { --tw-text-opacity: 1; color: rgb(239 246 255 / var(--tw-text-opacity)); }
.text-blue-100 { --tw-text-opacity: 1; color: rgb(219 234 254 / var(--tw-text-opacity)); }
.text-blue-200 { --tw-text-opacity: 1; color: rgb(191 219 254 / var(--tw-text-opacity)); }
.text-blue-300 { --tw-text-opacity: 1; color: rgb(147 197 253 / var(--tw-text-opacity)); }
.text-blue-400 { --tw-text-opacity: 1; color: rgb(96 165 250 / var(--tw-text-opacity)); }
.text-blue-500 { --tw-text-opacity: 1; color: rgb(59 130 246 / var(--tw-text-opacity)); }
.text-blue-600 { --tw-text-opacity: 1; color: rgb(37 99 235 / var(--tw-text-opacity)); }
.text-blue-700 { --tw-text-opacity: 1; color: rgb(29 78 216 / var(--tw-text-opacity)); }
.text-blue-800 { --tw-text-opacity: 1; color: rgb(30 64 175 / var(--tw-text-opacity)); }
.text-blue-900 { --tw-text-opacity: 1; color: rgb(30 58 138 / var(--tw-text-opacity)); }

/* Indigo Colors */
.text-indigo-50 { --tw-text-opacity: 1; color: rgb(238 242 255 / var(--tw-text-opacity)); }
.text-indigo-100 { --tw-text-opacity: 1; color: rgb(224 231 255 / var(--tw-text-opacity)); }
.text-indigo-200 { --tw-text-opacity: 1; color: rgb(199 210 254 / var(--tw-text-opacity)); }
.text-indigo-300 { --tw-text-opacity: 1; color: rgb(165 180 252 / var(--tw-text-opacity)); }
.text-indigo-400 { --tw-text-opacity: 1; color: rgb(129 140 248 / var(--tw-text-opacity)); }
.text-indigo-500 { --tw-text-opacity: 1; color: rgb(99 102 241 / var(--tw-text-opacity)); }
.text-indigo-600 { --tw-text-opacity: 1; color: rgb(79 70 229 / var(--tw-text-opacity)); }
.text-indigo-700 { --tw-text-opacity: 1; color: rgb(67 56 202 / var(--tw-text-opacity)); }
.text-indigo-800 { --tw-text-opacity: 1; color: rgb(55 48 163 / var(--tw-text-opacity)); }
.text-indigo-900 { --tw-text-opacity: 1; color: rgb(49 46 129 / var(--tw-text-opacity)); }

/* Violet Colors */
.text-violet-50 { --tw-text-opacity: 1; color: rgb(245 243 255 / var(--tw-text-opacity)); }
.text-violet-100 { --tw-text-opacity: 1; color: rgb(237 233 254 / var(--tw-text-opacity)); }
.text-violet-200 { --tw-text-opacity: 1; color: rgb(221 214 254 / var(--tw-text-opacity)); }
.text-violet-300 { --tw-text-opacity: 1; color: rgb(196 181 253 / var(--tw-text-opacity)); }
.text-violet-400 { --tw-text-opacity: 1; color: rgb(167 139 250 / var(--tw-text-opacity)); }
.text-violet-500 { --tw-text-opacity: 1; color: rgb(139 92 246 / var(--tw-text-opacity)); }
.text-violet-600 { --tw-text-opacity: 1; color: rgb(124 58 237 / var(--tw-text-opacity)); }
.text-violet-700 { --tw-text-opacity: 1; color: rgb(109 40 217 / var(--tw-text-opacity)); }
.text-violet-800 { --tw-text-opacity: 1; color: rgb(91 33 182 / var(--tw-text-opacity)); }
.text-violet-900 { --tw-text-opacity: 1; color: rgb(76 29 149 / var(--tw-text-opacity)); }

/* Purple Colors */
.text-purple-50 { --tw-text-opacity: 1; color: rgb(250 245 255 / var(--tw-text-opacity)); }
.text-purple-100 { --tw-text-opacity: 1; color: rgb(243 232 255 / var(--tw-text-opacity)); }
.text-purple-200 { --tw-text-opacity: 1; color: rgb(233 213 255 / var(--tw-text-opacity)); }
.text-purple-300 { --tw-text-opacity: 1; color: rgb(216 180 254 / var(--tw-text-opacity)); }
.text-purple-400 { --tw-text-opacity: 1; color: rgb(196 181 253 / var(--tw-text-opacity)); }
.text-purple-500 { --tw-text-opacity: 1; color: rgb(168 85 247 / var(--tw-text-opacity)); }
.text-purple-600 { --tw-text-opacity: 1; color: rgb(147 51 234 / var(--tw-text-opacity)); }
.text-purple-700 { --tw-text-opacity: 1; color: rgb(126 34 206 / var(--tw-text-opacity)); }
.text-purple-800 { --tw-text-opacity: 1; color: rgb(107 33 168 / var(--tw-text-opacity)); }
.text-purple-900 { --tw-text-opacity: 1; color: rgb(88 28 135 / var(--tw-text-opacity)); }

/* Fuchsia Colors */
.text-fuchsia-50 { --tw-text-opacity: 1; color: rgb(253 244 255 / var(--tw-text-opacity)); }
.text-fuchsia-100 { --tw-text-opacity: 1; color: rgb(250 232 255 / var(--tw-text-opacity)); }
.text-fuchsia-200 { --tw-text-opacity: 1; color: rgb(245 208 254 / var(--tw-text-opacity)); }
.text-fuchsia-300 { --tw-text-opacity: 1; color: rgb(240 171 252 / var(--tw-text-opacity)); }
.text-fuchsia-400 { --tw-text-opacity: 1; color: rgb(232 121 249 / var(--tw-text-opacity)); }
.text-fuchsia-500 { --tw-text-opacity: 1; color: rgb(217 70 239 / var(--tw-text-opacity)); }
.text-fuchsia-600 { --tw-text-opacity: 1; color: rgb(192 38 211 / var(--tw-text-opacity)); }
.text-fuchsia-700 { --tw-text-opacity: 1; color: rgb(162 28 175 / var(--tw-text-opacity)); }
.text-fuchsia-800 { --tw-text-opacity: 1; color: rgb(134 25 143 / var(--tw-text-opacity)); }
.text-fuchsia-900 { --tw-text-opacity: 1; color: rgb(112 26 117 / var(--tw-text-opacity)); }

/* Pink Colors */
.text-pink-50 { --tw-text-opacity: 1; color: rgb(253 242 248 / var(--tw-text-opacity)); }
.text-pink-100 { --tw-text-opacity: 1; color: rgb(252 231 243 / var(--tw-text-opacity)); }
.text-pink-200 { --tw-text-opacity: 1; color: rgb(251 207 232 / var(--tw-text-opacity)); }
.text-pink-300 { --tw-text-opacity: 1; color: rgb(249 168 212 / var(--tw-text-opacity)); }
.text-pink-400 { --tw-text-opacity: 1; color: rgb(244 114 182 / var(--tw-text-opacity)); }
.text-pink-500 { --tw-text-opacity: 1; color: rgb(236 72 153 / var(--tw-text-opacity)); }
.text-pink-600 { --tw-text-opacity: 1; color: rgb(219 39 119 / var(--tw-text-opacity)); }
.text-pink-700 { --tw-text-opacity: 1; color: rgb(190 24 93 / var(--tw-text-opacity)); }
.text-pink-800 { --tw-text-opacity: 1; color: rgb(157 23 77 / var(--tw-text-opacity)); }
.text-pink-900 { --tw-text-opacity: 1; color: rgb(131 24 67 / var(--tw-text-opacity)); }

/* Rose Colors */
.text-rose-50 { --tw-text-opacity: 1; color: rgb(255 241 242 / var(--tw-text-opacity)); }
.text-rose-100 { --tw-text-opacity: 1; color: rgb(255 228 230 / var(--tw-text-opacity)); }
.text-rose-200 { --tw-text-opacity: 1; color: rgb(254 205 211 / var(--tw-text-opacity)); }
.text-rose-300 { --tw-text-opacity: 1; color: rgb(253 164 175 / var(--tw-text-opacity)); }
.text-rose-400 { --tw-text-opacity: 1; color: rgb(251 113 133 / var(--tw-text-opacity)); }
.text-rose-500 { --tw-text-opacity: 1; color: rgb(244 63 94 / var(--tw-text-opacity)); }
.text-rose-600 { --tw-text-opacity: 1; color: rgb(225 29 72 / var(--tw-text-opacity)); }
.text-rose-700 { --tw-text-opacity: 1; color: rgb(190 18 60 / var(--tw-text-opacity)); }
.text-rose-800 { --tw-text-opacity: 1; color: rgb(159 18 57 / var(--tw-text-opacity)); }
.text-rose-900 { --tw-text-opacity: 1; color: rgb(136 19 55 / var(--tw-text-opacity)); }

/* Islamic Colors */
.text-islamic-primary { color: var(--islamic-primary); }
.text-islamic-light { color: var(--islamic-light); }
.text-islamic-dark { color: var(--islamic-dark); }
.text-islamic-gold { color: var(--islamic-gold); }
.text-islamic-light-gold { color: var(--islamic-light-gold); }
.text-islamic-light-blue { color: var(--islamic-light-blue); }
.text-islamic-cream { color: var(--islamic-cream); }
.text-islamic-emerald { color: var(--islamic-emerald); }
.text-islamic-sage { color: var(--islamic-sage); }
.text-islamic-mint { color: var(--islamic-mint); }
.text-islamic-secondary { color: var(--islamic-secondary); }

/* Additional Color Variations */
.text-purple-400 { --tw-text-opacity: 1; color: rgb(196 181 253 / var(--tw-text-opacity)); }
.text-purple-500 { --tw-text-opacity: 1; color: rgb(168 85 247 / var(--tw-text-opacity)); }
.text-purple-600 { --tw-text-opacity: 1; color: rgb(147 51 234 / var(--tw-text-opacity)); }

.text-yellow-400 { --tw-text-opacity: 1; color: rgb(250 204 21 / var(--tw-text-opacity)); }
.text-yellow-500 { --tw-text-opacity: 1; color: rgb(234 179 8 / var(--tw-text-opacity)); }
.text-yellow-600 { --tw-text-opacity: 1; color: rgb(202 138 4 / var(--tw-text-opacity)); }

.text-orange-400 { --tw-text-opacity: 1; color: rgb(251 146 60 / var(--tw-text-opacity)); }
.text-orange-500 { --tw-text-opacity: 1; color: rgb(249 115 22 / var(--tw-text-opacity)); }
.text-orange-600 { --tw-text-opacity: 1; color: rgb(234 88 12 / var(--tw-text-opacity)); }

/* Additional Text Colors */
.text-teal-50 { --tw-text-opacity: 1; color: rgb(240 253 250 / var(--tw-text-opacity)); }
.text-teal-100 { --tw-text-opacity: 1; color: rgb(204 251 241 / var(--tw-text-opacity)); }
.text-teal-200 { --tw-text-opacity: 1; color: rgb(153 246 228 / var(--tw-text-opacity)); }
.text-teal-300 { --tw-text-opacity: 1; color: rgb(94 234 212 / var(--tw-text-opacity)); }
.text-teal-400 { --tw-text-opacity: 1; color: rgb(45 212 191 / var(--tw-text-opacity)); }
.text-teal-500 { --tw-text-opacity: 1; color: rgb(20 184 166 / var(--tw-text-opacity)); }
.text-teal-600 { --tw-text-opacity: 1; color: rgb(13 148 136 / var(--tw-text-opacity)); }
.text-teal-700 { --tw-text-opacity: 1; color: rgb(15 118 110 / var(--tw-text-opacity)); }

.text-cyan-50 { --tw-text-opacity: 1; color: rgb(236 254 255 / var(--tw-text-opacity)); }
.text-cyan-100 { --tw-text-opacity: 1; color: rgb(207 250 254 / var(--tw-text-opacity)); }
.text-cyan-200 { --tw-text-opacity: 1; color: rgb(165 243 252 / var(--tw-text-opacity)); }
.text-cyan-300 { --tw-text-opacity: 1; color: rgb(103 232 249 / var(--tw-text-opacity)); }
.text-cyan-400 { --tw-text-opacity: 1; color: rgb(34 211 238 / var(--tw-text-opacity)); }
.text-cyan-500 { --tw-text-opacity: 1; color: rgb(6 182 212 / var(--tw-text-opacity)); }
.text-cyan-600 { --tw-text-opacity: 1; color: rgb(8 145 178 / var(--tw-text-opacity)); }
.text-cyan-700 { --tw-text-opacity: 1; color: rgb(14 116 144 / var(--tw-text-opacity)); }

.text-emerald-50 { --tw-text-opacity: 1; color: rgb(236 253 245 / var(--tw-text-opacity)); }
.text-emerald-100 { --tw-text-opacity: 1; color: rgb(209 250 229 / var(--tw-text-opacity)); }
.text-emerald-200 { --tw-text-opacity: 1; color: rgb(167 243 208 / var(--tw-text-opacity)); }
.text-emerald-300 { --tw-text-opacity: 1; color: rgb(110 231 183 / var(--tw-text-opacity)); }
.text-emerald-400 { --tw-text-opacity: 1; color: rgb(52 211 153 / var(--tw-text-opacity)); }
.text-emerald-500 { --tw-text-opacity: 1; color: rgb(16 185 129 / var(--tw-text-opacity)); }
.text-emerald-600 { --tw-text-opacity: 1; color: rgb(5 150 105 / var(--tw-text-opacity)); }
.text-emerald-700 { --tw-text-opacity: 1; color: rgb(4 120 87 / var(--tw-text-opacity)); }

.text-violet-50 { --tw-text-opacity: 1; color: rgb(245 243 255 / var(--tw-text-opacity)); }
.text-violet-100 { --tw-text-opacity: 1; color: rgb(237 233 254 / var(--tw-text-opacity)); }
.text-violet-200 { --tw-text-opacity: 1; color: rgb(221 214 254 / var(--tw-text-opacity)); }
.text-violet-300 { --tw-text-opacity: 1; color: rgb(196 181 253 / var(--tw-text-opacity)); }
.text-violet-400 { --tw-text-opacity: 1; color: rgb(167 139 250 / var(--tw-text-opacity)); }
.text-violet-500 { --tw-text-opacity: 1; color: rgb(139 92 246 / var(--tw-text-opacity)); }
.text-violet-600 { --tw-text-opacity: 1; color: rgb(124 58 237 / var(--tw-text-opacity)); }
.text-violet-700 { --tw-text-opacity: 1; color: rgb(109 40 217 / var(--tw-text-opacity)); }

.text-pink-50 { --tw-text-opacity: 1; color: rgb(253 242 248 / var(--tw-text-opacity)); }
.text-pink-100 { --tw-text-opacity: 1; color: rgb(252 231 243 / var(--tw-text-opacity)); }
.text-pink-200 { --tw-text-opacity: 1; color: rgb(251 207 232 / var(--tw-text-opacity)); }
.text-pink-300 { --tw-text-opacity: 1; color: rgb(249 168 212 / var(--tw-text-opacity)); }
.text-pink-400 { --tw-text-opacity: 1; color: rgb(244 114 182 / var(--tw-text-opacity)); }
.text-pink-500 { --tw-text-opacity: 1; color: rgb(236 72 153 / var(--tw-text-opacity)); }
.text-pink-600 { --tw-text-opacity: 1; color: rgb(219 39 119 / var(--tw-text-opacity)); }
.text-pink-700 { --tw-text-opacity: 1; color: rgb(190 24 93 / var(--tw-text-opacity)); }

.text-rose-50 { --tw-text-opacity: 1; color: rgb(255 241 242 / var(--tw-text-opacity)); }
.text-rose-100 { --tw-text-opacity: 1; color: rgb(255 228 230 / var(--tw-text-opacity)); }
.text-rose-200 { --tw-text-opacity: 1; color: rgb(254 205 211 / var(--tw-text-opacity)); }
.text-rose-300 { --tw-text-opacity: 1; color: rgb(253 164 175 / var(--tw-text-opacity)); }
.text-rose-400 { --tw-text-opacity: 1; color: rgb(251 113 133 / var(--tw-text-opacity)); }
.text-rose-500 { --tw-text-opacity: 1; color: rgb(244 63 94 / var(--tw-text-opacity)); }
.text-rose-600 { --tw-text-opacity: 1; color: rgb(225 29 72 / var(--tw-text-opacity)); }
.text-rose-700 { --tw-text-opacity: 1; color: rgb(190 18 60 / var(--tw-text-opacity)); }

.text-amber-50 { --tw-text-opacity: 1; color: rgb(255 251 235 / var(--tw-text-opacity)); }
.text-amber-100 { --tw-text-opacity: 1; color: rgb(254 243 199 / var(--tw-text-opacity)); }
.text-amber-200 { --tw-text-opacity: 1; color: rgb(253 230 138 / var(--tw-text-opacity)); }
.text-amber-300 { --tw-text-opacity: 1; color: rgb(252 211 77 / var(--tw-text-opacity)); }
.text-amber-400 { --tw-text-opacity: 1; color: rgb(251 191 36 / var(--tw-text-opacity)); }
.text-amber-500 { --tw-text-opacity: 1; color: rgb(245 158 11 / var(--tw-text-opacity)); }
.text-amber-600 { --tw-text-opacity: 1; color: rgb(217 119 6 / var(--tw-text-opacity)); }
.text-amber-700 { --tw-text-opacity: 1; color: rgb(180 83 9 / var(--tw-text-opacity)); }

/* ========================================
   🎨 ألوان الخلفية (Background Colors)
======================================== */
.bg-transparent { background-color: transparent; }
.bg-current { background-color: currentColor; }
.bg-black { --tw-bg-opacity: 1; background-color: rgb(0 0 0 / var(--tw-bg-opacity)); }
.bg-white { --tw-bg-opacity: 1; background-color: rgb(255 255 255 / var(--tw-bg-opacity)); }

/* Gray Backgrounds */
.bg-gray-50 { --tw-bg-opacity: 1; background-color: rgb(249 250 251 / var(--tw-bg-opacity)); }
.bg-gray-100 { --tw-bg-opacity: 1; background-color: rgb(243 244 246 / var(--tw-bg-opacity)); }
.bg-gray-200 { --tw-bg-opacity: 1; background-color: rgb(229 231 235 / var(--tw-bg-opacity)); }
.bg-gray-300 { --tw-bg-opacity: 1; background-color: rgb(209 213 219 / var(--tw-bg-opacity)); }
.bg-gray-400 { --tw-bg-opacity: 1; background-color: rgb(156 163 175 / var(--tw-bg-opacity)); }
.bg-gray-500 { --tw-bg-opacity: 1; background-color: rgb(107 114 128 / var(--tw-bg-opacity)); }
.bg-gray-600 { --tw-bg-opacity: 1; background-color: rgb(75 85 99 / var(--tw-bg-opacity)); }
.bg-gray-700 { --tw-bg-opacity: 1; background-color: rgb(55 65 81 / var(--tw-bg-opacity)); }
.bg-gray-800 { --tw-bg-opacity: 1; background-color: rgb(31 41 55 / var(--tw-bg-opacity)); }
.bg-gray-900 { --tw-bg-opacity: 1; background-color: rgb(17 24 39 / var(--tw-bg-opacity)); }

/* Red Backgrounds */
.bg-red-50 { --tw-bg-opacity: 1; background-color: rgb(254 242 242 / var(--tw-bg-opacity)); }
.bg-red-100 { --tw-bg-opacity: 1; background-color: rgb(254 226 226 / var(--tw-bg-opacity)); }
.bg-red-200 { --tw-bg-opacity: 1; background-color: rgb(252 165 165 / var(--tw-bg-opacity)); }
.bg-red-300 { --tw-bg-opacity: 1; background-color: rgb(248 113 113 / var(--tw-bg-opacity)); }
.bg-red-400 { --tw-bg-opacity: 1; background-color: rgb(248 113 113 / var(--tw-bg-opacity)); }
.bg-red-500 { --tw-bg-opacity: 1; background-color: rgb(239 68 68 / var(--tw-bg-opacity)); }
.bg-red-600 { --tw-bg-opacity: 1; background-color: rgb(220 38 38 / var(--tw-bg-opacity)); }
.bg-red-700 { --tw-bg-opacity: 1; background-color: rgb(185 28 28 / var(--tw-bg-opacity)); }
.bg-red-800 { --tw-bg-opacity: 1; background-color: rgb(153 27 27 / var(--tw-bg-opacity)); }
.bg-red-900 { --tw-bg-opacity: 1; background-color: rgb(127 29 29 / var(--tw-bg-opacity)); }

/* Orange Backgrounds */
.bg-orange-50 { --tw-bg-opacity: 1; background-color: rgb(255 247 237 / var(--tw-bg-opacity)); }
.bg-orange-100 { --tw-bg-opacity: 1; background-color: rgb(254 215 170 / var(--tw-bg-opacity)); }
.bg-orange-200 { --tw-bg-opacity: 1; background-color: rgb(253 186 116 / var(--tw-bg-opacity)); }
.bg-orange-300 { --tw-bg-opacity: 1; background-color: rgb(251 146 60 / var(--tw-bg-opacity)); }
.bg-orange-400 { --tw-bg-opacity: 1; background-color: rgb(251 146 60 / var(--tw-bg-opacity)); }
.bg-orange-500 { --tw-bg-opacity: 1; background-color: rgb(249 115 22 / var(--tw-bg-opacity)); }
.bg-orange-600 { --tw-bg-opacity: 1; background-color: rgb(234 88 12 / var(--tw-bg-opacity)); }
.bg-orange-700 { --tw-bg-opacity: 1; background-color: rgb(194 65 12 / var(--tw-bg-opacity)); }
.bg-orange-800 { --tw-bg-opacity: 1; background-color: rgb(154 52 18 / var(--tw-bg-opacity)); }
.bg-orange-900 { --tw-bg-opacity: 1; background-color: rgb(124 45 18 / var(--tw-bg-opacity)); }

/* Green Backgrounds */
.bg-green-50 { --tw-bg-opacity: 1; background-color: rgb(240 253 244 / var(--tw-bg-opacity)); }
.bg-green-100 { --tw-bg-opacity: 1; background-color: rgb(220 252 231 / var(--tw-bg-opacity)); }
.bg-green-200 { --tw-bg-opacity: 1; background-color: rgb(187 247 208 / var(--tw-bg-opacity)); }
.bg-green-300 { --tw-bg-opacity: 1; background-color: rgb(134 239 172 / var(--tw-bg-opacity)); }
.bg-green-400 { --tw-bg-opacity: 1; background-color: rgb(74 222 128 / var(--tw-bg-opacity)); }
.bg-green-500 { --tw-bg-opacity: 1; background-color: rgb(34 197 94 / var(--tw-bg-opacity)); }
.bg-green-600 { --tw-bg-opacity: 1; background-color: rgb(22 163 74 / var(--tw-bg-opacity)); }
.bg-green-700 { --tw-bg-opacity: 1; background-color: rgb(21 128 61 / var(--tw-bg-opacity)); }
.bg-green-800 { --tw-bg-opacity: 1; background-color: rgb(22 101 52 / var(--tw-bg-opacity)); }
.bg-green-900 { --tw-bg-opacity: 1; background-color: rgb(20 83 45 / var(--tw-bg-opacity)); }

/* Blue Backgrounds */
.bg-blue-50 { --tw-bg-opacity: 1; background-color: rgb(239 246 255 / var(--tw-bg-opacity)); }
.bg-blue-100 { --tw-bg-opacity: 1; background-color: rgb(219 234 254 / var(--tw-bg-opacity)); }
.bg-blue-200 { --tw-bg-opacity: 1; background-color: rgb(191 219 254 / var(--tw-bg-opacity)); }
.bg-blue-300 { --tw-bg-opacity: 1; background-color: rgb(147 197 253 / var(--tw-bg-opacity)); }
.bg-blue-400 { --tw-bg-opacity: 1; background-color: rgb(96 165 250 / var(--tw-bg-opacity)); }
.bg-blue-500 { --tw-bg-opacity: 1; background-color: rgb(59 130 246 / var(--tw-bg-opacity)); }
.bg-blue-600 { --tw-bg-opacity: 1; background-color: rgb(37 99 235 / var(--tw-bg-opacity)); }
.bg-blue-700 { --tw-bg-opacity: 1; background-color: rgb(29 78 216 / var(--tw-bg-opacity)); }
.bg-blue-800 { --tw-bg-opacity: 1; background-color: rgb(30 64 175 / var(--tw-bg-opacity)); }
.bg-blue-900 { --tw-bg-opacity: 1; background-color: rgb(30 58 138 / var(--tw-bg-opacity)); }

/* Purple Backgrounds */
.bg-purple-50 { --tw-bg-opacity: 1; background-color: rgb(250 245 255 / var(--tw-bg-opacity)); }
.bg-purple-100 { --tw-bg-opacity: 1; background-color: rgb(243 232 255 / var(--tw-bg-opacity)); }
.bg-purple-200 { --tw-bg-opacity: 1; background-color: rgb(233 213 255 / var(--tw-bg-opacity)); }
.bg-purple-300 { --tw-bg-opacity: 1; background-color: rgb(216 180 254 / var(--tw-bg-opacity)); }
.bg-purple-400 { --tw-bg-opacity: 1; background-color: rgb(196 181 253 / var(--tw-bg-opacity)); }
.bg-purple-500 { --tw-bg-opacity: 1; background-color: rgb(168 85 247 / var(--tw-bg-opacity)); }
.bg-purple-600 { --tw-bg-opacity: 1; background-color: rgb(147 51 234 / var(--tw-bg-opacity)); }
.bg-purple-700 { --tw-bg-opacity: 1; background-color: rgb(126 34 206 / var(--tw-bg-opacity)); }
.bg-purple-800 { --tw-bg-opacity: 1; background-color: rgb(107 33 168 / var(--tw-bg-opacity)); }
.bg-purple-900 { --tw-bg-opacity: 1; background-color: rgb(88 28 135 / var(--tw-bg-opacity)); }

/* Islamic Backgrounds */
.bg-islamic-primary { background-color: var(--islamic-primary); }
.bg-islamic-light { background-color: var(--islamic-light); }
.bg-islamic-dark { background-color: var(--islamic-dark); }
.bg-islamic-gold { background-color: var(--islamic-gold); }
.bg-islamic-light-gold { background-color: var(--islamic-light-gold); }
.bg-islamic-light-blue { background-color: var(--islamic-light-blue); }
.bg-islamic-cream { background-color: var(--islamic-cream); }
.bg-islamic-emerald { background-color: var(--islamic-emerald); }
.bg-islamic-sage { background-color: var(--islamic-sage); }
.bg-islamic-mint { background-color: var(--islamic-mint); }
.bg-islamic-secondary { background-color: var(--islamic-secondary); }

/* Additional Background Colors */
.bg-yellow-100 { --tw-bg-opacity: 1; background-color: rgb(254 249 195 / var(--tw-bg-opacity)); }
.bg-yellow-200 { --tw-bg-opacity: 1; background-color: rgb(254 240 138 / var(--tw-bg-opacity)); }
.bg-yellow-300 { --tw-bg-opacity: 1; background-color: rgb(253 224 71 / var(--tw-bg-opacity)); }
.bg-yellow-400 { --tw-bg-opacity: 1; background-color: rgb(250 204 21 / var(--tw-bg-opacity)); }
.bg-yellow-500 { --tw-bg-opacity: 1; background-color: rgb(234 179 8 / var(--tw-bg-opacity)); }
.bg-yellow-600 { --tw-bg-opacity: 1; background-color: rgb(202 138 4 / var(--tw-bg-opacity)); }

.bg-purple-100 { --tw-bg-opacity: 1; background-color: rgb(243 232 255 / var(--tw-bg-opacity)); }
.bg-purple-200 { --tw-bg-opacity: 1; background-color: rgb(233 213 255 / var(--tw-bg-opacity)); }
.bg-purple-300 { --tw-bg-opacity: 1; background-color: rgb(216 180 254 / var(--tw-bg-opacity)); }
.bg-purple-400 { --tw-bg-opacity: 1; background-color: rgb(196 181 253 / var(--tw-bg-opacity)); }
.bg-purple-500 { --tw-bg-opacity: 1; background-color: rgb(168 85 247 / var(--tw-bg-opacity)); }
.bg-purple-600 { --tw-bg-opacity: 1; background-color: rgb(147 51 234 / var(--tw-bg-opacity)); }

.bg-indigo-100 { --tw-bg-opacity: 1; background-color: rgb(224 231 255 / var(--tw-bg-opacity)); }
.bg-indigo-200 { --tw-bg-opacity: 1; background-color: rgb(199 210 254 / var(--tw-bg-opacity)); }
.bg-indigo-300 { --tw-bg-opacity: 1; background-color: rgb(165 180 252 / var(--tw-bg-opacity)); }
.bg-indigo-400 { --tw-bg-opacity: 1; background-color: rgb(129 140 248 / var(--tw-bg-opacity)); }
.bg-indigo-500 { --tw-bg-opacity: 1; background-color: rgb(99 102 241 / var(--tw-bg-opacity)); }
.bg-indigo-600 { --tw-bg-opacity: 1; background-color: rgb(79 70 229 / var(--tw-bg-opacity)); }

/* Additional Missing Colors */
.bg-teal-50 { --tw-bg-opacity: 1; background-color: rgb(240 253 250 / var(--tw-bg-opacity)); }
.bg-teal-100 { --tw-bg-opacity: 1; background-color: rgb(204 251 241 / var(--tw-bg-opacity)); }
.bg-teal-200 { --tw-bg-opacity: 1; background-color: rgb(153 246 228 / var(--tw-bg-opacity)); }
.bg-teal-300 { --tw-bg-opacity: 1; background-color: rgb(94 234 212 / var(--tw-bg-opacity)); }
.bg-teal-400 { --tw-bg-opacity: 1; background-color: rgb(45 212 191 / var(--tw-bg-opacity)); }
.bg-teal-500 { --tw-bg-opacity: 1; background-color: rgb(20 184 166 / var(--tw-bg-opacity)); }
.bg-teal-600 { --tw-bg-opacity: 1; background-color: rgb(13 148 136 / var(--tw-bg-opacity)); }

.bg-cyan-50 { --tw-bg-opacity: 1; background-color: rgb(236 254 255 / var(--tw-bg-opacity)); }
.bg-cyan-100 { --tw-bg-opacity: 1; background-color: rgb(207 250 254 / var(--tw-bg-opacity)); }
.bg-cyan-200 { --tw-bg-opacity: 1; background-color: rgb(165 243 252 / var(--tw-bg-opacity)); }
.bg-cyan-300 { --tw-bg-opacity: 1; background-color: rgb(103 232 249 / var(--tw-bg-opacity)); }
.bg-cyan-400 { --tw-bg-opacity: 1; background-color: rgb(34 211 238 / var(--tw-bg-opacity)); }
.bg-cyan-500 { --tw-bg-opacity: 1; background-color: rgb(6 182 212 / var(--tw-bg-opacity)); }
.bg-cyan-600 { --tw-bg-opacity: 1; background-color: rgb(8 145 178 / var(--tw-bg-opacity)); }

.bg-emerald-50 { --tw-bg-opacity: 1; background-color: rgb(236 253 245 / var(--tw-bg-opacity)); }
.bg-emerald-100 { --tw-bg-opacity: 1; background-color: rgb(209 250 229 / var(--tw-bg-opacity)); }
.bg-emerald-200 { --tw-bg-opacity: 1; background-color: rgb(167 243 208 / var(--tw-bg-opacity)); }
.bg-emerald-300 { --tw-bg-opacity: 1; background-color: rgb(110 231 183 / var(--tw-bg-opacity)); }
.bg-emerald-400 { --tw-bg-opacity: 1; background-color: rgb(52 211 153 / var(--tw-bg-opacity)); }
.bg-emerald-500 { --tw-bg-opacity: 1; background-color: rgb(16 185 129 / var(--tw-bg-opacity)); }
.bg-emerald-600 { --tw-bg-opacity: 1; background-color: rgb(5 150 105 / var(--tw-bg-opacity)); }

.bg-violet-50 { --tw-bg-opacity: 1; background-color: rgb(245 243 255 / var(--tw-bg-opacity)); }
.bg-violet-100 { --tw-bg-opacity: 1; background-color: rgb(237 233 254 / var(--tw-bg-opacity)); }
.bg-violet-200 { --tw-bg-opacity: 1; background-color: rgb(221 214 254 / var(--tw-bg-opacity)); }
.bg-violet-300 { --tw-bg-opacity: 1; background-color: rgb(196 181 253 / var(--tw-bg-opacity)); }
.bg-violet-400 { --tw-bg-opacity: 1; background-color: rgb(167 139 250 / var(--tw-bg-opacity)); }
.bg-violet-500 { --tw-bg-opacity: 1; background-color: rgb(139 92 246 / var(--tw-bg-opacity)); }
.bg-violet-600 { --tw-bg-opacity: 1; background-color: rgb(124 58 237 / var(--tw-bg-opacity)); }

.bg-pink-50 { --tw-bg-opacity: 1; background-color: rgb(253 242 248 / var(--tw-bg-opacity)); }
.bg-pink-100 { --tw-bg-opacity: 1; background-color: rgb(252 231 243 / var(--tw-bg-opacity)); }
.bg-pink-200 { --tw-bg-opacity: 1; background-color: rgb(251 207 232 / var(--tw-bg-opacity)); }
.bg-pink-300 { --tw-bg-opacity: 1; background-color: rgb(249 168 212 / var(--tw-bg-opacity)); }
.bg-pink-400 { --tw-bg-opacity: 1; background-color: rgb(244 114 182 / var(--tw-bg-opacity)); }
.bg-pink-500 { --tw-bg-opacity: 1; background-color: rgb(236 72 153 / var(--tw-bg-opacity)); }
.bg-pink-600 { --tw-bg-opacity: 1; background-color: rgb(219 39 119 / var(--tw-bg-opacity)); }

.bg-rose-50 { --tw-bg-opacity: 1; background-color: rgb(255 241 242 / var(--tw-bg-opacity)); }
.bg-rose-100 { --tw-bg-opacity: 1; background-color: rgb(255 228 230 / var(--tw-bg-opacity)); }
.bg-rose-200 { --tw-bg-opacity: 1; background-color: rgb(254 205 211 / var(--tw-bg-opacity)); }
.bg-rose-300 { --tw-bg-opacity: 1; background-color: rgb(253 164 175 / var(--tw-bg-opacity)); }
.bg-rose-400 { --tw-bg-opacity: 1; background-color: rgb(251 113 133 / var(--tw-bg-opacity)); }
.bg-rose-500 { --tw-bg-opacity: 1; background-color: rgb(244 63 94 / var(--tw-bg-opacity)); }
.bg-rose-600 { --tw-bg-opacity: 1; background-color: rgb(225 29 72 / var(--tw-bg-opacity)); }

.bg-amber-50 { --tw-bg-opacity: 1; background-color: rgb(255 251 235 / var(--tw-bg-opacity)); }
.bg-amber-100 { --tw-bg-opacity: 1; background-color: rgb(254 243 199 / var(--tw-bg-opacity)); }
.bg-amber-200 { --tw-bg-opacity: 1; background-color: rgb(253 230 138 / var(--tw-bg-opacity)); }
.bg-amber-300 { --tw-bg-opacity: 1; background-color: rgb(252 211 77 / var(--tw-bg-opacity)); }
.bg-amber-400 { --tw-bg-opacity: 1; background-color: rgb(251 191 36 / var(--tw-bg-opacity)); }
.bg-amber-500 { --tw-bg-opacity: 1; background-color: rgb(245 158 11 / var(--tw-bg-opacity)); }
.bg-amber-600 { --tw-bg-opacity: 1; background-color: rgb(217 119 6 / var(--tw-bg-opacity)); }

/* Background Gradients */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-l { background-image: linear-gradient(to left, var(--tw-gradient-stops)); }
.bg-gradient-to-t { background-image: linear-gradient(to top, var(--tw-gradient-stops)); }
.bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }
.bg-gradient-to-tr { background-image: linear-gradient(to top right, var(--tw-gradient-stops)); }
.bg-gradient-to-tl { background-image: linear-gradient(to top left, var(--tw-gradient-stops)); }
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.bg-gradient-to-bl { background-image: linear-gradient(to bottom left, var(--tw-gradient-stops)); }

.from-gray-50 { --tw-gradient-from: #f9fafb; --tw-gradient-to: rgb(249 250 251 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-islamic-primary { --tw-gradient-from: var(--islamic-primary); --tw-gradient-to: rgb(45 80 22 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-emerald-600 { --tw-gradient-from: #059669; --tw-gradient-to: rgb(5 150 105 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }

/* Additional Gradient From Colors */
.from-orange-500 { --tw-gradient-from: #f97316; --tw-gradient-to: rgb(249 115 22 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-red-500 { --tw-gradient-from: #ef4444; --tw-gradient-to: rgb(239 68 68 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-purple-500 { --tw-gradient-from: #a855f7; --tw-gradient-to: rgb(168 85 247 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-violet-600 { --tw-gradient-from: #7c3aed; --tw-gradient-to: rgb(124 58 237 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-green-500 { --tw-gradient-from: #22c55e; --tw-gradient-to: rgb(34 197 94 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-emerald-500 { --tw-gradient-from: #10b981; --tw-gradient-to: rgb(16 185 129 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-blue-500 { --tw-gradient-from: #3b82f6; --tw-gradient-to: rgb(59 130 246 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-indigo-500 { --tw-gradient-from: #6366f1; --tw-gradient-to: rgb(99 102 241 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-indigo-600 { --tw-gradient-from: #4f46e5; --tw-gradient-to: rgb(79 70 229 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-yellow-500 { --tw-gradient-from: #eab308; --tw-gradient-to: rgb(234 179 8 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-orange-500 { --tw-gradient-from: #f97316; --tw-gradient-to: rgb(249 115 22 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-pink-500 { --tw-gradient-from: #ec4899; --tw-gradient-to: rgb(236 72 153 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-rose-600 { --tw-gradient-from: #e11d48; --tw-gradient-to: rgb(225 29 72 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-teal-500 { --tw-gradient-from: #14b8a6; --tw-gradient-to: rgb(20 184 166 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-cyan-600 { --tw-gradient-from: #0891b2; --tw-gradient-to: rgb(8 145 178 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-amber-500 { --tw-gradient-from: #f59e0b; --tw-gradient-to: rgb(245 158 11 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.from-yellow-600 { --tw-gradient-from: #ca8a04; --tw-gradient-to: rgb(202 138 4 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }

/* Additional Gradient To Colors */
.to-islamic-mint { --tw-gradient-to: var(--islamic-mint); }
.to-emerald-600 { --tw-gradient-to: #059669; }
.to-red-500 { --tw-gradient-to: #ef4444; }
.to-pink-600 { --tw-gradient-to: #db2777; }
.to-violet-600 { --tw-gradient-to: #7c3aed; }
.to-emerald-600 { --tw-gradient-to: #059669; }
.to-indigo-600 { --tw-gradient-to: #4f46e5; }
.to-orange-500 { --tw-gradient-to: #f97316; }
.to-purple-600 { --tw-gradient-to: #9333ea; }
.to-cyan-600 { --tw-gradient-to: #0891b2; }
.to-green-600 { --tw-gradient-to: #16a34a; }
.to-yellow-600 { --tw-gradient-to: #ca8a04; }

/* Background Opacity */
.bg-opacity-10 { --tw-bg-opacity: 0.1; }
.bg-opacity-20 { --tw-bg-opacity: 0.2; }
.bg-opacity-30 { --tw-bg-opacity: 0.3; }
.bg-opacity-40 { --tw-bg-opacity: 0.4; }
.bg-opacity-50 { --tw-bg-opacity: 0.5; }
.bg-opacity-60 { --tw-bg-opacity: 0.6; }
.bg-opacity-70 { --tw-bg-opacity: 0.7; }
.bg-opacity-80 { --tw-bg-opacity: 0.8; }
.bg-opacity-90 { --tw-bg-opacity: 0.9; }
.bg-opacity-100 { --tw-bg-opacity: 1; }

/* ========================================
   ✏️ الخطوط (Typography)
======================================== */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

.italic { font-style: italic; }
.not-italic { font-style: normal; }

.leading-3 { line-height: .75rem; }
.leading-4 { line-height: 1rem; }
.leading-5 { line-height: 1.25rem; }
.leading-6 { line-height: 1.5rem; }
.leading-7 { line-height: 1.75rem; }
.leading-8 { line-height: 2rem; }
.leading-9 { line-height: 2.25rem; }
.leading-10 { line-height: 2.5rem; }
.leading-none { line-height: 1; }
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

.underline { text-decoration-line: underline; }
.overline { text-decoration-line: overline; }
.line-through { text-decoration-line: line-through; }
.no-underline { text-decoration-line: none; }

/* ========================================
   🔲 الحدود (Borders)
======================================== */
.border-0 { border-width: 0px; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }
.border-8 { border-width: 8px; }

.border-t-0 { border-top-width: 0px; }
.border-t { border-top-width: 1px; }
.border-t-2 { border-top-width: 2px; }
.border-t-4 { border-top-width: 4px; }

.border-r-0 { border-right-width: 0px; }
.border-r { border-right-width: 1px; }
.border-r-2 { border-right-width: 2px; }
.border-r-4 { border-right-width: 4px; }

.border-b-0 { border-bottom-width: 0px; }
.border-b { border-bottom-width: 1px; }
.border-b-2 { border-bottom-width: 2px; }
.border-b-4 { border-bottom-width: 4px; }

.border-l-0 { border-left-width: 0px; }
.border-l { border-left-width: 1px; }
.border-l-2 { border-left-width: 2px; }
.border-l-4 { border-left-width: 4px; }

.border-solid { border-style: solid; }
.border-dashed { border-style: dashed; }
.border-dotted { border-style: dotted; }
.border-double { border-style: double; }
.border-none { border-style: none; }

.border-transparent { border-color: transparent; }
.border-current { border-color: currentColor; }
.border-black { --tw-border-opacity: 1; border-color: rgb(0 0 0 / var(--tw-border-opacity)); }
.border-white { --tw-border-opacity: 1; border-color: rgb(255 255 255 / var(--tw-border-opacity)); }
.border-gray-200 { --tw-border-opacity: 1; border-color: rgb(229 231 235 / var(--tw-border-opacity)); }
.border-gray-300 { --tw-border-opacity: 1; border-color: rgb(209 213 219 / var(--tw-border-opacity)); }
.border-gray-400 { --tw-border-opacity: 1; border-color: rgb(156 163 175 / var(--tw-border-opacity)); }
.border-red-400 { --tw-border-opacity: 1; border-color: rgb(248 113 113 / var(--tw-border-opacity)); }
.border-green-400 { --tw-border-opacity: 1; border-color: rgb(74 222 128 / var(--tw-border-opacity)); }
.border-blue-400 { --tw-border-opacity: 1; border-color: rgb(96 165 250 / var(--tw-border-opacity)); }
.border-yellow-400 { --tw-border-opacity: 1; border-color: rgb(250 204 21 / var(--tw-border-opacity)); }
.border-islamic-primary { border-color: var(--islamic-primary); }
.border-islamic-gold { border-color: var(--islamic-gold); }
.border-islamic-accent { border-color: var(--islamic-gold); }
.border-islamic-light { border-color: var(--islamic-light); }
.border-islamic-dark { border-color: var(--islamic-dark); }
.border-islamic-secondary { border-color: var(--islamic-secondary); }

/* Additional Border Colors */
.border-purple-400 { --tw-border-opacity: 1; border-color: rgb(196 181 253 / var(--tw-border-opacity)); }
.border-purple-500 { --tw-border-opacity: 1; border-color: rgb(168 85 247 / var(--tw-border-opacity)); }
.border-indigo-400 { --tw-border-opacity: 1; border-color: rgb(129 140 248 / var(--tw-border-opacity)); }
.border-indigo-500 { --tw-border-opacity: 1; border-color: rgb(99 102 241 / var(--tw-border-opacity)); }

.border-opacity-20 { --tw-border-opacity: 0.2; }
.border-opacity-30 { --tw-border-opacity: 0.3; }
.border-opacity-40 { --tw-border-opacity: 0.4; }
.border-opacity-50 { --tw-border-opacity: 0.5; }

.rounded-none { border-radius: 0px; }
.rounded-sm { border-radius: 0.125rem; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-full { border-radius: 9999px; }

.rounded-t-none { border-top-left-radius: 0px; border-top-right-radius: 0px; }
.rounded-t-sm { border-top-left-radius: 0.125rem; border-top-right-radius: 0.125rem; }
.rounded-t { border-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem; }
.rounded-t-md { border-top-left-radius: 0.375rem; border-top-right-radius: 0.375rem; }
.rounded-t-lg { border-top-left-radius: 0.5rem; border-top-right-radius: 0.5rem; }
.rounded-t-xl { border-top-left-radius: 0.75rem; border-top-right-radius: 0.75rem; }
.rounded-t-2xl { border-top-left-radius: 1rem; border-top-right-radius: 1rem; }
.rounded-t-3xl { border-top-left-radius: 1.5rem; border-top-right-radius: 1.5rem; }
.rounded-t-full { border-top-left-radius: 9999px; border-top-right-radius: 9999px; }

/* ========================================
   🌟 الظلال والتأثيرات (Shadows & Effects)
======================================== */
.shadow-sm { --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }
.shadow { --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }
.shadow-md { --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }
.shadow-lg { --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }
.shadow-xl { --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }
.shadow-2xl { --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }
.shadow-inner { --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05); --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }
.shadow-none { --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }

.opacity-0 { opacity: 0; }
.opacity-5 { opacity: 0.05; }
.opacity-10 { opacity: 0.1; }
.opacity-20 { opacity: 0.2; }
.opacity-25 { opacity: 0.25; }
.opacity-30 { opacity: 0.3; }
.opacity-40 { opacity: 0.4; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-70 { opacity: 0.7; }
.opacity-75 { opacity: 0.75; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }
.opacity-95 { opacity: 0.95; }
.opacity-100 { opacity: 1; }

.backdrop-blur-sm { --tw-backdrop-blur: blur(4px); -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia); backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia); }

/* ========================================
   🔄 الانتقالات والحركة (Transitions & Animations)
======================================== */
.transition-none { transition-property: none; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-opacity { transition-property: opacity; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-shadow { transition-property: box-shadow; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.duration-700 { transition-duration: 700ms; }
.duration-1000 { transition-duration: 1000ms; }

.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

.scale-0 { --tw-scale-x: 0; --tw-scale-y: 0; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-50 { --tw-scale-x: .5; --tw-scale-y: .5; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-75 { --tw-scale-x: .75; --tw-scale-y: .75; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-90 { --tw-scale-x: .9; --tw-scale-y: .9; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-95 { --tw-scale-x: .95; --tw-scale-y: .95; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-100 { --tw-scale-x: 1; --tw-scale-y: 1; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-110 { --tw-scale-x: 1.1; --tw-scale-y: 1.1; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-125 { --tw-scale-x: 1.25; --tw-scale-y: 1.25; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-150 { --tw-scale-x: 1.5; --tw-scale-y: 1.5; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.transform-gpu { transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.transform-none { transform: none; }

.-translate-y-1 { --tw-translate-y: -0.25rem; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.-translate-y-2 { --tw-translate-y: -0.5rem; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.translate-y-0 { --tw-translate-y: 0px; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.-translate-x-100 { --tw-translate-x: -100%; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.translate-x-100 { --tw-translate-x: 100%; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* ========================================
   🎬 الحركات (Animations)
======================================== */
.animate-none { animation: none; }
.animate-spin { animation: spin 1s linear infinite; }
.animate-ping { animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-bounce { animation: bounce 1s infinite; }

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

/* Custom Islamic Animations */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ========================================
   📱 التجاوب (Responsive Design)
======================================== */
/* Small screens (sm) - 640px and up */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:hidden { display: none; }
  .sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .sm\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:py-4 { padding-top: 1rem; padding-bottom: 1rem; }
}

/* Medium screens (md) - 768px and up */
@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .md\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .md\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .md\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .md\:py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
}

/* Large screens (lg) - 1024px and up */
@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:hidden { display: none; }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .lg\:mr-64 { margin-right: 16rem; }
  .lg\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .lg\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .lg\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
}

/* Extra large screens (xl) - 1280px and up */
@media (min-width: 1280px) {
  .xl\:block { display: block; }
  .xl\:flex { display: flex; }
  .xl\:hidden { display: none; }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .xl\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .xl\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .xl\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
}

/* ========================================
   🎯 حالات التفاعل (Interactive States)
======================================== */
.hover\:bg-gray-50:hover { --tw-bg-opacity: 1; background-color: rgb(249 250 251 / var(--tw-bg-opacity)); }
.hover\:bg-gray-100:hover { --tw-bg-opacity: 1; background-color: rgb(243 244 246 / var(--tw-bg-opacity)); }
.hover\:bg-blue-50:hover { --tw-bg-opacity: 1; background-color: rgb(239 246 255 / var(--tw-bg-opacity)); }
.hover\:bg-green-50:hover { --tw-bg-opacity: 1; background-color: rgb(240 253 244 / var(--tw-bg-opacity)); }
.hover\:bg-red-50:hover { --tw-bg-opacity: 1; background-color: rgb(254 242 242 / var(--tw-bg-opacity)); }
.hover\:bg-islamic-dark:hover { background-color: var(--islamic-dark); }

.hover\:text-gray-300:hover { --tw-text-opacity: 1; color: rgb(209 213 219 / var(--tw-text-opacity)); }
.hover\:text-gray-600:hover { --tw-text-opacity: 1; color: rgb(75 85 99 / var(--tw-text-opacity)); }
.hover\:text-gray-900:hover { --tw-text-opacity: 1; color: rgb(17 24 39 / var(--tw-text-opacity)); }
.hover\:text-blue-600:hover { --tw-text-opacity: 1; color: rgb(37 99 235 / var(--tw-text-opacity)); }
.hover\:text-green-600:hover { --tw-text-opacity: 1; color: rgb(22 163 74 / var(--tw-text-opacity)); }
.hover\:text-red-600:hover { --tw-text-opacity: 1; color: rgb(220 38 38 / var(--tw-text-opacity)); }

.hover\:shadow-md:hover { --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }
.hover\:shadow-lg:hover { --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow); }

.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.hover\:scale-110:hover { --tw-scale-x: 1.1; --tw-scale-y: 1.1; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.hover\:-translate-y-1:hover { --tw-translate-y: -0.25rem; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.hover\:bg-opacity-30:hover { --tw-bg-opacity: 0.3; }

.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:ring-blue-500:focus { --tw-ring-opacity: 1; --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity)); }

.group:hover .group-hover\:scale-110 { --tw-scale-x: 1.1; --tw-scale-y: 1.1; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* ========================================
   🔧 فئات مساعدة (Utility Classes)
======================================== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

.select-none { -webkit-user-select: none; -moz-user-select: none; user-select: none; }
.select-text { -webkit-user-select: text; -moz-user-select: text; user-select: text; }
.select-all { -webkit-user-select: all; -moz-user-select: all; user-select: all; }
.select-auto { -webkit-user-select: auto; -moz-user-select: auto; user-select: auto; }

.resize-none { resize: none; }
.resize-y { resize: vertical; }
.resize-x { resize: horizontal; }
.resize { resize: both; }

.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

.break-normal { overflow-wrap: normal; word-break: normal; }
.break-words { overflow-wrap: break-word; }
.break-all { word-break: break-all; }

/* ========================================
   🎨 نظام الخطوط الموحد للمشروع الإسلامي
======================================== */

/* تعريف الخطوط الأساسية */
:root {
  /* الخطوط العربية */
  --font-arabic-primary: 'Cairo', 'Amiri', 'Traditional Arabic', sans-serif;
  --font-arabic-serif: 'Amiri', 'Scheherazade', 'Traditional Arabic', serif;
  --font-arabic-modern: 'Cairo', 'Tajawal', 'Noto Sans Arabic', sans-serif;

  /* الخطوط الإنجليزية */
  --font-english-primary: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  --font-english-serif: 'Georgia', 'Times New Roman', serif;
  --font-english-mono: 'Fira Code', 'Monaco', 'Consolas', monospace;

  /* الخطوط المختلطة (عربي/إنجليزي) */
  --font-mixed: 'Cairo', 'Inter', 'Segoe UI', sans-serif;
}

/* الخطوط الأساسية للتطبيق */
html, body {
  font-family: var(--font-mixed);
  direction: rtl;
  text-align: right;
}

/* فئات الخطوط المخصصة */
.font-arabic {
  font-family: var(--font-arabic-primary);
  direction: rtl;
  text-align: right;
}

.font-arabic-serif {
  font-family: var(--font-arabic-serif);
  direction: rtl;
  text-align: right;
}

.font-arabic-modern {
  font-family: var(--font-arabic-modern);
  direction: rtl;
  text-align: right;
}

.font-english {
  font-family: var(--font-english-primary);
  direction: ltr;
  text-align: left;
}

.font-english-serif {
  font-family: var(--font-english-serif);
  direction: ltr;
  text-align: left;
}

.font-mixed {
  font-family: var(--font-mixed);
}

/* النصوص العربية التراثية */
.arabic-text {
  font-family: var(--font-arabic-serif);
  direction: rtl;
  text-align: right;
  line-height: 1.8;
  font-weight: 400;
}

.arabic-text-modern {
  font-family: var(--font-arabic-modern);
  direction: rtl;
  text-align: right;
  line-height: 1.6;
}

/* النصوص القرآنية */
.quran-text {
  font-family: var(--font-arabic-serif);
  direction: rtl;
  text-align: center;
  line-height: 2;
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--islamic-primary);
}

/* العناوين */
.heading-arabic {
  font-family: var(--font-arabic-primary);
  direction: rtl;
  text-align: right;
  font-weight: 700;
}

.heading-english {
  font-family: var(--font-english-primary);
  direction: ltr;
  text-align: left;
  font-weight: 700;
}

/* النصوص المختلطة */
.text-mixed {
  font-family: var(--font-mixed);
  line-height: 1.6;
}

/* أحجام الخطوط المخصصة */
.text-xs-arabic { font-size: 0.8rem; line-height: 1.4; }
.text-sm-arabic { font-size: 0.9rem; line-height: 1.5; }
.text-base-arabic { font-size: 1rem; line-height: 1.6; }
.text-lg-arabic { font-size: 1.125rem; line-height: 1.7; }
.text-xl-arabic { font-size: 1.25rem; line-height: 1.8; }
.text-2xl-arabic { font-size: 1.5rem; line-height: 1.8; }
.text-3xl-arabic { font-size: 1.875rem; line-height: 1.9; }
.text-4xl-arabic { font-size: 2.25rem; line-height: 2; }

/* ========================================
   🎨 فئات Tailwind للخطوط
======================================== */

/* Font Family Classes */
.font-sans { font-family: var(--font-mixed); }
.font-serif { font-family: var(--font-arabic-serif); }
.font-mono { font-family: var(--font-english-mono); }

/* Custom Font Family Classes */
.font-cairo { font-family: 'Cairo', sans-serif; }
.font-amiri { font-family: 'Amiri', serif; }
.font-inter { font-family: 'Inter', sans-serif; }
.font-roboto { font-family: 'Roboto', sans-serif; }

/* Font Weight Classes */
.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* Font Size Classes */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }
.text-7xl { font-size: 4.5rem; line-height: 1; }
.text-8xl { font-size: 6rem; line-height: 1; }
.text-9xl { font-size: 8rem; line-height: 1; }

/* Line Height Classes */
.leading-3 { line-height: .75rem; }
.leading-4 { line-height: 1rem; }
.leading-5 { line-height: 1.25rem; }
.leading-6 { line-height: 1.5rem; }
.leading-7 { line-height: 1.75rem; }
.leading-8 { line-height: 2rem; }
.leading-9 { line-height: 2.25rem; }
.leading-10 { line-height: 2.5rem; }
.leading-none { line-height: 1; }
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

/* Letter Spacing Classes */
.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0em; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

/* Text Transform Classes */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* Text Decoration Classes */
.underline { text-decoration-line: underline; }
.overline { text-decoration-line: overline; }
.line-through { text-decoration-line: line-through; }
.no-underline { text-decoration-line: none; }

/* Text Decoration Style Classes */
.decoration-solid { text-decoration-style: solid; }
.decoration-double { text-decoration-style: double; }
.decoration-dotted { text-decoration-style: dotted; }
.decoration-dashed { text-decoration-style: dashed; }
.decoration-wavy { text-decoration-style: wavy; }

/* Text Decoration Thickness Classes */
.decoration-auto { text-decoration-thickness: auto; }
.decoration-from-font { text-decoration-thickness: from-font; }
.decoration-0 { text-decoration-thickness: 0px; }
.decoration-1 { text-decoration-thickness: 1px; }
.decoration-2 { text-decoration-thickness: 2px; }
.decoration-4 { text-decoration-thickness: 4px; }
.decoration-8 { text-decoration-thickness: 8px; }

/* Text Underline Offset Classes */
.underline-offset-auto { text-underline-offset: auto; }
.underline-offset-0 { text-underline-offset: 0px; }
.underline-offset-1 { text-underline-offset: 1px; }
.underline-offset-2 { text-underline-offset: 2px; }
.underline-offset-4 { text-underline-offset: 4px; }
.underline-offset-8 { text-underline-offset: 8px; }

.islamic-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.enhanced-card {
  background: linear-gradient(135deg, var(--islamic-primary) 0%, var(--islamic-light) 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-medium);
  transition: var(--transition-medium);
  overflow: hidden;
  position: relative;
}

.enhanced-card-header {
  padding: 2rem;
  background: var(--gradient-primary);
  color: white;
}

.enhanced-card-body {
  padding: 2rem;
  background: white;
}

.stats-card {
  background: white;
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-light);
  transition: var(--transition-fast);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.stats-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold));
}

.stats-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: bold;
  position: relative;
  overflow: hidden;
}

.stats-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.1;
  border-radius: 50%;
}

.islamic-content-card {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-strong);
  overflow: hidden;
  position: relative;
}

/* Enhanced Gradient Backgrounds for Stats Cards */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* إصلاح التدرجات المشكلة - إزالة الألوان الفاتحة جداً */
.bg-gradient-to-br.from-gray-50 {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
}

.bg-gradient-to-br.from-white {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%) !important;
}

/* تدرجات محسنة للبطاقات */
.bg-gradient-orange-red {
  background: linear-gradient(135deg, #f97316 0%, #ef4444 100%);
  color: white;
}

.bg-gradient-purple-violet {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
  color: white;
}

.bg-gradient-green-emerald {
  background: linear-gradient(135deg, #22c55e 0%, #059669 100%);
  color: white;
}

.bg-gradient-blue-indigo {
  background: linear-gradient(135deg, #22c55e 0%, #059669 100%);
  color: white;
}

.bg-gradient-red-pink {
  background: linear-gradient(135deg, #ef4444 0%, #db2777 100%);
  color: white;
}

.bg-gradient-yellow-orange {
  background: linear-gradient(135deg, #eab308 0%, #f97316 100%);
  color: white;
}

.bg-gradient-indigo-purple {
  background: linear-gradient(135deg, #6366f1 0%, #9333ea 100%);
  color: white;
}

.bg-gradient-teal-cyan {
  background: linear-gradient(135deg, #14b8a6 0%, #0891b2 100%);
  color: white;
}

.bg-gradient-pink-rose {
  background: linear-gradient(135deg, #ec4899 0%, #e11d48 100%);
  color: white;
}

.bg-gradient-amber-yellow {
  background: linear-gradient(135deg, #f59e0b 0%, #ca8a04 100%);
  color: white;
}

.bg-gradient-violet-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #9333ea 100%);
  color: white;
}

.bg-gradient-emerald-green {
  background: linear-gradient(135deg, #10b981 0%, #16a34a 100%);
  color: white;
}

/* تدرجات خاصة للبطاقات الفاتحة */
.bg-gradient-light-blue {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
}

.bg-gradient-light-green {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
}

.bg-gradient-light-purple {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  color: #7c2d12;
}

.bg-gradient-light-orange {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
  color: #9a3412;
}

.bg-gradient-light-red {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  color: #991b1b;
}

.bg-gradient-light-yellow {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
}

.bg-gradient-light-indigo {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  color: #3730a3;
}

.bg-gradient-light-pink {
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  color: #be185d;
}

.bg-gradient-light-teal {
  background: linear-gradient(135deg, #ccfbf1 0%, #99f6e4 100%);
  color: #134e4a;
}

.bg-gradient-light-cyan {
  background: linear-gradient(135deg, #cffafe 0%, #a5f3fc 100%);
  color: #155e75;
}

/* ========================================
   🎨 إصلاحات البطاقات والتدرجات المشكلة
======================================== */

/* إصلاح البطاقات التي تستخدم تدرجات فاتحة */
.stats-card .bg-gradient-to-br.from-blue-50,
.stats-card .bg-gradient-to-br.from-green-50,
.stats-card .bg-gradient-to-br.from-purple-50,
.stats-card .bg-gradient-to-br.from-orange-50,
.stats-card .bg-gradient-to-br.from-red-50,
.stats-card .bg-gradient-to-br.from-yellow-50,
.stats-card .bg-gradient-to-br.from-indigo-50,
.stats-card .bg-gradient-to-br.from-pink-50,
.stats-card .bg-gradient-to-br.from-teal-50,
.stats-card .bg-gradient-to-br.from-cyan-50 {
  background: white !important;
  border: 2px solid;
  position: relative;
  overflow: hidden;
}

/* إضافة خط علوي ملون للبطاقات */
.stats-card .bg-gradient-to-br.from-blue-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stats-card .bg-gradient-to-br.from-green-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #22c55e, #15803d);
}

.stats-card .bg-gradient-to-br.from-purple-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #a855f7, #7c3aed);
}

.stats-card .bg-gradient-to-br.from-orange-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f97316, #ea580c);
}

.stats-card .bg-gradient-to-br.from-red-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.stats-card .bg-gradient-to-br.from-yellow-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #eab308, #ca8a04);
}

.stats-card .bg-gradient-to-br.from-indigo-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6366f1, #4f46e5);
}

.stats-card .bg-gradient-to-br.from-pink-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ec4899, #db2777);
}

.stats-card .bg-gradient-to-br.from-teal-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #14b8a6, #0d9488);
}

.stats-card .bg-gradient-to-br.from-cyan-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #06b6d4, #0891b2);
}

/* إصلاح ألوان الحدود للبطاقات */
.stats-card .bg-gradient-to-br.from-blue-50 { border-color: #3b82f6; }
.stats-card .bg-gradient-to-br.from-green-50 { border-color: #22c55e; }
.stats-card .bg-gradient-to-br.from-purple-50 { border-color: #a855f7; }
.stats-card .bg-gradient-to-br.from-orange-50 { border-color: #f97316; }
.stats-card .bg-gradient-to-br.from-red-50 { border-color: #ef4444; }
.stats-card .bg-gradient-to-br.from-yellow-50 { border-color: #eab308; }
.stats-card .bg-gradient-to-br.from-indigo-50 { border-color: #6366f1; }
.stats-card .bg-gradient-to-br.from-pink-50 { border-color: #ec4899; }
.stats-card .bg-gradient-to-br.from-teal-50 { border-color: #14b8a6; }
.stats-card .bg-gradient-to-br.from-cyan-50 { border-color: #06b6d4; }

/* إصلاح التباين للنصوص */
.stats-card .bg-gradient-to-br.from-blue-50 * { color: #1e40af !important; }
.stats-card .bg-gradient-to-br.from-green-50 * { color: #166534 !important; }
.stats-card .bg-gradient-to-br.from-purple-50 * { color: #7c2d12 !important; }
.stats-card .bg-gradient-to-br.from-orange-50 * { color: #9a3412 !important; }
.stats-card .bg-gradient-to-br.from-red-50 * { color: #991b1b !important; }
.stats-card .bg-gradient-to-br.from-yellow-50 * { color: #92400e !important; }
.stats-card .bg-gradient-to-br.from-indigo-50 * { color: #3730a3 !important; }
.stats-card .bg-gradient-to-br.from-pink-50 * { color: #be185d !important; }
.stats-card .bg-gradient-to-br.from-teal-50 * { color: #134e4a !important; }
.stats-card .bg-gradient-to-br.from-cyan-50 * { color: #155e75 !important; }

/* ========================================
   🎨 إصلاحات شاملة للمشاكل المكتشفة
======================================== */

/* إصلاح مشاكل التجاوب للبطاقات */
@media (max-width: 768px) {
  .stats-card {
    padding: 1rem !important;
  }

  .stats-icon {
    width: 2.5rem !important;
    height: 2.5rem !important;
    font-size: 1rem !important;
  }

  .enhanced-card-header {
    padding: 1.5rem !important;
  }

  .enhanced-card-body {
    padding: 1.5rem !important;
  }

  .grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    gap: 1rem !important;
  }

  .grid.grid-cols-1.lg\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }

  .grid.grid-cols-1.lg\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
}

@media (max-width: 640px) {
  .enhanced-card-header h1 {
    font-size: 1.5rem !important;
  }

  .enhanced-card-header p {
    font-size: 0.875rem !important;
  }

  .stats-card .text-3xl {
    font-size: 1.875rem !important;
  }

  .islamic-content-card .text-2xl {
    font-size: 1.25rem !important;
  }

  .islamic-content-card .text-xl {
    font-size: 1.125rem !important;
  }
}

/* إصلاح مشاكل الأيقونات والتباين */
.stats-icon i {
  font-size: inherit !important;
  color: inherit !important;
}

.bg-gradient-to-br.from-blue-500.to-indigo-600 {
  background: linear-gradient(135deg, #3b82f6 0%, #4f46e5 100%) !important;
  color: white !important;
}

.bg-gradient-to-br.from-green-500.to-emerald-600 {
  background: linear-gradient(135deg, #22c55e 0%, #059669 100%) !important;
  color: white !important;
}

.bg-gradient-to-br.from-purple-500.to-violet-600 {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%) !important;
  color: white !important;
}

.bg-gradient-to-br.from-yellow-500.to-orange-500 {
  background: linear-gradient(135deg, #eab308 0%, #f97316 100%) !important;
  color: white !important;
}

/* إصلاح البطاقات الإسلامية */
.islamic-content-card {
  position: relative;
  overflow: hidden;
}

.islamic-content-card .islamic-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-repeat: repeat;
}

/* إصلاح الشارات والعلامات */
.badge-info-enhanced {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-block;
}

.badge-success-enhanced {
  background: linear-gradient(135deg, #22c55e, #15803d);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-block;
}

.badge-warning-enhanced {
  background: linear-gradient(135deg, #eab308, #ca8a04);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-block;
}

/* إصلاح شريط التقدم */
.progress-enhanced {
  width: 100%;
  height: 0.5rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar-enhanced {
  height: 100%;
  background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold));
  border-radius: 9999px;
  transition: width 0.5s ease-in-out;
  position: relative;
}

.progress-bar-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ========================================
   🎨 إصلاحات إضافية للمشاكل المتبقية
======================================== */

/* إصلاح مشاكل الحصص المباشرة */
.bg-gradient-to-r.from-green-500.to-teal-600 {
  background: linear-gradient(90deg, #22c55e 0%, #0d9488 100%) !important;
  color: white !important;
}

.bg-gradient-to-r.from-red-500.to-pink-600 {
  background: linear-gradient(90deg, #ef4444 0%, #db2777 100%) !important;
  color: white !important;
}

.bg-gradient-to-r.from-islamic-primary.to-emerald-600 {
  background: linear-gradient(90deg, var(--islamic-primary) 0%, #059669 100%) !important;
  color: white !important;
}

/* إصلاح مشاكل البطاقات الفاتحة في التقارير */
.bg-gradient-to-br.from-indigo-50.to-blue-50 {
  background: white !important;
  border: 2px solid #3b82f6 !important;
  position: relative;
}

.bg-gradient-to-br.from-indigo-50.to-blue-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.bg-gradient-to-br.from-emerald-50.to-teal-50 {
  background: white !important;
  border: 2px solid #22c55e !important;
  position: relative;
}

.bg-gradient-to-br.from-emerald-50.to-teal-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #22c55e, #0d9488);
}

.bg-gradient-to-br.from-amber-50.to-yellow-50 {
  background: white !important;
  border: 2px solid #eab308 !important;
  position: relative;
}

.bg-gradient-to-br.from-amber-50.to-yellow-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #eab308, #ca8a04);
}

.bg-gradient-to-br.from-purple-50.to-violet-50 {
  background: white !important;
  border: 2px solid #a855f7 !important;
  position: relative;
}

.bg-gradient-to-br.from-purple-50.to-violet-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #a855f7, #7c3aed);
}

/* إصلاح ألوان النصوص في البطاقات الفاتحة */
.bg-gradient-to-br.from-indigo-50.to-blue-50 * {
  color: #1e40af !important;
}

.bg-gradient-to-br.from-emerald-50.to-teal-50 * {
  color: #166534 !important;
}

.bg-gradient-to-br.from-amber-50.to-yellow-50 * {
  color: #92400e !important;
}

.bg-gradient-to-br.from-purple-50.to-violet-50 * {
  color: #7c2d12 !important;
}

/* إصلاح مشاكل الحدود */
.border-indigo-100 {
  border-color: #e0e7ff !important;
}

.border-emerald-100 {
  border-color: #d1fae5 !important;
}

.border-amber-100 {
  border-color: #fef3c7 !important;
}

.border-purple-100 {
  border-color: #f3e8ff !important;
}

/* إصلاح مشاكل الهوفر */
.hover\:from-indigo-100.hover\:to-blue-100:hover {
  background: linear-gradient(135deg, #c7d2fe 0%, #dbeafe 100%) !important;
}

.hover\:from-emerald-100.hover\:to-teal-100:hover {
  background: linear-gradient(135deg, #d1fae5 0%, #ccfbf1 100%) !important;
}

.hover\:from-amber-100.hover\:to-yellow-100:hover {
  background: linear-gradient(135deg, #fef3c7 0%, #fef9c3 100%) !important;
}

.hover\:from-purple-100.hover\:to-violet-100:hover {
  background: linear-gradient(135deg, #f3e8ff 0%, #ede9fe 100%) !important;
}

/* إصلاح مشاكل الحدود عند الهوفر */
.hover\:border-indigo-200:hover {
  border-color: #c7d2fe !important;
}

.hover\:border-emerald-200:hover {
  border-color: #a7f3d0 !important;
}

.hover\:border-amber-200:hover {
  border-color: #fde68a !important;
}

.hover\:border-purple-200:hover {
  border-color: #e9d5ff !important;
}

/* إصلاح مشاكل الأيقونات الملونة */
.w-16.h-16.bg-indigo-500 {
  background: linear-gradient(135deg, #6366f1, #4f46e5) !important;
  color: white !important;
}

.w-16.h-16.bg-emerald-500 {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: white !important;
}

.w-16.h-16.bg-amber-500 {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
  color: white !important;
}

.w-16.h-16.bg-purple-500 {
  background: linear-gradient(135deg, #a855f7, #9333ea) !important;
  color: white !important;
}

/* تحسينات للتجاوب */
@media (max-width: 1024px) {
  .mr-64 { margin-right: 0 !important; }
  .lg\:mr-64 { margin-right: 0 !important; }
}

/* ========================================
   🎨 فئات مخصصة إضافية من custom.css
======================================== */

/* Progress Bar Animations */
.progress-bar {
  transition: width 0.5s ease-in-out;
}

/* Enhanced Hover Effects */
.hover\:bg-islamic-dark:hover {
  background-color: var(--islamic-dark);
}

.hover\:bg-islamic-secondary:hover {
  background-color: var(--islamic-secondary);
}

/* Card Enhancements */
.card-islamic {
  background: linear-gradient(135deg, var(--islamic-primary) 0%, var(--islamic-light) 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-medium);
  transition: var(--transition-medium);
  overflow: hidden;
  position: relative;
}

.card-islamic:hover {
  box-shadow: var(--shadow-strong);
  transform: translateY(-2px);
}

/* Stats Card Specific */
.stats-card-islamic {
  background: white;
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-light);
  transition: var(--transition-fast);
  border: 1px solid rgba(45, 80, 22, 0.1);
  position: relative;
  overflow: hidden;
}

.stats-card-islamic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.stats-card-islamic:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
  border-color: var(--islamic-gold);
}

/* Icon Containers */
.icon-container-islamic {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: bold;
  position: relative;
  overflow: hidden;
}

.icon-container-islamic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-gold);
  opacity: 0.1;
  border-radius: 50%;
}

/* Button Enhancements */
.btn-islamic-primary {
  background: var(--gradient-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-md);
  font-weight: 600;
  transition: var(--transition-fast);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn-islamic-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-islamic-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-islamic-primary:hover::before {
  left: 100%;
}

/* Notification Badges */
.notification-badge-islamic {
  background: var(--islamic-gold);
  color: var(--islamic-dark);
  font-size: 0.75rem;
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  animation: pulse 2s infinite;
}

/* Enhanced Sidebar Links */
.sidebar-link-enhanced {
  position: relative;
  overflow: hidden;
}

.sidebar-link-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.3s;
}

.sidebar-link-enhanced:hover::before {
  left: 100%;
}

/* Islamic Pattern Overlay */
.islamic-pattern-overlay {
  position: relative;
}

.islamic-pattern-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  pointer-events: none;
}

/* ========================================
   🔧 إصلاحات شاملة للمشاكل المكتشفة في جميع لوحات التحكم
======================================== */

/* إصلاح الخلفية الرئيسية للوحات التحكم */
.min-h-screen.bg-gradient-to-br.from-gray-50.to-islamic-mint {
  background: linear-gradient(135deg, #f8fafc 0%, #e6fffa 50%, #d1fae5 100%) !important;
}

/* إصلاح البطاقات الإحصائية */
.stats-card {
  background: white !important;
  border: 2px solid rgba(45, 80, 22, 0.15) !important;
  border-radius: 0.75rem !important;
  padding: 1.5rem !important;
  box-shadow: 0 4px 15px rgba(45, 80, 22, 0.1) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.stats-card:hover {
  box-shadow: 0 8px 25px rgba(45, 80, 22, 0.2) !important;
  transform: translateY(-3px) !important;
  border-color: rgba(45, 80, 22, 0.25) !important;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold));
}

/* تحسين النصوص داخل البطاقات */
.stats-card .text-gray-600 {
  color: #374151 !important;
  font-weight: 600 !important;
}

.stats-card .text-gray-900 {
  color: #111827 !important;
  font-weight: 800 !important;
}

.stats-card .text-sm {
  color: #4b5563 !important;
  font-weight: 500 !important;
}

/* إصلاح أيقونات الإحصائيات */
.stats-icon {
  width: 3rem !important;
  height: 3rem !important;
  border-radius: 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.25rem !important;
  font-weight: bold !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

.stats-icon:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
}

/* تحسين ألوان الأيقونات */
.stats-icon i {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* تحسين شارات الحالة */
.stats-card .bg-green-100 {
  background-color: #dcfce7 !important;
  border: 1px solid #16a34a !important;
}

.stats-card .text-green-800 {
  color: #14532d !important;
  font-weight: 700 !important;
}

.stats-card .bg-red-100 {
  background-color: #fee2e2 !important;
  border: 1px solid #ef4444 !important;
}

.stats-card .text-red-800 {
  color: #7f1d1d !important;
  font-weight: 700 !important;
}

.stats-card .bg-blue-100 {
  background-color: #dbeafe !important;
  border: 1px solid #3b82f6 !important;
}

.stats-card .text-blue-800 {
  color: #1e3a8a !important;
  font-weight: 700 !important;
}

/* تحسين النصوص الصغيرة */
.stats-card .text-xs {
  color: #374151 !important;
  font-weight: 600 !important;
}

/* تحسين الأرقام الكبيرة */
.stats-card .text-2xl,
.stats-card .text-3xl {
  color: #111827 !important;
  font-weight: 900 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

/* تحسينات خاصة لمركز مراقبة الحصص */
.stats-card .bg-gray-100 {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
  border: 1px solid #9ca3af !important;
  color: #374151 !important;
  font-weight: 700 !important;
}

.stats-card .text-gray-600 {
  color: #374151 !important;
  font-weight: 600 !important;
}

/* تحسين الروابط والأزرار داخل البطاقات */
.stats-card a {
  color: var(--islamic-primary) !important;
  font-weight: 600 !important;
  text-decoration: none !important;
  transition: all 0.2s ease !important;
}

.stats-card a:hover {
  color: var(--islamic-light) !important;
  text-decoration: underline !important;
}

/* تحسين العناوين داخل البطاقات */
.stats-card h3,
.stats-card h4 {
  color: #111827 !important;
  font-weight: 800 !important;
}

/* تحسين الفواصل */
.stats-card .border-gray-200 {
  border-color: rgba(45, 80, 22, 0.2) !important;
}

.stats-icon:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
}

/* إصلاح الشارات المحسنة */
.badge-info-enhanced {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  color: white !important;
  padding: 0.375rem 0.75rem !important;
  border-radius: 9999px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  display: inline-flex !important;
  align-items: center !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3) !important;
}

.badge-success-enhanced {
  background: linear-gradient(135deg, #22c55e, #15803d) !important;
  color: white !important;
  padding: 0.375rem 0.75rem !important;
  border-radius: 9999px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  display: inline-flex !important;
  align-items: center !important;
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3) !important;
}

.badge-warning-enhanced {
  background: linear-gradient(135deg, #eab308, #ca8a04) !important;
  color: white !important;
  padding: 0.375rem 0.75rem !important;
  border-radius: 9999px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  display: inline-flex !important;
  align-items: center !important;
  box-shadow: 0 2px 4px rgba(234, 179, 8, 0.3) !important;
}

/* إصلاح شريط التقدم المحسن */
.progress-enhanced {
  width: 100% !important;
  height: 0.5rem !important;
  background-color: #e5e7eb !important;
  border-radius: 9999px !important;
  overflow: hidden !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.progress-bar-enhanced {
  height: 100% !important;
  background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold)) !important;
  border-radius: 9999px !important;
  transition: width 0.5s ease-in-out !important;
  position: relative !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

.progress-bar-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

/* إصلاح الخطوط العربية */
.arabic-text {
  font-family: 'Cairo', 'Amiri', 'Traditional Arabic', sans-serif !important;
  line-height: 1.8 !important;
  letter-spacing: 0.025em !important;
}

/* إصلاح البطاقات المحسنة */
.enhanced-card {
  background: white !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.enhanced-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  transform: translateY(-2px) !important;
}

.enhanced-card-header {
  background: linear-gradient(135deg, var(--islamic-primary) 0%, var(--islamic-secondary) 100%) !important;
  padding: 2rem !important;
  color: white !important;
  position: relative !important;
  overflow: hidden !important;
}

.enhanced-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-repeat: repeat;
}

.enhanced-card-body {
  padding: 2rem !important;
  position: relative !important;
}

/* ========================================
   📱 إصلاحات التجاوب الشاملة
======================================== */

/* إصلاحات للشاشات الصغيرة */
@media (max-width: 768px) {
  .stats-card {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .stats-icon {
    width: 2.5rem !important;
    height: 2.5rem !important;
    font-size: 1rem !important;
  }

  .enhanced-card-header {
    padding: 1.5rem !important;
  }

  .enhanced-card-body {
    padding: 1.5rem !important;
  }

  .grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    gap: 1rem !important;
  }

  .grid.grid-cols-1.lg\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    gap: 1rem !important;
  }

  .grid.grid-cols-1.lg\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    gap: 1rem !important;
  }

  .text-3xl {
    font-size: 1.875rem !important;
  }

  .text-2xl {
    font-size: 1.5rem !important;
  }

  .text-xl {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 640px) {
  .enhanced-card-header h1 {
    font-size: 1.5rem !important;
  }

  .enhanced-card-header p {
    font-size: 0.875rem !important;
  }

  .stats-card .text-3xl {
    font-size: 1.5rem !important;
  }

  .islamic-content-card .text-2xl {
    font-size: 1.125rem !important;
  }

  .islamic-content-card .text-xl {
    font-size: 1rem !important;
  }

  .space-x-4 > * + * {
    margin-right: 0.5rem !important;
  }

  .space-x-reverse > * + * {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }
}

/* ========================================
   🎨 إصلاحات إضافية للألوان والتدرجات
======================================== */

/* إصلاح جميع التدرجات الفاتحة المشكلة */
.bg-gradient-to-r.from-blue-50.to-indigo-50,
.bg-gradient-to-r.from-green-50.to-emerald-50,
.bg-gradient-to-r.from-purple-50.to-violet-50,
.bg-gradient-to-r.from-orange-50.to-red-50,
.bg-gradient-to-r.from-yellow-50.to-amber-50,
.bg-gradient-to-r.from-pink-50.to-rose-50,
.bg-gradient-to-r.from-indigo-50.to-blue-50,
.bg-gradient-to-r.from-emerald-50.to-teal-50,
.bg-gradient-to-r.from-amber-50.to-yellow-50,
.bg-gradient-to-r.from-violet-50.to-purple-50,
.bg-gradient-to-r.from-teal-50.to-cyan-50,
.bg-gradient-to-r.from-cyan-50.to-blue-50 {
  background: white !important;
  border: 2px solid #e5e7eb !important;
  position: relative !important;
}

.bg-gradient-to-r.from-blue-50.to-indigo-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #4f46e5);
}

.bg-gradient-to-r.from-green-50.to-emerald-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #22c55e, #10b981);
}

.bg-gradient-to-r.from-purple-50.to-violet-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #a855f7, #8b5cf6);
}

.bg-gradient-to-r.from-orange-50.to-red-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f97316, #ef4444);
}

/* إصلاح ألوان النصوص في البطاقات الفاتحة */
.bg-gradient-to-r.from-blue-50.to-indigo-50 * {
  color: #1e40af !important;
}

.bg-gradient-to-r.from-green-50.to-emerald-50 * {
  color: #166534 !important;
}

.bg-gradient-to-r.from-purple-50.to-violet-50 * {
  color: #7c2d12 !important;
}

.bg-gradient-to-r.from-orange-50.to-red-50 * {
  color: #9a3412 !important;
}

/* إصلاح الحدود الملونة */
.border-blue-100 {
  border-color: #dbeafe !important;
}

.border-green-100 {
  border-color: #dcfce7 !important;
}

.border-purple-100 {
  border-color: #f3e8ff !important;
}

.border-orange-100 {
  border-color: #fed7aa !important;
}

/* ========================================
   📝 إصلاحات شاملة لحقول النماذج والملف الشخصي
======================================== */

/* إصلاح الحشو والتباعد للحقول */
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

/* إصلاح الزوايا المدورة */
.rounded-lg { border-radius: 0.5rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

/* إصلاح الحدود */
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-t { border-top-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-l { border-left-width: 1px; }
.border-r { border-right-width: 1px; }

/* إصلاح ألوان الحدود المفقودة */
.border-transparent { border-color: transparent; }
.border-gray-100 { border-color: #f3f4f6; }
.border-gray-500 { border-color: #6b7280; }
.border-gray-600 { border-color: #4b5563; }
.border-gray-700 { border-color: #374151; }
.border-gray-800 { border-color: #1f2937; }
.border-gray-900 { border-color: #111827; }

/* إصلاح تأثيرات التركيز */
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-islamic-primary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(16 185 129 / var(--tw-ring-opacity));
}

.focus\:border-transparent:focus {
  border-color: transparent;
}

.focus\:border-islamic-primary:focus {
  border-color: var(--islamic-primary);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* إصلاح أنماط الحقول الأساسية */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="url"],
input[type="date"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="number"],
input[type="search"],
select,
textarea {
  appearance: none;
  background-color: #fff;
  border-color: #d1d5db;
  border-width: 1px;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #111827;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.15s ease-in-out;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
input[type="date"]:focus,
input[type="datetime-local"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
input[type="time"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
select:focus,
textarea:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(16 185 129 / 0.5);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  border-color: var(--islamic-primary);
}

/* إصلاح أنماط الحقول المحسنة */
.form-input-enhanced {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: #fff;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #111827;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.form-input-enhanced:focus {
  outline: none;
  border-color: var(--islamic-primary);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.form-input-enhanced:hover {
  border-color: #9ca3af;
}

.form-input-enhanced::placeholder {
  color: #9ca3af;
}

/* إصلاح أنماط التحديد (Select) */
select.form-input-enhanced {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-left: 2.5rem;
  print-color-adjust: exact;
}

/* إصلاح أنماط منطقة النص (Textarea) */
textarea.form-input-enhanced {
  resize: vertical;
  min-height: 6rem;
}

/* إصلاح أنماط رفع الملفات */
input[type="file"] {
  width: 100%;
  padding: 0.5rem;
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  background-color: #f9fafb;
  font-size: 0.875rem;
  color: #374151;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

input[type="file"]:hover {
  border-color: var(--islamic-primary);
  background-color: #f0fdf4;
}

input[type="file"]:focus {
  outline: none;
  border-color: var(--islamic-primary);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* إصلاح أنماط التسميات (Labels) */
.form-label-enhanced {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-label-required::after {
  content: ' *';
  color: #ef4444;
}

/* إصلاح رسائل الخطأ */
.form-error-enhanced {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
}

.form-error-enhanced::before {
  content: '⚠';
  margin-left: 0.25rem;
  font-size: 0.875rem;
}

/* إصلاح رسائل المساعدة */
.form-help-enhanced {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* إصلاح مجموعات الحقول */
.form-group-enhanced {
  margin-bottom: 1.5rem;
}

.form-group-enhanced:last-child {
  margin-bottom: 0;
}

/* إصلاح الحقول في الشبكة */
.form-grid-enhanced {
  display: grid;
  gap: 1.5rem;
}

.form-grid-enhanced.cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.form-grid-enhanced.cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 768px) {
  .form-grid-enhanced.cols-2,
  .form-grid-enhanced.cols-3 {
    grid-template-columns: 1fr;
  }
}

/* ========================================
   👤 إصلاحات خاصة بصفحة الملف الشخصي
======================================== */

/* إصلاح بطاقة الملف الشخصي */
.profile-card-enhanced {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
}

.profile-card-enhanced:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

/* إصلاح رأس بطاقة الملف الشخصي */
.profile-header-enhanced {
  background: linear-gradient(135deg, var(--islamic-primary) 0%, var(--islamic-secondary) 100%);
  padding: 2rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.profile-header-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-repeat: repeat;
}

/* إصلاح صورة الملف الشخصي */
.profile-avatar-enhanced {
  width: 6rem;
  height: 6rem;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  object-fit: cover;
  transition: all 0.3s ease;
}

.profile-avatar-enhanced:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* إصلاح معلومات الملف الشخصي */
.profile-info-enhanced {
  padding: 2rem;
}

.profile-name-enhanced {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

.profile-role-enhanced {
  font-size: 0.875rem;
  color: #6b7280;
  background: #f3f4f6;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  display: inline-block;
  margin-bottom: 1rem;
}

/* إصلاح نماذج الملف الشخصي */
.profile-form-enhanced {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}

.profile-form-title-enhanced {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #e5e7eb;
  position: relative;
}

.profile-form-title-enhanced::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 3rem;
  height: 2px;
  background: var(--islamic-primary);
}

/* إصلاح أزرار الملف الشخصي */
.profile-btn-enhanced {
  background: linear-gradient(135deg, var(--islamic-primary), var(--islamic-secondary));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.profile-btn-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.profile-btn-secondary-enhanced {
  background: white;
  color: var(--islamic-primary);
  border: 2px solid var(--islamic-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.profile-btn-secondary-enhanced:hover {
  background: var(--islamic-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

/* إصلاح تبويبات الملف الشخصي */
.profile-tabs-enhanced {
  display: flex;
  border-bottom: 2px solid #e5e7eb;
  margin-bottom: 2rem;
  overflow-x: auto;
}

.profile-tab-enhanced {
  padding: 1rem 1.5rem;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.profile-tab-enhanced:hover {
  color: var(--islamic-primary);
}

.profile-tab-enhanced.active {
  color: var(--islamic-primary);
  border-bottom-color: var(--islamic-primary);
  background: rgba(16, 185, 129, 0.05);
}

/* إصلاح محتوى التبويبات */
.profile-tab-content-enhanced {
  display: none;
  animation: fadeInUp 0.3s ease-in-out;
}

.profile-tab-content-enhanced.active {
  display: block;
}

/* إصلاح إحصائيات الملف الشخصي */
.profile-stats-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.profile-stat-enhanced {
  background: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  text-align: center;
  transition: all 0.3s ease;
}

.profile-stat-enhanced:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.profile-stat-number-enhanced {
  font-size: 2rem;
  font-weight: 700;
  color: var(--islamic-primary);
  display: block;
}

.profile-stat-label-enhanced {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

/* إصلاح رفع الصورة */
.profile-image-upload-enhanced {
  position: relative;
  display: inline-block;
}

.profile-image-upload-enhanced input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.profile-image-upload-overlay-enhanced {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-size: 1.5rem;
}

.profile-image-upload-enhanced:hover .profile-image-upload-overlay-enhanced {
  opacity: 1;
}

/* إصلاح التجاوب للملف الشخصي */
@media (max-width: 768px) {
  .profile-header-enhanced {
    padding: 1.5rem;
    text-align: center;
  }

  .profile-info-enhanced {
    padding: 1.5rem;
  }

  .profile-form-enhanced {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .profile-avatar-enhanced {
    width: 5rem;
    height: 5rem;
  }

  .profile-tabs-enhanced {
    flex-wrap: wrap;
  }

  .profile-tab-enhanced {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .profile-stats-enhanced {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .profile-stat-enhanced {
    padding: 1rem;
  }

  .profile-stat-number-enhanced {
    font-size: 1.5rem;
  }
}

/* ========================================
   📑 إصلاحات شاملة للتبويبات (Tabs) في الملف الشخصي
======================================== */

/* إصلاح التبويبات الضيقة */
.tab-button {
  padding: 1rem 2rem !important;
  font-size: 0.95rem !important;
  font-weight: 600 !important;
  border-bottom: 3px solid transparent !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  background: transparent !important;
  cursor: pointer !important;
  white-space: nowrap !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  min-width: 150px !important;
  justify-content: center !important;
}

.tab-button:hover {
  background: rgba(16, 185, 129, 0.05) !important;
  color: var(--islamic-primary) !important;
  border-bottom-color: rgba(16, 185, 129, 0.3) !important;
  transform: translateY(-1px) !important;
}

.tab-button.active {
  color: var(--islamic-primary) !important;
  border-bottom-color: var(--islamic-primary) !important;
  background: rgba(16, 185, 129, 0.1) !important;
  font-weight: 700 !important;
}

.tab-button.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold));
  border-radius: 0 0 3px 3px;
}

/* إصلاح حاوي التبويبات */
.profile-tabs-container {
  background: white !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e5e7eb !important;
  overflow: hidden !important;
}

.profile-tabs-nav {
  display: flex !important;
  border-bottom: 2px solid #f3f4f6 !important;
  background: #fafafa !important;
  overflow-x: auto !important;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.profile-tabs-nav::-webkit-scrollbar {
  display: none !important;
}

/* إصلاح محتوى التبويبات */
.tab-content {
  padding: 2rem !important;
  background: white !important;
  min-height: 400px !important;
}

.tab-content h3 {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 1.5rem !important;
  padding-bottom: 0.75rem !important;
  border-bottom: 2px solid #f3f4f6 !important;
  position: relative !important;
}

.tab-content h3::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 3rem;
  height: 2px;
  background: var(--islamic-primary);
}

/* إصلاح أيقونات التبويبات */
.tab-button i {
  font-size: 1rem !important;
  margin-left: 0.5rem !important;
}

/* إصلاح التجاوب للتبويبات */
@media (max-width: 768px) {
  .tab-button {
    padding: 0.75rem 1rem !important;
    font-size: 0.875rem !important;
    min-width: 120px !important;
  }

  .profile-tabs-nav {
    padding: 0 0.5rem !important;
  }

  .tab-content {
    padding: 1.5rem !important;
  }

  .tab-content h3 {
    font-size: 1.125rem !important;
  }
}

@media (max-width: 640px) {
  .tab-button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.8rem !important;
    min-width: 100px !important;
  }

  .tab-content {
    padding: 1rem !important;
  }

  .tab-content h3 {
    font-size: 1rem !important;
    margin-bottom: 1rem !important;
  }
}

/* إصلاح التبويبات المحسنة */
.profile-tabs-enhanced {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-bottom: 2rem;
}

.profile-tabs-nav-enhanced {
  display: flex;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid #e5e7eb;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.profile-tabs-nav-enhanced::-webkit-scrollbar {
  display: none;
}

.profile-tab-button-enhanced {
  padding: 1.25rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  color: #6b7280;
  background: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 180px;
  justify-content: center;
  position: relative;
}

.profile-tab-button-enhanced:hover {
  color: var(--islamic-primary);
  background: rgba(16, 185, 129, 0.05);
  border-bottom-color: rgba(16, 185, 129, 0.3);
  transform: translateY(-1px);
}

.profile-tab-button-enhanced.active {
  color: var(--islamic-primary);
  background: white;
  border-bottom-color: var(--islamic-primary);
  font-weight: 700;
  box-shadow: 0 -2px 4px rgba(16, 185, 129, 0.1);
}

.profile-tab-button-enhanced.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold));
}

.profile-tab-content-enhanced {
  padding: 2.5rem;
  background: white;
  min-height: 500px;
}

.profile-tab-content-enhanced.hidden {
  display: none;
}

.profile-tab-title-enhanced {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f3f4f6;
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.profile-tab-title-enhanced::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 4rem;
  height: 2px;
  background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold));
}

.profile-tab-title-enhanced i {
  color: var(--islamic-primary);
  font-size: 1.25rem;
}

/* ========================================
   🔧 إصلاحات إضافية للتبويبات المحددة
======================================== */

/* إصلاح التبويبات في صفحة الملف الشخصي تحديداً */
nav.-mb-px.flex.space-x-8.space-x-reverse.px-6 {
  padding: 0 1rem !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  border-bottom: 2px solid #e5e7eb !important;
}

nav.-mb-px.flex.space-x-8.space-x-reverse.px-6 .tab-button {
  padding: 1.25rem 2rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  min-width: 180px !important;
  border-bottom: 3px solid transparent !important;
  margin-bottom: -2px !important;
}

/* إصلاح التباعد بين التبويبات */
.space-x-8.space-x-reverse > * + * {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.space-x-8.space-x-reverse {
  gap: 0.5rem !important;
}

/* إصلاح حاوي التبويبات الرئيسي */
.bg-white.rounded-lg.shadow-sm.border.border-gray-200.mb-6 {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  overflow: hidden !important;
}

.border-b.border-gray-200 {
  border-bottom: 2px solid #e5e7eb !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
}

/* إصلاح التبويبات النشطة */
.tab-button.active {
  background: white !important;
  color: var(--islamic-primary) !important;
  border-bottom-color: var(--islamic-primary) !important;
  font-weight: 700 !important;
  box-shadow: 0 -2px 4px rgba(16, 185, 129, 0.1) !important;
  position: relative !important;
  z-index: 1 !important;
}

.tab-button.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold));
  z-index: 2;
}

/* إصلاح التبويبات غير النشطة */
.tab-button:not(.active) {
  color: #6b7280 !important;
  background: transparent !important;
  border-bottom-color: transparent !important;
}

.tab-button:not(.active):hover {
  color: var(--islamic-primary) !important;
  background: rgba(16, 185, 129, 0.05) !important;
  border-bottom-color: rgba(16, 185, 129, 0.3) !important;
  transform: translateY(-1px) !important;
}

/* إصلاح محتوى التبويبات */
.tab-content.p-6 {
  padding: 2.5rem !important;
  background: white !important;
  min-height: 500px !important;
}

.tab-content.p-6.hidden {
  display: none !important;
}

/* إصلاح عناوين التبويبات */
.tab-content h3.text-lg.font-semibold.text-gray-900.mb-4.flex.items-center {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 2rem !important;
  padding-bottom: 1rem !important;
  border-bottom: 2px solid #f3f4f6 !important;
  position: relative !important;
}

.tab-content h3.text-lg.font-semibold.text-gray-900.mb-4.flex.items-center::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 4rem;
  height: 2px;
  background: linear-gradient(90deg, var(--islamic-primary), var(--islamic-gold));
}

.tab-content h3 i.fas {
  color: var(--islamic-primary) !important;
  font-size: 1.25rem !important;
  margin-left: 0.75rem !important;
}

/* إصلاح التجاوب المحسن للتبويبات */
@media (max-width: 1024px) {
  nav.-mb-px.flex.space-x-8.space-x-reverse.px-6 .tab-button {
    padding: 1rem 1.5rem !important;
    font-size: 0.95rem !important;
    min-width: 150px !important;
  }
}

@media (max-width: 768px) {
  nav.-mb-px.flex.space-x-8.space-x-reverse.px-6 {
    padding: 0 0.5rem !important;
    overflow-x: auto !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  nav.-mb-px.flex.space-x-8.space-x-reverse.px-6::-webkit-scrollbar {
    display: none !important;
  }

  nav.-mb-px.flex.space-x-8.space-x-reverse.px-6 .tab-button {
    padding: 0.75rem 1.25rem !important;
    font-size: 0.875rem !important;
    min-width: 130px !important;
  }

  .tab-content.p-6 {
    padding: 1.5rem !important;
  }

  .tab-content h3.text-lg.font-semibold.text-gray-900.mb-4.flex.items-center {
    font-size: 1.25rem !important;
    margin-bottom: 1.5rem !important;
  }
}

@media (max-width: 640px) {
  nav.-mb-px.flex.space-x-8.space-x-reverse.px-6 .tab-button {
    padding: 0.5rem 1rem !important;
    font-size: 0.8rem !important;
    min-width: 110px !important;
  }

  .tab-content.p-6 {
    padding: 1rem !important;
  }

  .tab-content h3.text-lg.font-semibold.text-gray-900.mb-4.flex.items-center {
    font-size: 1.125rem !important;
    margin-bottom: 1rem !important;
  }
}

/* إصلاح الأزرار في التبويبات */
.tab-content button[type="submit"] {
  background: linear-gradient(135deg, var(--islamic-primary), var(--islamic-secondary)) !important;
  color: white !important;
  padding: 0.75rem 2rem !important;
  border-radius: 0.5rem !important;
  font-weight: 600 !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3) !important;
}

.tab-content button[type="submit"]:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4) !important;
}

.tab-content button[type="submit"].bg-red-600 {
  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3) !important;
}

.tab-content button[type="submit"].bg-red-600:hover {
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.4) !important;
}

/* ========================================
   📝 إصلاحات شاملة للحقول والبطاقات في صفحة الملف الشخصي
======================================== */

/* إصلاح جميع حقول الإدخال في التبويبات */
.tab-content input[type="text"],
.tab-content input[type="email"],
.tab-content input[type="tel"],
.tab-content input[type="date"],
.tab-content input[type="password"],
.tab-content input[type="file"],
.tab-content select,
.tab-content textarea {
  width: 100% !important;
  padding: 0.75rem 1rem !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 0.5rem !important;
  font-size: 0.95rem !important;
  line-height: 1.5 !important;
  color: #374151 !important;
  background-color: #ffffff !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.tab-content input[type="text"]:focus,
.tab-content input[type="email"]:focus,
.tab-content input[type="tel"]:focus,
.tab-content input[type="date"]:focus,
.tab-content input[type="password"]:focus,
.tab-content select:focus,
.tab-content textarea:focus {
  outline: none !important;
  border-color: var(--islamic-primary) !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
  background-color: #fafafa !important;
}

.tab-content input[type="text"]:hover,
.tab-content input[type="email"]:hover,
.tab-content input[type="tel"]:hover,
.tab-content input[type="date"]:hover,
.tab-content input[type="password"]:hover,
.tab-content select:hover,
.tab-content textarea:hover {
  border-color: #d1d5db !important;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1) !important;
}

/* إصلاح حقل الملف */
.tab-content input[type="file"] {
  padding: 0.5rem !important;
  border: 2px dashed #d1d5db !important;
  background-color: #f9fafb !important;
  cursor: pointer !important;
}

.tab-content input[type="file"]:hover {
  border-color: var(--islamic-primary) !important;
  background-color: rgba(16, 185, 129, 0.05) !important;
}

.tab-content input[type="file"]:focus {
  border-color: var(--islamic-primary) !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

/* إصلاح حقل النص الطويل */
.tab-content textarea {
  min-height: 120px !important;
  resize: vertical !important;
}

/* إصلاح التسميات */
.tab-content label {
  font-size: 0.95rem !important;
  font-weight: 600 !important;
  color: #374151 !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
}

/* إصلاح رسائل الخطأ */
.tab-content .text-red-600 {
  color: #dc2626 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  margin-top: 0.25rem !important;
}

/* إصلاح النصوص المساعدة */
.tab-content .text-gray-500 {
  color: #6b7280 !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
  line-height: 1.4 !important;
}

/* إصلاح الشبكة للحقول */
.tab-content .grid.grid-cols-1.md\\:grid-cols-2.gap-6 {
  display: grid !important;
  grid-template-columns: 1fr !important;
  gap: 1.5rem !important;
}

@media (min-width: 768px) {
  .tab-content .grid.grid-cols-1.md\\:grid-cols-2.gap-6 {
    grid-template-columns: 1fr 1fr !important;
  }
}

/* إصلاح المساحات بين الحقول */
.tab-content .space-y-6 > * + * {
  margin-top: 1.5rem !important;
}

/* إصلاح بطاقة متطلبات كلمة المرور */
.tab-content .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.05) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  border-radius: 0.75rem !important;
  padding: 1.25rem !important;
}

.tab-content .bg-blue-50 h4 {
  color: #1e40af !important;
  font-size: 0.95rem !important;
  font-weight: 600 !important;
  margin-bottom: 0.75rem !important;
}

.tab-content .bg-blue-50 ul {
  color: #1e40af !important;
  font-size: 0.875rem !important;
  line-height: 1.6 !important;
}

.tab-content .bg-blue-50 li {
  margin-bottom: 0.25rem !important;
}

/* إصلاح صورة الملف الشخصي */
.tab-content .flex.items-center.space-x-6.space-x-reverse {
  display: flex !important;
  align-items: center !important;
  gap: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.tab-content .shrink-0 img {
  width: 5rem !important;
  height: 5rem !important;
  border-radius: 50% !important;
  object-fit: cover !important;
  border: 3px solid #e5e7eb !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.tab-content .shrink-0 .bg-gray-200 {
  width: 5rem !important;
  height: 5rem !important;
  border-radius: 50% !important;
  background-color: #f3f4f6 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 3px solid #e5e7eb !important;
}

.tab-content .shrink-0 .bg-gray-200 i {
  color: var(--islamic-primary) !important;
  font-size: 1.5rem !important;
}

/* إصلاح أزرار الإرسال */
.tab-content .flex.justify-end {
  display: flex !important;
  justify-content: flex-end !important;
  margin-top: 2rem !important;
  padding-top: 1.5rem !important;
  border-top: 1px solid #f3f4f6 !important;
}

.tab-content button[type="submit"] {
  padding: 0.75rem 2rem !important;
  font-size: 0.95rem !important;
  font-weight: 600 !important;
  border-radius: 0.5rem !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

/* إصلاح التجاوب للحقول */
@media (max-width: 768px) {
  .tab-content input[type="text"],
  .tab-content input[type="email"],
  .tab-content input[type="tel"],
  .tab-content input[type="date"],
  .tab-content input[type="password"],
  .tab-content select,
  .tab-content textarea {
    padding: 0.625rem 0.875rem !important;
    font-size: 0.9rem !important;
  }

  .tab-content label {
    font-size: 0.9rem !important;
  }

  .tab-content .flex.items-center.space-x-6.space-x-reverse {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 1rem !important;
  }

  .tab-content .shrink-0 {
    align-self: center !important;
  }
}

@media (max-width: 640px) {
  .tab-content input[type="text"],
  .tab-content input[type="email"],
  .tab-content input[type="tel"],
  .tab-content input[type="date"],
  .tab-content input[type="password"],
  .tab-content select,
  .tab-content textarea {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }

  .tab-content label {
    font-size: 0.875rem !important;
  }

  .tab-content button[type="submit"] {
    width: 100% !important;
    justify-content: center !important;
  }

  .tab-content .flex.justify-end {
    justify-content: stretch !important;
  }
}

/* ========================================
   🎴 إصلاحات شاملة للبطاقات الجانبية في صفحة الملف الشخصي
======================================== */

/* إصلاح بطاقة معلومات المستخدم */
.lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6.mb-6 {
  background: white !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  padding: 2rem !important;
  margin-bottom: 1.5rem !important;
  transition: all 0.3s ease !important;
}

.lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6.mb-6:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-1px) !important;
}

/* إصلاح صورة المستخدم في البطاقة الجانبية */
.lg\\:col-span-1 .text-center img {
  width: 6rem !important;
  height: 6rem !important;
  border-radius: 50% !important;
  object-fit: cover !important;
  margin: 0 auto 1rem auto !important;
  border: 4px solid #e5e7eb !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
}

.lg\\:col-span-1 .text-center img:hover {
  border-color: var(--islamic-primary) !important;
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.3) !important;
  transform: scale(1.05) !important;
}

.lg\\:col-span-1 .text-center .bg-gray-200 {
  width: 6rem !important;
  height: 6rem !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto 1rem auto !important;
  border: 4px solid #e5e7eb !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.lg\\:col-span-1 .text-center .bg-gray-200:hover {
  border-color: var(--islamic-primary) !important;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05)) !important;
  transform: scale(1.05) !important;
}

.lg\\:col-span-1 .text-center .bg-gray-200 i {
  color: var(--islamic-primary) !important;
  font-size: 2rem !important;
}

/* إصلاح نصوص البطاقة الجانبية */
.lg\\:col-span-1 .text-center h3 {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 0.5rem !important;
  line-height: 1.3 !important;
}

.lg\\:col-span-1 .text-center p.text-sm.text-gray-600 {
  color: #6b7280 !important;
  font-size: 0.95rem !important;
  font-weight: 500 !important;
  margin-bottom: 0.25rem !important;
}

.lg\\:col-span-1 .text-center p.text-xs.text-gray-500 {
  color: #9ca3af !important;
  font-size: 0.875rem !important;
  font-weight: 400 !important;
}

/* إصلاح بطاقة الإحصائيات */
.lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6:not(.mb-6) {
  background: white !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  padding: 2rem !important;
  transition: all 0.3s ease !important;
}

.lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6:not(.mb-6):hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-1px) !important;
}

/* إصلاح عنوان بطاقة الإحصائيات */
.lg\\:col-span-1 h4.text-md.font-semibold.text-gray-900.mb-4 {
  font-size: 1.125rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 1.5rem !important;
  padding-bottom: 0.75rem !important;
  border-bottom: 2px solid #f3f4f6 !important;
  position: relative !important;
}

.lg\\:col-span-1 h4.text-md.font-semibold.text-gray-900.mb-4::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 2rem;
  height: 2px;
  background: var(--islamic-primary);
}

/* إصلاح عناصر الإحصائيات */
.lg\\:col-span-1 .space-y-3 {
  display: flex !important;
  flex-direction: column !important;
  gap: 1rem !important;
}

.lg\\:col-span-1 .space-y-3 .flex.justify-between {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 0.75rem 1rem !important;
  background: #f9fafb !important;
  border-radius: 0.5rem !important;
  border: 1px solid #f3f4f6 !important;
  transition: all 0.3s ease !important;
}

.lg\\:col-span-1 .space-y-3 .flex.justify-between:hover {
  background: rgba(16, 185, 129, 0.05) !important;
  border-color: rgba(16, 185, 129, 0.2) !important;
  transform: translateX(2px) !important;
}

.lg\\:col-span-1 .space-y-3 .flex.justify-between span.text-sm.text-gray-600 {
  color: #6b7280 !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
}

.lg\\:col-span-1 .space-y-3 .flex.justify-between span.text-sm.font-medium {
  color: var(--islamic-primary) !important;
  font-size: 0.95rem !important;
  font-weight: 700 !important;
  background: rgba(16, 185, 129, 0.1) !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 0.375rem !important;
}

/* إصلاح التجاوب للبطاقات الجانبية */
@media (max-width: 1024px) {
  .lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6.mb-6,
  .lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6:not(.mb-6) {
    padding: 1.5rem !important;
  }

  .lg\\:col-span-1 .text-center img,
  .lg\\:col-span-1 .text-center .bg-gray-200 {
    width: 5rem !important;
    height: 5rem !important;
  }

  .lg\\:col-span-1 .text-center .bg-gray-200 i {
    font-size: 1.75rem !important;
  }
}

@media (max-width: 768px) {
  .lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6.mb-6,
  .lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6:not(.mb-6) {
    padding: 1.25rem !important;
    margin-bottom: 1rem !important;
  }

  .lg\\:col-span-1 .text-center img,
  .lg\\:col-span-1 .text-center .bg-gray-200 {
    width: 4.5rem !important;
    height: 4.5rem !important;
  }

  .lg\\:col-span-1 .text-center .bg-gray-200 i {
    font-size: 1.5rem !important;
  }

  .lg\\:col-span-1 .text-center h3 {
    font-size: 1.125rem !important;
  }

  .lg\\:col-span-1 h4.text-md.font-semibold.text-gray-900.mb-4 {
    font-size: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .lg\\:col-span-1 .space-y-3 .flex.justify-between {
    padding: 0.5rem 0.75rem !important;
  }

  .lg\\:col-span-1 .space-y-3 .flex.justify-between span.text-sm.text-gray-600,
  .lg\\:col-span-1 .space-y-3 .flex.justify-between span.text-sm.font-medium {
    font-size: 0.875rem !important;
  }
}

@media (max-width: 640px) {
  .lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6.mb-6,
  .lg\\:col-span-1 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6:not(.mb-6) {
    padding: 1rem !important;
  }

  .lg\\:col-span-1 .text-center img,
  .lg\\:col-span-1 .text-center .bg-gray-200 {
    width: 4rem !important;
    height: 4rem !important;
  }

  .lg\\:col-span-1 .text-center .bg-gray-200 i {
    font-size: 1.25rem !important;
  }

  .lg\\:col-span-1 .space-y-3 .flex.justify-between {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.25rem !important;
    padding: 0.75rem !important;
  }

  .lg\\:col-span-1 .space-y-3 .flex.justify-between span.text-sm.font-medium {
    align-self: flex-end !important;
  }
}

/* ========================================
   📐 إصلاحات شاملة لتخطيط صفحة الملف الشخصي (Layout)
======================================== */

/* إصلاح التخطيط الرئيسي للصفحة */
.grid.grid-cols-1.lg\\:grid-cols-4.gap-6.px-6 {
  display: grid !important;
  grid-template-columns: 1fr !important;
  gap: 1.5rem !important;
  padding: 0 1.5rem !important;
  max-width: 100% !important;
  width: 100% !important;
}

@media (min-width: 1024px) {
  .grid.grid-cols-1.lg\\:grid-cols-4.gap-6.px-6 {
    grid-template-columns: 300px 1fr !important;
    gap: 2rem !important;
    padding: 0 2rem !important;
  }
}

/* إصلاح عمود السايدبار */
.lg\\:col-span-1 {
  width: 100% !important;
  max-width: 100% !important;
}

@media (min-width: 1024px) {
  .lg\\:col-span-1 {
    width: 300px !important;
    max-width: 300px !important;
    flex-shrink: 0 !important;
  }
}

/* إصلاح عمود المحتوى الرئيسي */
.lg\\:col-span-3 {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  flex: 1 !important;
}

@media (min-width: 1024px) {
  .lg\\:col-span-3 {
    width: 100% !important;
    max-width: none !important;
    min-width: 600px !important;
    flex: 1 !important;
  }
}

/* إصلاح حاوي التبويبات الرئيسي */
.lg\\:col-span-3 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.mb-6 {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  background: white !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  margin-bottom: 1.5rem !important;
  overflow: hidden !important;
}

/* إصلاح محتوى التبويبات */
.lg\\:col-span-3 .tab-content {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  padding: 2rem !important;
  background: white !important;
  box-sizing: border-box !important;
}

/* إصلاح النماذج داخل التبويبات */
.lg\\:col-span-3 .tab-content form {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
}

.lg\\:col-span-3 .tab-content .space-y-6 {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 1.5rem !important;
}

/* إصلاح الشبكة للحقول في المحتوى الرئيسي */
.lg\\:col-span-3 .tab-content .grid.grid-cols-1.md\\:grid-cols-2.gap-6 {
  display: grid !important;
  grid-template-columns: 1fr !important;
  gap: 1.5rem !important;
  width: 100% !important;
  max-width: 100% !important;
}

@media (min-width: 768px) {
  .lg\\:col-span-3 .tab-content .grid.grid-cols-1.md\\:grid-cols-2.gap-6 {
    grid-template-columns: 1fr 1fr !important;
    gap: 2rem !important;
  }
}

/* إصلاح الحقول الفردية */
.lg\\:col-span-3 .tab-content .grid.grid-cols-1.md\\:grid-cols-2.gap-6 > div {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
}

/* إصلاح حقول الإدخال في المحتوى الرئيسي */
.lg\\:col-span-3 .tab-content input[type="text"],
.lg\\:col-span-3 .tab-content input[type="email"],
.lg\\:col-span-3 .tab-content input[type="tel"],
.lg\\:col-span-3 .tab-content input[type="date"],
.lg\\:col-span-3 .tab-content input[type="password"],
.lg\\:col-span-3 .tab-content input[type="file"],
.lg\\:col-span-3 .tab-content select,
.lg\\:col-span-3 .tab-content textarea {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* إصلاح التجاوب للتخطيط */
@media (max-width: 1023px) {
  .grid.grid-cols-1.lg\\:grid-cols-4.gap-6.px-6 {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
    padding: 0 1rem !important;
  }

  .lg\\:col-span-1,
  .lg\\:col-span-3 {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
  }

  .lg\\:col-span-3 .tab-content {
    padding: 1.5rem !important;
  }
}

@media (max-width: 768px) {
  .grid.grid-cols-1.lg\\:grid-cols-4.gap-6.px-6 {
    padding: 0 0.75rem !important;
    gap: 1rem !important;
  }

  .lg\\:col-span-3 .tab-content {
    padding: 1.25rem !important;
  }

  .lg\\:col-span-3 .tab-content .grid.grid-cols-1.md\\:grid-cols-2.gap-6 {
    grid-template-columns: 1fr !important;
    gap: 1.25rem !important;
  }
}

@media (max-width: 640px) {
  .grid.grid-cols-1.lg\\:grid-cols-4.gap-6.px-6 {
    padding: 0 0.5rem !important;
    gap: 0.75rem !important;
  }

  .lg\\:col-span-3 .tab-content {
    padding: 1rem !important;
  }

  .lg\\:col-span-3 .tab-content .space-y-6 {
    gap: 1rem !important;
  }

  .lg\\:col-span-3 .tab-content .grid.grid-cols-1.md\\:grid-cols-2.gap-6 {
    gap: 1rem !important;
  }
}

/* إصلاح عرض الحاوي الرئيسي */
.min-h-screen.bg-gray-50 {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  background-color: #f9fafb !important;
  min-height: 100vh !important;
}

/* إصلاح الحاوي العام */
.min-h-screen.bg-gray-50 > .grid {
  width: 100% !important;
  max-width: 1400px !important;
  margin: 0 auto !important;
  min-width: 0 !important;
}

@media (min-width: 1200px) {
  .min-h-screen.bg-gray-50 > .grid {
    max-width: 1200px !important;
  }
}

@media (min-width: 1400px) {
  .min-h-screen.bg-gray-50 > .grid {
    max-width: 1400px !important;
  }
}

/* ========================================
   🔧 إصلاحات شاملة للتبويبات (Tabs) والمحتوى - إصلاح جذري
======================================== */

/* إزالة جميع القيود على الحاوي الرئيسي للتبويبات */
.lg\\:col-span-3 .bg-white.rounded-lg.shadow-sm.border.border-gray-200.mb-6 {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  margin: 0 0 1.5rem 0 !important;
  padding: 0 !important;
  border-radius: 0.75rem !important;
  background: white !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  overflow: visible !important;
}

/* إصلاح شريط التبويبات */
.lg\\:col-span-3 .border-b.border-gray-200 {
  width: 100% !important;
  max-width: 100% !important;
  border-bottom: 1px solid #e5e7eb !important;
  margin: 0 !important;
  padding: 0 !important;
  background: white !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* إصلاح nav التبويبات - إزالة جميع القيود */
.lg\\:col-span-3 nav.-mb-px.flex.space-x-8.space-x-reverse.px-6,
.lg\\:col-span-3 nav.-mb-px.flex.space-x-8.space-x-reverse {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  flex-wrap: nowrap !important;
  gap: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  margin-bottom: -1px !important;
  background: white !important;
  border: none !important;
  overflow-x: auto !important;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

.lg\\:col-span-3 nav.-mb-px.flex.space-x-8.space-x-reverse.px-6::-webkit-scrollbar,
.lg\\:col-span-3 nav.-mb-px.flex.space-x-8.space-x-reverse::-webkit-scrollbar {
  display: none !important;
}

/* إصلاح أزرار التبويبات - عرض كامل */
.lg\\:col-span-3 .tab-button {
  flex: 1 !important;
  min-width: 0 !important;
  max-width: none !important;
  width: 100% !important;
  white-space: nowrap !important;
  padding: 1.25rem 1rem !important;
  margin: 0 !important;
  border: none !important;
  border-bottom: 3px solid transparent !important;
  font-weight: 500 !important;
  font-size: 0.9rem !important;
  color: #6b7280 !important;
  background: white !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-align: center !important;
  border-radius: 0 !important;
  position: relative !important;
  z-index: 1 !important;
}

.lg\\:col-span-3 .tab-button:hover {
  color: #374151 !important;
  background: #f9fafb !important;
  border-bottom-color: #d1d5db !important;
}

.lg\\:col-span-3 .tab-button.active {
  color: #059669 !important;
  background: white !important;
  border-bottom-color: #059669 !important;
  font-weight: 600 !important;
  position: relative !important;
  z-index: 2 !important;
}

/* إصلاح محتوى التبويبات - عرض كامل بدون قيود */
.lg\\:col-span-3 .tab-content,
.lg\\:col-span-3 .tab-content.p-6,
.lg\\:col-span-3 div[id$="-tab"] {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  background: white !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  display: block !important;
  border: none !important;
  border-radius: 0 0 0.75rem 0.75rem !important;
}

.lg\\:col-span-3 .tab-content.hidden,
.lg\\:col-span-3 div[id$="-tab"].hidden {
  display: none !important;
}

/* إضافة padding داخلي للمحتوى */
.lg\\:col-span-3 .tab-content > *,
.lg\\:col-span-3 div[id$="-tab"] > * {
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}

.lg\\:col-span-3 .tab-content > h3,
.lg\\:col-span-3 div[id$="-tab"] > h3 {
  padding-top: 2rem !important;
  padding-bottom: 0 !important;
  margin-bottom: 1.5rem !important;
}

.lg\\:col-span-3 .tab-content > form,
.lg\\:col-span-3 div[id$="-tab"] > form {
  padding-top: 0 !important;
  padding-bottom: 2rem !important;
}

/* إصلاح العناوين داخل التبويبات */
.lg\\:col-span-3 .tab-content h3,
.lg\\:col-span-3 div[id$="-tab"] h3 {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  margin-bottom: 1.5rem !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  color: #111827 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding-left: 2rem !important;
  padding-right: 2rem !important;
  padding-top: 2rem !important;
}

/* إصلاح النماذج داخل التبويبات */
.lg\\:col-span-3 .tab-content form,
.lg\\:col-span-3 div[id$="-tab"] form {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  display: block !important;
  padding: 0 2rem 2rem 2rem !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

.lg\\:col-span-3 .tab-content .space-y-6,
.lg\\:col-span-3 div[id$="-tab"] .space-y-6 {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 1.5rem !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* إصلاح الشبكة للحقول */
.lg\\:col-span-3 .tab-content .grid {
  width: 100% !important;
  max-width: 100% !important;
  display: grid !important;
  box-sizing: border-box !important;
}

.lg\\:col-span-3 .tab-content .grid.grid-cols-1 {
  grid-template-columns: 1fr !important;
  gap: 1.5rem !important;
}

.lg\\:col-span-3 .tab-content .grid.grid-cols-1.md\\:grid-cols-2 {
  grid-template-columns: 1fr !important;
  gap: 1.5rem !important;
}

@media (min-width: 768px) {
  .lg\\:col-span-3 .tab-content .grid.grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr 1fr !important;
    gap: 2rem !important;
  }
}

/* إصلاح الحقول الفردية */
.lg\\:col-span-3 .tab-content .grid > div {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
}

/* إصلاح التسميات */
.lg\\:col-span-3 .tab-content label {
  width: 100% !important;
  display: block !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 0.5rem !important;
}

/* إصلاح حقول الإدخال */
.lg\\:col-span-3 .tab-content input,
.lg\\:col-span-3 .tab-content select,
.lg\\:col-span-3 .tab-content textarea {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
  padding: 0.75rem !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.5rem !important;
  font-size: 0.875rem !important;
  background: white !important;
  transition: border-color 0.2s ease !important;
}

.lg\\:col-span-3 .tab-content input:focus,
.lg\\:col-span-3 .tab-content select:focus,
.lg\\:col-span-3 .tab-content textarea:focus {
  outline: none !important;
  border-color: #059669 !important;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1) !important;
}

/* إصلاح أزرار الحفظ */
.lg\\:col-span-3 .tab-content .flex.justify-end {
  width: 100% !important;
  display: flex !important;
  justify-content: flex-end !important;
  margin-top: 2rem !important;
}

.lg\\:col-span-3 .tab-content button[type="submit"] {
  padding: 0.75rem 1.5rem !important;
  background: #059669 !important;
  color: white !important;
  border: none !important;
  border-radius: 0.5rem !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}

.lg\\:col-span-3 .tab-content button[type="submit"]:hover {
  background: #047857 !important;
}

/* إصلاح التجاوب للتبويبات */
@media (max-width: 1023px) {
  .lg\\:col-span-3 nav.-mb-px.flex.space-x-8.space-x-reverse.px-6 {
    padding: 0 1rem !important;
    gap: 0 !important;
  }

  .lg\\:col-span-3 .tab-button {
    padding: 0.75rem 0.25rem !important;
    font-size: 0.8rem !important;
  }

  .lg\\:col-span-3 .tab-content {
    padding: 1.5rem !important;
  }
}

@media (max-width: 768px) {
  .lg\\:col-span-3 nav.-mb-px.flex.space-x-8.space-x-reverse.px-6 {
    padding: 0 0.75rem !important;
    flex-direction: row !important;
    overflow-x: auto !important;
  }

  .lg\\:col-span-3 .tab-button {
    flex: 0 0 auto !important;
    min-width: 120px !important;
    padding: 0.75rem 0.5rem !important;
    font-size: 0.75rem !important;
  }

  .lg\\:col-span-3 .tab-content {
    padding: 1.25rem !important;
  }

  .lg\\:col-span-3 .tab-content .grid.grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr !important;
    gap: 1.25rem !important;
  }
}

@media (max-width: 640px) {
  .lg\\:col-span-3 nav.-mb-px.flex.space-x-8.space-x-reverse.px-6 {
    padding: 0 0.5rem !important;
  }

  .lg\\:col-span-3 .tab-button {
    min-width: 100px !important;
    padding: 0.5rem 0.25rem !important;
    font-size: 0.7rem !important;
  }

  .lg\\:col-span-3 .tab-content {
    padding: 1rem !important;
  }

  .lg\\:col-span-3 .tab-content .space-y-6 {
    gap: 1rem !important;
  }

  .lg\\:col-span-3 .tab-content .grid.grid-cols-1.md\\:grid-cols-2 {
    gap: 1rem !important;
  }
}

/* ========================================
   🚀 إصلاح نهائي وجذري للتبويبات - إزالة جميع القيود
======================================== */

/* إزالة padding من جميع عناصر التبويبات */
.lg\\:col-span-3 [class*="tab-content"],
.lg\\:col-span-3 [id*="tab"],
.lg\\:col-span-3 .p-6 {
  padding: 0 !important;
}

/* إعادة تعريف شامل للتبويبات */
.lg\\:col-span-3 > .bg-white.rounded-lg.shadow-sm.border.border-gray-200.mb-6 {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  margin: 0 0 1.5rem 0 !important;
  padding: 0 !important;
  overflow: visible !important;
}

/* إعادة تعريف nav التبويبات */
.lg\\:col-span-3 .border-b.border-gray-200 > nav {
  width: 100% !important;
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
}

/* إعادة تعريف أزرار التبويبات */
.lg\\:col-span-3 nav > .tab-button {
  flex: 1 !important;
  width: 100% !important;
  max-width: none !important;
  padding: 1.25rem 1rem !important;
  margin: 0 !important;
  border: none !important;
  border-bottom: 3px solid transparent !important;
  background: white !important;
  text-align: center !important;
}

/* إعادة تعريف محتوى التبويبات */
.lg\\:col-span-3 [id$="-tab"] {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  padding: 2rem !important;
  margin: 0 !important;
  background: white !important;
  border-radius: 0 0 0.75rem 0.75rem !important;
}

/* إعادة تعريف النماذج */
.lg\\:col-span-3 [id$="-tab"] form {
  width: 100% !important;
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* إعادة تعريف الحقول */
.lg\\:col-span-3 [id$="-tab"] .grid {
  width: 100% !important;
  max-width: 100% !important;
  display: grid !important;
}

.lg\\:col-span-3 [id$="-tab"] input,
.lg\\:col-span-3 [id$="-tab"] select,
.lg\\:col-span-3 [id$="-tab"] textarea {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* ========================================
   📱 نظام التجاوب الشامل للخطوط (Comprehensive Responsive Typography)
======================================== */

/* ========================================
   💻 شاشات متوسطة كبيرة (Large Tablets) - max-width: 1024px
======================================== */
@media (max-width: 1024px) {
  .text-9xl { font-size: 6rem !important; line-height: 1 !important; }
  .text-8xl { font-size: 5rem !important; line-height: 1 !important; }
  .text-7xl { font-size: 4rem !important; line-height: 1.1 !important; }
  .text-6xl { font-size: 3.25rem !important; line-height: 1.1 !important; }
  .text-5xl { font-size: 2.75rem !important; line-height: 1.1 !important; }
  .text-4xl { font-size: 2rem !important; line-height: 1.2 !important; }
  .text-3xl { font-size: 1.75rem !important; line-height: 1.3 !important; }
  .text-2xl { font-size: 1.4rem !important; line-height: 1.4 !important; }
  .text-xl { font-size: 1.2rem !important; line-height: 1.5 !important; }
  .text-lg { font-size: 1.05rem !important; line-height: 1.6 !important; }
}

/* ========================================
   📱 شاشات متوسطة (Tablets) - max-width: 768px
======================================== */
@media (max-width: 768px) {
  .text-9xl { font-size: 4rem !important; line-height: 1 !important; }
  .text-8xl { font-size: 3.5rem !important; line-height: 1 !important; }
  .text-7xl { font-size: 3rem !important; line-height: 1.1 !important; }
  .text-6xl { font-size: 2.5rem !important; line-height: 1.1 !important; }
  .text-5xl { font-size: 2.25rem !important; line-height: 1.1 !important; }
  .text-4xl { font-size: 1.875rem !important; line-height: 1.2 !important; }
  .text-3xl { font-size: 1.5rem !important; line-height: 1.3 !important; }
  .text-2xl { font-size: 1.25rem !important; line-height: 1.4 !important; }
  .text-xl { font-size: 1.125rem !important; line-height: 1.5 !important; }
  .text-lg { font-size: 1rem !important; line-height: 1.6 !important; }
  .text-base { font-size: 0.95rem !important; line-height: 1.6 !important; }
  .text-sm { font-size: 0.85rem !important; line-height: 1.5 !important; }
}

/* ========================================
   📱 شاشات صغيرة (Mobile) - max-width: 640px
======================================== */
@media (max-width: 640px) {
  .text-9xl { font-size: 3rem !important; line-height: 1 !important; }
  .text-8xl { font-size: 2.75rem !important; line-height: 1 !important; }
  .text-7xl { font-size: 2.5rem !important; line-height: 1.1 !important; }
  .text-6xl { font-size: 2.25rem !important; line-height: 1.1 !important; }
  .text-5xl { font-size: 2rem !important; line-height: 1.1 !important; }
  .text-4xl { font-size: 1.75rem !important; line-height: 1.2 !important; }
  .text-3xl { font-size: 1.375rem !important; line-height: 1.3 !important; }
  .text-2xl { font-size: 1.125rem !important; line-height: 1.4 !important; }
  .text-xl { font-size: 1rem !important; line-height: 1.5 !important; }
  .text-lg { font-size: 0.95rem !important; line-height: 1.6 !important; }
  .text-base { font-size: 0.9rem !important; line-height: 1.6 !important; }
  .text-sm { font-size: 0.8rem !important; line-height: 1.5 !important; }
  .text-xs { font-size: 0.7rem !important; line-height: 1.4 !important; }
}

/* ========================================
   📱 شاشات صغيرة جداً (Small Mobile) - max-width: 480px
======================================== */
@media (max-width: 480px) {
  .text-9xl { font-size: 2.5rem !important; line-height: 1 !important; }
  .text-8xl { font-size: 2.25rem !important; line-height: 1 !important; }
  .text-7xl { font-size: 2rem !important; line-height: 1.1 !important; }
  .text-6xl { font-size: 1.875rem !important; line-height: 1.1 !important; }
  .text-5xl { font-size: 1.75rem !important; line-height: 1.1 !important; }
  .text-4xl { font-size: 1.5rem !important; line-height: 1.2 !important; }
  .text-3xl { font-size: 1.25rem !important; line-height: 1.3 !important; }
  .text-2xl { font-size: 1.125rem !important; line-height: 1.4 !important; }
  .text-xl { font-size: 1rem !important; line-height: 1.5 !important; }
  .text-lg { font-size: 0.9rem !important; line-height: 1.6 !important; }
  .text-base { font-size: 0.85rem !important; line-height: 1.6 !important; }
  .text-sm { font-size: 0.75rem !important; line-height: 1.5 !important; }
  .text-xs { font-size: 0.65rem !important; line-height: 1.4 !important; }
}

/* ========================================
   📱 شاشات صغيرة جداً جداً (Extra Small) - max-width: 360px
======================================== */
@media (max-width: 360px) {
  .text-9xl { font-size: 2rem !important; line-height: 1 !important; }
  .text-8xl { font-size: 1.875rem !important; line-height: 1 !important; }
  .text-7xl { font-size: 1.75rem !important; line-height: 1.1 !important; }
  .text-6xl { font-size: 1.625rem !important; line-height: 1.1 !important; }
  .text-5xl { font-size: 1.5rem !important; line-height: 1.1 !important; }
  .text-4xl { font-size: 1.375rem !important; line-height: 1.2 !important; }
  .text-3xl { font-size: 1.125rem !important; line-height: 1.3 !important; }
  .text-2xl { font-size: 1rem !important; line-height: 1.4 !important; }
  .text-xl { font-size: 0.95rem !important; line-height: 1.5 !important; }
  .text-lg { font-size: 0.85rem !important; line-height: 1.6 !important; }
  .text-base { font-size: 0.8rem !important; line-height: 1.6 !important; }
  .text-sm { font-size: 0.7rem !important; line-height: 1.5 !important; }
  .text-xs { font-size: 0.6rem !important; line-height: 1.4 !important; }
}

/* ========================================
   🎨 تحسينات إضافية للتجاوب
======================================== */

/* تحسين المسافات والحشو للشاشات الصغيرة */
@media (max-width: 768px) {
  .p-8 { padding: 1rem !important; }
  .p-6 { padding: 0.75rem !important; }
  .p-4 { padding: 0.5rem !important; }
  .px-8 { padding-left: 1rem !important; padding-right: 1rem !important; }
  .py-8 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
  .m-8 { margin: 1rem !important; }
  .mx-8 { margin-left: 1rem !important; margin-right: 1rem !important; }
  .my-8 { margin-top: 1rem !important; margin-bottom: 1rem !important; }

  /* تحسين الأيقونات */
  .stats-icon {
    width: 2.5rem !important;
    height: 2.5rem !important;
    font-size: 1rem !important;
  }

  .w-12.h-12 {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }

  .w-16.h-16 {
    width: 3rem !important;
    height: 3rem !important;
  }
}

@media (max-width: 640px) {
  .p-6 { padding: 0.5rem !important; }
  .p-4 { padding: 0.375rem !important; }
  .px-6 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
  .py-6 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
  .m-6 { margin: 0.5rem !important; }
  .mx-6 { margin-left: 0.5rem !important; margin-right: 0.5rem !important; }
  .my-6 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !important; }

  /* تحسين الأيقونات للشاشات الصغيرة */
  .stats-icon {
    width: 2rem !important;
    height: 2rem !important;
    font-size: 0.875rem !important;
  }

  .w-12.h-12 {
    width: 2rem !important;
    height: 2rem !important;
  }

  .w-16.h-16 {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }
}

@media (max-width: 480px) {
  .p-4 { padding: 0.25rem !important; }
  .p-3 { padding: 0.25rem !important; }
  .px-4 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
  .py-4 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
  .m-4 { margin: 0.25rem !important; }
  .mx-4 { margin-left: 0.25rem !important; margin-right: 0.25rem !important; }
  .my-4 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !important; }
}

/* ========================================
   🎨 نهاية نظام التجاوب الشامل
======================================== */
