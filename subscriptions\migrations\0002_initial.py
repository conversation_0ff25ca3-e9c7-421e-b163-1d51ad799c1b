# Generated by Django 4.2.7 on 2025-06-01 18:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('subscriptions', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='studentsubscription',
            name='student',
            field=models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to=settings.AUTH_USER_MODEL, verbose_name='الطالب'),
        ),
        migrations.AddField(
            model_name='scheduledlesson',
            name='subscription',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scheduled_lessons', to='subscriptions.studentsubscription', verbose_name='الاشتراك'),
        ),
        migrations.AddField(
            model_name='scheduledlesson',
            name='teacher',
            field=models.ForeignKey(blank=True, limit_choices_to={'user_type': 'teacher'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scheduled_lessons_as_teacher', to=settings.AUTH_USER_MODEL, verbose_name='المعلم'),
        ),
        migrations.AddField(
            model_name='invoiceitem',
            name='invoice',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='subscriptions.invoice', verbose_name='الفاتورة'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئت بواسطة'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='subscription',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='invoice', to='subscriptions.studentsubscription', verbose_name='الاشتراك'),
        ),
        migrations.AddField(
            model_name='banktransferproof',
            name='payment',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='bank_proof', to='subscriptions.subscriptionpayment', verbose_name='الدفعة'),
        ),
        migrations.AddField(
            model_name='banktransferproof',
            name='verified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_transfers', to=settings.AUTH_USER_MODEL, verbose_name='تم التحقق بواسطة'),
        ),
        migrations.AlterUniqueTogether(
            name='scheduledlesson',
            unique_together={('subscription', 'lesson_number')},
        ),
    ]
