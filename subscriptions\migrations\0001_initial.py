# Generated by Django 4.2.7 on 2025-06-01 18:13

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BankTransferProof',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_receipt', models.ImageField(upload_to='bank_transfers/', verbose_name='إيصال التحويل')),
                ('sender_name', models.CharField(max_length=100, verbose_name='اسم المرسل')),
                ('transfer_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ التحويل')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('bank_name', models.CharField(max_length=100, verbose_name='اسم البنك')),
                ('reference_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم المرجع')),
                ('is_verified', models.BooleanField(default=False, verbose_name='تم التحقق')),
                ('verified_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
            ],
            options={
                'verbose_name': 'إثبات تحويل بنكي',
                'verbose_name_plural': 'إثباتات التحويلات البنكية',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('student_name', models.CharField(max_length=200, verbose_name='اسم الطالب')),
                ('student_email', models.EmailField(max_length=254, verbose_name='بريد الطالب الإلكتروني')),
                ('student_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='هاتف الطالب')),
                ('plan_name', models.CharField(max_length=200, verbose_name='اسم الباقة')),
                ('plan_description', models.TextField(verbose_name='وصف الباقة')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ الفرعي')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ الإجمالي')),
                ('currency', models.CharField(default='SAR', max_length=3, verbose_name='العملة')),
                ('issue_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الإصدار')),
                ('due_date', models.DateTimeField(verbose_name='تاريخ الاستحقاق')),
                ('paid_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الدفع')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسلة'), ('paid', 'مدفوعة'), ('overdue', 'متأخرة'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فاتورة',
                'verbose_name_plural': 'الفواتير',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=500, verbose_name='الوصف')),
                ('quantity', models.PositiveIntegerField(default=1, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر الإجمالي')),
            ],
            options={
                'verbose_name': 'عنصر فاتورة',
                'verbose_name_plural': 'عناصر الفاتورة',
            },
        ),
        migrations.CreateModel(
            name='ScheduledLesson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lesson_number', models.PositiveIntegerField(help_text='رقم الحصة ضمن الاشتراك', verbose_name='رقم الحصة')),
                ('scheduled_date', models.DateTimeField(verbose_name='تاريخ ووقت الحصة')),
                ('duration_minutes', models.PositiveIntegerField(default=60, verbose_name='مدة الحصة بالدقائق')),
                ('status', models.CharField(choices=[('scheduled', 'مجدولة'), ('converted_to_live', 'تم تحويلها إلى حصة مباشرة'), ('completed', 'مكتملة'), ('cancelled', 'ملغية'), ('rescheduled', 'معاد جدولتها'), ('no_show', 'غياب بدون عذر')], default='scheduled', max_length=20, verbose_name='حالة الحصة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('live_lesson_id', models.PositiveIntegerField(blank=True, help_text='معرف الحصة المباشرة المرتبطة عند التحويل', null=True, verbose_name='معرف الحصة المباشرة')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'حصة مجدولة',
                'verbose_name_plural': 'حصص مجدولة',
                'ordering': ['scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='StudentSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('payment_pending', 'في انتظار الدفع'), ('pending_approval', 'في انتظار الموافقة'), ('active', 'نشط'), ('expired', 'منتهي'), ('cancelled', 'ملغي'), ('suspended', 'معلق')], default='pending', max_length=20, verbose_name='الحالة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('remaining_lessons', models.PositiveIntegerField(verbose_name='الحصص المتبقية')),
                ('amount_paid', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ المدفوع')),
                ('auto_renewal', models.BooleanField(default=False, verbose_name='تجديد تلقائي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الاشتراك')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'اشتراك طالب',
                'verbose_name_plural': 'اشتراكات الطلاب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الباقة')),
                ('description', models.TextField(verbose_name='وصف الباقة')),
                ('plan_type', models.CharField(choices=[('basic', 'باقة أساسية'), ('standard', 'باقة قياسية'), ('premium', 'باقة مميزة'), ('vip', 'باقة VIP')], max_length=20, verbose_name='نوع الباقة')),
                ('duration_type', models.CharField(choices=[('monthly', 'شهرية'), ('quarterly', 'ربع سنوية'), ('semi_annual', 'نصف سنوية'), ('annual', 'سنوية')], max_length=20, verbose_name='مدة الاشتراك')),
                ('duration_days', models.PositiveIntegerField(verbose_name='مدة الباقة بالأيام')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الباقة')),
                ('currency', models.CharField(choices=[('SAR', 'ريال سعودي (SAR)'), ('USD', 'دولار أمريكي (USD)'), ('EUR', 'يورو (EUR)'), ('GBP', 'جنيه إسترليني (GBP)'), ('AED', 'درهم إماراتي (AED)'), ('KWD', 'دينار كويتي (KWD)'), ('QAR', 'ريال قطري (QAR)'), ('BHD', 'دينار بحريني (BHD)'), ('OMR', 'ريال عماني (OMR)'), ('JOD', 'دينار أردني (JOD)'), ('EGP', 'جنيه مصري (EGP)'), ('LBP', 'ليرة لبنانية (LBP)'), ('SYP', 'ليرة سورية (SYP)'), ('IQD', 'دينار عراقي (IQD)'), ('YER', 'ريال يمني (YER)'), ('MAD', 'درهم مغربي (MAD)'), ('TND', 'دينار تونسي (TND)'), ('DZD', 'دينار جزائري (DZD)'), ('LYD', 'دينار ليبي (LYD)'), ('SDG', 'جنيه سوداني (SDG)'), ('JPY', 'ين ياباني (JPY)'), ('CNY', 'يوان صيني (CNY)'), ('INR', 'روبية هندية (INR)'), ('KRW', 'وون كوري جنوبي (KRW)'), ('SGD', 'دولار سنغافوري (SGD)'), ('MYR', 'رينغت ماليزي (MYR)'), ('THB', 'بات تايلاندي (THB)'), ('IDR', 'روبية إندونيسية (IDR)'), ('PHP', 'بيزو فلبيني (PHP)'), ('VND', 'دونغ فيتنامي (VND)'), ('PKR', 'روبية باكستانية (PKR)'), ('BDT', 'تاكا بنغلاديشية (BDT)'), ('LKR', 'روبية سريلانكية (LKR)'), ('NPR', 'روبية نيبالية (NPR)'), ('AFN', 'أفغاني أفغانستاني (AFN)'), ('IRR', 'ريال إيراني (IRR)'), ('TRY', 'ليرة تركية (TRY)'), ('RUB', 'روبل روسي (RUB)'), ('UAH', 'هريفنيا أوكرانية (UAH)'), ('PLN', 'زلوتي بولندي (PLN)'), ('CZK', 'كورونا تشيكية (CZK)'), ('HUF', 'فورنت هنغاري (HUF)'), ('RON', 'ليو روماني (RON)'), ('BGN', 'ليف بلغاري (BGN)'), ('CAD', 'دولار كندي (CAD)'), ('AUD', 'دولار أسترالي (AUD)'), ('NZD', 'دولار نيوزيلندي (NZD)'), ('CHF', 'فرنك سويسري (CHF)'), ('SEK', 'كرونا سويدية (SEK)'), ('NOK', 'كرونا نرويجية (NOK)'), ('DKK', 'كرونا دنماركية (DKK)'), ('BRL', 'ريال برازيلي (BRL)'), ('ARS', 'بيزو أرجنتيني (ARS)'), ('CLP', 'بيزو تشيلي (CLP)'), ('COP', 'بيزو كولومبي (COP)'), ('PEN', 'سول بيروفي (PEN)'), ('MXN', 'بيزو مكسيكي (MXN)'), ('ZAR', 'راند جنوب أفريقي (ZAR)'), ('KES', 'شلن كيني (KES)'), ('NGN', 'نايرا نيجيرية (NGN)'), ('GHS', 'سيدي غاني (GHS)'), ('XOF', 'فرنك غرب أفريقي (XOF)'), ('XAF', 'فرنك وسط أفريقي (XAF)')], default='SAR', max_length=3, verbose_name='العملة')),
                ('lessons_count', models.PositiveIntegerField(verbose_name='عدد الحصص')),
                ('lesson_duration', models.PositiveIntegerField(choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')], default=45, verbose_name='مدة الحصة (بالدقائق)')),
                ('lessons_per_week', models.PositiveIntegerField(default=2, help_text='عدد الحصص في الأسبوع الواحد', verbose_name='عدد الحصص أسبوعياً')),
                ('preferred_days', models.JSONField(default=list, help_text='أرقام أيام الأسبوع: 0=أحد، 1=اثنين، 2=ثلاثاء، 3=أربعاء، 4=خميس، 5=جمعة، 6=سبت', verbose_name='أيام الأسبوع المفضلة')),
                ('preferred_times', models.JSONField(default=list, help_text='قائمة بالأوقات المفضلة بصيغة HH:MM مثل: ["16:00", "18:00"]', verbose_name='الأوقات المفضلة')),
                ('auto_schedule', models.BooleanField(default=True, help_text='تفعيل الجدولة التلقائية للحصص عند الموافقة على الاشتراك', verbose_name='جدولة تلقائية')),
                ('features', models.JSONField(default=list, help_text='قائمة بمميزات الباقة', verbose_name='مميزات الباقة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('is_featured', models.BooleanField(default=False, verbose_name='مميزة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'باقة اشتراك',
                'verbose_name_plural': 'باقات الاشتراك',
                'ordering': ['plan_type', 'price'],
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('paypal', 'PayPal'), ('stripe', 'Stripe'), ('bank_transfer', 'تحويل بنكي'), ('credit_card', 'بطاقة ائتمان'), ('wallet', 'محفظة إلكترونية')], max_length=20, verbose_name='طريقة الدفع')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('processing', 'قيد المعالجة'), ('completed', 'مكتمل'), ('failed', 'فاشل'), ('refunded', 'مسترد'), ('cancelled', 'ملغي')], default='pending', max_length=15, verbose_name='حالة الدفع')),
                ('transaction_id', models.CharField(blank=True, max_length=200, null=True, verbose_name='معرف المعاملة')),
                ('gateway_response', models.JSONField(blank=True, default=dict, verbose_name='استجابة بوابة الدفع')),
                ('payment_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الدفع')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المعالجة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='subscriptions.studentsubscription', verbose_name='الاشتراك')),
            ],
            options={
                'verbose_name': 'دفعة اشتراك',
                'verbose_name_plural': 'دفعات الاشتراكات',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.AddField(
            model_name='studentsubscription',
            name='plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='subscriptions.subscriptionplan', verbose_name='الباقة'),
        ),
    ]
