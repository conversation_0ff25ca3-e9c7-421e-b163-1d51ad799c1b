from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
import json

from .models import LiveLesson, ComprehensiveLessonReport
from users.models import User


@login_required
@require_http_methods(["POST"])
def start_live_lesson_api(request, lesson_id):
    """API لبدء الحصة المباشرة"""
    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id, teacher=request.user)
        
        if live_lesson.status != 'scheduled':
            return JsonResponse({
                'success': False,
                'message': 'لا يمكن بدء هذه الحصة'
            })
        
        # تحديث حالة الحصة
        live_lesson.status = 'live'
        live_lesson.started_at = timezone.now()
        live_lesson.save()
        
        # إنشاء تقرير شامل للحصة
        lesson_type = 'trial' if hasattr(live_lesson, 'lesson_type') and live_lesson.lesson_type == 'trial' else 'subscription'
        
        ComprehensiveLessonReport.objects.get_or_create(
            live_lesson=live_lesson,
            teacher=live_lesson.teacher,
            student=live_lesson.student,
            defaults={
                'lesson_type': lesson_type,
            }
        )
        
        return JsonResponse({
            'success': True,
            'message': 'تم بدء الحصة بنجاح'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def end_live_lesson_api(request, lesson_id):
    """API لإنهاء الحصة المباشرة"""
    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id, teacher=request.user)
        
        if live_lesson.status != 'live':
            return JsonResponse({
                'success': False,
                'message': 'الحصة ليست مباشرة حالياً'
            })
        
        # تحديث حالة الحصة
        live_lesson.status = 'completed'
        live_lesson.ended_at = timezone.now()
        live_lesson.save()
        
        return JsonResponse({
            'success': True,
            'message': 'تم إنهاء الحصة بنجاح'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def time_up_lesson_api(request, lesson_id):
    """API لإشعار انتهاء وقت الحصة"""
    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)
        
        # التحقق من أن المستخدم مشارك في الحصة
        if request.user != live_lesson.teacher and request.user != live_lesson.student:
            return JsonResponse({
                'success': False,
                'message': 'غير مصرح'
            })
        
        # تسجيل انتهاء الوقت
        live_lesson.time_up_notified = True
        live_lesson.save()
        
        return JsonResponse({
            'success': True,
            'message': 'تم تسجيل انتهاء الوقت'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })


@login_required
@require_http_methods(["GET"])
def lesson_status_api(request, lesson_id):
    """API للتحقق من حالة الحصة"""
    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)
        
        # التحقق من أن المستخدم مشارك في الحصة
        if request.user != live_lesson.teacher and request.user != live_lesson.student:
            return JsonResponse({
                'success': False,
                'message': 'غير مصرح'
            })
        
        return JsonResponse({
            'success': True,
            'status': live_lesson.status,
            'started_at': live_lesson.started_at.isoformat() if live_lesson.started_at else None,
            'ended_at': live_lesson.ended_at.isoformat() if live_lesson.ended_at else None,
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def submit_teacher_report_api(request, lesson_id):
    """API لإرسال تقرير المعلم"""
    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id, teacher=request.user)
        
        # الحصول على التقرير الشامل أو إنشاؤه
        lesson_type = 'trial' if hasattr(live_lesson, 'lesson_type') and live_lesson.lesson_type == 'trial' else 'subscription'
        
        report, created = ComprehensiveLessonReport.objects.get_or_create(
            live_lesson=live_lesson,
            teacher=live_lesson.teacher,
            student=live_lesson.student,
            defaults={
                'lesson_type': lesson_type,
            }
        )
        
        # تحديث بيانات التقرير
        with transaction.atomic():
            report.teacher_report_submitted = True
            report.teacher_report_date = timezone.now()
            
            # تقييمات المعلم للطالب
            report.student_performance = request.POST.get('student_performance')
            report.student_participation = request.POST.get('student_participation')
            report.student_understanding = request.POST.get('student_understanding')
            report.overall_lesson_rating = request.POST.get('overall_lesson_rating')
            
            # النصوص
            report.lesson_summary = request.POST.get('lesson_summary', '')
            report.strengths = request.POST.get('strengths', '')
            report.areas_for_improvement = request.POST.get('areas_for_improvement', '')
            report.additional_notes = request.POST.get('additional_notes', '')
            
            report.save()
            
            # تحديث حالة الحصة
            live_lesson.teacher_report_submitted = True
            live_lesson.save()
        
        return JsonResponse({
            'success': True,
            'message': 'تم إرسال التقرير بنجاح'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def submit_student_rating_api(request, lesson_id):
    """API لإرسال تقييم الطالب"""
    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id, student=request.user)
        
        # الحصول على التقرير الشامل أو إنشاؤه
        lesson_type = 'trial' if hasattr(live_lesson, 'lesson_type') and live_lesson.lesson_type == 'trial' else 'subscription'
        
        report, created = ComprehensiveLessonReport.objects.get_or_create(
            live_lesson=live_lesson,
            teacher=live_lesson.teacher,
            student=live_lesson.student,
            defaults={
                'lesson_type': lesson_type,
            }
        )
        
        # تحديث بيانات التقييم
        with transaction.atomic():
            report.student_evaluation_submitted = True
            report.student_evaluation_date = timezone.now()
            
            # تقييمات الطالب للمعلم
            report.overall_rating = request.POST.get('overall_rating')
            report.teaching_quality = request.POST.get('teaching_quality')
            report.lesson_content = request.POST.get('lesson_content')
            report.interaction_quality = request.POST.get('interaction_quality')
            report.punctuality = request.POST.get('punctuality')
            
            # التعليق
            report.student_comment = request.POST.get('student_comment', '')
            
            # أسئلة خاصة بالحصص التجريبية
            if lesson_type == 'trial':
                would_recommend = request.POST.get('would_recommend')
                interested_in_subscription = request.POST.get('interested_in_subscription')
                
                if would_recommend is not None:
                    report.would_recommend = would_recommend == 'true'
                if interested_in_subscription is not None:
                    report.interested_in_subscription = interested_in_subscription == 'true'
            
            report.save()
            
            # تحديث حالة الحصة
            live_lesson.student_evaluation_submitted = True
            live_lesson.save()
        
        return JsonResponse({
            'success': True,
            'message': 'تم إرسال التقييم بنجاح'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })
