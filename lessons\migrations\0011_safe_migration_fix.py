# Generated manually to fix migration issues on fresh database
from django.db import migrations


def check_table_exists(schema_editor, table_name):
    """Check if a table exists in the database"""
    with schema_editor.connection.cursor() as cursor:
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name=%s;
        """, [table_name])
        return cursor.fetchone() is not None


def safe_remove_field(apps, schema_editor):
    """Safely remove fields only if the table exists"""
    # Check if the problematic tables exist
    tables_to_check = [
        'lessons_lessonrating',
        'lessons_teacherlessonreport'
    ]
    
    for table_name in tables_to_check:
        if check_table_exists(schema_editor, table_name):
            print(f"Table {table_name} exists, migration will proceed normally")
        else:
            print(f"Table {table_name} does not exist, skipping related operations")


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0010_add_unified_lesson_system'),
    ]

    operations = [
        migrations.RunPython(
            safe_remove_field,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
