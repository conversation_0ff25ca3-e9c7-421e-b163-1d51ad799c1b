{% extends 'base.html' %}
{% load static %}

{% block title %}{{ system_message.title }}{% endblock %}

{% block extra_css %}
<style>
    .message-detail-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .message-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .message-header {
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 1.5rem;
        margin-bottom: 2rem;
    }

    .message-title {
        font-size: 2rem;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 1rem;
    }

    .message-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        align-items: center;
        margin-bottom: 1rem;
    }

    .message-type-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .priority-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .priority-low { background: #f3f4f6; color: #6b7280; }
    .priority-medium { background: #dbeafe; color: #1d4ed8; }
    .priority-high { background: #fed7aa; color: #ea580c; }
    .priority-urgent { background: #fecaca; color: #dc2626; }

    .type-announcement { background: #dbeafe; color: #1d4ed8; }
    .type-warning { background: #fecaca; color: #dc2626; }
    .type-reminder { background: #fef3c7; color: #d97706; }
    .type-update { background: #d1fae5; color: #059669; }
    .type-maintenance { background: #e9d5ff; color: #7c3aed; }
    .type-policy { background: #e0e7ff; color: #4338ca; }
    .type-feature { background: #ccfbf1; color: #0d9488; }
    .type-urgent { background: #fecaca; color: #dc2626; }

    .message-content {
        font-size: 1.125rem;
        line-height: 1.8;
        color: #374151;
        white-space: pre-line;
    }

    .message-info {
        background: #f9fafb;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .info-icon {
        width: 2.5rem;
        height: 2.5rem;
        background: #3b82f6;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-weight: 600;
        color: #1f2937;
    }

    .btn {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .btn-primary {
        background: #3b82f6;
        color: white;
    }

    .btn-primary:hover {
        background: #2563eb;
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
    }

    .alert {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }

    .alert-warning {
        background: #fef3c7;
        color: #92400e;
        border: 1px solid #fbbf24;
    }

    .alert-success {
        background: #d1fae5;
        color: #065f46;
        border: 1px solid #34d399;
    }

    .read-status {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .read-status.read {
        background: #d1fae5;
        color: #065f46;
    }

    .read-status.unread {
        background: #fef3c7;
        color: #92400e;
    }
</style>
{% endblock %}

{% block content %}
<div class="message-detail-container">
    <div class="container mx-auto px-4 py-8">
        <!-- Back Button -->
        <div class="max-w-4xl mx-auto mb-6">
            <a href="{% url 'system_messages' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة إلى رسائل النظام
            </a>
        </div>

        <!-- Message Card -->
        <div class="max-w-4xl mx-auto">
            <div class="message-card">
                <!-- Message Header -->
                <div class="message-header">
                    <div class="flex items-start justify-between mb-4">
                        <h1 class="message-title">{{ system_message.title }}</h1>
                        <div class="read-status {% if message_receipt.is_read %}read{% else %}unread{% endif %}">
                            {% if message_receipt.is_read %}
                                <i class="fas fa-check-circle"></i>
                                مقروءة
                            {% else %}
                                <i class="fas fa-envelope"></i>
                                جديدة
                            {% endif %}
                        </div>
                    </div>

                    <!-- Message Meta -->
                    <div class="message-meta">
                        <span class="message-type-badge type-{{ system_message.message_type }}">
                            <i class="fas fa-tag ml-1"></i>
                            {{ system_message.get_message_type_display }}
                        </span>

                        <span class="priority-badge priority-{{ system_message.priority }}">
                            <i class="fas fa-exclamation-triangle ml-1"></i>
                            {{ system_message.get_priority_display }}
                        </span>

                        {% if system_message.is_expired %}
                            <span class="priority-badge priority-urgent">
                                <i class="fas fa-clock ml-1"></i>
                                منتهية الصلاحية
                            </span>
                        {% endif %}
                    </div>

                    <!-- Expiry Warning -->
                    {% if system_message.is_expired %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle ml-2"></i>
                            <strong>تنبيه:</strong> هذه الرسالة منتهية الصلاحية منذ {{ system_message.expires_at|date:"Y/m/d H:i" }}
                        </div>
                    {% elif system_message.expires_at %}
                        <div class="alert alert-warning">
                            <i class="fas fa-clock ml-2"></i>
                            <strong>ملاحظة:</strong> هذه الرسالة ستنتهي صلاحيتها في {{ system_message.expires_at|date:"Y/m/d H:i" }}
                        </div>
                    {% endif %}

                    <!-- Read Confirmation -->
                    {% if message_receipt.is_read %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle ml-2"></i>
                            تم تمييز هذه الرسالة كمقروءة في {{ message_receipt.read_at|date:"Y/m/d H:i" }}
                        </div>
                    {% endif %}
                </div>

                <!-- Message Content -->
                <div class="message-content">
                    {{ system_message.content|safe }}
                </div>

                <!-- Message Info -->
                <div class="message-info">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-info-circle text-blue-600 ml-2"></i>
                        معلومات الرسالة
                    </h3>

                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">مُرسل بواسطة</div>
                                <div class="info-value">{{ system_message.sent_by.get_full_name }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">تاريخ الإرسال</div>
                                <div class="info-value">{{ system_message.created_at|date:"Y/m/d H:i" }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">تاريخ الاستلام</div>
                                <div class="info-value">{{ message_receipt.created_at|date:"Y/m/d H:i" }}</div>
                            </div>
                        </div>

                        {% if system_message.expires_at %}
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">تاريخ انتهاء الصلاحية</div>
                                <div class="info-value">{{ system_message.expires_at|date:"Y/m/d H:i" }}</div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">حالة القراءة</div>
                                <div class="info-value">
                                    {% if message_receipt.is_read %}
                                        <span class="text-green-600">مقروءة</span>
                                    {% else %}
                                        <span class="text-orange-600">غير مقروءة</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">مصدر الرسالة</div>
                                <div class="info-value">مركز الدعم الفني</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <a href="{% url 'system_messages' %}" class="btn btn-secondary">
                        <i class="fas fa-list ml-2"></i>
                        جميع رسائل النظام
                    </a>

                    <div class="flex gap-3">
                        {% if not message_receipt.is_read %}
                            <button onclick="markAsRead()" class="btn btn-primary" id="markReadBtn">
                                <i class="fas fa-check ml-2"></i>
                                تمييز كمقروءة
                            </button>
                        {% endif %}

                        <button onclick="window.print()" class="btn btn-secondary">
                            <i class="fas fa-print ml-2"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Mark message as read
function markAsRead() {
    const btn = document.getElementById('markReadBtn');
    if (!btn) return;

    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري التحديث...';

    fetch('{% url "mark_system_message_read" system_message.id %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token }}',
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            btn.style.display = 'none';

            // Update read status
            const readStatus = document.querySelector('.read-status');
            readStatus.className = 'read-status read';
            readStatus.innerHTML = '<i class="fas fa-check-circle"></i> مقروءة';

            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success';
            alert.innerHTML = '<i class="fas fa-check-circle ml-2"></i>تم تمييز الرسالة كمقروءة بنجاح';

            const messageHeader = document.querySelector('.message-header');
            messageHeader.appendChild(alert);

            // Remove alert after 3 seconds
            setTimeout(() => alert.remove(), 3000);
        } else {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-check ml-2"></i>تمييز كمقروءة';
            alert('حدث خطأ في تحديث حالة الرسالة');
        }
    })
    .catch(error => {
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-check ml-2"></i>تمييز كمقروءة';
        alert('حدث خطأ في الاتصال');
    });
}

// Auto-mark as read after 5 seconds
{% if not message_receipt.is_read %}
setTimeout(() => {
    if (document.getElementById('markReadBtn')) {
        markAsRead();
    }
}, 5000);
{% endif %}
</script>
{% endblock %}
