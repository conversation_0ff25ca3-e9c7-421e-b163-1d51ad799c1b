{% extends 'base.html' %}

{% block title %}الملف الشخصي - {{ academy_settings.academy_slogan|default:"نظام قرآنيا التعليمي" }}{% endblock %}

{% block extra_css %}
<style>
/* تصميم متجاوب مبسط لصفحة الملف الشخصي */
.profile-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-family: 'Cairo', sans-serif;
}

.profile-header {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%);
    color: white;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="2" fill="white" opacity="0.1"/><circle cx="40" cy="60" r="1" fill="white" opacity="0.1"/></svg>');
    opacity: 0.3;
}

.profile-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
    position: relative;
    z-index: 1;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.main-content {
    padding: 2rem 0;
}

.main-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    max-width: 800px;
}

.tabs-nav {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tabs-nav::-webkit-scrollbar {
    display: none;
}

.tab-btn {
    flex: 1;
    min-width: 150px;
    padding: 1rem 1.5rem;
    background: transparent;
    border: none;
    color: #64748b;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    white-space: nowrap;
    font-size: 0.9rem;
}

.tab-btn:hover {
    color: #2D5016;
    background: rgba(45, 80, 22, 0.05);
}

.tab-btn.active {
    color: #2D5016;
    background: white;
    font-weight: 600;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #2D5016;
}

.tab-content {
    padding: 2rem;
    display: none;
}

.tab-content.active {
    display: block;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.form-input {
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: #fafafa;
    width: 100%;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #2D5016;
    background: white;
    box-shadow: 0 0 0 3px rgba(45, 80, 22, 0.1);
}

.btn {
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #2D5016 0%, #1A3009 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(45, 80, 22, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(45, 80, 22, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(220, 38, 38, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(220, 38, 38, 0.4);
}



.alert {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-success {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.alert-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.alert-info {
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    color: #1d4ed8;
}

.photo-upload {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.photo-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #e5e7eb;
    object-fit: cover;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #9ca3af;
}

.file-input-wrapper {
    flex: 1;
}

.file-input-wrapper input[type="file"] {
    width: 100%;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
    .profile-header {
        padding: 1.5rem 0;
    }

    .main-content {
        padding: 1.5rem 0;
    }

    .main-card {
        margin: 0 1rem;
    }

    .tab-content {
        padding: 1.5rem;
    }

    .tab-btn {
        min-width: 120px;
        padding: 0.875rem 1rem;
        font-size: 0.8rem;
    }

    .photo-upload {
        flex-direction: column;
        text-align: center;
    }

    .form-actions {
        justify-content: center;
    }
}

@media (max-width: 640px) {
    .profile-container {
        padding: 0 0.75rem;
    }

    .main-card {
        border-radius: 12px;
        margin: 0 0.5rem;
    }

    .tab-content {
        padding: 1rem;
    }

    .tab-btn {
        min-width: 100px;
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }

    .form-grid {
        gap: 1rem;
        grid-template-columns: 1fr;
    }

    .photo-upload {
        gap: 1rem;
    }

    .photo-preview {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .profile-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .profile-header .flex {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .profile-header .flex .flex {
        flex-direction: row;
        gap: 1rem;
        justify-content: center;
        align-items: center;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- عنصر مخفي لتخزين المنطقة الزمنية للمستخدم -->
<input type="hidden" id="user-timezone" value="{{ profile.timezone|default:'Asia/Riyadh' }}">

<div class="profile-page">
    <!-- Header -->
    <div class="profile-header">
        <div class="profile-container">
            <div class="flex items-center mb-6">
                <a href="{% if user.user_type == 'admin' %}{% url 'admin_dashboard' %}{% elif user.user_type == 'teacher' %}{% url 'teacher_dashboard' %}{% else %}{% url 'student_dashboard' %}{% endif %}" 
                   class="text-white hover:text-green-200 transition-colors ml-4 p-2 rounded-lg hover:bg-white/10">
                    <i class="fas fa-arrow-right text-xl"></i>
                </a>
                <div>
                    <h1 class="text-3xl font-bold">الملف الشخصي</h1>
                    <p class="text-green-100 mt-1">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
                </div>
            </div>
            
            <!-- معلومات المستخدم -->
            <div class="flex items-center gap-6">
                {% if user.profile_picture %}
                    <img class="profile-avatar" src="{{ user.profile_picture.url }}" alt="صورة الملف الشخصي">
                {% else %}
                    <div class="profile-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                {% endif %}
                <div>
                    <h2 class="text-2xl font-bold">{{ user.get_full_name|default:user.username }}</h2>
                    <p class="text-green-100 text-lg">{{ user.get_user_type_display }}</p>
                    <p class="text-green-200 text-sm">عضو منذ {{ user.date_joined|date:"Y/m/d" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    {% if messages %}
        <div class="profile-container" style="padding-top: 1rem;">
            {% for message in messages %}
                <div class="alert {% if message.tags == 'success' %}alert-success{% elif message.tags == 'error' %}alert-error{% else %}alert-info{% endif %}">
                    <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-circle{% else %}info-circle{% endif %}"></i>
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <div class="profile-container">
            <!-- المحتوى الرئيسي -->
            <div class="main-card">
                    <!-- التبويبات -->
                    <div class="tabs-nav">
                        <button class="tab-btn active" data-tab="basic">
                            <i class="fas fa-user ml-2"></i>
                            المعلومات الأساسية
                        </button>
                        <button class="tab-btn" data-tab="additional">
                            <i class="fas fa-info-circle ml-2"></i>
                            المعلومات الإضافية
                        </button>
                        <button class="tab-btn" data-tab="security">
                            <i class="fas fa-shield-alt ml-2"></i>
                            الأمان
                        </button>
                    </div>

                    <!-- محتوى التبويب الأول: المعلومات الأساسية -->
                    <div id="basic-tab" class="tab-content active">
                        <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                            <i class="fas fa-user" style="color: #2D5016;" class="ml-3"></i>
                            المعلومات الأساسية
                        </h3>

                        <form method="post" enctype="multipart/form-data">
                            {% csrf_token %}
                            <input type="hidden" name="form_type" value="profile">

                            <!-- صورة الملف الشخصي -->
                            <div class="photo-upload">
                                {% if user.profile_picture %}
                                    <img class="photo-preview" src="{{ user.profile_picture.url }}" alt="صورة الملف الشخصي" id="profile-preview">
                                {% else %}
                                    <div class="photo-preview" id="profile-preview">
                                        <i class="fas fa-user"></i>
                                    </div>
                                {% endif %}
                                <div class="file-input-wrapper">
                                    <label class="form-label">صورة الملف الشخصي</label>
                                    {{ form.profile_picture }}
                                    <p class="text-xs text-gray-500 mt-2">يُسمح بملفات JPG, PNG, GIF, WebP. الحد الأقصى: 5 ميجابايت</p>
                                    {% if form.profile_picture.errors %}
                                        {% for error in form.profile_picture.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>

                            <!-- الحقول الأساسية -->
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">{{ form.first_name.label }}</label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        {% for error in form.first_name.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label class="form-label">{{ form.last_name.label }}</label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        {% for error in form.last_name.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label class="form-label">{{ form.email.label }}</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        {% for error in form.email.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label class="form-label">{{ form.phone.label }}</label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        {% for error in form.phone.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>

                                {% if form.date_of_birth %}
                                <div class="form-group">
                                    <label class="form-label">{{ form.date_of_birth.label }}</label>
                                    {{ form.date_of_birth }}
                                    {% if form.date_of_birth.errors %}
                                        {% for error in form.date_of_birth.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                {% endif %}

                                {% if form.student_level %}
                                <div class="form-group">
                                    <label class="form-label">{{ form.student_level.label }}</label>
                                    {{ form.student_level }}
                                    {% if form.student_level.errors %}
                                        {% for error in form.student_level.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>

                            {% if form.bio %}
                            <div class="form-group">
                                <label class="form-label">{{ form.bio.label }}</label>
                                {{ form.bio }}
                                {% if form.bio.errors %}
                                    {% for error in form.bio.errors %}
                                        <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            {% endif %}

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- محتوى التبويب الثاني: المعلومات الإضافية -->
                    <div id="additional-tab" class="tab-content">
                        <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                            <i class="fas fa-info-circle" style="color: #2D5016;" class="ml-3"></i>
                            المعلومات الإضافية
                        </h3>

                        <form method="post">
                            {% csrf_token %}
                            <input type="hidden" name="form_type" value="additional">

                            <div class="form-grid">
                                {% if additional_form.emergency_contact %}
                                <div class="form-group">
                                    <label class="form-label">{{ additional_form.emergency_contact.label }}</label>
                                    {{ additional_form.emergency_contact }}
                                    {% if additional_form.emergency_contact.errors %}
                                        {% for error in additional_form.emergency_contact.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                {% endif %}

                                {% if additional_form.emergency_phone %}
                                <div class="form-group">
                                    <label class="form-label">{{ additional_form.emergency_phone.label }}</label>
                                    {{ additional_form.emergency_phone }}
                                    {% if additional_form.emergency_phone.errors %}
                                        {% for error in additional_form.emergency_phone.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                {% endif %}

                                {% if additional_form.preferred_language %}
                                <div class="form-group">
                                    <label class="form-label">{{ additional_form.preferred_language.label }}</label>
                                    {{ additional_form.preferred_language }}
                                    {% if additional_form.preferred_language.errors %}
                                        {% for error in additional_form.preferred_language.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                {% endif %}

                                {% if additional_form.timezone %}
                                <div class="form-group">
                                    <label class="form-label">{{ additional_form.timezone.label }}</label>
                                    {{ additional_form.timezone }}
                                    {% if additional_form.timezone.errors %}
                                        {% for error in additional_form.timezone.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>

                            {% if additional_form.address %}
                            <div class="form-group">
                                <label class="form-label">{{ additional_form.address.label }}</label>
                                {{ additional_form.address }}
                                {% if additional_form.address.errors %}
                                    {% for error in additional_form.address.errors %}
                                        <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            {% endif %}

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- محتوى التبويب الثالث: الأمان -->
                    <div id="security-tab" class="tab-content">
                        <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                            <i class="fas fa-shield-alt" style="color: #2D5016;" class="ml-3"></i>
                            إعدادات الأمان
                        </h3>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-yellow-600 ml-3"></i>
                                <div>
                                    <h4 class="text-sm font-medium text-yellow-800">تغيير كلمة المرور</h4>
                                    <p class="text-sm text-yellow-700 mt-1">تأكد من استخدام كلمة مرور قوية تحتوي على أحرف وأرقام ورموز</p>
                                </div>
                            </div>
                        </div>

                        <form method="post">
                            {% csrf_token %}
                            <input type="hidden" name="form_type" value="password">

                            <div class="space-y-4">
                                <div class="form-group">
                                    <label class="form-label">{{ password_form.current_password.label }}</label>
                                    {{ password_form.current_password }}
                                    {% if password_form.current_password.errors %}
                                        {% for error in password_form.current_password.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label class="form-label">{{ password_form.new_password.label }}</label>
                                    {{ password_form.new_password }}
                                    {% if password_form.new_password.errors %}
                                        {% for error in password_form.new_password.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label class="form-label">{{ password_form.confirm_password.label }}</label>
                                    {{ password_form.confirm_password }}
                                    {% if password_form.confirm_password.errors %}
                                        {% for error in password_form.confirm_password.errors %}
                                            <p class="text-red-500 text-sm mt-1">{{ error }}</p>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-key"></i>
                                    تغيير كلمة المرور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إعداد التبويبات
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // إزالة الفئة النشطة من جميع الأزرار
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // إخفاء جميع محتويات التبويبات
            tabContents.forEach(content => content.classList.remove('active'));

            // تفعيل الزر المحدد
            this.classList.add('active');

            // إظهار المحتوى المحدد
            const targetContent = document.getElementById(targetTab + '-tab');
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });

    // إعداد حقول النموذج بالأنماط الحديثة
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.classList.add('form-input');

        // إضافة تأثيرات التفاعل
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });

    // إعداد تحديث المنطقة الزمنية تلقائياً
    const timezoneField = document.querySelector('select[name="timezone"]');
    if (timezoneField && !timezoneField.value) {
        // محاولة تحديد المنطقة الزمنية تلقائياً
        try {
            const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const options = timezoneField.querySelectorAll('option');
            options.forEach(option => {
                if (option.value === userTimezone) {
                    option.selected = true;
                }
            });
        } catch (e) {
            console.log('لا يمكن تحديد المنطقة الزمنية تلقائياً');
        }
    }

    // إعداد معاينة الصورة
    const profilePictureInput = document.querySelector('input[type="file"][name="profile_picture"]');
    if (profilePictureInput) {
        profilePictureInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('profile-preview');
                    if (preview) {
                        if (preview.tagName === 'IMG') {
                            preview.src = e.target.result;
                        } else {
                            // إنشاء عنصر img جديد
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'photo-preview';
                            img.alt = 'صورة الملف الشخصي';
                            img.id = 'profile-preview';
                            preview.parentNode.replaceChild(img, preview);
                        }
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // إعداد التحقق من صحة النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                const originalText = submitButton.innerHTML;
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحفظ...';

                // إعادة تفعيل الزر بعد 3 ثوان في حالة عدم إعادة تحميل الصفحة
                setTimeout(() => {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }, 3000);
            }
        });
    });

    // إخفاء الرسائل تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.transition = 'opacity 0.5s ease';
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 500);
        }, 5000);
    });
});
</script>
{% endblock %}
