{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/enhanced-breadcrumb.css' %}">
<style>
    .events-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .events-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 1.5rem;
    }

    .event-item {
        padding: 1.5rem;
        border-bottom: 1px solid #eee;
        transition: all 0.3s ease;
    }

    .event-item:hover {
        background-color: #f8f9fa;
    }

    .event-item:last-child {
        border-bottom: none;
    }

    .event-category {
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
        margin-bottom: 1rem;
        display: inline-block;
    }

    .event-category.student {
        background: #e8f5e8;
        color: #388e3c;
    }

    .event-category.teacher {
        background: #fff3e0;
        color: #f57c00;
    }

    .event-category.admin {
        background: #fce4ec;
        color: #c2185b;
    }

    .status-toggle {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .status-toggle input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #11998e;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .template-badge {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
    }

    .template-badge.assigned {
        background: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .delay-badge {
        background: #fff3cd;
        color: #856404;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
    }

    /* تحسين مسارات التوجيه */
    .breadcrumb {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    .breadcrumb-item {
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #11998e;
        text-decoration: none;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: inline-block;
    }

    .breadcrumb-item a:hover {
        background: rgba(17, 153, 142, 0.1);
        color: #0d7377;
        transform: translateY(-1px);
    }

    .breadcrumb-item.active {
        color: #2D5016;
        font-weight: 600;
        background: rgba(45, 80, 22, 0.1);
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
        color: #11998e;
        font-weight: bold;
        margin: 0 0.5rem;
    }

    .page-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    .btn-configure {
        background: #667eea;
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .btn-configure:hover {
        background: #5a6fd8;
        color: white;
        transform: translateY(-1px);
    }

    @media (max-width: 768px) {
        .event-item {
            padding: 1rem;
        }
        
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'admin_dashboard' %}">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'technical_settings' %}">
                    <i class="fas fa-cogs me-2"></i>الإعدادات التقنية
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'email_settings_dashboard' %}">
                    <i class="fas fa-envelope-open-text me-2"></i>إعدادات البريد
                </a>
            </li>
            <li class="breadcrumb-item active">
                <i class="fas fa-bell me-2"></i>أحداث الإشعارات
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="page-header text-center">
        <div class="container">
            <h1 class="mb-3">
                <i class="fas fa-bell me-3"></i>
                إعدادات أحداث الإشعارات
            </h1>
            <p class="lead mb-0">تخصيص الأحداث التي تؤدي إلى إرسال إشعارات البريد الإلكتروني</p>
        </div>
    </div>

    <!-- Student Events -->
    <div class="events-card mb-4">
        <div class="events-header">
            <h4 class="mb-0">
                <i class="fas fa-user-graduate me-2"></i>
                أحداث الطلاب
            </h4>
        </div>
        
        {% for event in events %}
            {% if 'student' in event.event_type or 'subscription' in event.event_type or 'lesson' in event.event_type or 'payment' in event.event_type %}
                <div class="event-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <span class="event-category student">طلاب</span>
                            <h6 class="mb-2">{{ event.get_event_type_display }}</h6>
                            <div class="d-flex align-items-center gap-3 mb-2">
                                <span class="template-badge {% if event.template %}assigned{% endif %}">
                                    {% if event.template %}
                                        <i class="fas fa-check me-1"></i>{{ event.template.name }}
                                    {% else %}
                                        <i class="fas fa-times me-1"></i>لا يوجد قالب
                                    {% endif %}
                                </span>
                                {% if event.send_delay > 0 %}
                                    <span class="delay-badge">
                                        <i class="fas fa-clock me-1"></i>تأخير {{ event.send_delay }} دقيقة
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <label class="status-toggle">
                                <input type="checkbox" {% if event.is_active %}checked{% endif %} 
                                       onchange="toggleEvent('{{ event.event_type }}', this.checked)">
                                <span class="slider"></span>
                            </label>
                            <button class="btn btn-configure btn-sm" onclick="configureEvent('{{ event.event_type }}')">
                                <i class="fas fa-cog me-1"></i>تكوين
                            </button>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    </div>

    <!-- Teacher Events -->
    <div class="events-card mb-4">
        <div class="events-header">
            <h4 class="mb-0">
                <i class="fas fa-chalkboard-teacher me-2"></i>
                أحداث المعلمين
            </h4>
        </div>
        
        {% for event in events %}
            {% if 'teacher' in event.event_type %}
                <div class="event-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <span class="event-category teacher">معلمين</span>
                            <h6 class="mb-2">{{ event.get_event_type_display }}</h6>
                            <div class="d-flex align-items-center gap-3 mb-2">
                                <span class="template-badge {% if event.template %}assigned{% endif %}">
                                    {% if event.template %}
                                        <i class="fas fa-check me-1"></i>{{ event.template.name }}
                                    {% else %}
                                        <i class="fas fa-times me-1"></i>لا يوجد قالب
                                    {% endif %}
                                </span>
                                {% if event.send_delay > 0 %}
                                    <span class="delay-badge">
                                        <i class="fas fa-clock me-1"></i>تأخير {{ event.send_delay }} دقيقة
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <label class="status-toggle">
                                <input type="checkbox" {% if event.is_active %}checked{% endif %} 
                                       onchange="toggleEvent('{{ event.event_type }}', this.checked)">
                                <span class="slider"></span>
                            </label>
                            <button class="btn btn-configure btn-sm" onclick="configureEvent('{{ event.event_type }}')">
                                <i class="fas fa-cog me-1"></i>تكوين
                            </button>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    </div>

    <!-- Admin Events -->
    <div class="events-card mb-4">
        <div class="events-header">
            <h4 class="mb-0">
                <i class="fas fa-user-shield me-2"></i>
                أحداث المديرين
            </h4>
        </div>
        
        {% for event in events %}
            {% if 'new_user' in event.event_type or 'new_subscription' in event.event_type or 'payment_received' in event.event_type or 'daily' in event.event_type or 'weekly' in event.event_type or 'monthly' in event.event_type or 'system' in event.event_type %}
                <div class="event-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <span class="event-category admin">مديرين</span>
                            <h6 class="mb-2">{{ event.get_event_type_display }}</h6>
                            <div class="d-flex align-items-center gap-3 mb-2">
                                <span class="template-badge {% if event.template %}assigned{% endif %}">
                                    {% if event.template %}
                                        <i class="fas fa-check me-1"></i>{{ event.template.name }}
                                    {% else %}
                                        <i class="fas fa-times me-1"></i>لا يوجد قالب
                                    {% endif %}
                                </span>
                                {% if event.send_delay > 0 %}
                                    <span class="delay-badge">
                                        <i class="fas fa-clock me-1"></i>تأخير {{ event.send_delay }} دقيقة
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <label class="status-toggle">
                                <input type="checkbox" {% if event.is_active %}checked{% endif %} 
                                       onchange="toggleEvent('{{ event.event_type }}', this.checked)">
                                <span class="slider"></span>
                            </label>
                            <button class="btn btn-configure btn-sm" onclick="configureEvent('{{ event.event_type }}')">
                                <i class="fas fa-cog me-1"></i>تكوين
                            </button>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    </div>

    <!-- Summary Card -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">{{ events|length }}</h5>
                    <p class="card-text">إجمالي الأحداث</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">
                        {% for event in events %}{% if event.is_active %}{{ forloop.counter0|add:1 }}{% endif %}{% endfor %}
                    </h5>
                    <p class="card-text">أحداث نشطة</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">
                        {% for event in events %}{% if event.template %}{{ forloop.counter0|add:1 }}{% endif %}{% endfor %}
                    </h5>
                    <p class="card-text">أحداث مكونة</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configure Event Modal -->
<div class="modal fade" id="configureModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تكوين الحدث</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="configureContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveEventConfig()">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleEvent(eventType, isActive) {
    fetch(`/dashboard/admin/email-settings/events/toggle/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            event_type: eventType,
            is_active: isActive
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إظهار رسالة نجاح
            showToast('تم تحديث حالة الحدث بنجاح', 'success');
        } else {
            // إعادة تعيين الحالة في حالة الفشل
            event.target.checked = !isActive;
            showToast('فشل في تحديث حالة الحدث', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // إعادة تعيين الحالة في حالة الفشل
        event.target.checked = !isActive;
        showToast('حدث خطأ غير متوقع', 'error');
    });
}

function configureEvent(eventType) {
    const modal = new bootstrap.Modal(document.getElementById('configureModal'));
    const content = document.getElementById('configureContent');
    
    // إظهار تحميل
    content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">جاري التحميل...</p></div>';
    modal.show();
    
    // تحميل نموذج التكوين (سيتم إضافة API لاحقاً)
    setTimeout(() => {
        content.innerHTML = `
            <div class="alert alert-info">
                <h6>تكوين الحدث: ${eventType}</h6>
                <p>سيتم إضافة نموذج تكوين مفصل قريباً يتضمن:</p>
                <ul>
                    <li>اختيار القالب المستخدم</li>
                    <li>تحديد تأخير الإرسال</li>
                    <li>إعدادات إضافية للحدث</li>
                </ul>
            </div>
        `;
    }, 1000);
}

function saveEventConfig() {
    // سيتم تنفيذ حفظ التكوين لاحقاً
    showToast('سيتم إضافة وظيفة الحفظ قريباً', 'info');
    bootstrap.Modal.getInstance(document.getElementById('configureModal')).hide();
}

function showToast(message, type) {
    // إنشاء toast بسيط
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
        ${message}
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// إضافة CSRF token للطلبات
document.addEventListener('DOMContentLoaded', function() {
    // إنشاء CSRF token مخفي إذا لم يكن موجوداً
    if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = '{{ csrf_token }}';
        document.body.appendChild(csrfInput);
    }
});
</script>
{% endblock %}
