{% extends 'base.html' %}
{% load static %}

{% block title %}الفاتورة رقم {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-islamic-primary">فاتورة الاشتراك</h1>
                    <p class="text-gray-600 mt-1">رقم الفاتورة: {{ invoice.invoice_number }}</p>
                </div>
                <div class="flex space-x-4 space-x-reverse">
                    <a href="{% url 'student_invoice_print' invoice.id %}"
                       target="_blank"
                       class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-print ml-2"></i>
                        طباعة
                    </a>
                    <a href="{% url 'student_invoice_pdf' invoice.id %}"
                       class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-file-pdf ml-2"></i>
                        تحميل PDF
                    </a>
                    <a href="{% url 'student_subscriptions' %}"
                       class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>

        <!-- Invoice Content -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- Company Header -->
            <div class="bg-islamic-primary text-white p-8">
                <div class="flex justify-between items-start">
                    <div class="flex items-start space-x-4 space-x-reverse">
                        {% if company_info.logo %}
                        <div class="flex-shrink-0">
                            <img src="{{ company_info.logo }}" alt="{{ company_info.name }}" class="w-16 h-16 rounded-lg bg-white p-2">
                        </div>
                        {% endif %}
                        <div>
                            <h2 class="text-3xl font-bold mb-2">{{ company_info.name }}</h2>
                            {% if company_info.description %}
                            <p class="text-islamic-light text-sm mb-2">{{ company_info.description }}</p>
                            {% endif %}
                            <p class="text-islamic-light">{{ company_info.address }}</p>
                            <p class="text-islamic-light">{{ company_info.phone }}</p>
                            <p class="text-islamic-light">{{ company_info.email }}</p>
                            {% if company_info.website %}
                            <p class="text-islamic-light">{{ company_info.website }}</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="text-left">
                        <h3 class="text-2xl font-bold mb-2">فاتورة</h3>
                        <p class="text-islamic-light">رقم: {{ invoice.invoice_number }}</p>
                        <p class="text-islamic-light">التاريخ: {{ invoice.issue_date|date:"Y-m-d" }}</p>
                        <p class="text-islamic-light">الاستحقاق: {{ invoice.due_date|date:"Y-m-d" }}</p>
                    </div>
                </div>
            </div>

            <!-- Invoice Body -->
            <div class="p-8">
                <!-- Customer Information -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">معلومات العميل</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <span class="text-sm font-medium text-gray-600">الاسم:</span>
                                <p class="font-semibold">{{ invoice.customer_name }}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-600">البريد الإلكتروني:</span>
                                <p class="font-semibold">{{ invoice.customer_email }}</p>
                            </div>
                            {% if invoice.customer_phone %}
                            <div>
                                <span class="text-sm font-medium text-gray-600">رقم الهاتف:</span>
                                <p class="font-semibold">{{ invoice.customer_phone }}</p>
                            </div>
                            {% endif %}
                            <div>
                                <span class="text-sm font-medium text-gray-600">حالة الفاتورة:</span>
                                <span class="px-2 py-1 rounded-full text-xs font-medium
                                    {% if invoice.status == 'paid' %}bg-green-100 text-green-800
                                    {% elif invoice.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif invoice.status == 'overdue' %}bg-red-100 text-red-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ invoice.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">تفاصيل الفاتورة</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المجموع</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for item in items %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ item.description }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ item.quantity }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ item.unit_price|floatformat:2 }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ item.total_price|floatformat:2 }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Invoice Totals -->
                <div class="flex justify-end mb-8">
                    <div class="w-full max-w-sm">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex justify-between items-center py-2">
                                <span class="text-sm font-medium text-gray-600">المجموع الفرعي:</span>
                                <span class="font-semibold">{{ invoice.subtotal|floatformat:2 }}</span>
                            </div>
                            {% if invoice.tax_amount > 0 %}
                            <div class="flex justify-between items-center py-2 border-t border-gray-200">
                                <span class="text-sm font-medium text-gray-600">الضريبة:</span>
                                <span class="font-semibold">{{ invoice.tax_amount|floatformat:2 }}</span>
                            </div>
                            {% endif %}
                            {% if invoice.discount_amount > 0 %}
                            <div class="flex justify-between items-center py-2 border-t border-gray-200">
                                <span class="text-sm font-medium text-gray-600">الخصم:</span>
                                <span class="font-semibold text-green-600">-{{ invoice.discount_amount|floatformat:2 }}</span>
                            </div>
                            {% endif %}
                            <div class="flex justify-between items-center py-2 border-t-2 border-gray-300">
                                <span class="text-lg font-bold text-gray-900">المجموع الإجمالي:</span>
                                <span class="text-lg font-bold text-islamic-primary">{{ invoice.total_amount|floatformat:2 }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Details -->
                <div class="mt-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">تفاصيل الاشتراك</h4>
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <span class="text-sm font-medium text-gray-600">الباقة:</span>
                                <p class="font-semibold">{{ invoice.plan_name }}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-600">تاريخ البداية:</span>
                                <p class="font-semibold">{{ invoice.subscription.start_date|date:"Y-m-d" }}</p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-600">تاريخ النهاية:</span>
                                <p class="font-semibold">{{ invoice.subscription.end_date|date:"Y-m-d" }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="mt-8 pt-8 border-t border-gray-200">
                    <div class="text-center text-gray-600">
                        <p class="text-sm">شكراً لك على اختيار {{ company_info.name }}</p>
                        <p class="text-xs mt-2">تم إنشاء هذه الفاتورة إلكترونياً في {{ invoice.created_at|date:"Y-m-d H:i" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
