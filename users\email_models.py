"""
نماذج نظام إشعارات البريد الإلكتروني
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from cryptography.fernet import Fernet
from django.conf import settings
import json

User = get_user_model()


class EmailSettings(models.Model):
    """إعدادات البريد الإلكتروني وخوادم SMTP"""
    
    SMTP_PROVIDERS = [
        ('gmail', 'Gmail'),
        ('outlook', 'Outlook/Hotmail'),
        ('yahoo', 'Yahoo Mail'),
        ('namecheap', 'Namecheap'),
        ('hostinger', 'Hostinger'),
        ('godaddy', 'GoDaddy'),
        ('cpanel', 'cPanel/WHM'),
        ('custom', 'إعدادات مخصصة'),
    ]
    
    # معلومات المزود
    provider = models.Char<PERSON>ield(
        max_length=50,
        choices=SMTP_PROVIDERS,
        default='custom',
        verbose_name=_('مزود خدمة SMTP')
    )
    
    # إعدادات الخادم
    smtp_host = models.CharField(
        max_length=255,
        verbose_name=_('خادم SMTP')
    )
    
    smtp_port = models.IntegerField(
        default=587,
        verbose_name=_('منفذ SMTP')
    )
    
    smtp_username = models.CharField(
        max_length=255,
        verbose_name=_('اسم المستخدم')
    )
    
    smtp_password = models.TextField(
        verbose_name=_('كلمة المرور (مشفرة)')
    )
    
    use_tls = models.BooleanField(
        default=True,
        verbose_name=_('استخدام TLS')
    )
    
    use_ssl = models.BooleanField(
        default=False,
        verbose_name=_('استخدام SSL')
    )
    
    # إعدادات الإرسال
    from_email = models.EmailField(
        verbose_name=_('البريد المرسل')
    )
    
    from_name = models.CharField(
        max_length=255,
        verbose_name=_('اسم المرسل')
    )
    
    # إعدادات النظام
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    max_emails_per_hour = models.IntegerField(
        default=100,
        verbose_name=_('الحد الأقصى للرسائل في الساعة')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    
    class Meta:
        verbose_name = _('إعدادات البريد الإلكتروني')
        verbose_name_plural = _('إعدادات البريد الإلكتروني')
    
    def __str__(self):
        return f"{self.get_provider_display()} - {self.from_email}"
    
    def encrypt_password(self, password):
        """تشفير كلمة المرور"""
        if not password:
            return ''
        try:
            key = getattr(settings, 'EMAIL_ENCRYPTION_KEY', Fernet.generate_key())
            cipher = Fernet(key)
            return cipher.encrypt(password.encode()).decode()
        except Exception:
            return password
    
    def decrypt_password(self):
        """فك تشفير كلمة المرور"""
        if not self.smtp_password:
            return ''
        try:
            key = getattr(settings, 'EMAIL_ENCRYPTION_KEY', Fernet.generate_key())
            cipher = Fernet(key)
            return cipher.decrypt(self.smtp_password.encode()).decode()
        except Exception:
            return self.smtp_password
    
    def save(self, *args, **kwargs):
        """حفظ مع تشفير كلمة المرور"""
        if self.smtp_password and not self.smtp_password.startswith('gAAAAA'):
            self.smtp_password = self.encrypt_password(self.smtp_password)
        super().save(*args, **kwargs)
    
    @classmethod
    def get_active_settings(cls):
        """الحصول على إعدادات البريد النشطة"""
        return cls.objects.filter(is_active=True).first()


class EmailTemplate(models.Model):
    """قوالب البريد الإلكتروني"""
    
    TEMPLATE_TYPES = [
        ('welcome', 'رسالة ترحيب'),
        ('lesson_reminder', 'تذكير بالحصة'),
        ('lesson_report', 'تقرير الحصة'),
        ('subscription_expiry', 'انتهاء الاشتراك'),
        ('admin_report', 'تقرير إداري'),
        ('general', 'إشعار عام'),
        ('payment', 'إشعار دفع'),
        ('system', 'إشعار نظام'),
    ]
    
    # معلومات القالب
    name = models.CharField(
        max_length=255,
        verbose_name=_('اسم القالب')
    )
    
    template_type = models.CharField(
        max_length=50,
        choices=TEMPLATE_TYPES,
        verbose_name=_('نوع القالب')
    )
    
    subject = models.CharField(
        max_length=255,
        verbose_name=_('موضوع الرسالة')
    )
    
    # محتوى القالب
    html_content = models.TextField(
        verbose_name=_('المحتوى HTML')
    )
    
    text_content = models.TextField(
        blank=True,
        verbose_name=_('المحتوى النصي')
    )
    
    # المتغيرات المستخدمة
    variables = models.JSONField(
        default=dict,
        verbose_name=_('المتغيرات المستخدمة')
    )
    
    # إعدادات القالب
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    is_default = models.BooleanField(
        default=False,
        verbose_name=_('قالب افتراضي')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    
    class Meta:
        verbose_name = _('قالب البريد الإلكتروني')
        verbose_name_plural = _('قوالب البريد الإلكتروني')
    
    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"
    
    def get_academy_variables(self):
        """الحصول على متغيرات الأكاديمية"""
        from .models import AcademySettings
        academy = AcademySettings.get_settings()
        
        return {
            'academy_name': academy.academy_name,
            'academy_logo': academy.academy_logo.url if academy.academy_logo else '',
            'academy_slogan': academy.academy_slogan or '',
            'academy_email': academy.academy_email,
            'academy_phone': academy.academy_phone or '',
            'academy_whatsapp': academy.academy_whatsapp or '',
            'academy_address': academy.academy_address or '',
            'academy_website': academy.academy_website or '',
            'academy_description': academy.academy_description or '',
            'academy_working_hours': academy.academy_working_hours or '',
        }
    
    def render_content(self, context=None):
        """عرض المحتوى مع المتغيرات"""
        if context is None:
            context = {}
        
        # إضافة متغيرات الأكاديمية
        academy_vars = self.get_academy_variables()
        context.update(academy_vars)
        
        # عرض المحتوى
        from django.template import Template, Context
        
        html_template = Template(self.html_content)
        subject_template = Template(self.subject)
        
        rendered_html = html_template.render(Context(context))
        rendered_subject = subject_template.render(Context(context))
        
        return {
            'subject': rendered_subject,
            'html_content': rendered_html,
            'text_content': self.text_content
        }


class EmailEvent(models.Model):
    """أحداث إرسال الإشعارات"""
    
    EVENT_TYPES = [
        # أحداث الطلاب
        ('student_registration', 'تسجيل طالب جديد'),
        ('subscription_created', 'اشتراك جديد'),
        ('subscription_renewed', 'تجديد اشتراك'),
        ('lesson_scheduled', 'جدولة حصة جديدة'),
        ('lesson_reminder_24h', 'تذكير بالحصة (24 ساعة)'),
        ('lesson_reminder_1h', 'تذكير بالحصة (ساعة واحدة)'),
        ('lesson_completed', 'انتهاء الحصة'),
        ('lesson_report_received', 'استلام تقرير الحصة'),
        ('subscription_expiry_7d', 'انتهاء الاشتراك خلال 7 أيام'),
        ('subscription_expiry_3d', 'انتهاء الاشتراك خلال 3 أيام'),
        ('subscription_expiry_1d', 'انتهاء الاشتراك خلال يوم واحد'),
        ('subscription_expired', 'انتهى الاشتراك'),
        ('payment_successful', 'نجح الدفع'),
        ('payment_failed', 'فشل الدفع'),
        
        # أحداث المعلمين
        ('teacher_assigned', 'تعيين معلم لطالب'),
        ('lesson_assigned', 'تعيين حصة للمعلم'),
        ('lesson_reminder_teacher', 'تذكير المعلم بالحصة'),
        ('student_enrolled', 'تسجيل طالب جديد للمعلم'),
        ('lesson_report_required', 'مطلوب كتابة تقرير الحصة'),
        ('student_evaluation_received', 'استلام تقييم من الطالب'),
        ('monthly_performance_report', 'تقرير الأداء الشهري'),
        
        # أحداث المديرين
        ('new_user_registration', 'تسجيل مستخدم جديد'),
        ('new_subscription', 'اشتراك جديد في النظام'),
        ('subscription_renewal', 'تجديد اشتراك'),
        ('payment_received', 'استلام دفعة جديدة'),
        ('daily_summary', 'ملخص يومي'),
        ('weekly_report', 'تقرير أسبوعي'),
        ('monthly_report', 'تقرير شهري'),
        ('system_alert', 'تنبيه النظام'),
    ]
    
    event_type = models.CharField(
        max_length=100,
        choices=EVENT_TYPES,
        unique=True,
        verbose_name=_('نوع الحدث')
    )
    
    template = models.ForeignKey(
        EmailTemplate,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('القالب المستخدم')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    send_delay = models.IntegerField(
        default=0,
        verbose_name=_('تأخير الإرسال (بالدقائق)')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    
    class Meta:
        verbose_name = _('حدث البريد الإلكتروني')
        verbose_name_plural = _('أحداث البريد الإلكتروني')
    
    def __str__(self):
        return self.get_event_type_display()


class EmailQueue(models.Model):
    """طابور إرسال البريد الإلكتروني"""

    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('sending', 'جاري الإرسال'),
        ('sent', 'تم الإرسال'),
        ('failed', 'فشل الإرسال'),
        ('cancelled', 'ملغي'),
    ]

    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name=_('المستلم')
    )

    template = models.ForeignKey(
        EmailTemplate,
        on_delete=models.CASCADE,
        verbose_name=_('القالب')
    )

    event_type = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('نوع الحدث')
    )

    subject = models.CharField(
        max_length=255,
        verbose_name=_('موضوع الرسالة')
    )

    context_data = models.JSONField(
        default=dict,
        verbose_name=_('بيانات السياق')
    )

    scheduled_at = models.DateTimeField(
        verbose_name=_('موعد الإرسال')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    attempts = models.IntegerField(
        default=0,
        verbose_name=_('عدد المحاولات')
    )

    max_attempts = models.IntegerField(
        default=3,
        verbose_name=_('الحد الأقصى للمحاولات')
    )

    error_message = models.TextField(
        blank=True,
        verbose_name=_('رسالة الخطأ')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('طابور البريد الإلكتروني')
        verbose_name_plural = _('طابور البريد الإلكتروني')
        ordering = ['scheduled_at']

    def __str__(self):
        return f"{self.recipient.email} - {self.subject}"

    def can_retry(self):
        """هل يمكن إعادة المحاولة؟"""
        return self.attempts < self.max_attempts and self.status == 'failed'


class EmailLog(models.Model):
    """سجل إرسال البريد الإلكتروني"""

    STATUS_CHOICES = [
        ('sent', 'تم الإرسال'),
        ('failed', 'فشل الإرسال'),
        ('bounced', 'مرتد'),
        ('opened', 'تم فتحه'),
        ('clicked', 'تم النقر'),
    ]

    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name=_('المستلم')
    )

    subject = models.CharField(
        max_length=255,
        verbose_name=_('موضوع الرسالة')
    )

    template_name = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('اسم القالب')
    )

    event_type = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('نوع الحدث')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        verbose_name=_('الحالة')
    )

    sent_at = models.DateTimeField(
        verbose_name=_('تاريخ الإرسال')
    )

    opened_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الفتح')
    )

    clicked_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ النقر')
    )

    error_message = models.TextField(
        blank=True,
        verbose_name=_('رسالة الخطأ')
    )

    smtp_response = models.TextField(
        blank=True,
        verbose_name=_('استجابة SMTP')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('سجل البريد الإلكتروني')
        verbose_name_plural = _('سجلات البريد الإلكتروني')
        ordering = ['-sent_at']

    def __str__(self):
        return f"{self.recipient.email} - {self.subject} ({self.status})"


class EmailSubscription(models.Model):
    """اشتراكات المستخدمين في الإشعارات"""

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        verbose_name=_('المستخدم')
    )

    # إعدادات الاشتراك العامة
    email_notifications_enabled = models.BooleanField(
        default=True,
        verbose_name=_('تفعيل إشعارات البريد الإلكتروني')
    )

    # إعدادات الطلاب
    lesson_reminders = models.BooleanField(
        default=True,
        verbose_name=_('تذكيرات الحصص')
    )

    subscription_notifications = models.BooleanField(
        default=True,
        verbose_name=_('إشعارات الاشتراك')
    )

    lesson_reports = models.BooleanField(
        default=True,
        verbose_name=_('تقارير الحصص')
    )

    payment_notifications = models.BooleanField(
        default=True,
        verbose_name=_('إشعارات الدفع')
    )

    # إعدادات المعلمين
    teacher_notifications = models.BooleanField(
        default=True,
        verbose_name=_('إشعارات المعلم')
    )

    student_updates = models.BooleanField(
        default=True,
        verbose_name=_('تحديثات الطلاب')
    )

    performance_reports = models.BooleanField(
        default=True,
        verbose_name=_('تقارير الأداء')
    )

    # إعدادات المديرين
    admin_reports = models.BooleanField(
        default=True,
        verbose_name=_('التقارير الإدارية')
    )

    system_alerts = models.BooleanField(
        default=True,
        verbose_name=_('تنبيهات النظام')
    )

    daily_summaries = models.BooleanField(
        default=True,
        verbose_name=_('الملخصات اليومية')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('اشتراك البريد الإلكتروني')
        verbose_name_plural = _('اشتراكات البريد الإلكتروني')

    def __str__(self):
        return f"{self.user.email} - {'مفعل' if self.email_notifications_enabled else 'معطل'}"

    @classmethod
    def get_or_create_for_user(cls, user):
        """الحصول على اشتراك المستخدم أو إنشاؤه"""
        subscription, created = cls.objects.get_or_create(
            user=user,
            defaults={
                'email_notifications_enabled': True,
                'lesson_reminders': True,
                'subscription_notifications': True,
                'lesson_reports': True,
                'payment_notifications': True,
                'teacher_notifications': user.is_teacher(),
                'student_updates': user.is_teacher(),
                'performance_reports': user.is_teacher(),
                'admin_reports': user.is_admin(),
                'system_alerts': user.is_admin(),
                'daily_summaries': user.is_admin(),
            }
        )
        return subscription
