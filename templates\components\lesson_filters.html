<!-- نظام الفلترة الموحد للحصص -->
<div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 md:mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-4">
        <h2 class="text-lg md:text-xl font-bold text-islamic-primary">
            <i class="fas fa-filter text-islamic-gold ml-2"></i>
            فلترة متقدمة
        </h2>
        <button onclick="clearAllFilters()" 
                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
            <i class="fas fa-times ml-1"></i>
            مسح جميع الفلاتر
        </button>
    </div>

    <form id="filter-form" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        
        <!-- فلتر المعلم (للمدير فقط) -->
        {% if user_type == 'admin' %}
        <div class="form-group">
            <label class="form-label">
                <i class="fas fa-chalkboard-teacher text-green-600 ml-1"></i>
                المعلم
            </label>
            <select name="teacher_id" id="teacher-filter" class="form-control">
                <option value="">جميع المعلمين</option>
                {% for teacher in available_teachers %}
                    <option value="{{ teacher.id }}" 
                            {% if teacher_filter == teacher.id|stringformat:"s" %}selected{% endif %}>
                        {{ teacher.get_full_name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        
        <!-- فلتر الطالب (للمدير فقط) -->
        <div class="form-group">
            <label class="form-label">
                <i class="fas fa-user-graduate text-blue-600 ml-1"></i>
                الطالب
            </label>
            <select name="student_id" id="student-filter" class="form-control">
                <option value="">جميع الطلاب</option>
                {% for student in available_students %}
                    <option value="{{ student.id }}" 
                            {% if student_filter == student.id|stringformat:"s" %}selected{% endif %}>
                        {{ student.get_full_name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        {% endif %}
        
        <!-- فلتر نوع الحصة -->
        <div class="form-group">
            <label class="form-label">
                <i class="fas fa-tags text-purple-600 ml-1"></i>
                نوع الحصة
            </label>
            <select name="lesson_type" id="lesson-type-filter" class="form-control">
                <option value="">جميع الأنواع</option>
                <option value="subscription" {% if lesson_type_filter == 'subscription' %}selected{% endif %}>
                    حصص الاشتراك
                </option>
                <option value="trial" {% if lesson_type_filter == 'trial' %}selected{% endif %}>
                    حصص تجريبية
                </option>
                <option value="makeup" {% if lesson_type_filter == 'makeup' %}selected{% endif %}>
                    حصص تعويضية
                </option>
            </select>
        </div>
        
        <!-- فلتر الحالة -->
        <div class="form-group">
            <label class="form-label">
                <i class="fas fa-flag text-red-600 ml-1"></i>
                الحالة
            </label>
            <select name="status" id="status-filter" class="form-control">
                <option value="">جميع الحالات</option>
                <option value="scheduled" {% if status_filter == 'scheduled' %}selected{% endif %}>
                    مجدولة
                </option>
                <option value="live" {% if status_filter == 'live' %}selected{% endif %}>
                    جارية
                </option>
                <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>
                    مكتملة
                </option>
                <option value="rated" {% if status_filter == 'rated' %}selected{% endif %}>
                    مقيمة
                </option>
                <option value="cancelled_by_student" {% if status_filter == 'cancelled_by_student' %}selected{% endif %}>
                    ألغاها الطالب
                </option>
                <option value="cancelled_by_teacher" {% if status_filter == 'cancelled_by_teacher' %}selected{% endif %}>
                    ألغاها المعلم
                </option>
                <option value="cancelled_by_admin" {% if status_filter == 'cancelled_by_admin' %}selected{% endif %}>
                    ألغاها المدير
                </option>
            </select>
        </div>
        
        <!-- فلتر التاريخ -->
        <div class="form-group">
            <label class="form-label">
                <i class="fas fa-calendar text-indigo-600 ml-1"></i>
                التاريخ
            </label>
            <select name="date_filter" id="date-filter" class="form-control">
                <option value="">جميع التواريخ</option>
                <option value="today" {% if date_filter == 'today' %}selected{% endif %}>
                    اليوم
                </option>
                <option value="tomorrow" {% if date_filter == 'tomorrow' %}selected{% endif %}>
                    غداً
                </option>
                <option value="this_week" {% if date_filter == 'this_week' %}selected{% endif %}>
                    هذا الأسبوع
                </option>
                <option value="next_week" {% if date_filter == 'next_week' %}selected{% endif %}>
                    الأسبوع القادم
                </option>
                <option value="this_month" {% if date_filter == 'this_month' %}selected{% endif %}>
                    هذا الشهر
                </option>
                <option value="next_month" {% if date_filter == 'next_month' %}selected{% endif %}>
                    الشهر القادم
                </option>
            </select>
        </div>
        
        <!-- البحث النصي -->
        <div class="form-group">
            <label class="form-label">
                <i class="fas fa-search text-orange-600 ml-1"></i>
                البحث
            </label>
            <input type="text" 
                   name="search" 
                   id="search-input" 
                   class="form-control" 
                   placeholder="ابحث في العناوين..."
                   value="{{ search_query|default:'' }}">
        </div>
        
        <!-- زر التطبيق -->
        <div class="form-group flex items-end">
            <button type="submit" 
                    class="btn-primary w-full">
                <i class="fas fa-filter ml-1"></i>
                تطبيق الفلاتر
            </button>
        </div>
    </form>
    
    <!-- عرض الفلاتر النشطة -->
    <div id="active-filters" class="mt-4 flex flex-wrap gap-2">
        <!-- سيتم ملؤها بـ JavaScript -->
    </div>
</div>

<script>
// تطبيق الفلاتر تلقائياً عند التغيير
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.getElementById('filter-form');
    const selects = filterForm.querySelectorAll('select');
    const searchInput = document.getElementById('search-input');
    
    // تطبيق الفلاتر عند تغيير أي select
    selects.forEach(select => {
        select.addEventListener('change', function() {
            applyFilters();
        });
    });
    
    // تطبيق البحث مع تأخير
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            applyFilters();
        }, 500);
    });
    
    // عرض الفلاتر النشطة
    updateActiveFilters();
});

function applyFilters() {
    const form = document.getElementById('filter-form');
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    // إضافة المعاملات غير الفارغة فقط
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            params.append(key, value);
        }
    }
    
    // تحديث URL
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.pushState({}, '', newUrl);
    
    // تحديث المحتوى
    if (typeof window.lessonsManager !== 'undefined') {
        window.lessonsManager.fetchLessons(params);
    } else {
        // إعادة تحميل الصفحة كـ fallback
        window.location.href = newUrl;
    }
    
    updateActiveFilters();
}

function clearAllFilters() {
    const form = document.getElementById('filter-form');
    form.reset();
    
    // تحديث URL
    window.history.pushState({}, '', window.location.pathname);
    
    // تحديث المحتوى
    if (typeof window.lessonsManager !== 'undefined') {
        window.lessonsManager.fetchLessons(new URLSearchParams());
    } else {
        window.location.href = window.location.pathname;
    }
    
    updateActiveFilters();
}

function updateActiveFilters() {
    const activeFiltersContainer = document.getElementById('active-filters');
    const params = new URLSearchParams(window.location.search);
    
    activeFiltersContainer.innerHTML = '';
    
    for (let [key, value] of params.entries()) {
        if (value.trim() !== '') {
            const filterBadge = createFilterBadge(key, value);
            activeFiltersContainer.appendChild(filterBadge);
        }
    }
}

function createFilterBadge(key, value) {
    const badge = document.createElement('span');
    badge.className = 'inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800';
    
    const filterNames = {
        'teacher_id': 'المعلم',
        'student_id': 'الطالب',
        'lesson_type': 'نوع الحصة',
        'status': 'الحالة',
        'date_filter': 'التاريخ',
        'search': 'البحث'
    };
    
    badge.innerHTML = `
        <span>${filterNames[key] || key}: ${value}</span>
        <button onclick="removeFilter('${key}')" class="mr-1 text-blue-600 hover:text-blue-800">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    return badge;
}

function removeFilter(key) {
    const params = new URLSearchParams(window.location.search);
    params.delete(key);
    
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.pushState({}, '', newUrl);
    
    // تحديث النموذج
    const input = document.querySelector(`[name="${key}"]`);
    if (input) {
        if (input.type === 'select-one') {
            input.selectedIndex = 0;
        } else {
            input.value = '';
        }
    }
    
    // تحديث المحتوى
    if (typeof window.lessonsManager !== 'undefined') {
        window.lessonsManager.fetchLessons(params);
    } else {
        window.location.href = newUrl;
    }
    
    updateActiveFilters();
}
</script>
