{% extends 'base.html' %}
{% load static %}

{% block title %}محادثة مع {{ other_participant.get_full_name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-islamic-light-blue to-islamic-light-green py-8">
    <div class="container mx-auto px-4 max-w-6xl">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-full flex items-center justify-center ml-4">
                        <span class="text-white font-bold text-lg">{{ other_participant.get_full_name|first }}</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ other_participant.get_full_name }}</h1>
                        <p class="text-gray-600">
                            <span class="bg-gray-100 px-2 py-1 rounded text-sm">{{ other_participant.get_user_type_display }}</span>
                            • محادثة منذ {{ conversation.created_at|timesince }}
                        </p>
                    </div>
                </div>

                <div class="flex items-center space-x-4 space-x-reverse">
                    <button onclick="markAsRead()"
                            class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-check ml-2"></i>
                        تمييز كمقروءة
                    </button>
                    <a href="{% url 'conversations_list' %}"
                       class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Messages Area -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <!-- Messages Header -->
                    <div class="bg-islamic-primary text-white p-4">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-bold">
                                <i class="fas fa-comments ml-2"></i>
                                الرسائل
                            </h2>
                            <span class="text-sm bg-white bg-opacity-20 px-3 py-1 rounded-full">
                                {{ messages|length }} رسالة
                            </span>
                        </div>
                    </div>

                    <!-- Messages Container -->
                    <div id="messages-container" class="h-96 overflow-y-auto p-6 space-y-4 bg-gray-50">
                        {% if messages %}
                            {% for message in messages %}
                            <div class="message-bubble {% if message.sender == user %}sent{% else %}received{% endif %}" data-message-id="{{ message.id }}">
                                <div class="flex items-start {% if message.sender == user %}justify-end{% else %}justify-start{% endif %}">
                                    {% if message.sender != user %}
                                    <div class="w-8 h-8 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                                        <span class="text-white text-sm font-bold">{{ message.sender.get_full_name|first }}</span>
                                    </div>
                                    {% endif %}

                                    <div class="max-w-xs lg:max-w-md">
                                        <div class="{% if message.sender == user %}bg-islamic-primary text-white{% else %}bg-white border border-gray-200{% endif %} rounded-lg px-4 py-3 shadow-sm relative group">
                                            {% if message.is_deleted %}
                                                <p class="text-sm leading-relaxed italic text-gray-500">
                                                    <i class="fas fa-trash-alt ml-1"></i>
                                                    تم حذف هذه الرسالة
                                                </p>
                                                {% if user.user_type == 'admin' %}
                                                <button onclick="restoreMessage({{ message.id }})"
                                                        class="text-xs text-blue-500 hover:text-blue-700 mt-1">
                                                    <i class="fas fa-undo ml-1"></i>
                                                    استعادة
                                                </button>
                                                {% endif %}
                                            {% else %}
                                                <div class="text-sm leading-relaxed">{{ message.content|safe }}</div>

                                                <!-- زر الحذف -->
                                                {% if message.sender == user or user.user_type == 'admin' %}
                                                <button onclick="deleteMessage({{ message.id }})"
                                                        class="absolute top-1 {% if message.sender == user %}left-1{% else %}right-1{% endif %} opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white text-xs px-2 py-1 rounded">
                                                    <i class="fas fa-trash text-xs"></i>
                                                </button>
                                                {% endif %}
                                            {% endif %}
                                            {% if message.attachment %}
                                            <div class="mt-2 pt-2 border-t {% if message.sender == user %}border-white border-opacity-20{% else %}border-gray-200{% endif %}">
                                                <a href="{{ message.attachment.url }}" target="_blank"
                                                   class="{% if message.sender == user %}text-white{% else %}text-islamic-primary{% endif %} hover:underline text-sm">
                                                    <i class="fas fa-paperclip ml-1"></i>
                                                    مرفق
                                                </a>
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="flex items-center {% if message.sender == user %}justify-end{% else %}justify-start{% endif %} mt-1">
                                            <span class="text-xs text-gray-500" data-created-at="{{ message.created_at.isoformat }}" data-datetime="{{ message.created_at|date:'c' }}" data-format="time">
                                                {{ message.created_at|date:"H:i" }}
                                                {% if message.sender == user %}
                                                    <i class="fas fa-check{% if message.is_read %}-double text-blue-500{% endif %} mr-1"></i>
                                                {% endif %}
                                            </span>
                                        </div>
                                    </div>

                                    {% if message.sender == user %}
                                    <div class="w-8 h-8 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="text-white text-sm font-bold">{{ message.sender.get_full_name|first }}</span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-comment text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">لا توجد رسائل</h3>
                            <p class="text-gray-600">ابدأ المحادثة بإرسال رسالة أدناه</p>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Message Input -->
                    <div class="border-t border-gray-200 p-4 bg-white">
                        <form method="post" id="message-form" class="flex items-end space-x-4 space-x-reverse">
                            {% csrf_token %}
                            <div class="flex-1">
                                <textarea name="content" id="message-input"
                                          placeholder="اكتب رسالتك هنا..."
                                          rows="2"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent resize-none"
                                          required></textarea>
                                <div class="flex items-center justify-between mt-2">
                                    <span id="char-count" class="text-xs text-gray-500">0 حرف</span>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <button type="button" onclick="addEmoji('😊')" class="text-gray-400 hover:text-gray-600">😊</button>
                                        <button type="button" onclick="addEmoji('👍')" class="text-gray-400 hover:text-gray-600">👍</button>
                                        <button type="button" onclick="addEmoji('❤️')" class="text-gray-400 hover:text-gray-600">❤️</button>
                                        <button type="button" onclick="addEmoji('🤲')" class="text-gray-400 hover:text-gray-600">🤲</button>
                                    </div>
                                </div>
                            </div>
                            <button type="submit"
                                    class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors flex-shrink-0">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Conversation Info -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-info-circle text-islamic-primary ml-2"></i>
                        معلومات المحادثة
                    </h3>

                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">بدأت في:</span>
                            <span class="font-medium" data-datetime="{{ conversation.created_at|date:'c' }}" data-format="date">{{ conversation.created_at|date:"Y/m/d" }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">آخر تحديث:</span>
                            <span class="font-medium" data-relative-time="{{ conversation.updated_at|date:'c' }}">{{ conversation.updated_at|timesince }} مضت</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">عدد الرسائل:</span>
                            <span class="font-medium">{{ messages|length }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">الحالة:</span>
                            <span class="font-medium text-green-600">
                                <i class="fas fa-circle text-xs ml-1"></i>
                                نشطة
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Participant Info -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-user text-islamic-primary ml-2"></i>
                        معلومات المشارك
                    </h3>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="text-white font-bold text-xl">{{ other_participant.get_full_name|first }}</span>
                        </div>

                        <h4 class="font-bold text-gray-900">{{ other_participant.get_full_name }}</h4>
                        <p class="text-sm text-gray-600 mb-3">{{ other_participant.get_user_type_display }}</p>

                        {% if other_participant.user_type == 'teacher' and user.user_type == 'student' %}
                        <div class="text-xs text-gray-500 bg-gray-50 rounded-lg p-3">
                            <i class="fas fa-graduation-cap ml-1"></i>
                            معلمك في الدورات المسجل بها
                        </div>
                        {% elif other_participant.user_type == 'student' and user.user_type == 'teacher' %}
                        <div class="text-xs text-gray-500 bg-gray-50 rounded-lg p-3">
                            <i class="fas fa-user-graduate ml-1"></i>
                            طالب في دوراتك
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Configuration
window.userType = '{{ user.user_type }}';

// Auto-scroll to bottom
function scrollToBottom() {
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;
}

// Character counter
document.getElementById('message-input').addEventListener('input', function() {
    const charCount = this.value.length;
    document.getElementById('char-count').textContent = charCount + ' حرف';

    // Auto-resize
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
});

// Add emoji function
function addEmoji(emoji) {
    const input = document.getElementById('message-input');
    input.value += emoji;
    input.focus();
    input.dispatchEvent(new Event('input'));
}

// Mark as read function
function markAsRead() {
    fetch(`/messages/conversation/{{ conversation.id }}/mark-read/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Visual feedback
            const btn = event.target;
            btn.innerHTML = '<i class="fas fa-check ml-2"></i>تم التمييز';
            btn.classList.remove('bg-green-600', 'hover:bg-green-700');
            btn.classList.add('bg-gray-400');
            btn.disabled = true;
        }
    });
}

// Real-time messaging variables
let lastMessageTime = null;
let isPolling = false;
let pollInterval = null;

// Form submission with AJAX
document.getElementById('message-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const input = document.getElementById('message-input');
    const content = input.value.trim();

    if (!content) {
        alert('يرجى كتابة رسالة');
        return;
    }

    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الإرسال...';
    submitBtn.disabled = true;

    // Send message via AJAX
    fetch(`/messages/api/conversation/{{ conversation.id }}/send/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Clear input
            input.value = '';
            input.style.height = 'auto';
            document.getElementById('char-count').textContent = '0 حرف';

            // Add message to chat immediately
            addMessageToChat(data.message);

            // Update last message time
            lastMessageTime = data.message.created_at;

            // Scroll to bottom
            scrollToBottom();

            // عرض إشعار نجاح الإرسال (اختياري)
            if (data.notification_sent && data.recipient_name) {
                console.log(`تم إرسال إشعار إلى ${data.recipient_name}`);
            }
        } else {
            alert('حدث خطأ في إرسال الرسالة: ' + (data.error || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('Error sending message:', error);
        alert('حدث خطأ في إرسال الرسالة');
    })
    .finally(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        input.focus();
    });
});

// Function to add message to chat
function addMessageToChat(message) {
    const container = document.getElementById('messages-container');
    const messageDiv = document.createElement('div');

    const isOwnMessage = message.is_own_message;
    const alignmentClass = isOwnMessage ? 'justify-end' : 'justify-start';
    const bubbleClass = isOwnMessage ? 'bg-islamic-primary text-white' : 'bg-gray-100 text-gray-800';
    const avatarPosition = isOwnMessage ? 'order-2 mr-3' : 'order-1 ml-3';
    const bubblePosition = isOwnMessage ? 'order-1' : 'order-2';

    messageDiv.className = `flex ${alignmentClass} mb-4 message-bubble`;
    messageDiv.setAttribute('data-message-id', message.id);
    messageDiv.innerHTML = `
        <!-- Avatar -->
        <div class="${avatarPosition}">
            <div class="w-10 h-10 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-full flex items-center justify-center">
                <span class="text-white font-bold text-sm">${message.sender_avatar}</span>
            </div>
        </div>

        <!-- Message -->
        <div class="${bubblePosition} max-w-xs lg:max-w-md">
            <div class="${bubbleClass} rounded-lg px-4 py-2 shadow-sm relative group">
                ${message.is_deleted ?
                    `<p class="text-sm leading-relaxed italic text-gray-500">
                        <i class="fas fa-trash-alt ml-1"></i>
                        تم حذف هذه الرسالة
                    </p>` :
                    `<p class="text-sm leading-relaxed">${escapeHtml(message.content)}</p>
                    ${(message.is_own_message || window.userType === 'admin') ?
                        `<button onclick="deleteMessage(${message.id})"
                                class="absolute top-1 ${isOwnMessage ? 'left-1' : 'right-1'} opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white text-xs px-2 py-1 rounded">
                            <i class="fas fa-trash text-xs"></i>
                        </button>` : ''
                    }`
                }
            </div>
            <div class="text-xs text-gray-500 mt-1 ${isOwnMessage ? 'text-left' : 'text-right'}">
                <span>${message.sender_name}</span>
                <span class="mx-1">•</span>
                <span>${message.created_at_display}</span>
                ${message.is_read ? '<i class="fas fa-check-double text-blue-500 mr-1"></i>' : '<i class="fas fa-check text-gray-400 mr-1"></i>'}
            </div>
        </div>
    `;

    container.appendChild(messageDiv);
}

// Function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Function to check for new messages
function checkForNewMessages() {
    if (isPolling) return;
    isPolling = true;

    let url = `/messages/api/conversation/{{ conversation.id }}/messages/`;
    if (lastMessageTime) {
        url += `?last_message_time=${encodeURIComponent(lastMessageTime)}`;
    }

    fetch(url, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.messages && data.messages.length > 0) {
            // Add new messages to chat
            data.messages.forEach(message => {
                // Check if message already exists
                const existingMessage = document.querySelector(`[data-message-id="${message.id}"]`);
                if (!existingMessage) {
                    addMessageToChat(message);
                }
            });

            // Update last message time
            const lastMessage = data.messages[data.messages.length - 1];
            lastMessageTime = lastMessage.created_at;

            // Scroll to bottom if there are new messages
            scrollToBottom();

            // لا نعرض إشعارات صوتية أو منبثقة داخل المحادثة
            // لأن المستخدم موجود بالفعل في المحادثة ويرى الرسائل
        }
    })
    .catch(error => {
        console.log('Error checking for new messages:', error);
    })
    .finally(() => {
        isPolling = false;
    });
}

// Function to play notification sound
function playNotificationSound() {
    try {
        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
        audio.play().catch(() => {});
    } catch (e) {}
}

// Start real-time polling
function startRealTimePolling() {
    // Initial check
    checkForNewMessages();

    // Poll every 10 seconds (تقليل التكرار)
    pollInterval = setInterval(() => {
        if (document.visibilityState === 'visible') {
            checkForNewMessages();
        }
    }, 10000);
}

// Stop polling when page is hidden
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'hidden') {
        if (pollInterval) {
            clearInterval(pollInterval);
            pollInterval = null;
        }
    } else {
        if (!pollInterval) {
            startRealTimePolling();
        }
    }
});

// Keyboard shortcuts
document.getElementById('message-input').addEventListener('keydown', function(e) {
    // Ctrl/Cmd + Enter to send
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        document.getElementById('message-form').dispatchEvent(new Event('submit'));
    }
});

// Delete message function
function deleteMessage(messageId) {
    if (!confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
        return;
    }

    fetch(`/messages/message/${messageId}/delete/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the page to show updated messages
            location.reload();
        } else {
            alert('حدث خطأ في حذف الرسالة: ' + (data.error || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('Error deleting message:', error);
        alert('حدث خطأ في حذف الرسالة');
    });
}

// Restore message function (for admins)
function restoreMessage(messageId) {
    if (!confirm('هل أنت متأكد من استعادة هذه الرسالة؟')) {
        return;
    }

    fetch(`/messages/message/${messageId}/restore/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the page to show updated messages
            location.reload();
        } else {
            alert('حدث خطأ في استعادة الرسالة: ' + (data.error || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('Error restoring message:', error);
        alert('حدث خطأ في استعادة الرسالة');
    });
}

// Initialize real-time messaging
document.addEventListener('DOMContentLoaded', function() {
    // Scroll to bottom initially
    scrollToBottom();

    // Get the timestamp of the last message for polling
    const lastMessageElement = document.querySelector('.message-bubble:last-child [data-created-at]');
    if (lastMessageElement) {
        lastMessageTime = lastMessageElement.getAttribute('data-created-at');
    }

    // Start real-time polling
    startRealTimePolling();

    // Add data attributes to existing messages for duplicate detection
    document.querySelectorAll('.message-bubble').forEach((bubble, index) => {
        bubble.setAttribute('data-message-id', `existing-${index}`);
    });

    // مسح الإشعارات المعروضة للمحادثة الحالية من الذاكرة
    if (typeof window.clearShownMessagesForConversation === 'function') {
        window.clearShownMessagesForConversation({{ conversation.id }});
    }

    // إخفاء أي إشعارات منبثقة موجودة
    const existingNotifications = document.querySelectorAll('.fixed.top-4.left-4');
    existingNotifications.forEach(notification => {
        if (notification.innerHTML.includes('رسالة جديدة')) {
            notification.remove();
        }
    });

    // تحديث العدادات يدوياً في صفحة المحادثة (بدون إشعارات منبثقة)
    function updateCountersOnly() {
        fetch('/api/notifications-count/', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            },
        })
        .then(response => response.json())
        .then(data => {
            // تحديث عداد الإشعارات فقط
            const notificationCount = document.getElementById('notification-count');
            if (notificationCount) {
                if (data.unread_notifications > 0) {
                    notificationCount.textContent = data.unread_notifications;
                    notificationCount.classList.remove('hidden');
                } else {
                    notificationCount.classList.add('hidden');
                }
            }

            // تحديث عداد الرسائل في القائمة الجانبية فقط
            const messagesBadges = document.querySelectorAll('.messages-count');
            messagesBadges.forEach(badge => {
                if (data.unread_messages > 0) {
                    badge.textContent = data.unread_messages;
                    badge.classList.remove('hidden');
                } else {
                    badge.classList.add('hidden');
                }
            });
        })
        .catch(error => console.log('تحديث العدادات:', error));
    }

    // تحديث العدادات كل 60 ثانية في صفحة المحادثة
    setInterval(updateCountersOnly, 60000);
    updateCountersOnly(); // تحديث فوري
});
</script>
{% endblock %}
