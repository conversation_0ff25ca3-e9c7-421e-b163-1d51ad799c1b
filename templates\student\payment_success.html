{% extends 'base.html' %}
{% load static %}

{% block title %}تم الدفع بنجاح{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-green-50 to-islamic-mint flex items-center justify-center">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <!-- Success Card -->
            <div class="bg-white rounded-lg shadow-xl p-8 text-center">
                <!-- Success Icon -->
                <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-check text-green-600 text-4xl"></i>
                </div>

                <!-- Success Message -->
                <h1 class="text-3xl font-bold text-gray-900 mb-4">
                    تم الدفع بنجاح!
                </h1>
                <p class="text-gray-600 text-lg mb-8">
                    تهانينا! تم تفعيل اشتراكك بنجاح ويمكنك الآن الاستفادة من جميع الخدمات.
                </p>

                <!-- Payment Details -->
                {% if payment %}
                <div class="bg-gray-50 rounded-lg p-6 mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">تفاصيل الدفع</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">رقم العملية:</span>
                            <span class="font-semibold">{{ payment.id }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">المبلغ:</span>
                            <span class="font-semibold">{{ payment.amount }} {{ payment.subscription.plan.get_currency_symbol }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الباقة:</span>
                            <span class="font-semibold">{{ payment.subscription.plan.name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">طريقة الدفع:</span>
                            <span class="font-semibold">
                                {% if payment.payment_method == 'stripe' %}
                                    <i class="fab fa-stripe text-purple-600 ml-1"></i>
                                    Stripe
                                {% elif payment.payment_method == 'paypal' %}
                                    <i class="fab fa-paypal text-blue-600 ml-1"></i>
                                    PayPal
                                {% else %}
                                    {{ payment.get_payment_method_display }}
                                {% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">تاريخ الدفع:</span>
                            <span class="font-semibold">{{ payment.processed_at|date:"d/m/Y H:i" }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">حالة الاشتراك:</span>
                            <span class="font-semibold text-green-600">
                                <i class="fas fa-check-circle ml-1"></i>
                                نشط
                            </span>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Next Steps -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                    <h3 class="text-lg font-semibold text-blue-900 mb-3">
                        <i class="fas fa-lightbulb ml-2"></i>
                        الخطوات التالية
                    </h3>
                    <ul class="text-blue-800 text-sm space-y-2 text-right">
                        <li class="flex items-center">
                            <i class="fas fa-calendar-alt text-blue-600 ml-2"></i>
                            تحقق من جدول حصصك في قسم "حصصي"
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-user-graduate text-blue-600 ml-2"></i>
                            ستتم جدولة حصصك قريباً من قبل الإدارة
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-bell text-blue-600 ml-2"></i>
                            ستصلك إشعارات قبل موعد كل حصة
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-headset text-blue-600 ml-2"></i>
                            يمكنك التواصل مع الدعم الفني في أي وقت
                        </li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{% url 'student_lessons' %}" 
                       class="bg-islamic-primary hover:bg-islamic-primary-dark text-white py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center">
                        <i class="fas fa-calendar-alt ml-2"></i>
                        عرض حصصي
                    </a>
                    <a href="{% url 'student_subscriptions' %}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center">
                        <i class="fas fa-credit-card ml-2"></i>
                        إدارة الاشتراكات
                    </a>
                    <a href="{% url 'student_dashboard' %}" 
                       class="bg-blue-500 hover:bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center">
                        <i class="fas fa-home ml-2"></i>
                        الصفحة الرئيسية
                    </a>
                </div>

                <!-- Support Contact -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <p class="text-gray-600 text-sm">
                        هل تحتاج مساعدة؟ 
                        <a href="{% url 'student_support' %}" class="text-islamic-primary hover:underline font-semibold">
                            تواصل مع الدعم الفني
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-redirect Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إذا كانت هذه نافذة منبثقة، أغلقها بعد 10 ثوانِ
    if (window.opener) {
        let countdown = 10;
        const countdownElement = document.createElement('div');
        countdownElement.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        countdownElement.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-clock ml-2"></i>
                <span>سيتم إغلاق النافذة خلال <span id="countdown">${countdown}</span> ثانية</span>
            </div>
        `;
        document.body.appendChild(countdownElement);

        const timer = setInterval(() => {
            countdown--;
            document.getElementById('countdown').textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                
                // إرسال رسالة نجاح للنافذة الأصلية
                try {
                    window.opener.postMessage({
                        type: 'payment_completed',
                        status: 'success',
                        data: {
                            payment_id: '{{ payment.id|default:"" }}',
                            amount: '{{ payment.amount|default:"" }}',
                            currency: '{{ payment.subscription.plan.currency|default:"USD" }}',
                            payment_method: '{{ payment.payment_method|default:"" }}'
                        }
                    }, '*');
                } catch (error) {
                    console.error('خطأ في إرسال الرسالة:', error);
                }
                
                window.close();
            }
        }, 1000);
    }
});
</script>
{% endblock %}
