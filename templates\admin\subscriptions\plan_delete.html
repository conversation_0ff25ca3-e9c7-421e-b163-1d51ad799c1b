{% extends 'base.html' %}
{% load static %}

{% block title %}حذف الباقة - {{ plan.name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-4xl font-bold text-red-600 mb-2 flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-500 ml-3"></i>
                    حذف الباقة
                </h1>
                <p class="text-gray-600">تأكيد حذف الباقة من النظام</p>
            </div>
            <a href="{% url 'admin_plan_detail' plan.id %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للتفاصيل
            </a>
        </div>

        <!-- Warning Card -->
        <div class="bg-white rounded-lg shadow-lg p-8 max-w-4xl mx-auto">
            <!-- Warning Icon -->
            <div class="text-center mb-8">
                <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-exclamation-triangle text-red-500 text-4xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">هل أنت متأكد من حذف هذه الباقة؟</h2>
                <p class="text-gray-600">هذا الإجراء لا يمكن التراجع عنه</p>
            </div>

            <!-- Plan Details -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-bold text-gray-900 mb-4">تفاصيل الباقة المراد حذفها:</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-600">اسم الباقة:</span>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.name }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-600">نوع الباقة:</span>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.get_plan_type_display }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-600">السعر:</span>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.get_price_with_currency }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-600">عدد الحصص:</span>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.lessons_count }} حصة</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-600">المدة:</span>
                        <p class="text-lg font-semibold text-gray-900">{{ plan.duration_days }} يوم</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-600">الحالة:</span>
                        {% if plan.is_active %}
                            <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-semibold rounded-full">نشطة</span>
                        {% else %}
                            <span class="px-3 py-1 bg-red-100 text-red-800 text-sm font-semibold rounded-full">معطلة</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mt-4">
                    <span class="text-sm font-medium text-gray-600">وصف الباقة:</span>
                    <p class="text-gray-700 mt-1">{{ plan.description }}</p>
                </div>
            </div>

            <!-- Active Subscriptions Warning -->
            {% if active_subscriptions > 0 %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
                <div class="flex items-center mb-4">
                    <i class="fas fa-exclamation-circle text-red-500 text-xl ml-3"></i>
                    <h3 class="text-lg font-bold text-red-800">تحذير: لا يمكن حذف هذه الباقة</h3>
                </div>
                <p class="text-red-700 mb-4">
                    هذه الباقة لديها <strong>{{ active_subscriptions }}</strong> اشتراك نشط. 
                    لا يمكن حذف الباقة إلا بعد انتهاء أو إلغاء جميع الاشتراكات النشطة.
                </p>
                <div class="bg-red-100 rounded-lg p-4">
                    <h4 class="font-semibold text-red-800 mb-2">الخيارات المتاحة:</h4>
                    <ul class="list-disc list-inside text-red-700 space-y-1">
                        <li>انتظار انتهاء الاشتراكات النشطة تلقائياً</li>
                        <li>إلغاء الاشتراكات النشطة يدوياً من قسم إدارة الاشتراكات</li>
                        <li>تعطيل الباقة بدلاً من حذفها (لمنع اشتراكات جديدة)</li>
                    </ul>
                </div>
            </div>
            {% else %}
            <!-- Confirmation Form -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
                <div class="flex items-center mb-4">
                    <i class="fas fa-info-circle text-yellow-500 text-xl ml-3"></i>
                    <h3 class="text-lg font-bold text-yellow-800">معلومات مهمة</h3>
                </div>
                <ul class="list-disc list-inside text-yellow-700 space-y-2">
                    <li>سيتم حذف الباقة نهائياً من النظام</li>
                    <li>لن تظهر الباقة للطلاب في صفحة الاشتراكات</li>
                    <li>ستبقى سجلات الاشتراكات السابقة محفوظة للمراجعة</li>
                    <li>لا يمكن التراجع عن هذا الإجراء</li>
                </ul>
            </div>

            <form method="post" class="text-center">
                {% csrf_token %}
                <div class="mb-6">
                    <label class="flex items-center justify-center">
                        <input type="checkbox" id="confirm_delete" required 
                               class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 focus:ring-2 ml-2">
                        <span class="text-gray-700">أؤكد أنني أريد حذف هذه الباقة نهائياً</span>
                    </label>
                </div>
                
                <div class="flex justify-center space-x-4 space-x-reverse">
                    <a href="{% url 'admin_plan_detail' plan.id %}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-8 py-3 rounded-lg transition-colors">
                        إلغاء
                    </a>
                    <button type="submit" id="delete_button" disabled
                            class="bg-red-500 hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-8 py-3 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-trash ml-2"></i>
                        حذف الباقة نهائياً
                    </button>
                </div>
            </form>
            {% endif %}

            <!-- Alternative Actions -->
            <div class="mt-8 pt-8 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">بدائل أخرى:</h3>
                <div class="flex flex-wrap gap-4 justify-center">
                    <a href="{% url 'admin_plan_edit' plan.id %}" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-edit ml-2"></i>
                        تعديل الباقة
                    </a>
                    
                    {% if plan.is_active %}
                    <button onclick="togglePlanStatus()" 
                            class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-pause ml-2"></i>
                        تعطيل الباقة
                    </button>
                    {% else %}
                    <button onclick="togglePlanStatus()" 
                            class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-play ml-2"></i>
                        تفعيل الباقة
                    </button>
                    {% endif %}
                    
                    <a href="{% url 'admin_plans_list' %}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center transition-colors">
                        <i class="fas fa-list ml-2"></i>
                        قائمة الباقات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Enable/disable delete button based on checkbox
document.getElementById('confirm_delete').addEventListener('change', function() {
    const deleteButton = document.getElementById('delete_button');
    if (deleteButton) {
        deleteButton.disabled = !this.checked;
    }
});

// Toggle plan status function (placeholder)
function togglePlanStatus() {
    // This would need to be implemented with an AJAX call
    alert('سيتم تنفيذ هذه الميزة قريباً');
}
</script>
{% endblock %}
