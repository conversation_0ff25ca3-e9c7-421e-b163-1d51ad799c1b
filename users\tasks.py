"""
مهام Celery للإشعارات البريدية التلقائية
"""

"""
مهام WhatsApp المجدولة - تم استبدال نظام البريد الإلكتروني
"""

from celery import shared_task
import logging

logger = logging.getLogger(__name__)

@shared_task
def send_lesson_reminder_1day():
    """إرسال تذكيرات WhatsApp للحصص قبل يوم واحد"""
    try:
        from .whatsapp_tasks import send_whatsapp_lesson_reminder_1day
        return send_whatsapp_lesson_reminder_1day()
    except Exception as e:
        logger.error(f"خطأ في إرسال تذكيرات WhatsApp (24 ساعة): {e}")
        return f"خطأ: {str(e)}"

@shared_task
def send_lesson_reminder_30min():
    """إرسال تذكيرات WhatsApp للحصص قبل 30 دقيقة"""
    try:
        from .whatsapp_tasks import send_whatsapp_lesson_reminder_30min
        return send_whatsapp_lesson_reminder_30min()
    except Exception as e:
        logger.error(f"خطأ في إرسال تذكيرات WhatsApp (30 دقيقة): {e}")
        return f"خطأ: {str(e)}"

@shared_task
def send_scheduled_messages():
    """معالجة رسائل WhatsApp المجدولة"""
    try:
        from .whatsapp_tasks import process_scheduled_whatsapp_messages
        return process_scheduled_whatsapp_messages()
    except Exception as e:
        logger.error(f"خطأ في معالجة رسائل WhatsApp المجدولة: {e}")
        return f"خطأ: {str(e)}"

# تم استبدال جميع مهام البريد الإلكتروني بمهام WhatsApp
# انظر whatsapp_tasks.py للمهام الجديدة
