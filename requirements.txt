# Django Core - Latest stable
Django>=4.2,<5.0

# Database
psycopg2-binary>=2.9,<3.0
dj-database-url>=2.0,<3.0

# Production Server
gunicorn>=21.0,<22.0
whitenoise>=6.0,<7.0

# Forms
django-crispy-forms>=2.0,<3.0
crispy-bootstrap5>=0.7,<1.0

# Essential utilities
Pillow>=10.0,<11.0
requests>=2.31,<3.0
pytz>=2023.3

# Security and encryption
cryptography>=41.0,<42.0
PyJWT>=2.8,<3.0

# Reports and data processing
reportlab>=4.0,<5.0
openpyxl>=3.1,<4.0

# Date and time utilities
python-dateutil>=2.8,<3.0

# Django Extensions (optional)
django-extensions>=3.2,<4.0

# Email Integration
django-crontab>=0.7,<1.0

# Background Tasks (for email automation)
celery>=5.3,<6.0
redis>=5.0,<6.0

# PDF Generation (for email attachments)
weasyprint>=60.0,<61.0
xhtml2pdf>=0.2,<1.0
