{% extends 'base.html' %}
{% load static %}

{% block title %}الاشتراكات والباقات{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
/* تحسين ألوان أزرار الدفع */
.payment-button-paypal {
    background: linear-gradient(45deg, #2563eb, #1d4ed8) !important;
    color: #ffffff !important;
    border: none !important;
}

.payment-button-paypal:hover {
    background: linear-gradient(45deg, #1d4ed8, #1e40af) !important;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.4) !important;
}

.payment-button-stripe {
    background: linear-gradient(45deg, #7c3aed, #6d28d9) !important;
    color: #ffffff !important;
    border: none !important;
}

.payment-button-stripe:hover {
    background: linear-gradient(45deg, #6d28d9, #5b21b6) !important;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(124, 58, 237, 0.4) !important;
}

.payment-button-bank {
    background: linear-gradient(45deg, #059669, #047857) !important;
    color: #ffffff !important;
    border: none !important;
}

.payment-button-bank:hover {
    background: linear-gradient(45deg, #047857, #065f46) !important;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(5, 150, 105, 0.4) !important;
}

.payment-button-disabled {
    background: #9ca3af !important;
    color: #4b5563 !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
    border: none !important;
}

.payment-button-disabled:hover {
    background: #9ca3af !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ضمان تطبيق الألوان على جميع أزرار الدفع */
button[type="submit"]:not(.payment-button-disabled) {
    transition: all 0.3s ease !important;
}

/* إصلاح مشكلة اللون الأبيض */
.bg-white button[type="submit"] {
    color: #ffffff !important;
}

/* تحسين التباين */
.text-white {
    color: #ffffff !important;
}
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-islamic-mint">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-islamic-primary mb-4 flex items-center justify-center">
                <i class="fas fa-credit-card text-islamic-gold ml-3"></i>
                الاشتراكات والباقات
            </h1>
            <p class="text-gray-600 text-lg">اختر الباقة المناسبة لك وابدأ رحلتك التعليمية</p>
        </div>

        <!-- Current Subscription -->
        {% if current_subscription %}
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8 border-r-4
            {% if current_subscription.status == 'active' %}
                {% if current_subscription.is_lessons_exhausted %}border-red-500{% else %}border-green-500{% endif %}
            {% elif current_subscription.status == 'pending_approval' %}border-orange-500
            {% elif current_subscription.status == 'payment_pending' %}border-blue-500
            {% elif current_subscription.status == 'cancelled' %}border-red-500
            {% else %}border-gray-500{% endif %}">

            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <!-- Dynamic Title Based on Status -->
                    {% if current_subscription.status == 'active' %}
                        <h3 class="text-xl font-bold {% if current_subscription.is_lessons_exhausted %}text-red-800{% else %}text-green-800{% endif %} mb-2">
                            <i class="fas fa-{% if current_subscription.is_lessons_exhausted %}exclamation-triangle{% else %}check-circle{% endif %} ml-2"></i>
                            اشتراكك الحالي
                        </h3>
                    {% elif current_subscription.status == 'pending_approval' %}
                        <h3 class="text-xl font-bold text-orange-800 mb-2">
                            <i class="fas fa-clock ml-2"></i>
                            اشتراكك في انتظار الموافقة
                        </h3>
                    {% elif current_subscription.status == 'payment_pending' %}
                        <h3 class="text-xl font-bold text-blue-800 mb-2">
                            <i class="fas fa-credit-card ml-2"></i>
                            اشتراكك في انتظار إكمال الدفع
                        </h3>
                    {% elif current_subscription.status == 'cancelled' %}
                        <h3 class="text-xl font-bold text-red-800 mb-2">
                            <i class="fas fa-times-circle ml-2"></i>
                            تم إلغاء اشتراكك من قبل الإدارة
                        </h3>
                    {% else %}
                        <h3 class="text-xl font-bold text-gray-800 mb-2">
                            <i class="fas fa-info-circle ml-2"></i>
                            حالة الاشتراك: {{ current_subscription.get_status_display }}
                        </h3>
                    {% endif %}

                    <!-- Subscription Details -->
                    <p class="text-gray-700 mb-1">
                        <strong>الباقة:</strong> {{ current_subscription.plan.name }}
                    </p>

                    <!-- Status-specific Information -->
                    {% if current_subscription.status == 'active' %}
                        <p class="text-gray-700 mb-1">
                            <strong>الحصص المتبقية:</strong>
                            <span class="{% if current_subscription.is_lessons_exhausted %}text-red-600 font-bold{% else %}text-green-600{% endif %}">
                                {{ current_subscription.remaining_lessons }} حصة
                            </span>
                            <span class="text-sm text-gray-500">
                                (أكملت {{ current_subscription.get_completed_lessons_count }} من {{ current_subscription.plan.lessons_count }})
                            </span>
                        </p>
                        <p class="text-gray-700 mb-2">
                            <strong>ينتهي في:</strong> {{ current_subscription.end_date }}
                            <span class="text-sm text-gray-500">({{ current_subscription.days_remaining }} يوم متبقي)</span>
                        </p>

                        <!-- Progress Bar for Active Subscriptions -->
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                            <div class="{% if current_subscription.is_lessons_exhausted %}bg-red-600{% else %}bg-green-600{% endif %} h-2 rounded-full transition-all duration-300"
                                 style="width: {{ current_subscription.get_progress_percentage }}%"></div>
                        </div>
                        <p class="text-xs text-gray-500">{{ current_subscription.get_progress_percentage|floatformat:1 }}% مكتمل</p>

                    {% elif current_subscription.status == 'pending_approval' %}
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-3">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-info-circle text-orange-500 ml-2"></i>
                                <span class="font-semibold text-orange-800">تم استلام دفعتك بنجاح!</span>
                            </div>
                            <p class="text-orange-700 text-sm mb-2">
                                نحن نراجع اشتراكك حالياً وسيتم تفعيله خلال 24 ساعة من موافقة المدير.
                            </p>
                            <p class="text-gray-600 text-sm">
                                <strong>تاريخ الاشتراك:</strong> {{ current_subscription.created_at|date:"d M Y - H:i" }}
                            </p>
                            <p class="text-gray-600 text-sm">
                                <strong>المبلغ المدفوع:</strong> {{ current_subscription.amount_paid }} {{ current_subscription.plan.get_currency_symbol }}
                            </p>
                        </div>

                    {% elif current_subscription.status == 'payment_pending' %}
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exclamation-triangle text-blue-500 ml-2"></i>
                                <span class="font-semibold text-blue-800">يرجى إكمال عملية الدفع</span>
                            </div>
                            <p class="text-blue-700 text-sm mb-2">
                                تم إنشاء اشتراكك لكن لم يتم إكمال عملية الدفع بعد. يرجى إكمال الدفع لتفعيل الاشتراك.
                            </p>
                            <p class="text-gray-600 text-sm">
                                <strong>تاريخ الإنشاء:</strong> {{ current_subscription.created_at|date:"d M Y - H:i" }}
                            </p>
                            <p class="text-gray-600 text-sm">
                                <strong>المبلغ المطلوب:</strong> {{ current_subscription.amount_paid }} {{ current_subscription.plan.get_currency_symbol }}
                            </p>
                        </div>

                    {% elif current_subscription.status == 'cancelled' %}
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-3">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exclamation-triangle text-red-500 ml-2"></i>
                                <span class="font-semibold text-red-800">تم إلغاء اشتراكك</span>
                            </div>
                            <p class="text-red-700 text-sm mb-2">
                                تم إلغاء اشتراكك من قبل الإدارة. يمكنك التواصل مع الدعم الفني لمعرفة السبب أو الاشتراك في باقة جديدة.
                            </p>
                            <p class="text-gray-600 text-sm">
                                <strong>تاريخ الاشتراك:</strong> {{ current_subscription.created_at|date:"d M Y - H:i" }}
                            </p>
                            <p class="text-gray-600 text-sm">
                                <strong>تاريخ الإلغاء:</strong> {{ current_subscription.updated_at|date:"d M Y - H:i" }}
                            </p>
                            {% if current_subscription.notes %}
                            <p class="text-gray-600 text-sm">
                                <strong>ملاحظات الإدارة:</strong> {{ current_subscription.notes }}
                            </p>
                            {% endif %}
                        </div>

                    {% else %}
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3">
                            <p class="text-gray-700 text-sm">
                                <strong>حالة الاشتراك:</strong> {{ current_subscription.get_status_display }}
                            </p>
                            <p class="text-gray-600 text-sm">
                                <strong>تاريخ الاشتراك:</strong> {{ current_subscription.created_at|date:"d M Y - H:i" }}
                            </p>
                        </div>
                    {% endif %}
                </div>

                <!-- Status Badge -->
                <div class="text-center mr-4">
                    {% if current_subscription.status == 'active' %}
                        {% if current_subscription.is_lessons_exhausted %}
                            <!-- Renewal Options -->
                            <div class="space-y-2">
                                <button onclick="renewCurrentPlan({{ current_subscription.id }})"
                                        class="inline-flex items-center px-4 py-2 text-sm font-bold rounded-full bg-blue-100 text-blue-800 border border-blue-200 hover:bg-blue-200 transition-colors">
                                    <i class="fas fa-redo ml-1"></i>تجديد الباقة
                                </button>
                                <p class="text-xs text-gray-500">أو اختر باقة جديدة بالأسفل</p>
                            </div>
                        {% else %}
                            <span class="bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-semibold">
                                <i class="fas fa-check-circle ml-1"></i>نشط
                            </span>
                        {% endif %}
                    {% elif current_subscription.status == 'pending_approval' %}
                        <span class="bg-orange-100 text-orange-800 px-4 py-2 rounded-full text-sm font-semibold">
                            <i class="fas fa-clock ml-1"></i>في انتظار الموافقة
                        </span>
                    {% elif current_subscription.status == 'payment_pending' %}
                        <span class="bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-semibold">
                            <i class="fas fa-credit-card ml-1"></i>في انتظار الدفع
                        </span>
                    {% elif current_subscription.status == 'cancelled' %}
                        <span class="bg-red-100 text-red-800 px-4 py-2 rounded-full text-sm font-semibold">
                            <i class="fas fa-times-circle ml-1"></i>ملغي
                        </span>
                    {% else %}
                        <span class="bg-gray-100 text-gray-800 px-4 py-2 rounded-full text-sm font-semibold">
                            {{ current_subscription.get_status_display }}
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Available Plans -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-islamic-primary mb-8 text-center">
                <i class="fas fa-gem text-islamic-gold ml-3"></i>
                الباقات المتاحة
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                {% for plan in available_plans %}
                <div class="relative bg-white rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-all duration-300 hover:shadow-2xl {% if plan.is_featured %}border-3 border-islamic-gold ring-4 ring-islamic-gold ring-opacity-20{% endif %}">
                    {% if plan.is_featured %}
                    <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="bg-gradient-to-r from-islamic-gold to-yellow-500 text-white px-6 py-2 rounded-full shadow-lg">
                            <span class="font-bold text-sm flex items-center">
                                <i class="fas fa-crown ml-2"></i>
                                الأكثر شعبية
                            </span>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Plan Type Badge -->
                    <div class="absolute top-4 right-4">
                        <span class="bg-islamic-primary text-white px-3 py-1 rounded-full text-xs font-semibold">
                            {{ plan.get_plan_type_display }}
                        </span>
                    </div>

                    <div class="p-8 {% if plan.is_featured %}pt-12{% endif %}">
                        <!-- Plan Header -->
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-book-open text-white text-2xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-islamic-primary mb-3">{{ plan.name }}</h3>

                            <!-- Price Display -->
                            <div class="mb-4">
                                {% if plan.discount_percentage > 0 %}
                                    <div class="flex items-center justify-center space-x-2 space-x-reverse mb-2">
                                        <span class="text-lg line-through text-gray-400">{{ plan.get_formatted_price }}</span>
                                        <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full animate-pulse">
                                            خصم {{ plan.discount_percentage }}%
                                        </span>
                                    </div>
                                    <div class="text-4xl font-bold text-green-600 mb-1">
                                        {{ plan.get_formatted_discounted_price }}
                                    </div>
                                {% else %}
                                    <div class="text-4xl font-bold text-islamic-primary mb-1">
                                        {{ plan.get_formatted_price }}
                                    </div>
                                {% endif %}
                                <div class="flex items-center justify-center">
                                    <span class="text-gray-600 text-sm">{{ plan.get_currency_display }}</span>
                                    <span class="text-gray-400 text-sm mx-2">/</span>
                                    <span class="text-gray-600 text-sm">{{ plan.get_duration_type_display }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Features -->
                        <div class="mb-8">
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-semibold text-gray-800 mb-4 text-center">مميزات الباقة</h4>
                                <ul class="space-y-3">
                                    <li class="flex items-center text-gray-700">
                                        <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-graduation-cap text-green-600 text-xs"></i>
                                        </div>
                                        <span class="font-medium">{{ plan.lessons_count }} حصة تعليمية</span>
                                    </li>
                                    <li class="flex items-center text-gray-700">
                                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-clock text-blue-600 text-xs"></i>
                                        </div>
                                        <span class="font-medium">{{ plan.lesson_duration }} دقيقة لكل حصة</span>
                                    </li>
                                    <li class="flex items-center text-gray-700">
                                        <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-calendar-alt text-purple-600 text-xs"></i>
                                        </div>
                                        <span class="font-medium">صالحة لمدة {{ plan.duration_days }} يوم</span>
                                    </li>
                                    <li class="flex items-center text-gray-700">
                                        <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-calculator text-orange-600 text-xs"></i>
                                        </div>
                                        <span class="font-medium">{{ plan.get_formatted_price_per_lesson }} {{ plan.get_currency_display }} للحصة الواحدة</span>
                                    </li>
                                    {% if plan.features %}
                                        {% for feature in plan.features %}
                                        <li class="flex items-center text-gray-700">
                                            <div class="w-6 h-6 bg-islamic-gold bg-opacity-20 rounded-full flex items-center justify-center ml-3">
                                                <i class="fas fa-star text-islamic-gold text-xs"></i>
                                            </div>
                                            <span class="font-medium">{{ feature }}</span>
                                        </li>
                                        {% endfor %}
                                    {% endif %}
                                </ul>
                            </div>
                        </div>

                        <!-- Plan Description -->
                        {% if plan.description %}
                        <div class="mb-6 p-4 bg-islamic-mint bg-opacity-10 rounded-lg">
                            <p class="text-gray-700 text-sm text-center italic">{{ plan.description }}</p>
                        </div>
                        {% endif %}

                        <!-- Subscribe Button -->
                        {% if not current_subscription or current_subscription.is_lessons_exhausted or current_subscription.status == 'payment_pending' or current_subscription.status == 'cancelled' %}
                        <div class="space-y-4">
                            <div class="text-center">
                                <h5 class="text-lg font-semibold text-gray-800 mb-2">
                                    {% if current_subscription and current_subscription.is_lessons_exhausted %}
                                        تجديد أو اختيار باقة جديدة
                                    {% elif current_subscription and current_subscription.status == 'payment_pending' %}
                                        إكمال عملية الدفع
                                    {% elif current_subscription and current_subscription.status == 'cancelled' %}
                                        اشتراك في باقة جديدة
                                    {% else %}
                                        اختر طريقة الدفع
                                    {% endif %}
                                </h5>
                                {% if current_subscription and current_subscription.status == 'payment_pending' %}
                                    <p class="text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg">
                                        <i class="fas fa-info-circle ml-1"></i>
                                        يرجى إكمال الدفع للباقة التي اخترتها مسبقاً
                                    </p>
                                {% elif current_subscription and current_subscription.status == 'cancelled' %}
                                    <p class="text-sm text-red-600 bg-red-50 px-3 py-2 rounded-lg">
                                        <i class="fas fa-exclamation-triangle ml-1"></i>
                                        تم إلغاء اشتراكك السابق - يمكنك الاشتراك في باقة جديدة
                                    </p>
                                {% else %}
                                    <p class="text-sm text-gray-600">جميع المدفوعات آمنة ومحمية</p>
                                {% endif %}
                            </div>
                            <div class="space-y-3">
                                <!-- PayPal Payment Button -->
                                {% for method in all_payment_methods %}
                                    {% if method.id == 'paypal' %}
                                        {% if method.enabled %}
                                            <form method="post" action="{% url 'subscribe_to_plan' plan.id %}" class="w-full">
                                                {% csrf_token %}
                                                <input type="hidden" name="payment_method" value="paypal">
                                                <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-3 px-6 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                                    <i class="fab fa-paypal ml-3 text-xl"></i>
                                                    <span class="font-semibold">الدفع عبر PayPal</span>
                                                </button>
                                            </form>
                                        {% else %}
                                            <button type="button" onclick="showPaymentDisabledMessage('PayPal')" class="w-full bg-gray-400 text-gray-600 py-3 px-6 rounded-xl flex items-center justify-center cursor-not-allowed opacity-60">
                                                <i class="fab fa-paypal ml-3 text-xl"></i>
                                                <span class="font-semibold">PayPal غير متاح</span>
                                                <i class="fas fa-lock mr-2 text-sm"></i>
                                            </button>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}

                                <!-- Stripe Payment Button -->
                                {% for method in all_payment_methods %}
                                    {% if method.id == 'stripe' %}
                                        {% if method.enabled %}
                                            <form method="post" action="{% url 'subscribe_to_plan' plan.id %}" class="w-full">
                                                {% csrf_token %}
                                                <input type="hidden" name="payment_method" value="stripe">
                                                <button type="submit" class="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white py-3 px-6 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                                    <i class="fab fa-stripe ml-3 text-xl"></i>
                                                    <span class="font-semibold">الدفع عبر Stripe</span>
                                                </button>
                                            </form>
                                        {% else %}
                                            <button type="button" onclick="showPaymentDisabledMessage('Stripe')" class="w-full bg-gray-400 text-gray-600 py-3 px-6 rounded-xl flex items-center justify-center cursor-not-allowed opacity-60">
                                                <i class="fab fa-stripe ml-3 text-xl"></i>
                                                <span class="font-semibold">Stripe غير متاح</span>
                                                <i class="fas fa-lock mr-2 text-sm"></i>
                                            </button>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}

                                <!-- Bank Transfer Payment Button -->
                                {% for method in all_payment_methods %}
                                    {% if method.id == 'bank_transfer' %}
                                        {% if method.enabled %}
                                            <form method="post" action="{% url 'subscribe_to_plan' plan.id %}" class="w-full">
                                                {% csrf_token %}
                                                <input type="hidden" name="payment_method" value="bank_transfer">
                                                <button type="submit" class="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3 px-6 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                                    <i class="fas fa-university ml-3 text-xl"></i>
                                                    <span class="font-semibold">التحويل البنكي</span>
                                                </button>
                                            </form>
                                        {% else %}
                                            <button type="button" onclick="showPaymentDisabledMessage('التحويل البنكي')" class="w-full bg-gray-400 text-gray-600 py-3 px-6 rounded-xl flex items-center justify-center cursor-not-allowed opacity-60">
                                                <i class="fas fa-university ml-3 text-xl"></i>
                                                <span class="font-semibold">التحويل البنكي غير متاح</span>
                                                <i class="fas fa-lock mr-2 text-sm"></i>
                                            </button>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <div class="text-center pt-2">
                                <p class="text-xs text-gray-500 flex items-center justify-center">
                                    <i class="fas fa-shield-alt ml-2 text-green-500"></i>
                                    دفع آمن ومشفر 100%
                                </p>
                            </div>
                        </div>
                        {% elif current_subscription and current_subscription.status == 'active' and not current_subscription.is_lessons_exhausted %}
                        <div class="bg-gradient-to-r from-orange-50 to-yellow-50 border-2 border-orange-200 rounded-xl p-6 text-center">
                            <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-info-circle text-orange-500 text-xl"></i>
                            </div>
                            <h4 class="text-orange-700 font-bold text-lg mb-2">لديك اشتراك نشط</h4>
                            <div class="bg-white rounded-lg p-4 mb-3">
                                <p class="text-gray-700 font-medium">
                                    باقة "{{ current_subscription.plan.name }}"
                                </p>
                                <p class="text-gray-600 text-sm mt-1">
                                    ينتهي في {{ current_subscription.end_date|date:"d M Y" }}
                                </p>
                                <p class="text-green-600 text-sm mt-1">
                                    متبقي {{ current_subscription.remaining_lessons }} حصة
                                </p>
                            </div>
                            <p class="text-orange-600 text-sm">
                                لا يمكنك الاشتراك في باقة أخرى حتى انتهاء الحصص المتبقية
                            </p>
                        </div>
                        {% elif current_subscription and current_subscription.status == 'pending_approval' %}
                        <div class="bg-gradient-to-r from-orange-50 to-yellow-50 border-2 border-orange-200 rounded-xl p-6 text-center">
                            <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-clock text-orange-500 text-xl"></i>
                            </div>
                            <h4 class="text-orange-700 font-bold text-lg mb-2">اشتراكك في انتظار الموافقة</h4>
                            <div class="bg-white rounded-lg p-4 mb-3">
                                <p class="text-gray-700 font-medium">
                                    باقة "{{ current_subscription.plan.name }}"
                                </p>
                                <p class="text-gray-600 text-sm mt-1">
                                    تم الدفع بنجاح وننتظر موافقة المدير
                                </p>
                            </div>
                            <p class="text-orange-600 text-sm">
                                لا يمكنك الاشتراك في باقة أخرى حتى يتم تفعيل اشتراكك الحالي
                            </p>
                        </div>
                        {% elif current_subscription and current_subscription.status == 'cancelled' %}
                        <div class="bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200 rounded-xl p-6 text-center">
                            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-times-circle text-red-500 text-xl"></i>
                            </div>
                            <h4 class="text-red-700 font-bold text-lg mb-2">تم إلغاء اشتراكك</h4>
                            <div class="bg-white rounded-lg p-4 mb-3">
                                <p class="text-gray-700 font-medium">
                                    باقة "{{ current_subscription.plan.name }}"
                                </p>
                                <p class="text-gray-600 text-sm mt-1">
                                    تم الإلغاء في: {{ current_subscription.updated_at|date:"d M Y" }}
                                </p>
                                {% if current_subscription.notes %}
                                <p class="text-red-600 text-sm mt-1">
                                    السبب: {{ current_subscription.notes }}
                                </p>
                                {% endif %}
                            </div>
                            <p class="text-red-600 text-sm mb-3">
                                يمكنك الاشتراك في باقة جديدة أو التواصل مع الدعم الفني
                            </p>
                        </div>
                        {% elif current_subscription and current_subscription.status != 'payment_pending' %}
                        <div class="bg-gradient-to-r from-gray-50 to-blue-50 border-2 border-gray-200 rounded-xl p-6 text-center">
                            <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-info-circle text-gray-500 text-xl"></i>
                            </div>
                            <h4 class="text-gray-700 font-bold text-lg mb-2">{{ current_subscription.get_status_display }}</h4>
                            <div class="bg-white rounded-lg p-4 mb-3">
                                <p class="text-gray-700 font-medium">
                                    باقة "{{ current_subscription.plan.name }}"
                                </p>
                                <p class="text-gray-600 text-sm mt-1">
                                    تاريخ الاشتراك: {{ current_subscription.created_at|date:"d M Y" }}
                                </p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Subscription History -->
        {% if student_subscriptions %}
        <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-2xl font-bold text-islamic-primary flex items-center">
                    <div class="w-10 h-10 bg-islamic-primary rounded-full flex items-center justify-center ml-3">
                        <i class="fas fa-history text-white"></i>
                    </div>
                    تاريخ الاشتراكات
                </h3>
                <span class="bg-islamic-primary bg-opacity-10 text-islamic-primary px-4 py-2 rounded-full text-sm font-semibold">
                    {{ student_subscriptions|length }} اشتراك
                </span>
            </div>

            <!-- Mobile Cards View -->
            <div class="block md:hidden space-y-4">
                {% for subscription in student_subscriptions %}
                <div class="bg-gradient-to-r from-gray-50 to-white border border-gray-200 rounded-xl p-6 shadow-sm">
                    <div class="flex items-start justify-between mb-4">
                        <div>
                            <h4 class="font-bold text-lg text-gray-900">{{ subscription.plan.name }}</h4>
                            <p class="text-gray-600 text-sm">{{ subscription.plan.get_plan_type_display }}</p>
                        </div>
                        {% if subscription.status == 'active' %}
                            <span class="px-3 py-1 text-xs font-bold rounded-full bg-green-100 text-green-800 border border-green-200">
                                <i class="fas fa-check-circle ml-1"></i>نشط
                            </span>
                        {% elif subscription.status == 'expired' %}
                            <span class="px-3 py-1 text-xs font-bold rounded-full bg-red-100 text-red-800 border border-red-200">
                                <i class="fas fa-times-circle ml-1"></i>منتهي
                            </span>
                        {% elif subscription.status == 'pending_approval' %}
                            <span class="px-3 py-1 text-xs font-bold rounded-full bg-orange-100 text-orange-800 border border-orange-200">
                                <i class="fas fa-clock ml-1"></i>في انتظار الموافقة
                            </span>
                        {% elif subscription.status == 'payment_pending' %}
                            <span class="px-3 py-1 text-xs font-bold rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                <i class="fas fa-credit-card ml-1"></i>في انتظار الدفع
                            </span>
                        {% elif subscription.status == 'cancelled' %}
                            <span class="px-3 py-1 text-xs font-bold rounded-full bg-red-100 text-red-800 border border-red-200">
                                <i class="fas fa-times-circle ml-1"></i>ملغي
                            </span>
                        {% else %}
                            <span class="px-3 py-1 text-xs font-bold rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                {{ subscription.get_status_display }}
                            </span>
                        {% endif %}
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <p class="text-xs text-gray-500 mb-1">تاريخ البداية</p>
                            <p class="font-semibold text-gray-900">{{ subscription.start_date|date:"d M Y" }}</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500 mb-1">تاريخ النهاية</p>
                            <p class="font-semibold text-gray-900">{{ subscription.end_date|date:"d M Y" }}</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div>
                            <p class="text-xs text-gray-500 mb-1">المبلغ المدفوع</p>
                            <p class="font-bold text-lg text-islamic-primary">{{ subscription.amount_paid }} {{ subscription.plan.get_currency_symbol }}</p>
                        </div>
                        {% if subscription.status == 'active' and subscription.invoice %}
                        <div class="flex space-x-2 space-x-reverse">
                            <a href="{% url 'student_invoice_view' subscription.invoice.id %}"
                               class="bg-blue-100 text-blue-600 p-2 rounded-lg hover:bg-blue-200 transition-colors"
                               title="عرض الفاتورة">
                                <i class="fas fa-file-invoice"></i>
                            </a>
                            <a href="{% url 'student_invoice_print' subscription.invoice.id %}"
                               class="bg-green-100 text-green-600 p-2 rounded-lg hover:bg-green-200 transition-colors"
                               title="طباعة الفاتورة" target="_blank">
                                <i class="fas fa-print"></i>
                            </a>
                            <a href="{% url 'student_invoice_pdf' subscription.invoice.id %}"
                               class="bg-red-100 text-red-600 p-2 rounded-lg hover:bg-red-200 transition-colors"
                               title="تحميل PDF">
                                <i class="fas fa-file-pdf"></i>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Desktop Table View -->
            <div class="hidden md:block overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="bg-gradient-to-r from-islamic-primary to-islamic-secondary text-white">
                            <th class="px-6 py-4 text-right text-sm font-bold uppercase tracking-wider rounded-tr-xl">الباقة</th>
                            <th class="px-6 py-4 text-right text-sm font-bold uppercase tracking-wider">تاريخ البداية</th>
                            <th class="px-6 py-4 text-right text-sm font-bold uppercase tracking-wider">تاريخ النهاية</th>
                            <th class="px-6 py-4 text-right text-sm font-bold uppercase tracking-wider">المبلغ</th>
                            <th class="px-6 py-4 text-right text-sm font-bold uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-4 text-right text-sm font-bold uppercase tracking-wider rounded-tl-xl">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-100">
                        {% for subscription in student_subscriptions %}
                        <tr class="hover:bg-gradient-to-r hover:from-gray-50 hover:to-white transition-all duration-200 {% cycle 'bg-white' 'bg-gray-50' %}">
                            <td class="px-6 py-5">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-islamic-primary bg-opacity-10 rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-box text-islamic-primary"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-bold text-gray-900">{{ subscription.plan.name }}</p>
                                        <p class="text-xs text-gray-500">{{ subscription.plan.get_plan_type_display }}</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-5">
                                <div class="text-sm text-gray-900 font-medium">{{ subscription.start_date|date:"d M Y" }}</div>
                                <div class="text-xs text-gray-500">{{ subscription.created_at|date:"H:i" }}</div>
                            </td>
                            <td class="px-6 py-5">
                                <div class="text-sm text-gray-900 font-medium">{{ subscription.end_date|date:"d M Y" }}</div>
                                <div class="text-xs text-gray-500">انتهاء الاشتراك</div>
                            </td>
                            <td class="px-6 py-5">
                                <div class="text-lg font-bold text-islamic-primary">{{ subscription.amount_paid }}</div>
                                <div class="text-xs text-gray-500">{{ subscription.plan.get_currency_display }}</div>
                            </td>
                            <td class="px-6 py-5">
                                {% if subscription.status == 'active' %}
                                    <span class="inline-flex items-center px-3 py-1 text-xs font-bold rounded-full bg-green-100 text-green-800 border border-green-200">
                                        <i class="fas fa-check-circle ml-1"></i>نشط
                                    </span>
                                {% elif subscription.status == 'expired' %}
                                    <span class="inline-flex items-center px-3 py-1 text-xs font-bold rounded-full bg-red-100 text-red-800 border border-red-200">
                                        <i class="fas fa-times-circle ml-1"></i>منتهي
                                    </span>
                                {% elif subscription.status == 'pending_approval' %}
                                    <span class="inline-flex items-center px-3 py-1 text-xs font-bold rounded-full bg-orange-100 text-orange-800 border border-orange-200">
                                        <i class="fas fa-clock ml-1"></i>في انتظار الموافقة
                                    </span>
                                {% elif subscription.status == 'payment_pending' %}
                                    <span class="inline-flex items-center px-3 py-1 text-xs font-bold rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                        <i class="fas fa-credit-card ml-1"></i>في انتظار الدفع
                                    </span>
                                {% elif subscription.status == 'cancelled' %}
                                    <span class="inline-flex items-center px-3 py-1 text-xs font-bold rounded-full bg-red-100 text-red-800 border border-red-200">
                                        <i class="fas fa-times-circle ml-1"></i>ملغي
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-3 py-1 text-xs font-bold rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                        {{ subscription.get_status_display }}
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-5">
                                {% if subscription.status == 'active' and subscription.invoice %}
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="{% url 'student_invoice_view' subscription.invoice.id %}"
                                       class="bg-blue-100 text-blue-600 p-2 rounded-lg hover:bg-blue-200 transition-colors transform hover:scale-110"
                                       title="عرض الفاتورة">
                                        <i class="fas fa-file-invoice"></i>
                                    </a>
                                    <a href="{% url 'student_invoice_print' subscription.invoice.id %}"
                                       class="bg-green-100 text-green-600 p-2 rounded-lg hover:bg-green-200 transition-colors transform hover:scale-110"
                                       title="طباعة الفاتورة" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <a href="{% url 'student_invoice_pdf' subscription.invoice.id %}"
                                       class="bg-red-100 text-red-600 p-2 rounded-lg hover:bg-red-200 transition-colors transform hover:scale-110"
                                       title="تحميل PDF">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                </div>
                                {% else %}
                                <span class="text-gray-400 text-sm">لا توجد إجراءات</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Payment History -->
        {% if payment_history %}
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-islamic-primary mb-4">
                <i class="fas fa-receipt ml-2"></i>
                تاريخ المدفوعات
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباقة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة الدفع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الدفع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for payment in payment_history %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ payment.subscription.plan.name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ payment.amount }} {{ payment.subscription.plan.get_currency_symbol }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ payment.get_payment_method_display }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ payment.payment_date|date:"Y-m-d H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if payment.status == 'completed' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        مكتمل
                                    </span>
                                {% elif payment.status == 'pending' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        في الانتظار
                                    </span>
                                {% elif payment.status == 'processing' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        قيد المعالجة
                                    </span>
                                {% elif payment.status == 'failed' %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        فاشل
                                    </span>
                                {% else %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        {{ payment.get_status_display }}
                                    </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Function to show payment disabled message
function showPaymentDisabledMessage(paymentMethod) {
    Swal.fire({
        title: 'وسيلة الدفع غير متاحة',
        text: `الدفع عن طريق ${paymentMethod} غير متاح في الوقت الحالي. يرجى اختيار وسيلة دفع أخرى أو التواصل مع الإدارة.`,
        icon: 'warning',
        confirmButtonText: 'حسناً',
        confirmButtonColor: '#f59e0b',
        showClass: {
            popup: 'animate__animated animate__fadeInDown'
        },
        hideClass: {
            popup: 'animate__animated animate__fadeOutUp'
        }
    });
}

// Function to renew current plan
function renewCurrentPlan(subscriptionId) {
    if (confirm('هل تريد تجديد نفس الباقة الحالية؟')) {
        // Get current subscription plan ID
        fetch(`/api/student/subscription/${subscriptionId}/plan-id/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show payment method selection modal
                    showPaymentMethodModal(data.plan_id, true);
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تجديد الباقة');
            });
    }
}

// Function to show payment method modal
function showPaymentMethodModal(planId, isRenewal = false) {
    const modalHtml = `
        <div id="paymentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-bold mb-4">
                    ${isRenewal ? 'تجديد الباقة' : 'اختر طريقة الدفع'}
                </h3>
                <div class="space-y-3">
                    <form method="post" action="/dashboard/student/subscribe/${planId}/" class="w-full">
                        <input type="hidden" name="csrfmiddlewaretoken" value="${document.querySelector('[name=csrfmiddlewaretoken]').value}">
                        <input type="hidden" name="payment_method" value="paypal">
                        <button type="submit" class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <i class="fab fa-paypal ml-3"></i>
                            الدفع عبر PayPal
                        </button>
                    </form>
                    <form method="post" action="/dashboard/student/subscribe/${planId}/" class="w-full">
                        <input type="hidden" name="csrfmiddlewaretoken" value="${document.querySelector('[name=csrfmiddlewaretoken]').value}">
                        <input type="hidden" name="payment_method" value="stripe">
                        <button type="submit" class="w-full bg-purple-600 text-white py-3 px-6 rounded-lg flex items-center justify-center hover:bg-purple-700 transition-colors">
                            <i class="fab fa-stripe ml-3"></i>
                            الدفع عبر Stripe
                        </button>
                    </form>
                    <form method="post" action="/dashboard/student/subscribe/${planId}/" class="w-full">
                        <input type="hidden" name="csrfmiddlewaretoken" value="${document.querySelector('[name=csrfmiddlewaretoken]').value}">
                        <input type="hidden" name="payment_method" value="bank_transfer">
                        <button type="submit" class="w-full bg-green-600 text-white py-3 px-6 rounded-lg flex items-center justify-center hover:bg-green-700 transition-colors">
                            <i class="fas fa-university ml-3"></i>
                            التحويل البنكي
                        </button>
                    </form>
                </div>
                <button onclick="closePaymentModal()" class="w-full mt-4 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors">
                    إلغاء
                </button>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// Function to close payment modal
function closePaymentModal() {
    const modal = document.getElementById('paymentModal');
    if (modal) {
        modal.remove();
    }
}

// متغير لتخزين حالة بوابات الدفع محلي<|im_start|>
let paymentGatewayStatus = {
    paypal: { enabled: false },
    stripe: { enabled: false },
    bank_transfer: { enabled: false }
};

// تحديث أزرار الدفع بناءً على حالة البوابات
function updatePaymentButtons() {
    console.log('🔄 بدء تحديث أزرار الدفع من API...');

    fetch('/api/payment-gateway-status/')
        .then(response => {
            console.log('📡 استجابة API:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📊 بيانات API المستلمة:', data);

            if (data.success) {
                // تحديث الحالة المحلية
                paymentGatewayStatus = data.payment_methods;
                const paymentMethods = data.payment_methods;

                console.log('✅ تم تحديث الحالة المحلية:', paymentGatewayStatus);

                // تحديث أزرار PayPal
                document.querySelectorAll('form').forEach(form => {
                    const paymentMethodInput = form.querySelector('input[name="payment_method"]');
                    if (paymentMethodInput) {
                        const paymentMethod = paymentMethodInput.value;
                        const button = form.querySelector('button[type="submit"]');

                        if (button) {
                            if (paymentMethod === 'paypal') {
                                updatePaymentButton(button, paymentMethods.paypal, 'PayPal', paymentMethod);
                            } else if (paymentMethod === 'stripe') {
                                updatePaymentButton(button, paymentMethods.stripe, 'Stripe', paymentMethod);
                            } else if (paymentMethod === 'bank_transfer') {
                                updatePaymentButton(button, paymentMethods.bank_transfer, 'التحويل البنكي', paymentMethod);
                            }
                        }
                    }
                });

                console.log('✅ تم الانتهاء من تحديث جميع الأزرار');
            } else {
                console.error('❌ فشل في الحصول على بيانات API:', data);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحديث أزرار الدفع:', error);
        });
}

function updatePaymentButton(button, methodData, methodName, paymentMethod) {
    console.log(`🔧 تحديث زر ${methodName}: ${methodData.enabled ? 'مفعل' : 'معطل'}`);

    // إزالة جميع الـ classes السابقة أولاً لضمان التطبيق الصحيح
    button.classList.remove(
        // ألوان التعطيل
        'payment-button-disabled', 'bg-gray-400', 'text-gray-600', 'cursor-not-allowed', 'opacity-60',
        // ألوان PayPal
        'payment-button-paypal', 'bg-gradient-to-r', 'from-blue-600', 'to-blue-700', 'hover:from-blue-700', 'hover:to-blue-800',
        // ألوان Stripe
        'payment-button-stripe', 'from-purple-600', 'to-purple-700', 'hover:from-purple-700', 'hover:to-purple-800',
        // ألوان البنك
        'payment-button-bank', 'from-green-600', 'to-green-700', 'hover:from-green-700', 'hover:to-green-800',
        // تأثيرات عامة
        'text-white', 'hover:scale-105', 'shadow-lg', 'hover:shadow-xl'
    );

    if (methodData.enabled) {
        // تفعيل الزر
        button.disabled = false;

        // إضافة الألوان المناسبة مع ضمان التطبيق
        if (paymentMethod === 'paypal') {
            button.classList.add('payment-button-paypal', 'bg-gradient-to-r', 'from-blue-600', 'to-blue-700', 'hover:from-blue-700', 'hover:to-blue-800', 'text-white');
            button.style.backgroundColor = '#2563eb'; // fallback color
            button.style.color = '#ffffff';
        } else if (paymentMethod === 'stripe') {
            button.classList.add('payment-button-stripe', 'bg-gradient-to-r', 'from-purple-600', 'to-purple-700', 'hover:from-purple-700', 'hover:to-purple-800', 'text-white');
            button.style.backgroundColor = '#7c3aed'; // fallback color
            button.style.color = '#ffffff';
        } else if (paymentMethod === 'bank_transfer') {
            button.classList.add('payment-button-bank', 'bg-gradient-to-r', 'from-green-600', 'to-green-700', 'hover:from-green-700', 'hover:to-green-800', 'text-white');
            button.style.backgroundColor = '#059669'; // fallback color
            button.style.color = '#ffffff';
        }

        button.classList.add('hover:scale-105', 'shadow-lg', 'hover:shadow-xl');

        // إزالة أيقونة القفل
        const lockIcon = button.querySelector('.fa-lock');
        if (lockIcon) {
            lockIcon.remove();
        }

        // تحديث النص
        const textSpan = button.querySelector('.font-semibold');
        if (textSpan) {
            if (paymentMethod === 'paypal') {
                textSpan.textContent = 'الدفع عبر PayPal';
            } else if (paymentMethod === 'stripe') {
                textSpan.textContent = 'الدفع عبر Stripe';
            } else if (paymentMethod === 'bank_transfer') {
                textSpan.textContent = 'التحويل البنكي';
            }
        }

        // إزالة onclick للرسالة المعطلة
        button.onclick = null;

        // إزالة منع إرسال النموذج
        const form = button.closest('form');
        if (form) {
            form.onsubmit = null;
        }

    } else {
        // تعطيل الزر
        button.disabled = true;

        // إضافة ألوان التعطيل مع ضمان التطبيق
        button.classList.add('payment-button-disabled', 'bg-gray-400', 'text-gray-600', 'cursor-not-allowed', 'opacity-60');
        button.style.backgroundColor = '#9ca3af'; // fallback color
        button.style.color = '#4b5563';
        button.style.cursor = 'not-allowed';

        // إضافة أيقونة القفل إذا لم تكن موجودة
        if (!button.querySelector('.fa-lock')) {
            const lockIcon = document.createElement('i');
            lockIcon.className = 'fas fa-lock mr-2 text-sm';
            button.appendChild(lockIcon);
        }

        // تحديث النص
        const textSpan = button.querySelector('.font-semibold');
        if (textSpan) {
            textSpan.textContent = `${methodName} غير متاح`;
        }

        // منع إرسال النموذج وعرض الرسالة
        const form = button.closest('form');
        if (form) {
            form.onsubmit = function(e) {
                e.preventDefault();
                showPaymentDisabledMessage(methodName);
                return false;
            };
        }
    }
}

// الاستماع لتحديثات بوابات الدفع من الصفحات الأخرى
if (window.BroadcastChannel) {
    const channel = new BroadcastChannel('payment_gateway_updates');
    channel.addEventListener('message', function(event) {
        if (event.data.type === 'payment_gateway_status_updated') {
            console.log('📡 تم استقبال إشعار تحديث بوابات الدفع:', event.data);

            // تحديث الحالة المحلية مباشرة من الرسالة
            if (event.data.data) {
                paymentGatewayStatus = event.data.data;
                console.log('✅ تم تحديث الحالة المحلية:', paymentGatewayStatus);

                // تحديث الأزرار فور<|im_start|> بدون استدعاء API
                updatePaymentButtonsFromLocalData();

                // إظهار إشعار للمستخدم إذا كان التحديث من الأدمن
                if (event.data.source === 'admin_toggle' && event.data.gateway_changed) {
                    const gatewayName = event.data.gateway_changed === 'paypal' ? 'PayPal' :
                                       event.data.gateway_changed === 'stripe' ? 'Stripe' : 'التحويل البنكي';
                    const statusText = event.data.new_status ? 'تم تفعيل' : 'تم إيقاف';

                    // إشعار بسيط وغير مزعج
                    console.log(`🔔 ${statusText} ${gatewayName}`);
                }
            } else {
                // إذا لم تكن البيانات متوفرة، استدعي API
                console.log('⚠️ لا توجد بيانات في الإشعار - استدعاء API');
                updatePaymentButtons();
            }
        }
    });
    console.log('📻 تم تفعيل الاستماع لتحديثات بوابات الدفع');
}

// دالة تحديث الأزرار من البيانات المحلية
function updatePaymentButtonsFromLocalData() {
    console.log('🔄 تحديث أزرار الدفع من البيانات المحلية...');
    console.log('الحالة الحالية:', paymentGatewayStatus);

    let updatedCount = 0;

    // تحديث أزرار الدفع
    document.querySelectorAll('form').forEach(form => {
        const paymentMethodInput = form.querySelector('input[name="payment_method"]');
        if (paymentMethodInput) {
            const paymentMethod = paymentMethodInput.value;
            const button = form.querySelector('button[type="submit"]');

            if (button) {
                if (paymentMethod === 'paypal') {
                    updatePaymentButton(button, paymentGatewayStatus.paypal, 'PayPal', paymentMethod);
                    updatedCount++;
                } else if (paymentMethod === 'stripe') {
                    updatePaymentButton(button, paymentGatewayStatus.stripe, 'Stripe', paymentMethod);
                    updatedCount++;
                } else if (paymentMethod === 'bank_transfer') {
                    updatePaymentButton(button, paymentGatewayStatus.bank_transfer, 'التحويل البنكي', paymentMethod);
                    updatedCount++;
                }
            }
        }
    });

    console.log(`✅ تم تحديث ${updatedCount} زر دفع`);
}

// تحديث أزرار الدفع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل صفحة الاشتراكات...');

    // التحقق من تحميل المكتبات المطلوبة
    console.log('paymentPopupManager متاح:', !!window.paymentPopupManager);
    console.log('Swal متاح:', !!window.Swal);

    updatePaymentButtons();

    // تحديث دوري كل 30 ثانية
    setInterval(() => {
        updatePaymentButtons();
        // إعادة إعداد النوافذ المنبثقة بعد كل تحديث
        setTimeout(setupPaymentPopups, 500);
    }, 30000);

    // انتظار تحميل paymentPopupManager ثم إعداد النوافذ المنبثقة
    setTimeout(() => {
        console.log('🔧 محاولة إعداد النوافذ المنبثقة...');
        setupPaymentPopups();
    }, 1000);

    // إعداد مراقب للتغييرات في DOM
    setupDOMObserver();

    // إعداد event delegation للدفع
    setupPaymentDelegation();

    // إضافة تشخيص إضافي وإعداد مباشر للأزرار
    setTimeout(() => {
        const forms = document.querySelectorAll('form[action*="subscribe_to_plan"]');
        console.log(`📊 إحصائيات: تم العثور على ${forms.length} نموذج دفع`);

        forms.forEach((form, index) => {
            const method = form.querySelector('input[name="payment_method"]')?.value;
            console.log(`   - نموذج ${index + 1}: ${method}`);

            // إضافة event listener مباشر لكل نموذج
            if (method === 'paypal' || method === 'stripe') {
                console.log(`🔧 إضافة event listener مباشر لـ ${method}`);

                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    console.log(`🚀 تم اعتراض نموذج ${method}!`);

                    // التحقق من وجود paymentPopupManager
                    if (!window.paymentPopupManager) {
                        console.error('❌ paymentPopupManager غير متاح');
                        alert('نظام النوافذ المنبثقة غير متاح. سيتم استخدام الطريقة التقليدية.');
                        form.submit();
                        return;
                    }

                    const button = form.querySelector('button[type="submit"]');
                    if (button && !button.disabled) {
                        // تعطيل الزر مؤقتاً
                        const originalText = button.innerHTML;
                        button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري فتح النافذة...';
                        button.disabled = true;

                        // إرسال النموذج للحصول على رابط الدفع
                        const formData = new FormData(form);

                        fetch(form.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                            },
                            redirect: 'manual'
                        })
                        .then(response => {
                            console.log('📡 استجابة الخادم:', response.status, response.headers.get('Location'));

                            if (response.status === 302 || response.status === 301) {
                                const redirectUrl = response.headers.get('Location');

                                if (redirectUrl) {
                                    console.log('🎯 فتح النافذة المنبثقة:', redirectUrl);

                                    const paymentData = {
                                        plan_id: form.action.split('/').slice(-2, -1)[0],
                                        payment_method: method,
                                        amount: extractAmountFromForm(form)
                                    };

                                    const success = window.paymentPopupManager.openPayment(
                                        redirectUrl,
                                        method,
                                        paymentData
                                    );

                                    if (!success) {
                                        console.log('❌ فشل فتح النافذة المنبثقة');
                                        window.location.href = redirectUrl;
                                    }
                                } else {
                                    throw new Error('لم يتم العثور على رابط إعادة التوجيه');
                                }
                            } else {
                                throw new Error(`خطأ في الخادم: ${response.status}`);
                            }
                        })
                        .catch(error => {
                            console.error('❌ خطأ في إعداد الدفع:', error);
                            alert('حدث خطأ أثناء إعداد عملية الدفع. سيتم استخدام الطريقة التقليدية.');
                            form.submit();
                        })
                        .finally(() => {
                            // إعادة تفعيل الزر
                            button.innerHTML = originalText;
                            button.disabled = false;
                        });
                    }
                });
            }
        });
    }, 2000);
});

// إعداد النوافذ المنبثقة للدفع
function setupPaymentPopups() {
    console.log('إعداد النوافذ المنبثقة للدفع...');

    // التأكد من وجود paymentPopupManager
    if (!window.paymentPopupManager) {
        console.error('paymentPopupManager غير متاح!');
        return;
    }

    // البحث عن جميع نماذج الدفع
    const forms = document.querySelectorAll('form[action*="subscribe_to_plan"]');
    console.log(`تم العثور على ${forms.length} نموذج دفع`);

    forms.forEach((form, index) => {
        const paymentMethodInput = form.querySelector('input[name="payment_method"]');

        if (paymentMethodInput) {
            const paymentMethod = paymentMethodInput.value;
            console.log(`نموذج ${index + 1}: طريقة الدفع = ${paymentMethod}`);

            // إضافة معالج الأحداث للنوافذ المنبثقة (PayPal و Stripe فقط)
            if (paymentMethod === 'paypal' || paymentMethod === 'stripe') {
                console.log(`إضافة معالج الأحداث لـ ${paymentMethod}`);

                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    console.log(`تم الضغط على زر ${paymentMethod}`);

                    const button = form.querySelector('button[type="submit"]');
                    if (button && !button.disabled) {
                        // تعطيل الزر مؤقتاً
                        const originalText = button.innerHTML;
                        button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري التحضير...';
                        button.disabled = true;

                        // إرسال النموذج للحصول على رابط الدفع
                        const formData = new FormData(form);

                        fetch(form.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                            },
                            redirect: 'manual'  // منع إعادة التوجيه التلقائي
                        })
                        .then(response => {
                            if (response.status === 302 || response.status === 301) {
                                // الحصول على رابط إعادة التوجيه
                                const redirectUrl = response.headers.get('Location') || response.url;

                                // فتح النافذة المنبثقة
                                const paymentData = {
                                    plan_id: form.action.split('/').slice(-2, -1)[0],
                                    payment_method: paymentMethod,
                                    amount: extractAmountFromForm(form)
                                };

                                console.log('فتح النافذة المنبثقة:', redirectUrl);

                                const success = window.paymentPopupManager.openPayment(
                                    redirectUrl,
                                    paymentMethod,
                                    paymentData
                                );

                                if (!success) {
                                    // فشل فتح النافذة المنبثقة - استخدم الطريقة التقليدية
                                    console.log('فشل فتح النافذة المنبثقة، استخدام الطريقة التقليدية');
                                    window.location.href = redirectUrl;
                                }
                            } else if (response.ok) {
                                // استجابة ناجحة بدون إعادة توجيه
                                return response.text().then(html => {
                                    // البحث عن رابط إعادة التوجيه في HTML
                                    const parser = new DOMParser();
                                    const doc = parser.parseFromString(html, 'text/html');
                                    const metaRefresh = doc.querySelector('meta[http-equiv="refresh"]');

                                    if (metaRefresh) {
                                        const content = metaRefresh.getAttribute('content');
                                        const urlMatch = content.match(/url=(.+)/);
                                        if (urlMatch) {
                                            const redirectUrl = urlMatch[1];

                                            const paymentData = {
                                                plan_id: form.action.split('/').slice(-2, -1)[0],
                                                payment_method: paymentMethod,
                                                amount: extractAmountFromForm(form)
                                            };

                                            const success = window.paymentPopupManager.openPayment(
                                                redirectUrl,
                                                paymentMethod,
                                                paymentData
                                            );

                                            if (!success) {
                                                window.location.href = redirectUrl;
                                            }
                                            return;
                                        }
                                    }

                                    throw new Error('لم يتم العثور على رابط الدفع');
                                });
                            } else {
                                // خطأ في الخادم
                                throw new Error(`خطأ في الخادم: ${response.status}`);
                            }
                        })
                        .catch(error => {
                            console.error('خطأ في إعداد الدفع:', error);
                            Swal.fire({
                                title: 'خطأ في إعداد الدفع',
                                text: 'حدث خطأ أثناء إعداد عملية الدفع. يرجى المحاولة مرة أخرى.',
                                icon: 'error',
                                confirmButtonText: 'حسناً'
                            });
                        })
                        .finally(() => {
                            // إعادة تفعيل الزر
                            button.innerHTML = originalText;
                            button.disabled = false;
                        });
                    }
                });
            } else {
                console.log(`تخطي ${paymentMethod} - ليس PayPal أو Stripe`);
            }
            // التحويل البنكي يبقى كما هو (بدون نافذة منبثقة)
        } else {
            console.log(`نموذج ${index + 1}: لا يحتوي على input payment_method`);
        }
    });

    console.log('انتهى إعداد النوافذ المنبثقة');
}

// إعداد مراقب للتغييرات في DOM
function setupDOMObserver() {
    console.log('إعداد مراقب DOM...');

    // مراقبة التغييرات في أزرار الدفع
    const observer = new MutationObserver(function(mutations) {
        let shouldResetup = false;

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                // التحقق من إضافة أو إزالة أزرار الدفع
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && (
                        node.querySelector && (
                            node.querySelector('form[action*="subscribe_to_plan"]') ||
                            node.matches && node.matches('form[action*="subscribe_to_plan"]')
                        )
                    )) {
                        shouldResetup = true;
                    }
                });
            }
        });

        if (shouldResetup) {
            console.log('تم اكتشاف تغييرات في أزرار الدفع - إعادة إعداد النوافذ المنبثقة');
            setTimeout(setupPaymentPopups, 100);
        }
    });

    // مراقبة التغييرات في الصفحة
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// إعداد event delegation للدفع (طريقة أكثر فعالية)
function setupPaymentDelegation() {
    console.log('🎯 إعداد event delegation للدفع...');

    // إضافة event listener واحد للصفحة كاملة
    document.body.addEventListener('submit', function(e) {
        console.log('🔥 تم اكتشاف submit event:', e.target);
        const form = e.target;

        // التحقق من أن هذا نموذج اشتراك
        if (form.matches && form.matches('form[action*="subscribe_to_plan"]')) {
            console.log('✅ هذا نموذج اشتراك!', form.action);
            const paymentMethodInput = form.querySelector('input[name="payment_method"]');

            if (paymentMethodInput) {
                const paymentMethod = paymentMethodInput.value;
                console.log(`💳 تم الضغط على زر ${paymentMethod} (event delegation)`);

                // التحقق من أن هذا PayPal أو Stripe
                if (paymentMethod === 'paypal' || paymentMethod === 'stripe') {
                    e.preventDefault();
                    console.log(`🚫 منع الإرسال التقليدي لـ ${paymentMethod}`);

                    // التحقق من وجود paymentPopupManager
                    if (!window.paymentPopupManager) {
                        console.error('paymentPopupManager غير متاح - استخدام الطريقة التقليدية');
                        form.submit();
                        return;
                    }

                    const button = form.querySelector('button[type="submit"]');
                    if (button && !button.disabled) {
                        // تعطيل الزر مؤقتاً
                        const originalText = button.innerHTML;
                        button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري التحضير...';
                        button.disabled = true;

                        // إرسال النموذج للحصول على رابط الدفع
                        const formData = new FormData(form);

                        fetch(form.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                            },
                            redirect: 'manual'  // منع إعادة التوجيه التلقائي
                        })
                        .then(response => {
                            if (response.status === 302 || response.status === 301) {
                                // الحصول على رابط إعادة التوجيه
                                const redirectUrl = response.headers.get('Location') || response.url;

                                // فتح النافذة المنبثقة
                                const paymentData = {
                                    plan_id: form.action.split('/').slice(-2, -1)[0],
                                    payment_method: paymentMethod,
                                    amount: extractAmountFromForm(form)
                                };

                                console.log('فتح النافذة المنبثقة:', redirectUrl);

                                const success = window.paymentPopupManager.openPayment(
                                    redirectUrl,
                                    paymentMethod,
                                    paymentData
                                );

                                if (!success) {
                                    // فشل فتح النافذة المنبثقة - استخدم الطريقة التقليدية
                                    console.log('فشل فتح النافذة المنبثقة، استخدام الطريقة التقليدية');
                                    window.location.href = redirectUrl;
                                }
                            } else {
                                console.error(`خطأ في الخادم: ${response.status}`);
                                throw new Error(`خطأ في الخادم: ${response.status}`);
                            }
                        })
                        .catch(error => {
                            console.error('خطأ في إعداد الدفع:', error);
                            Swal.fire({
                                title: 'خطأ في إعداد الدفع',
                                text: 'حدث خطأ أثناء إعداد عملية الدفع. يرجى المحاولة مرة أخرى.',
                                icon: 'error',
                                confirmButtonText: 'حسناً'
                            });
                        })
                        .finally(() => {
                            // إعادة تفعيل الزر
                            button.innerHTML = originalText;
                            button.disabled = false;
                        });
                    }
                } else {
                    console.log(`السماح بالإرسال التقليدي لـ ${paymentMethod}`);
                    // السماح بالإرسال التقليدي للتحويل البنكي
                }
            }
        }
    });
}

// استخراج المبلغ من النموذج
function extractAmountFromForm(form) {
    // البحث عن المبلغ في النص المحيط بالنموذج
    const planCard = form.closest('.bg-white');
    if (planCard) {
        const priceElement = planCard.querySelector('.text-4xl');
        if (priceElement) {
            const priceText = priceElement.textContent.trim();
            const match = priceText.match(/[\d,]+\.?\d*/);
            return match ? match[0] : '0';
        }
    }
    return '0';
}

// دالة تجديد الباقة الحالية
function renewCurrentPlan(subscriptionId) {
    Swal.fire({
        title: 'تجديد الباقة',
        text: 'هل تريد تجديد باقتك الحالية؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، جدد الباقة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#3b82f6'
    }).then((result) => {
        if (result.isConfirmed) {
            // إعادة توجيه لصفحة تجديد الباقة
            window.location.href = `/dashboard/student/renew-subscription/${subscriptionId}/`;
        }
    });
}
</script>

<!-- تضمين نظام النوافذ المنبثقة -->
<script src="{% static 'js/payment_popup.js' %}"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- حل بسيط ومباشر للنوافذ المنبثقة -->
<script>
// حل بسيط ومباشر
document.addEventListener('DOMContentLoaded', function() {
    // انتظار تحميل كل شيء
    setTimeout(function() {
        // البحث عن جميع أزرار الدفع
        const forms = document.querySelectorAll('form[action*="subscribe_to_plan"]');

        forms.forEach(function(form) {
            const methodInput = form.querySelector('input[name="payment_method"]');
            if (methodInput) {
                const method = methodInput.value;

                if (method === 'paypal' || method === 'stripe') {
                    const button = form.querySelector('button[type="submit"]');
                    if (button) {
                        // إضافة onclick مباشر
                        button.onclick = function(e) {
                            e.preventDefault();

                            // التحقق من وجود نظام النوافذ المنبثقة
                            if (window.paymentPopupManager) {
                                // تعطيل الزر
                                button.disabled = true;
                                const originalText = button.innerHTML;
                                button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري فتح النافذة...';

                                // إرسال النموذج
                                const formData = new FormData(form);

                                fetch(form.action, {
                                    method: 'POST',
                                    body: formData,
                                    headers: {
                                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                                    },
                                    redirect: 'manual'
                                })
                                .then(function(response) {
                                    if (response.status === 302) {
                                        const location = response.headers.get('Location');
                                        if (location) {
                                            const paymentData = {
                                                plan_id: form.action.split('/').slice(-2, -1)[0],
                                                payment_method: method,
                                                amount: '18.00'
                                            };

                                            const success = window.paymentPopupManager.openPayment(
                                                location,
                                                method,
                                                paymentData
                                            );

                                            if (!success) {
                                                window.location.href = location;
                                            }
                                        }
                                    } else {
                                        throw new Error('خطأ في الخادم');
                                    }
                                })
                                .catch(function(error) {
                                    alert('حدث خطأ. سيتم استخدام الطريقة التقليدية.');
                                    form.submit();
                                })
                                .finally(function() {
                                    button.disabled = false;
                                    button.innerHTML = originalText;
                                });
                            } else {
                                // استخدام الطريقة التقليدية
                                form.submit();
                            }
                        };
                    }
                }
            }
        });
    }, 2000);
});
</script>
{% endblock %}
