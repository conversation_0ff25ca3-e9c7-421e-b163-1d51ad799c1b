<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>فاتورة رقم {{ invoice.invoice_number }}</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            font-size: 12px;
            direction: rtl;
        }

        .header {
            background: linear-gradient(135deg, #2D5A87 0%, #1E3A5F 100%);
            color: white;
            padding: 20px;
            margin-bottom: 20px;
        }

        .header-content {
            display: table;
            width: 100%;
        }

        .company-info {
            display: table-cell;
            width: 60%;
            vertical-align: top;
        }

        .company-info h1 {
            font-size: 20px;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .company-info p {
            margin-bottom: 3px;
            opacity: 0.9;
            font-size: 11px;
        }

        .invoice-info {
            display: table-cell;
            width: 40%;
            text-align: left;
            vertical-align: top;
        }

        .invoice-info h2 {
            font-size: 18px;
            margin-bottom: 8px;
        }

        .invoice-info p {
            margin-bottom: 3px;
            font-size: 11px;
        }

        .status-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            font-weight: bold;
        }

        .status-paid { background: #D1FAE5; color: #065F46; }
        .status-pending { background: #FEF3C7; color: #92400E; }
        .status-overdue { background: #FEE2E2; color: #991B1B; }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin: 15px 0 8px 0;
            color: #2D5A87;
            border-bottom: 1px solid #2D5A87;
            padding-bottom: 3px;
        }

        .info-box {
            background: #f9fafb;
            padding: 12px;
            margin-bottom: 15px;
            border-radius: 4px;
        }

        .label {
            font-weight: bold;
            color: #6b7280;
            font-size: 10px;
        }

        .value {
            color: #111827;
            font-weight: 600;
            font-size: 11px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .invoice-table th,
        .invoice-table td {
            padding: 8px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
            font-size: 10px;
        }

        .invoice-table th {
            background: #f9fafb;
            font-weight: bold;
            color: #374151;
            text-transform: uppercase;
        }

        .totals-box {
            float: left;
            width: 250px;
            background: #f9fafb;
            padding: 12px;
            margin-bottom: 15px;
            border-radius: 4px;
        }

        .total-row {
            display: table;
            width: 100%;
            padding: 4px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .total-row:last-child {
            border-bottom: 2px solid #2D5A87;
            font-weight: bold;
            color: #2D5A87;
        }

        .total-label {
            display: table-cell;
            text-align: right;
            font-size: 10px;
        }

        .total-value {
            display: table-cell;
            text-align: left;
            font-size: 10px;
            font-weight: 600;
        }

        .subscription-box {
            background: #eff6ff;
            padding: 12px;
            margin-bottom: 15px;
            border-radius: 4px;
            clear: both;
        }

        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 9px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="company-info">
                {% if company_info.logo %}
                <div style="display: table; width: 100%;">
                    <div style="display: table-cell; width: 60px; vertical-align: top; padding-left: 15px;">
                        <img src="{{ company_info.logo }}" alt="{{ company_info.name }}" style="width: 50px; height: 50px; border-radius: 5px; background: white; padding: 3px;">
                    </div>
                    <div style="display: table-cell; vertical-align: top;">
                        <h1>{{ company_info.name }}</h1>
                        {% if company_info.description %}
                        <p style="font-size: 10px; margin-bottom: 5px;">{{ company_info.description }}</p>
                        {% endif %}
                        <p>{{ company_info.address }}</p>
                        <p>{{ company_info.phone }}</p>
                        <p>{{ company_info.email }}</p>
                        {% if company_info.website %}
                        <p>{{ company_info.website }}</p>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <h1>{{ company_info.name }}</h1>
                {% if company_info.description %}
                <p style="font-size: 10px; margin-bottom: 5px;">{{ company_info.description }}</p>
                {% endif %}
                <p>{{ company_info.address }}</p>
                <p>{{ company_info.phone }}</p>
                <p>{{ company_info.email }}</p>
                {% if company_info.website %}
                <p>{{ company_info.website }}</p>
                {% endif %}
                {% endif %}
            </div>
            <div class="invoice-info">
                <h2>فاتورة</h2>
                <p>رقم: {{ invoice.invoice_number }}</p>
                <p>التاريخ: {{ invoice.issue_date|date:"Y-m-d" }}</p>
                <p>الاستحقاق: {{ invoice.due_date|date:"Y-m-d" }}</p>
                <p>
                    الحالة: 
                    <span class="status-badge status-{{ invoice.status }}">
                        {{ invoice.get_status_display }}
                    </span>
                </p>
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <h3 class="section-title">معلومات العميل</h3>
    <div class="info-box">
        <div style="display: table; width: 100%;">
            <div style="display: table-row;">
                <div style="display: table-cell; width: 30%; padding: 3px 0;"><span class="label">الاسم:</span></div>
                <div style="display: table-cell; padding: 3px 0;"><span class="value">{{ invoice.customer_name }}</span></div>
            </div>
            <div style="display: table-row;">
                <div style="display: table-cell; width: 30%; padding: 3px 0;"><span class="label">البريد الإلكتروني:</span></div>
                <div style="display: table-cell; padding: 3px 0;"><span class="value">{{ invoice.customer_email }}</span></div>
            </div>
            {% if invoice.customer_phone %}
            <div style="display: table-row;">
                <div style="display: table-cell; width: 30%; padding: 3px 0;"><span class="label">رقم الهاتف:</span></div>
                <div style="display: table-cell; padding: 3px 0;"><span class="value">{{ invoice.customer_phone }}</span></div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Invoice Items -->
    <h3 class="section-title">تفاصيل الفاتورة</h3>
    <table class="invoice-table">
        <thead>
            <tr>
                <th>الوصف</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>المجموع</th>
            </tr>
        </thead>
        <tbody>
            {% for item in items %}
            <tr>
                <td>{{ item.description }}</td>
                <td>{{ item.quantity }}</td>
                <td>{{ item.unit_price|floatformat:2 }}</td>
                <td>{{ item.total_price|floatformat:2 }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Totals -->
    <div class="totals-box">
        <div class="total-row">
            <div class="total-label">المجموع الفرعي:</div>
            <div class="total-value">{{ invoice.subtotal|floatformat:2 }}</div>
        </div>
        {% if invoice.tax_amount > 0 %}
        <div class="total-row">
            <div class="total-label">الضريبة:</div>
            <div class="total-value">{{ invoice.tax_amount|floatformat:2 }}</div>
        </div>
        {% endif %}
        {% if invoice.discount_amount > 0 %}
        <div class="total-row">
            <div class="total-label">الخصم:</div>
            <div class="total-value" style="color: #059669;">-{{ invoice.discount_amount|floatformat:2 }}</div>
        </div>
        {% endif %}
        <div class="total-row">
            <div class="total-label">المجموع الإجمالي:</div>
            <div class="total-value">{{ invoice.total_amount|floatformat:2 }}</div>
        </div>
    </div>

    <!-- Subscription Details -->
    <h3 class="section-title">تفاصيل الاشتراك</h3>
    <div class="subscription-box">
        <div style="display: table; width: 100%;">
            <div style="display: table-row;">
                <div style="display: table-cell; width: 30%; padding: 3px 0;"><span class="label">الباقة:</span></div>
                <div style="display: table-cell; padding: 3px 0;"><span class="value">{{ invoice.plan_name }}</span></div>
            </div>
            <div style="display: table-row;">
                <div style="display: table-cell; width: 30%; padding: 3px 0;"><span class="label">تاريخ البداية:</span></div>
                <div style="display: table-cell; padding: 3px 0;"><span class="value">{{ invoice.subscription.start_date|date:"Y-m-d" }}</span></div>
            </div>
            <div style="display: table-row;">
                <div style="display: table-cell; width: 30%; padding: 3px 0;"><span class="label">تاريخ النهاية:</span></div>
                <div style="display: table-cell; padding: 3px 0;"><span class="value">{{ invoice.subscription.end_date|date:"Y-m-d" }}</span></div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>شكراً لك على اختيار {{ company_info.name }}</p>
        <p>تم إنشاء هذه الفاتورة إلكترونياً في {{ invoice.created_at|date:"Y-m-d H:i" }}</p>
    </div>
</body>
</html>
