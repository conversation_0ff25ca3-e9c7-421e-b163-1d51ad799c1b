"""
Views للنظام الموحد للحصص
يدعم جميع أنواع المستخدمين والحصص
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json
from datetime import datetime, timedelta

from .models import Lesson, StudentEvaluation, MakeupRequest
from subscriptions.models import StudentSubscription
from users.models import User


def create_sample_lessons(admin_user):
    """إنشاء بيانات تجريبية للنظام"""
    try:
        # إنشاء مستخدمين تجريبيين
        teacher_user, created = User.objects.get_or_create(
            username='teacher_demo',
            defaults={
                'first_name': 'محمد',
                'last_name': 'المعلم',
                'email': '<EMAIL>'
            }
        )

        student_user, created = User.objects.get_or_create(
            username='student_demo',
            defaults={
                'first_name': 'علي',
                'last_name': 'الطالب',
                'email': '<EMAIL>'
            }
        )

        now = timezone.now()

        # حصة تجريبية
        Lesson.objects.create(
            title="حصة تجريبية - تعلم التجويد",
            lesson_type="trial",
            teacher=teacher_user,
            student=student_user,
            created_by=admin_user,
            scheduled_date=now + timedelta(hours=2),
            duration_minutes=45,
            status="scheduled",
            description="حصة تجريبية لتعلم أساسيات التجويد"
        )

        # حصة من الاشتراك
        Lesson.objects.create(
            title="حصة رقم 1 - سورة الفاتحة",
            lesson_type="subscription",
            lesson_number=1,
            teacher=teacher_user,
            student=student_user,
            created_by=admin_user,
            scheduled_date=now + timedelta(days=1),
            duration_minutes=60,
            status="scheduled",
            description="تعلم تلاوة سورة الفاتحة بالتجويد الصحيح"
        )

        # حصة جارية
        Lesson.objects.create(
            title="حصة مباشرة - مراجعة الحفظ",
            lesson_type="subscription",
            lesson_number=5,
            teacher=teacher_user,
            student=student_user,
            created_by=admin_user,
            scheduled_date=now - timedelta(minutes=10),
            started_at=now - timedelta(minutes=10),
            duration_minutes=45,
            status="live",
            description="مراجعة الحفظ السابق"
        )

        # حصة مكتملة
        Lesson.objects.create(
            title="حصة رقم 3 - سورة البقرة",
            lesson_type="subscription",
            lesson_number=3,
            teacher=teacher_user,
            student=student_user,
            created_by=admin_user,
            scheduled_date=now - timedelta(days=2),
            started_at=now - timedelta(days=2),
            ended_at=now - timedelta(days=2) + timedelta(minutes=45),
            duration_minutes=45,
            status="completed",
            description="تعلم تلاوة سورة البقرة"
        )

        # حصة تعويضية
        Lesson.objects.create(
            title="حصة تعويضية - تكملة سورة آل عمران",
            lesson_type="makeup",
            teacher=teacher_user,
            student=student_user,
            created_by=admin_user,
            scheduled_date=now + timedelta(days=3),
            duration_minutes=60,
            status="scheduled",
            description="حصة تعويضية لتكملة سورة آل عمران"
        )

    except Exception as e:
        print(f"خطأ في إنشاء البيانات التجريبية: {e}")


def get_user_lessons_queryset(user):
    """الحصول على queryset الحصص حسب نوع المستخدم"""
    if hasattr(user, 'is_admin') and user.is_admin():
        return Lesson.objects.all()
    elif user.user_type == 'teacher':
        return Lesson.objects.filter(teacher=user)
    elif user.user_type == 'student':
        return Lesson.objects.filter(student=user)
    else:
        return Lesson.objects.none()


def get_lessons_stats(user, lessons_queryset=None):
    """حساب إحصائيات الحصص"""
    if lessons_queryset is None:
        lessons_queryset = get_user_lessons_queryset(user)
    
    now = timezone.now()
    
    stats = {
        'total_lessons': lessons_queryset.count(),
        'completed_lessons': lessons_queryset.filter(status__in=['completed', 'rated']).count(),
        'upcoming_lessons': lessons_queryset.filter(
            status='scheduled',
            scheduled_date__gt=now
        ).count(),
        'today_lessons': lessons_queryset.filter(
            scheduled_date__date=now.date()
        ).count(),
        'live_lessons': lessons_queryset.filter(status='live').count(),
        'cancelled_lessons': lessons_queryset.filter(
            status__in=['cancelled_by_student', 'cancelled_by_teacher', 'cancelled_by_admin']
        ).count(),
    }
    
    # إحصائيات حسب النوع
    stats.update({
        'subscription_lessons': lessons_queryset.filter(lesson_type='subscription').count(),
        'trial_lessons': lessons_queryset.filter(lesson_type='trial').count(),
        'makeup_lessons': lessons_queryset.filter(lesson_type='makeup').count(),
    })
    
    # الحصة القادمة
    next_lesson = lessons_queryset.filter(
        status='scheduled',
        scheduled_date__gt=now
    ).order_by('scheduled_date').first()
    
    if next_lesson:
        stats['next_lesson_date'] = next_lesson.scheduled_date
    
    # إحصائيات خاصة بكل نوع مستخدم
    if user.user_type == 'teacher':
        stats['active_students'] = lessons_queryset.filter(
            status__in=['scheduled', 'completed', 'rated']
        ).values('student').distinct().count()
        
        stats['new_students_this_month'] = lessons_queryset.filter(
            created_at__gte=now.replace(day=1),
            lesson_type='trial'
        ).values('student').distinct().count()
        
    elif user.user_type == 'student':
        # متوسط التقييم
        evaluations = StudentEvaluation.objects.filter(student=user)
        if evaluations.exists():
            stats['average_rating'] = evaluations.aggregate(
                avg=Avg('overall_rating')
            )['avg']
            stats['total_evaluations'] = evaluations.count()
    
    return stats


def apply_lessons_filters(queryset, filters):
    """تطبيق الفلاتر على queryset الحصص"""
    if filters.get('teacher_id'):
        queryset = queryset.filter(teacher_id=filters['teacher_id'])
    
    if filters.get('student_id'):
        queryset = queryset.filter(student_id=filters['student_id'])
    
    if filters.get('lesson_type'):
        queryset = queryset.filter(lesson_type=filters['lesson_type'])
    
    if filters.get('status'):
        queryset = queryset.filter(status=filters['status'])
    
    if filters.get('date_filter'):
        now = timezone.now()
        date_filter = filters['date_filter']
        
        if date_filter == 'today':
            queryset = queryset.filter(scheduled_date__date=now.date())
        elif date_filter == 'tomorrow':
            tomorrow = now.date() + timedelta(days=1)
            queryset = queryset.filter(scheduled_date__date=tomorrow)
        elif date_filter == 'this_week':
            start_week = now.date() - timedelta(days=now.weekday())
            end_week = start_week + timedelta(days=6)
            queryset = queryset.filter(scheduled_date__date__range=[start_week, end_week])
        elif date_filter == 'next_week':
            start_next_week = now.date() + timedelta(days=(7 - now.weekday()))
            end_next_week = start_next_week + timedelta(days=6)
            queryset = queryset.filter(scheduled_date__date__range=[start_next_week, end_next_week])
        elif date_filter == 'this_month':
            queryset = queryset.filter(
                scheduled_date__year=now.year,
                scheduled_date__month=now.month
            )
        elif date_filter == 'next_month':
            next_month = now.replace(day=1) + timedelta(days=32)
            next_month = next_month.replace(day=1)
            queryset = queryset.filter(
                scheduled_date__year=next_month.year,
                scheduled_date__month=next_month.month
            )
    
    if filters.get('search'):
        search_term = filters['search']
        queryset = queryset.filter(
            Q(title__icontains=search_term) |
            Q(description__icontains=search_term) |
            Q(teacher__first_name__icontains=search_term) |
            Q(teacher__last_name__icontains=search_term) |
            Q(student__first_name__icontains=search_term) |
            Q(student__last_name__icontains=search_term)
        )
    
    return queryset


@login_required
def admin_lessons_dashboard(request):
    """لوحة تحكم المدير للحصص"""
    if not (hasattr(request.user, 'is_admin') and request.user.is_admin()):
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('dashboard')

    # تحديد المسار الحالي لتحديد نوع العرض
    current_path = request.path
    if '/dashboard/admin/lessons/' in current_path:
        # المسار الجديد الصحيح
        template_name = 'admin/lessons_dashboard.html'
    else:
        # المسار القديم - إعادة توجيه للمسار الصحيح
        return redirect('/dashboard/admin/lessons/')
    
    # إنشاء بيانات تجريبية إذا لم تكن موجودة
    if Lesson.objects.count() == 0:
        create_sample_lessons(request.user)

    # الحصول على جميع الحصص
    lessons_queryset = Lesson.objects.all().select_related('teacher', 'student', 'subscription')
    
    # تطبيق الفلاتر
    filters = {
        'teacher_id': request.GET.get('teacher_id'),
        'student_id': request.GET.get('student_id'),
        'lesson_type': request.GET.get('lesson_type'),
        'status': request.GET.get('status'),
        'date_filter': request.GET.get('date_filter'),
        'search': request.GET.get('search'),
    }
    
    filtered_lessons = apply_lessons_filters(lessons_queryset, filters)
    
    # ترتيب النتائج
    sort_by = request.GET.get('sort', 'date_desc')
    if sort_by == 'date_asc':
        filtered_lessons = filtered_lessons.order_by('scheduled_date')
    elif sort_by == 'date_desc':
        filtered_lessons = filtered_lessons.order_by('-scheduled_date')
    elif sort_by == 'status':
        filtered_lessons = filtered_lessons.order_by('status', 'scheduled_date')
    elif sort_by == 'type':
        filtered_lessons = filtered_lessons.order_by('lesson_type', 'scheduled_date')
    
    # Pagination
    paginator = Paginator(filtered_lessons, 20)
    page_number = request.GET.get('page')
    lessons = paginator.get_page(page_number)
    
    # حصص خاصة
    now = timezone.now()
    today_lessons = lessons_queryset.filter(scheduled_date__date=now.date())
    live_lessons = lessons_queryset.filter(status='live')
    upcoming_lessons = lessons_queryset.filter(
        status='scheduled',
        scheduled_date__gt=now
    ).order_by('scheduled_date')[:10]
    
    # الإحصائيات
    admin_stats = get_lessons_stats(request.user, lessons_queryset)
    
    # البيانات للفلاتر
    teachers = User.objects.filter(user_type='teacher', is_active=True)
    students = User.objects.filter(user_type='student', is_active=True)
    
    context = {
        'lessons': lessons,
        'today_lessons': today_lessons,
        'live_lessons': live_lessons,
        'upcoming_lessons': upcoming_lessons,
        'admin_stats': admin_stats,
        'teachers': teachers,
        'students': students,
        'today_date': now.date(),
        'filters': filters,
    }
    
    return render(request, template_name, context)


@login_required
def teacher_lessons_dashboard(request):
    """لوحة تحكم المعلم للحصص"""
    if request.user.user_type != 'teacher':
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('dashboard')

    # تحديد المسار الحالي لتحديد نوع العرض
    current_path = request.path
    if '/dashboard/teacher/lessons/' in current_path:
        # المسار الجديد الصحيح
        template_name = 'teacher/my_lessons.html'
    else:
        # المسار القديم - إعادة توجيه للمسار الصحيح
        return redirect('/dashboard/teacher/lessons/')
    
    # الحصول على حصص المعلم
    lessons_queryset = Lesson.objects.filter(teacher=request.user).select_related('student', 'subscription')
    
    # تطبيق الفلاتر
    filters = {
        'student_id': request.GET.get('student_id'),
        'lesson_type': request.GET.get('lesson_type'),
        'status': request.GET.get('status'),
        'date_filter': request.GET.get('date_filter'),
        'search': request.GET.get('search'),
    }
    
    filtered_lessons = apply_lessons_filters(lessons_queryset, filters)
    
    # ترتيب النتائج
    filtered_lessons = filtered_lessons.order_by('-scheduled_date')
    
    # Pagination
    paginator = Paginator(filtered_lessons, 15)
    page_number = request.GET.get('page')
    lessons = paginator.get_page(page_number)
    
    # حصص خاصة
    now = timezone.now()
    next_lesson = lessons_queryset.filter(
        status='scheduled',
        scheduled_date__gt=now
    ).order_by('scheduled_date').first()
    
    today_lessons = lessons_queryset.filter(scheduled_date__date=now.date())
    live_lessons = lessons_queryset.filter(status='live')
    
    # الحصص التي تحتاج تقارير
    pending_reports = lessons_queryset.filter(
        status='completed',
        teacher_report_submitted=False
    )
    
    # الإحصائيات
    teacher_stats = get_lessons_stats(request.user, lessons_queryset)
    
    # طلاب المعلم
    my_students = User.objects.filter(
        lessons_as_student__teacher=request.user
    ).distinct()
    
    context = {
        'lessons': lessons,
        'next_lesson': next_lesson,
        'today_lessons': today_lessons,
        'live_lessons': live_lessons,
        'pending_reports': pending_reports,
        'teacher_stats': teacher_stats,
        'my_students': my_students,
        'today_date': now.date(),
        'filters': filters,
    }
    
    return render(request, template_name, context)


@login_required
def student_lessons_dashboard(request):
    """لوحة تحكم الطالب للحصص"""
    if request.user.user_type != 'student':
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('dashboard')

    # تحديد المسار الحالي لتحديد نوع العرض
    current_path = request.path
    if '/dashboard/student/lessons/' in current_path:
        # المسار الجديد الصحيح
        template_name = 'student/my_lessons.html'
    else:
        # المسار القديم - إعادة توجيه للمسار الصحيح
        return redirect('/dashboard/student/lessons/')

    # الحصول على حصص الطالب
    lessons_queryset = Lesson.objects.filter(student=request.user).select_related('teacher', 'subscription')

    # الاشتراك النشط
    active_subscription = StudentSubscription.objects.filter(
        student=request.user,
        status='active'
    ).first()

    # تصنيف الحصص
    now = timezone.now()

    # الحصة القادمة
    next_lesson = lessons_queryset.filter(
        status='scheduled',
        scheduled_date__gt=now
    ).order_by('scheduled_date').first()

    # الحصص الجارية
    live_lessons = lessons_queryset.filter(status='live')

    # حصص الاشتراك
    subscription_lessons = lessons_queryset.filter(lesson_type='subscription')

    # الحصص التجريبية
    trial_lessons = lessons_queryset.filter(lesson_type='trial')

    # الحصص التعويضية
    makeup_lessons = lessons_queryset.filter(lesson_type='makeup')

    # الحصص التي تحتاج تقييم
    pending_evaluations = lessons_queryset.filter(
        status='completed',
        student_evaluation_submitted=False
    )

    # الإحصائيات
    student_stats = get_lessons_stats(request.user, lessons_queryset)

    context = {
        'active_subscription': active_subscription,
        'next_lesson': next_lesson,
        'live_lessons': live_lessons,
        'subscription_lessons': subscription_lessons,
        'trial_lessons': trial_lessons,
        'makeup_lessons': makeup_lessons,
        'pending_evaluations': pending_evaluations,
        'student_stats': student_stats,
        'today_date': now.date(),
    }

    return render(request, template_name, context)


# ===== API Views =====

@login_required
@require_http_methods(["GET"])
def lessons_api(request):
    """API للحصول على بيانات الحصص مع الفلاتر"""
    try:
        # الحصول على الحصص حسب المستخدم
        lessons_queryset = get_user_lessons_queryset(request.user)

        # تطبيق الفلاتر
        filters = {
            'teacher_id': request.GET.get('teacher_id'),
            'student_id': request.GET.get('student_id'),
            'lesson_type': request.GET.get('lesson_type'),
            'status': request.GET.get('status'),
            'date_filter': request.GET.get('date_filter'),
            'search': request.GET.get('search'),
        }

        filtered_lessons = apply_lessons_filters(lessons_queryset, filters)

        # ترتيب النتائج
        sort_by = request.GET.get('sort', 'date_desc')
        if sort_by == 'date_asc':
            filtered_lessons = filtered_lessons.order_by('scheduled_date')
        elif sort_by == 'date_desc':
            filtered_lessons = filtered_lessons.order_by('-scheduled_date')
        elif sort_by == 'status':
            filtered_lessons = filtered_lessons.order_by('status', 'scheduled_date')
        elif sort_by == 'type':
            filtered_lessons = filtered_lessons.order_by('lesson_type', 'scheduled_date')

        # تحويل البيانات إلى JSON
        lessons_data = []
        for lesson in filtered_lessons[:50]:  # حد أقصى 50 حصة
            lessons_data.append({
                'id': lesson.id,
                'title': lesson.title,
                'lesson_type': lesson.lesson_type,
                'status': lesson.status,
                'status_display': lesson.get_status_display_arabic(),
                'teacher_name': lesson.teacher.get_full_name(),
                'student_name': lesson.student.get_full_name(),
                'scheduled_date': lesson.scheduled_date.isoformat(),
                'scheduled_date_display': lesson.scheduled_date.strftime('%Y/%m/%d %H:%M'),
                'duration_minutes': lesson.duration_minutes,
                'description': lesson.description or '',
                'teacher_report': lesson.teacher_report or '',
                'teacher_report_submitted': lesson.teacher_report_submitted,
                'student_evaluation_submitted': lesson.student_evaluation_submitted,
                'cancellation_note': lesson.cancellation_note or '',
                'lesson_number': lesson.lesson_number,
            })

        # الإحصائيات
        stats = get_lessons_stats(request.user, filtered_lessons)

        return JsonResponse({
            'success': True,
            'lessons': lessons_data,
            'stats': stats,
            'total_count': filtered_lessons.count()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def lesson_detail_api(request, lesson_id):
    """API للحصول على تفاصيل حصة محددة"""
    try:
        # التحقق من الصلاحيات
        lessons_queryset = get_user_lessons_queryset(request.user)
        lesson = get_object_or_404(lessons_queryset, id=lesson_id)

        lesson_data = {
            'id': lesson.id,
            'title': lesson.title,
            'description': lesson.description or '',
            'lesson_type': lesson.lesson_type,
            'lesson_type_display': lesson.get_lesson_type_display(),
            'status': lesson.status,
            'status_display': lesson.get_status_display_arabic(),
            'teacher_name': lesson.teacher.get_full_name(),
            'student_name': lesson.student.get_full_name(),
            'scheduled_date': lesson.scheduled_date.isoformat(),
            'scheduled_date_display': lesson.scheduled_date.strftime('%Y/%m/%d %H:%M'),
            'duration_minutes': lesson.duration_minutes,
            'started_at': lesson.started_at.isoformat() if lesson.started_at else None,
            'ended_at': lesson.ended_at.isoformat() if lesson.ended_at else None,
            'teacher_report': lesson.teacher_report or '',
            'teacher_report_submitted': lesson.teacher_report_submitted,
            'student_evaluation_submitted': lesson.student_evaluation_submitted,
            'cancellation_note': lesson.cancellation_note or '',
            'cancellation_reason': lesson.cancellation_reason or '',
            'cancelled_at': lesson.cancelled_at.isoformat() if lesson.cancelled_at else None,
            'cancelled_by': lesson.cancelled_by.get_full_name() if lesson.cancelled_by else None,
            'lesson_number': lesson.lesson_number,
            'jitsi_room_id': lesson.jitsi_room_id,
            'created_at': lesson.created_at.isoformat(),
            'updated_at': lesson.updated_at.isoformat(),
        }

        # معلومات الاشتراك إذا كانت حصة من الباقة
        if lesson.subscription:
            lesson_data['subscription'] = {
                'id': lesson.subscription.id,
                'plan_name': lesson.subscription.plan.name,
                'remaining_lessons': lesson.subscription.remaining_lessons,
            }

        # معلومات الحصة الأصلية للحصص التعويضية
        if lesson.original_lesson:
            lesson_data['original_lesson'] = {
                'id': lesson.original_lesson.id,
                'scheduled_date': lesson.original_lesson.scheduled_date.strftime('%Y/%m/%d %H:%M'),
            }

        return JsonResponse({
            'success': True,
            'lesson': lesson_data
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


# ===== Views مؤقتة للتطوير =====

@login_required
def admin_create_trial_lesson(request):
    """صفحة إنشاء حصة تجريبية - مؤقتة"""
    messages.info(request, 'صفحة إنشاء الحصة التجريبية قيد التطوير')
    return redirect('lessons:admin_dashboard')

@login_required
def admin_create_subscription_lesson(request):
    """صفحة إنشاء حصة من الاشتراك - مؤقتة"""
    messages.info(request, 'صفحة إنشاء حصة الاشتراك قيد التطوير')
    return redirect('lessons:admin_dashboard')

@login_required
def student_trial_lesson(request):
    """صفحة حجز حصة تجريبية للطالب - مؤقتة"""
    messages.info(request, 'صفحة حجز الحصة التجريبية قيد التطوير')
    return redirect('lessons:student_dashboard')

@login_required
def student_book_lesson(request):
    """صفحة حجز حصة للطالب - مؤقتة"""
    messages.info(request, 'صفحة حجز الحصة قيد التطوير')
    return redirect('lessons:student_dashboard')

@login_required
def lesson_room(request, lesson_id):
    """غرفة الحصة - مؤقتة"""
    lesson = get_object_or_404(Lesson, id=lesson_id)
    messages.info(request, f'غرفة الحصة "{lesson.title}" قيد التطوير')
    return redirect('lessons:admin_dashboard')

@login_required
def teacher_availability(request):
    """إدارة أوقات المعلم - مؤقتة"""
    messages.info(request, 'صفحة إدارة الأوقات المتاحة قيد التطوير')
    return redirect('lessons:teacher_dashboard')

@login_required
def admin_bulk_reschedule(request):
    """إعادة جدولة متعددة - مؤقتة"""
    messages.info(request, 'صفحة إعادة الجدولة المتعددة قيد التطوير')
    return redirect('lessons:admin_dashboard')

@login_required
def admin_export_lessons(request):
    """تصدير البيانات - مؤقتة"""
    messages.info(request, 'صفحة تصدير البيانات قيد التطوير')
    return redirect('lessons:admin_dashboard')

@login_required
def admin_lesson_reports(request):
    """تقارير الحصص - مؤقتة"""
    messages.info(request, 'صفحة التقارير قيد التطوير')
    return redirect('lessons:admin_dashboard')

@login_required
def teacher_reports(request):
    """تقارير المعلم - مؤقتة"""
    messages.info(request, 'صفحة تقارير المعلم قيد التطوير')
    return redirect('lessons:teacher_dashboard')

@login_required
def teacher_performance(request):
    """إحصائيات المعلم - مؤقتة"""
    messages.info(request, 'صفحة إحصائيات المعلم قيد التطوير')
    return redirect('lessons:teacher_dashboard')

@login_required
def student_progress(request):
    """تقدم الطالب - مؤقتة"""
    messages.info(request, 'صفحة تقدم الطالب قيد التطوير')
    return redirect('lessons:student_dashboard')

@login_required
def student_certificates(request):
    """شهادات الطالب - مؤقتة"""
    messages.info(request, 'صفحة الشهادات قيد التطوير')
    return redirect('lessons:student_dashboard')

@login_required
def teacher_students(request):
    """طلاب المعلم - مؤقتة"""
    messages.info(request, 'صفحة طلاب المعلم قيد التطوير')
    return redirect('lessons:teacher_dashboard')

@login_required
def student_profile(request):
    """الملف الشخصي للطالب - مؤقتة"""
    messages.info(request, 'صفحة الملف الشخصي قيد التطوير')
    return redirect('lessons:student_dashboard')
